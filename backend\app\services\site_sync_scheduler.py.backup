"""
WordPress站点自动同步定时任务服务
"""

import asyncio
import logging
from datetime import datetime, time, timedelta
from typing import List
from sqlalchemy.orm import Session
from sqlalchemy import create_engine, text
from ..db.session import get_db
from ..models.wordpress_site import WordPressSite
from ..services.wordpress_service import WordPressService
import pytz

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SiteSyncScheduler:
    """站点同步定时调度器"""
    
    def __init__(self):
        self.beijing_tz = pytz.timezone('Asia/Shanghai')
        self.target_time = time(8, 0)  # 北京时间早上8点
        self.running = False
        
    async def start_scheduler(self):
        """启动定时调度器"""
        self.running = True
        logger.info("站点同步定时调度器已启动")
        
        while self.running:
            try:
                # 计算下次执行时间
                next_run_time = self._calculate_next_run_time()
                logger.info(f"下次自动同步时间: {next_run_time.strftime('%Y-%m-%d %H:%M:%S')} (北京时间)")
                
                # 等待到执行时间
                await self._wait_until(next_run_time)
                
                if self.running:
                    # 执行自动同步
                    await self._perform_auto_sync()
                    
            except Exception as e:
                logger.error(f"定时调度器异常: {str(e)}")
                # 出错后等待1小时再重试
                await asyncio.sleep(3600)
    
    def stop_scheduler(self):
        """停止定时调度器"""
        self.running = False
        logger.info("站点同步定时调度器已停止")
    
    def _calculate_next_run_time(self) -> datetime:
        """计算下次执行时间"""
        now_beijing = datetime.now(self.beijing_tz)
        
        # 今天的目标时间
        today_target = now_beijing.replace(
            hour=self.target_time.hour,
            minute=self.target_time.minute,
            second=0,
            microsecond=0
        )
        
        if now_beijing < today_target:
            # 今天还没到执行时间
            return today_target
        else:
            # 今天已过执行时间，安排明天
            tomorrow_target = today_target + timedelta(days=1)
            return tomorrow_target
    
    async def _wait_until(self, target_time: datetime):
        """等待直到指定时间"""
        now_beijing = datetime.now(self.beijing_tz)
        wait_seconds = (target_time - now_beijing).total_seconds()
        
        if wait_seconds > 0:
            # 分段等待，每5分钟检查一次是否需要停止
            while wait_seconds > 0 and self.running:
                sleep_time = min(300, wait_seconds)  # 最多等待5分钟
                await asyncio.sleep(sleep_time)
                
                # 重新计算剩余等待时间
                now_beijing = datetime.now(self.beijing_tz)
                wait_seconds = (target_time - now_beijing).total_seconds()
    
    async def _perform_auto_sync(self):
        """执行自动同步"""
        logger.info("开始执行定时自动同步")
        
        try:
            # 获取数据库会话
            db_gen = get_db()
            db = next(db_gen)
            
            try:
                # 获取所有启用的站点（排除占位符站点）
                sites = db.query(WordPressSite).filter(
                    WordPressSite.is_active == True,
                    WordPressSite.sync_status != "placeholder"
                ).all()
                
                if not sites:
                    logger.info("没有需要同步的站点")
                    return
                
                logger.info(f"找到 {len(sites)} 个站点需要同步")
                
                # 执行同步
                success_count, failed_count = await self._sync_sites(sites, db)
                
                logger.info(f"定时同步完成: 成功 {success_count} 个，失败 {failed_count} 个")
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"执行定时同步异常: {str(e)}")
    
    async def _sync_sites(self, sites: List[WordPressSite], db: Session) -> tuple[int, int]:
        """同步站点列表"""
        success_count = 0
        failed_count = 0
        
        # 并发同步，限制并发数为3
        semaphore = asyncio.Semaphore(3)
        
        async def sync_single_site(site: WordPressSite):
            nonlocal success_count, failed_count
            
            async with semaphore:
                try:
                    logger.info(f"正在同步站点: {site.name} ({site.url})")
                    
                    wp_service = WordPressService(site.url, site.wp_username, site.wp_app_password)
                    site_info_result = await wp_service.get_site_info()
                    
                    if site_info_result["success"]:
                        site_info = site_info_result["data"]
                        
                        # 更新站点信息
                        site.wordpress_version = site_info.get("wordpress_version")
                        site.theme_name = site_info.get("theme_name")
                        site.language = site_info.get("language")
                        site.timezone = site_info.get("timezone")
                        site.total_posts = site_info.get("total_posts", 0)
                        site.total_pages = site_info.get("total_pages", 0)
                        site.blog_categories = site_info.get("blog_categories", [])
                        site.blog_tags = site_info.get("blog_tags", [])
                        site.sync_status = "success"
                        site.error_message = None
                        site.last_sync_at = datetime.utcnow()
                        
                        success_count += 1
                        logger.info(f"站点同步成功: {site.name}")
                        
                    else:
                        site.sync_status = "failed"
                        site.error_message = site_info_result["error"]
                        site.last_sync_at = datetime.utcnow()
                        failed_count += 1
                        logger.error(f"站点同步失败: {site.name} - {site_info_result['error']}")
                    
                    db.commit()
                    
                except Exception as e:
                    failed_count += 1
                    logger.error(f"同步站点 {site.name} 异常: {str(e)}")
                    try:
                        site.sync_status = "failed"
                        site.error_message = f"同步异常: {str(e)}"
                        site.last_sync_at = datetime.utcnow()
                        db.commit()
                    except:
                        pass
        
        # 执行并发同步
        tasks = [sync_single_site(site) for site in sites]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        return success_count, failed_count


# 全局调度器实例
_site_sync_scheduler = None

def get_site_sync_scheduler() -> SiteSyncScheduler:
    """获取站点同步调度器实例"""
    global _site_sync_scheduler
    if _site_sync_scheduler is None:
        _site_sync_scheduler = SiteSyncScheduler()
    return _site_sync_scheduler

async def start_site_sync_scheduler():
    """启动站点同步调度器"""
    scheduler = get_site_sync_scheduler()
    if not scheduler.running:
        # 在后台启动调度器
        asyncio.create_task(scheduler.start_scheduler())

def stop_site_sync_scheduler():
    """停止站点同步调度器"""
    scheduler = get_site_sync_scheduler()
    scheduler.stop_scheduler() 