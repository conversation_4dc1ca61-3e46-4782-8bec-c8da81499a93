from typing import Optional
from pydantic import BaseModel, ConfigDict

# 共享属性
class TenantBase(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = True

class TenantCreate(TenantBase):
    """创建租户时的数据模型"""
    name: str
    id: Optional[str] = None  # 可选自定义ID

class TenantUpdate(TenantBase):
    """更新租户时的数据模型"""
    pass

class TenantInDBBase(TenantBase):
    """数据库中存储的租户数据模型"""
    id: str

    model_config = ConfigDict(from_attributes=True)

# API中返回的租户模型
class Tenant(TenantInDBBase):
    pass 