from typing import Any, List
import uuid

from fastapi import APIRouter, Body, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_current_active_superuser, get_current_user
from app.models.tenant import Tenant
from app.schemas.tenant import Tenant as TenantSchema, TenantCreate, TenantUpdate

router = APIRouter()

@router.get("/", response_model=List[TenantSchema])
def read_tenants(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: Any = Depends(get_current_active_superuser),
) -> Any:
    """
    获取所有租户
    """
    tenants = db.query(Tenant).offset(skip).limit(limit).all()
    return tenants

@router.post("/", response_model=TenantSchema)
def create_tenant(
    *,
    db: Session = Depends(get_db),
    tenant_in: TenantCreate,
    current_user: Any = Depends(get_current_active_superuser),
) -> Any:
    """
    创建新租户
    """
    # 如果未指定ID，生成一个
    if not tenant_in.id:
        tenant_in.id = str(uuid.uuid4())
    
    tenant = Tenant(
        id=tenant_in.id,
        name=tenant_in.name,
        description=tenant_in.description,
        is_active=tenant_in.is_active
    )
    db.add(tenant)
    db.commit()
    db.refresh(tenant)
    return tenant

@router.get("/{tenant_id}", response_model=TenantSchema)
def read_tenant(
    tenant_id: str,
    db: Session = Depends(get_db),
    current_user: Any = Depends(get_current_active_superuser),
) -> Any:
    """
    获取特定租户
    """
    tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
    if not tenant:
        raise HTTPException(
            status_code=404,
            detail="租户不存在",
        )
    return tenant

@router.put("/{tenant_id}", response_model=TenantSchema)
def update_tenant(
    *,
    db: Session = Depends(get_db),
    tenant_id: str,
    tenant_in: TenantUpdate,
    current_user: Any = Depends(get_current_active_superuser),
) -> Any:
    """
    更新租户
    """
    tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
    if not tenant:
        raise HTTPException(
            status_code=404,
            detail="租户不存在",
        )
    update_data = tenant_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(tenant, field, value)
    db.add(tenant)
    db.commit()
    db.refresh(tenant)
    return tenant

@router.delete("/{tenant_id}", response_model=TenantSchema)
def delete_tenant(
    *,
    db: Session = Depends(get_db),
    tenant_id: str,
    current_user: Any = Depends(get_current_active_superuser),
) -> Any:
    """
    删除租户
    """
    tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
    if not tenant:
        raise HTTPException(
            status_code=404,
            detail="租户不存在",
        )
    db.delete(tenant)
    db.commit()
    return tenant 