"""
事件管理器 - 用于实现轻量级的实时更新机制
使用Server-Sent Events (SSE) 替代轮询
"""

import asyncio
import json
import logging
from typing import Dict, Set, Any
from datetime import datetime
from app.utils.datetime_utils import utc_now, to_iso_string

logger = logging.getLogger(__name__)


class EventManager:
    """事件管理器，用于管理SSE连接和事件分发"""
    
    def __init__(self):
        # 存储活跃的SSE连接
        self.connections: Dict[str, Set[asyncio.Queue]] = {}
        self.connection_count = 0
    
    def add_connection(self, event_type: str, queue: asyncio.Queue) -> str:
        """添加新的SSE连接"""
        if event_type not in self.connections:
            self.connections[event_type] = set()
        
        self.connections[event_type].add(queue)
        self.connection_count += 1
        
        connection_id = f"{event_type}_{self.connection_count}"
        logger.info(f"新增SSE连接: {connection_id}, 当前{event_type}连接数: {len(self.connections[event_type])}")
        
        return connection_id
    
    def remove_connection(self, event_type: str, queue: asyncio.Queue):
        """移除SSE连接"""
        if event_type in self.connections and queue in self.connections[event_type]:
            self.connections[event_type].remove(queue)
            logger.info(f"移除SSE连接, 当前{event_type}连接数: {len(self.connections[event_type])}")
            
            # 如果没有连接了，清理空集合
            if not self.connections[event_type]:
                del self.connections[event_type]
    
    async def broadcast_event(self, event_type: str, data: Any):
        """广播事件到所有相关连接"""
        if event_type not in self.connections:
            print(f"[EVENT] 没有{event_type}类型的活跃连接")
            logger.info(f"没有{event_type}类型的活跃连接")
            return

        # 准备事件数据
        event_data = {
            "type": event_type,
            "data": data,
            "timestamp": utc_now().isoformat()
        }

        print(f"[EVENT] 📡 准备广播事件: {event_type}")
        print(f"[EVENT] 📡 事件数据: {event_data}")
        print(f"[EVENT] 📡 活跃连接数: {len(self.connections[event_type])}")

        # 格式化为SSE格式
        sse_data = f"data: {json.dumps(event_data, ensure_ascii=False)}\n\n"
        
        # 广播到所有连接
        disconnected_queues = []
        sent_count = 0
        for queue in self.connections[event_type].copy():
            try:
                await queue.put(sse_data)
                sent_count += 1
                print(f"[EVENT] ✅ 事件已发送到连接 {sent_count}")
            except Exception as e:
                print(f"[EVENT] ❌ 发送事件到连接失败: {e}")
                logger.warning(f"发送事件到连接失败: {e}")
                disconnected_queues.append(queue)

        print(f"[EVENT] 📡 事件广播完成: {event_type}, 成功发送: {sent_count}, 失败: {len(disconnected_queues)}")

        # 清理断开的连接
        for queue in disconnected_queues:
            self.remove_connection(event_type, queue)

        logger.info(f"广播{event_type}事件到 {len(self.connections[event_type])} 个连接")
    
    def get_connection_stats(self) -> Dict[str, int]:
        """获取连接统计信息"""
        return {
            event_type: len(queues) 
            for event_type, queues in self.connections.items()
        }


# 全局事件管理器实例
event_manager = EventManager()


async def queue_status_event_generator(queue: asyncio.Queue):
    """队列状态事件生成器"""
    try:
        while True:
            try:
                # 等待事件数据
                data = await asyncio.wait_for(queue.get(), timeout=30.0)
                yield data
            except asyncio.TimeoutError:
                # 发送心跳包保持连接
                yield "data: {\"type\": \"heartbeat\", \"timestamp\": \"" + utc_now().isoformat() + "\"}\n\n"
    except asyncio.CancelledError:
        logger.info("SSE连接被取消")
    except Exception as e:
        logger.error(f"SSE事件生成器错误: {e}")


def trigger_queue_status_update(data: Dict[str, Any]):
    """触发队列状态更新事件"""
    print(f"[EVENT] 触发队列状态更新事件: {data}")
    logger.info(f"触发队列状态更新事件: {data}")
    try:
        loop = asyncio.get_event_loop()
        if loop.is_running():
            asyncio.create_task(event_manager.broadcast_event("queue_status", data))
        else:
            loop.run_until_complete(event_manager.broadcast_event("queue_status", data))
    except Exception as e:
        print(f"[EVENT] 触发事件失败: {e}")
        logger.error(f"触发队列状态更新事件失败: {e}")


def trigger_plan_list_update(data: Dict[str, Any]):
    """触发计划列表更新事件"""
    logger.info(f"触发计划列表更新事件: {data}")
    asyncio.create_task(event_manager.broadcast_event("plan_list", data))


def trigger_task_status_update(data: Dict[str, Any]):
    """触发任务状态更新事件"""
    logger.info(f"触发任务状态更新事件: {data}")
    asyncio.create_task(event_manager.broadcast_event("task_status", data))
