#!/bin/bash

# CBEC 项目部署脚本
# 在AWS Linux 2023环境配置完成后使用
# 作者: AI Assistant
# 版本: 1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
PROJECT_DIR="/opt/cbec-app"
REPO_URL=""  # 将在运行时询问
DB_HOST=""
DB_USER=""
DB_PASS=""
DB_NAME="CBEC"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查部署环境..."
    
    # 检查必要的命令
    commands=("node" "npm" "python3" "pip3" "git" "nginx")
    for cmd in "${commands[@]}"; do
        if ! command -v $cmd &> /dev/null; then
            log_error "$cmd 未安装，请先运行环境配置脚本"
            exit 1
        fi
    done
    
    # 检查项目目录
    if [[ ! -d "$PROJECT_DIR" ]]; then
        log_error "项目目录 $PROJECT_DIR 不存在"
        exit 1
    fi
    
    # 检查权限
    if [[ ! -w "$PROJECT_DIR" ]]; then
        log_error "没有项目目录的写权限"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 获取用户输入
get_user_input() {
    log_info "请提供以下信息："
    
    # Git仓库地址
    read -p "请输入Git仓库地址: " REPO_URL
    if [[ -z "$REPO_URL" ]]; then
        log_error "Git仓库地址不能为空"
        exit 1
    fi
    
    # 数据库信息
    read -p "数据库主机地址 (默认: localhost): " DB_HOST
    DB_HOST=${DB_HOST:-localhost}
    
    read -p "数据库用户名 (默认: root): " DB_USER
    DB_USER=${DB_USER:-root}
    
    read -s -p "数据库密码: " DB_PASS
    echo
    
    read -p "数据库名称 (默认: CBEC): " DB_NAME
    DB_NAME=${DB_NAME:-CBEC}
    
    # 确认信息
    echo
    log_info "配置信息确认:"
    echo "Git仓库: $REPO_URL"
    echo "数据库: $DB_USER@$DB_HOST/$DB_NAME"
    echo
    
    read -p "确认配置信息是否正确？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "已取消部署"
        exit 0
    fi
}

# 克隆项目
clone_project() {
    log_info "克隆项目代码..."
    
    cd "$PROJECT_DIR"
    
    # 如果目录非空，先备份
    if [[ "$(ls -A .)" ]]; then
        log_warning "项目目录非空，创建备份..."
        BACKUP_DIR="backup-$(date +%Y%m%d-%H%M%S)"
        mkdir -p "../$BACKUP_DIR"
        mv * "../$BACKUP_DIR/" 2>/dev/null || true
        log_info "备份创建完成: $PROJECT_DIR/../$BACKUP_DIR"
    fi
    
    # 克隆代码
    git clone "$REPO_URL" .
    
    log_success "项目代码克隆完成"
}

# 配置环境变量
configure_environment() {
    log_info "配置环境变量..."
    
    cd "$PROJECT_DIR"
    
    # 创建.env文件
    if [[ -f "env.example" ]]; then
        cp env.example .env
    elif [[ -f ".env.example" ]]; then
        cp .env.example .env
    else
        log_warning "未找到环境变量模板，创建基础.env文件"
        touch .env
    fi
    
    # 配置数据库连接
    cat >> .env << EOF

# 部署时自动生成的配置
DATABASE_URL=mysql+pymysql://$DB_USER:$DB_PASS@$DB_HOST:3306/$DB_NAME
BACKEND_HOST=0.0.0.0
BACKEND_PORT=5000
FRONTEND_PORT=8080
PRODUCTION=true
LOG_LEVEL=INFO
EOF
    
    log_success "环境变量配置完成"
}

# 安装后端依赖
setup_backend() {
    log_info "配置后端环境..."
    
    cd "$PROJECT_DIR/backend"
    
    # 创建虚拟环境
    python3 -m venv venv
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装依赖
    if [[ -f "requirements.txt" ]]; then
        pip install -r requirements.txt
    else
        log_error "未找到 requirements.txt 文件"
        exit 1
    fi
    
    # 数据库迁移
    if [[ -f "alembic.ini" ]]; then
        log_info "执行数据库迁移..."
        python -m alembic upgrade head
    fi
    
    log_success "后端环境配置完成"
}

# 安装前端依赖
setup_frontend() {
    log_info "配置前端环境..."
    
    cd "$PROJECT_DIR/frontend"
    
    # 安装依赖
    if [[ -f "package.json" ]]; then
        npm install
    else
        log_error "未找到 package.json 文件"
        exit 1
    fi
    
    # 构建生产版本
    npm run build
    
    log_success "前端环境配置完成"
}

# 创建systemd服务
create_services() {
    log_info "创建系统服务..."
    
    # 后端服务
    sudo tee /etc/systemd/system/cbec-backend.service > /dev/null << EOF
[Unit]
Description=CBEC Backend API
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$PROJECT_DIR/backend
Environment=PATH=$PROJECT_DIR/backend/venv/bin
ExecStart=$PROJECT_DIR/backend/venv/bin/uvicorn main:app --host 0.0.0.0 --port 5000
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=cbec-backend

[Install]
WantedBy=multi-user.target
EOF
    
    # 前端服务（可选）
    sudo tee /etc/systemd/system/cbec-frontend.service > /dev/null << EOF
[Unit]
Description=CBEC Frontend Development Server
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$PROJECT_DIR/frontend
ExecStart=/usr/bin/npm run serve
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=cbec-frontend

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd
    sudo systemctl daemon-reload
    
    log_success "系统服务创建完成"
}

# 配置Nginx
configure_nginx() {
    log_info "配置Nginx..."
    
    # 备份原配置
    sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup
    
    # 创建CBEC站点配置
    sudo tee /etc/nginx/conf.d/cbec.conf > /dev/null << 'EOF'
upstream cbec_backend {
    server 127.0.0.1:5000;
    keepalive 32;
}

server {
    listen 80 default_server;
    server_name _;
    
    client_max_body_size 50M;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 前端静态文件
    location / {
        root /opt/cbec-app/frontend/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://cbec_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS设置
        add_header Access-Control-Allow-Origin $http_origin always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
        add_header Access-Control-Allow-Credentials true always;
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # 健康检查
    location /health {
        proxy_pass http://cbec_backend;
        access_log off;
    }
    
    # 文件上传
    location /uploads/ {
        alias /var/www/uploads/;
        expires 30d;
    }
}
EOF
    
    # 测试配置
    if sudo nginx -t; then
        log_success "Nginx配置测试通过"
    else
        log_error "Nginx配置测试失败"
        exit 1
    fi
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启用并启动后端服务
    sudo systemctl enable cbec-backend
    sudo systemctl start cbec-backend
    
    # 启动Nginx
    sudo systemctl enable nginx
    sudo systemctl start nginx
    
    # 检查服务状态
    sleep 5
    
    if sudo systemctl is-active cbec-backend &> /dev/null; then
        log_success "后端服务启动成功"
    else
        log_error "后端服务启动失败"
        sudo journalctl -u cbec-backend --no-pager -l
        exit 1
    fi
    
    if sudo systemctl is-active nginx &> /dev/null; then
        log_success "Nginx服务启动成功"
    else
        log_error "Nginx服务启动失败"
        sudo journalctl -u nginx --no-pager -l
        exit 1
    fi
}

# 创建管理脚本
create_management_scripts() {
    log_info "创建管理脚本..."
    
    # 项目管理脚本
    cat > "$PROJECT_DIR/manage.sh" << 'EOF'
#!/bin/bash

case "$1" in
    start)
        sudo systemctl start cbec-backend nginx
        echo "服务已启动"
        ;;
    stop)
        sudo systemctl stop cbec-backend nginx
        echo "服务已停止"
        ;;
    restart)
        sudo systemctl restart cbec-backend nginx
        echo "服务已重启"
        ;;
    status)
        echo "=== 服务状态 ==="
        sudo systemctl status cbec-backend nginx
        ;;
    logs)
        case "$2" in
            backend)
                sudo journalctl -u cbec-backend -f
                ;;
            nginx)
                sudo tail -f /var/log/nginx/access.log /var/log/nginx/error.log
                ;;
            *)
                echo "用法: $0 logs [backend|nginx]"
                ;;
        esac
        ;;
    update)
        echo "更新项目代码..."
        cd /opt/cbec-app
        git pull
        
        echo "更新后端依赖..."
        cd backend
        source venv/bin/activate
        pip install -r requirements.txt
        python -m alembic upgrade head
        
        echo "构建前端..."
        cd ../frontend
        npm install
        npm run build
        
        echo "重启服务..."
        sudo systemctl restart cbec-backend nginx
        echo "更新完成"
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs|update}"
        exit 1
        ;;
esac
EOF
    
    chmod +x "$PROJECT_DIR/manage.sh"
    
    # 创建软链接
    sudo ln -sf "$PROJECT_DIR/manage.sh" /usr/local/bin/cbec
    
    log_success "管理脚本创建完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查后端API
    for i in {1..30}; do
        if curl -f http://localhost:5000/health &> /dev/null; then
            log_success "后端API健康检查通过"
            break
        fi
        
        if [[ $i -eq 30 ]]; then
            log_error "后端API健康检查失败"
            return 1
        fi
        
        sleep 2
    done
    
    # 检查前端
    if curl -f http://localhost/ &> /dev/null; then
        log_success "前端服务健康检查通过"
    else
        log_warning "前端服务健康检查失败"
    fi
    
    return 0
}

# 生成部署报告
generate_deploy_report() {
    log_info "生成部署报告..."
    
    REPORT_FILE="cbec-deploy-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$REPORT_FILE" << EOF
CBEC 项目部署报告
部署时间: $(date)
部署用户: $USER
项目目录: $PROJECT_DIR

=== 配置信息 ===
Git仓库: $REPO_URL
数据库: $DB_USER@$DB_HOST/$DB_NAME

=== 服务状态 ===
$(sudo systemctl is-active cbec-backend nginx redis)

=== 端口监听 ===
$(ss -tuln | grep -E ':(80|443|5000|8000)')

=== 访问地址 ===
前端地址: http://$(curl -s ipinfo.io/ip 2>/dev/null || hostname)
API文档: http://$(curl -s ipinfo.io/ip 2>/dev/null || hostname)/api/docs

=== 管理命令 ===
cbec start    - 启动服务
cbec stop     - 停止服务
cbec restart  - 重启服务
cbec status   - 查看状态
cbec logs     - 查看日志
cbec update   - 更新项目

=== 重要文件位置 ===
项目代码: $PROJECT_DIR
配置文件: $PROJECT_DIR/.env
日志文件: /var/log/cbec/
Nginx配置: /etc/nginx/conf.d/cbec.conf
EOF
    
    log_success "部署报告已生成: $REPORT_FILE"
}

# 主函数
main() {
    echo
    log_info "开始 CBEC 项目部署..."
    echo
    
    # 预检查
    check_environment
    get_user_input
    
    echo
    log_info "开始部署流程..."
    echo
    
    # 项目部署
    clone_project
    configure_environment
    
    # 环境配置
    setup_backend
    setup_frontend
    
    # 服务配置
    create_services
    configure_nginx
    
    # 启动服务
    start_services
    
    # 后续配置
    create_management_scripts
    
    # 验证部署
    if health_check; then
        generate_deploy_report
        
        echo
        log_success "=== 部署完成 ==="
        log_info "访问地址: http://$(curl -s ipinfo.io/ip 2>/dev/null || hostname)"
        log_info "API文档: http://$(curl -s ipinfo.io/ip 2>/dev/null || hostname)/api/docs"
        echo
        log_info "管理命令："
        echo "cbec start|stop|restart|status|logs|update"
        echo
    else
        log_error "部署验证失败，请检查日志"
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 