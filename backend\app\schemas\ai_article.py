from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime


class AIArticleBase(BaseModel):
    """AI文章基础模式"""
    keywords: str = Field(..., description="关键词")
    wordpress_url: str = Field(..., description="WordPress站点URL")
    type: int = Field(..., description="博客分类ID")
    tags: Optional[str] = Field(None, description="博客标签ID，多个用逗号分隔")
    model: Optional[str] = Field(None, description="AI模型，WanX或Jimeng")
    image_url: Optional[str] = Field(None, description="上传的图片URL")


class AIArticleCreate(AIArticleBase):
    """AI文章创建模式"""
    ai_config_id: Optional[int] = Field(None, description="AI配置ID")


class AIArticleUpdate(BaseModel):
    """AI文章更新模式"""
    status: Optional[str] = Field(None, description="文章状态")
    title: Optional[str] = Field(None, description="生成的文章标题")
    article_url: Optional[str] = Field(None, description="发布后的文章链接")
    article_id: Optional[str] = Field(None, description="WordPress文章ID")
    featured_image_id: Optional[str] = Field(None, description="特色图片ID")
    error_message: Optional[str] = Field(None, description="错误信息")
    result_text: Optional[str] = Field(None, description="完整返回结果")
    workflow_run_id: Optional[str] = Field(None, description="DIFY工作流运行ID")
    task_id: Optional[str] = Field(None, description="DIFY任务ID")


class AIArticleResponse(AIArticleBase):
    """AI文章响应模式"""
    id: int
    title: Optional[str] = Field(None, description="生成的文章标题")
    article_url: Optional[str] = Field(None, description="发布后的文章链接")
    article_id: Optional[str] = Field(None, description="WordPress文章ID")
    featured_image_id: Optional[str] = Field(None, description="特色图片ID")
    status: str = Field(default="pending", description="文章状态")
    error_message: Optional[str] = Field(None, description="错误信息")
    result_text: Optional[str] = Field(None, description="完整返回结果")
    workflow_run_id: Optional[str] = Field(None, description="DIFY工作流运行ID")
    task_id: Optional[str] = Field(None, description="DIFY任务ID")
    ai_config_id: Optional[int] = Field(None, description="AI配置ID")
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AIArticleList(BaseModel):
    """AI文章列表模式"""
    items: list[AIArticleResponse]
    total: int
    page: int
    size: int


class DIFYWorkflowRequest(BaseModel):
    """DIFY工作流请求模式"""
    keywords: str = Field(..., description="关键词")
    wordpress_url: str = Field(..., description="WordPress站点URL")
    type: int = Field(..., description="博客分类ID")
    tags: Optional[str] = Field(None, description="博客标签ID，多个用逗号分隔")
    model: Optional[str] = Field(None, description="AI模型，WanX或Jimeng")
    image_url: Optional[str] = Field(None, description="图片URL")
    wp_user: Optional[str] = Field(None, description="WordPress用户名")
    wp_pwd: Optional[str] = Field(None, description="WordPress应用程序密码") 