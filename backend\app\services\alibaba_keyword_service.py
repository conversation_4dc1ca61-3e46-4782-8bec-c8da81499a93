from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, func
from datetime import date, datetime, timedelta
from decimal import Decimal
import logging
import random

from app.models.alibaba_keyword import AlibabaKeywordPerformance, AlibabaKeywordSummary, AlibabaKeywordDatePeriod
from app.services.alibaba_service import AlibabaAPIService
from app.utils.datetime_utils import utc_now, to_iso_string

logger = logging.getLogger(__name__)

class AlibabaKeywordService:
    """阿里巴巴关键词表现服务"""
    
    def __init__(self):
        self.alibaba_api = AlibabaAPIService()

    def get_keyword_performance(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str,
        start_date: date,
        end_date: date,
        keyword: Optional[str] = None
    ) -> List[AlibabaKeywordPerformance]:
        """获取关键词表现数据"""
        try:
            query = db.query(AlibabaKeywordPerformance).filter(
                and_(
                    AlibabaKeywordPerformance.user_id == user_id,
                    AlibabaKeywordPerformance.tenant_id == tenant_id,
                    AlibabaKeywordPerformance.statistics_type == statistics_type,
                    AlibabaKeywordPerformance.start_date >= start_date,
                    AlibabaKeywordPerformance.end_date <= end_date
                )
            )
            
            if keyword:
                query = query.filter(AlibabaKeywordPerformance.keyword.like(f"%{keyword}%"))
                
            return query.order_by(desc(AlibabaKeywordPerformance.sum_show_cnt)).all()
        except Exception as e:
            logger.error(f"获取关键词表现数据失败: {e}")
            raise

    def get_date_periods(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str
    ) -> Dict[str, Any]:
        """获取可用的时间周期"""
        try:
            # 从关键词日期周期表中查询可用周期
            periods = db.query(AlibabaKeywordDatePeriod).filter(
                and_(
                    AlibabaKeywordDatePeriod.user_id == user_id,
                    AlibabaKeywordDatePeriod.tenant_id == tenant_id,
                    AlibabaKeywordDatePeriod.period_type == statistics_type,
                    AlibabaKeywordDatePeriod.is_available == True
                )
            ).order_by(
                AlibabaKeywordDatePeriod.start_date.desc()
            ).all()
            
            if periods:
                # 返回可用的数据周期列表
                period_list = [
                    {
                        "start_date": period.start_date.isoformat(),
                        "end_date": period.end_date.isoformat(),
                        "period_type": period.period_type,
                        "is_current": period.is_current,
                        "description": f"{period.start_date.isoformat()} 至 {period.end_date.isoformat()}"
                    }
                    for period in periods
                ]
                
                return {
                    "resultList": period_list,
                    "total_count": len(period_list)
                }
            else:
                # 如果没有数据，返回空列表
                return {
                    "resultList": [],
                    "total_count": 0
                }
        except Exception as e:
            logger.error(f"获取关键词时间周期失败: {e}")
            raise

    def get_weekly_data(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        start_date: date,
        end_date: date,
        keyword: Optional[str] = None
    ) -> List[AlibabaKeywordPerformance]:
        """获取关键词周统计数据"""
        return self.get_keyword_performance(
            db, user_id, tenant_id, "week", start_date, end_date, keyword
        )

    def get_monthly_data(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        start_date: date,
        end_date: date,
        keyword: Optional[str] = None
    ) -> List[AlibabaKeywordPerformance]:
        """获取关键词月统计数据"""
        return self.get_keyword_performance(
            db, user_id, tenant_id, "month", start_date, end_date, keyword
        )

    def save_keyword_performance(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        performance_data: Dict[str, Any],
        keyword: str,
        statistics_type: str,
        start_date: date,
        end_date: date
    ) -> AlibabaKeywordPerformance:
        """保存关键词表现数据"""
        # 检查是否已存在
        existing = db.query(AlibabaKeywordPerformance).filter(
            and_(
                AlibabaKeywordPerformance.user_id == user_id,
                AlibabaKeywordPerformance.tenant_id == tenant_id,
                AlibabaKeywordPerformance.keyword == keyword,
                AlibabaKeywordPerformance.statistics_type == statistics_type,
                AlibabaKeywordPerformance.start_date == start_date,
                AlibabaKeywordPerformance.end_date == end_date
            )
        ).first()
        
        if existing:
            # 更新现有记录
            existing.sum_show_cnt = performance_data.get("sum_show_cnt", 0)
            existing.sum_click_cnt = performance_data.get("sum_click_cnt", 0)
            existing.ctr = Decimal(str(performance_data.get("ctr", 0)))
            existing.search_pv_index = performance_data.get("search_pv_index", 0)
            existing.gs_tp_member_set_cnt = performance_data.get("gs_tp_member_set_cnt", 0)
            existing.avg_sum_show_cnt = performance_data.get("avg_sum_show_cnt", 0)
            existing.avg_sum_click_cnt = performance_data.get("avg_sum_click_cnt", 0)
            existing.sum_p4p_show_cnt = performance_data.get("sum_p4p_show_cnt", 0)
            existing.sum_p4p_click_cnt = performance_data.get("sum_p4p_click_cnt", 0)
            existing.is_p4p_kw = performance_data.get("is_p4p_kw", False)
            existing.keywords_in_use = performance_data.get("keywords_in_use", False)
            existing.keywords_viewed = performance_data.get("keywords_viewed", False)
            existing.sync_time = utc_now()
            db_performance = existing
        else:
            # 创建新记录
            db_performance = AlibabaKeywordPerformance(
                user_id=user_id,
                tenant_id=tenant_id,
                keyword=keyword,
                statistics_type=statistics_type,
                start_date=start_date,
                end_date=end_date,
                sum_show_cnt=performance_data.get("sum_show_cnt", 0),
                sum_click_cnt=performance_data.get("sum_click_cnt", 0),
                ctr=Decimal(str(performance_data.get("ctr", 0))),
                search_pv_index=performance_data.get("search_pv_index", 0),
                gs_tp_member_set_cnt=performance_data.get("gs_tp_member_set_cnt", 0),
                avg_sum_show_cnt=performance_data.get("avg_sum_show_cnt", 0),
                avg_sum_click_cnt=performance_data.get("avg_sum_click_cnt", 0),
                sum_p4p_show_cnt=performance_data.get("sum_p4p_show_cnt", 0),
                sum_p4p_click_cnt=performance_data.get("sum_p4p_click_cnt", 0),
                is_p4p_kw=performance_data.get("is_p4p_kw", False),
                keywords_in_use=performance_data.get("keywords_in_use", False),
                keywords_viewed=performance_data.get("keywords_viewed", False),
                sync_time=utc_now(),
                data_source="mock_api"
            )
            db.add(db_performance)
        
        db.commit()
        db.refresh(db_performance)
        return db_performance

    async def sync_data_from_alibaba(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str,
        start_date: date,
        end_date: date,
        access_token: str = None
    ) -> Dict[str, int]:
        """从阿里国际站同步关键词表现数据"""
        try:
            # 模拟关键词数据
            mock_keywords = [
                "phone case", "bluetooth headphones", "power bank", "phone stand",
                "usb cable", "wireless charger", "mobile phone", "smartphone accessories",
                "charging cable", "phone holder", "earphones", "portable charger",
                "phone cover", "cell phone case", "mobile accessories", "phone mount",
                "wireless headset", "usb charger", "phone grip", "mobile stand",
                "bluetooth speaker", "phone ring", "mobile charger", "phone cable",
                "wireless earbuds", "phone wallet", "mobile holder", "phone strap",
                "charging station", "phone screen protector"
            ]
            
            saved_performance = []
            current_date = start_date
            
            while current_date <= end_date:
                # 计算该周期的结束日期
                if statistics_type == "week":
                    period_end = current_date + timedelta(days=6)
                elif statistics_type == "month":
                    period_end = current_date + timedelta(days=29)  # 简化处理
                else:
                    period_end = current_date
                
                # 确保不超过指定的结束日期
                if period_end > end_date:
                    period_end = end_date
                
                for keyword in mock_keywords:
                    # 生成模拟关键词表现数据
                    base_show = random.randint(10000, 100000)
                    click = random.randint(100, base_show // 50)
                    p4p_show = random.randint(0, base_show // 10)
                    p4p_click = random.randint(0, p4p_show // 20) if p4p_show > 0 else 0
                    
                    mock_performance = {
                        "sum_show_cnt": base_show,
                        "sum_click_cnt": click,
                        "ctr": round((click / base_show) * 100, 4) if base_show > 0 else 0,
                        "search_pv_index": random.randint(1000, 50000),
                        "gs_tp_member_set_cnt": random.randint(50, 500),
                        "avg_sum_show_cnt": random.randint(5000, 20000),
                        "avg_sum_click_cnt": random.randint(50, 500),
                        "sum_p4p_show_cnt": p4p_show,
                        "sum_p4p_click_cnt": p4p_click,
                        "is_p4p_kw": random.choice([True, False]),
                        "keywords_in_use": random.choice([True, False]),
                        "keywords_viewed": True
                    }
                    
                    performance = self.save_keyword_performance(
                        db, user_id, tenant_id, mock_performance,
                        keyword, statistics_type, current_date, period_end
                    )
                    saved_performance.append(performance)
                
                # 根据统计类型递增日期
                if statistics_type == "week":
                    current_date += timedelta(days=7)
                elif statistics_type == "month":
                    current_date += timedelta(days=30)  # 简化处理
                else:
                    break  # 如果不是周或月，只执行一次
            
            return {
                "keywords": len(mock_keywords),
                "performance_records": len(saved_performance)
            }
            
        except Exception as e:
            logger.error(f"同步关键词表现数据失败: {e}")
            raise

    def calculate_summary(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str,
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """计算关键词表现汇总数据"""
        performance_data = self.get_keyword_performance(
            db, user_id, tenant_id, statistics_type, start_date, end_date
        )
        
        if not performance_data:
            return {
                "total_keywords": 0,
                "active_keywords": 0,
                "p4p_keywords": 0,
                "total_show": 0,
                "total_click": 0,
                "avg_ctr": 0.0,
                "total_search_pv_index": 0,
                "total_p4p_show": 0,
                "total_p4p_click": 0,
                "p4p_ctr": 0.0
            }
        
        total_keywords = len(performance_data)
        active_keywords = len([p for p in performance_data if p.keywords_viewed])
        p4p_keywords = len([p for p in performance_data if p.is_p4p_kw])
        total_show = sum(p.sum_show_cnt for p in performance_data)
        total_click = sum(p.sum_click_cnt for p in performance_data)
        total_search_pv_index = sum(p.search_pv_index for p in performance_data)
        total_p4p_show = sum(p.sum_p4p_show_cnt for p in performance_data)
        total_p4p_click = sum(p.sum_p4p_click_cnt for p in performance_data)
        
        avg_ctr = (total_click / total_show * 100) if total_show > 0 else 0.0
        p4p_ctr = (total_p4p_click / total_p4p_show * 100) if total_p4p_show > 0 else 0.0
        
        return {
            "total_keywords": total_keywords,
            "active_keywords": active_keywords,
            "p4p_keywords": p4p_keywords,
            "total_show": total_show,
            "total_click": total_click,
            "avg_ctr": round(avg_ctr, 4),
            "total_search_pv_index": total_search_pv_index,
            "total_p4p_show": total_p4p_show,
            "total_p4p_click": total_p4p_click,
            "p4p_ctr": round(p4p_ctr, 4)
        } 