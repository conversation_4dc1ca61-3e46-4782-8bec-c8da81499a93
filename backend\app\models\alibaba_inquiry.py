from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, Date, BigInteger, DECIMAL, Enum

from app.models.base import BaseModel
from app.utils.datetime_utils import utc_now

class AlibabaTopIndustry(BaseModel):
    """阿里TOP行业列表模型"""
    __tablename__ = "alibaba_top_industries"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(50), index=True, comment="租户ID")
    user_id = Column(Integer, index=True, comment="关联的用户ID")
    industry_id = Column(String(50), index=True, comment="行业ID")
    industry_desc = Column(String(255), comment="行业描述")
    main_category = Column(Boolean, default=False, comment="是否主营行业")
    data_start_date = Column(Date, comment="数据开始日期")
    data_end_date = Column(Date, comment="数据结束日期")
    
    class Config:
        table = True

class AlibabaInquiryPerformance(BaseModel):
    """阿里询盘流量行业表现模型"""
    __tablename__ = "alibaba_inquiry_performance"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(50), index=True, comment="租户ID")
    user_id = Column(Integer, index=True, comment="关联的用户ID")
    industry_id = Column(String(50), index=True, comment="行业ID")
    industry_desc = Column(String(255), comment="行业描述")
    main_category = Column(Boolean, default=False, comment="是否主营行业")
    data_start_date = Column(Date, comment="数据开始日期")
    data_end_date = Column(Date, comment="数据结束日期")
    
    # 询盘流量指标
    clk = Column(BigInteger, default=0, comment="点击数")
    clk_rate = Column(String(20), comment="点击率")
    fb = Column(BigInteger, default=0, comment="反馈（询盘）")
    imps = Column(BigInteger, default=0, comment="曝光数")
    reply = Column(String(20), comment="最近30天询盘一次回复率")
    visitor = Column(BigInteger, default=0, comment="访客数")
    
    class Config:
        table = True

class AlibabaDataPeriod(BaseModel):
    """阿里数据周期模型"""
    __tablename__ = "alibaba_data_periods"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(50), index=True, comment="租户ID")
    user_id = Column(Integer, index=True, comment="关联的用户ID")
    start_date = Column(Date, comment="开始日期")
    end_date = Column(Date, comment="结束日期")
    is_available = Column(Boolean, default=True, comment="是否可用")
    
    class Config:
        table = True

class AlibabaInquirySummary(BaseModel):
    """阿里询盘统计汇总模型"""
    __tablename__ = "alibaba_inquiry_summary"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(50), index=True, comment="租户ID")
    user_id = Column(Integer, index=True, comment="关联的用户ID")
    summary_date = Column(Date, comment="汇总日期")
    summary_type = Column(Enum("daily", "weekly", "monthly", "custom", "dashboard", "quarterly", "yearly", name="summary_type_enum"), comment="汇总类型")
    
    # 汇总数据
    total_industries = Column(Integer, default=0, comment="总行业数")
    main_industries = Column(Integer, default=0, comment="主营行业数")
    total_clk = Column(BigInteger, default=0, comment="总点击数")
    total_fb = Column(BigInteger, default=0, comment="总询盘数")
    total_imps = Column(BigInteger, default=0, comment="总曝光数")
    total_visitor = Column(BigInteger, default=0, comment="总访客数")
    avg_clk_rate = Column(DECIMAL(5,2), default=0.00, comment="平均点击率")
    avg_reply_rate = Column(DECIMAL(5,2), default=0.00, comment="平均回复率")
    
    class Config:
        table = True 