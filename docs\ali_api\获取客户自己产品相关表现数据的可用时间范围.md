# 获取客户自己产品相关表现数据的可用时间范围

获取客户自己产品相关表现数据的可用时间范围

GET/POST

alibaba.mydata.self.product.date.get

描述：获取客户产品相关表现数据的可用时间范围

## 参数

|  名称  |  类型  |  是否必须  |  描述  |
| --- | --- | --- | --- |
|  statistics\_type  |  String  |  否  |  统计周期类型，可以为"day"，"week"，"month"  |

## 响应参数

|  名称  |  类型  |  描述  |
| --- | --- | --- |
|  end\_date  |  String  |  end\_date  |
|  start\_date  |  String  |  start\_date  |

## 错误码

|  错误码  |  错误信息  |  解决方案  |
| --- | --- | --- |
|  没有数据  |  |  |

GET/POSTalibaba.mydata.self.product.date.get

*   PYTHON
    

```PYTHON
client = iop.IopClient(url, appkey ,appSecret)
request = iop.IopRequest('alibaba.mydata.self.product.date.get')
request.add_api_param('statistics_type', 'day')
response = client.execute(request, access_token)
print(response.type)
print(response.body)

```

*   非精简返回
    

---
---
```json
{
  "code": "0",
  "request_id": "0ba2887315178178017221014",
  "alibaba_mydata_self_product_date_get_response": {
    "end_date": "end_date",
    "start_date": "start_date"
  }
}
```