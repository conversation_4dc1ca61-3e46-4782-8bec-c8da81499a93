#!/bin/bash

# AWS Linux 2023 环境检测和依赖安装脚本
# 适用于 CBEC 跨境电商管理系统部署
# 作者: AI Assistant
# 版本: 1.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本！"
        log_info "建议使用普通用户（如ec2-user）运行，脚本会在需要时使用sudo"
        exit 1
    fi
}

# 检查操作系统
check_os() {
    log_info "检查操作系统..."
    
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法检测操作系统版本"
        exit 1
    fi
    
    source /etc/os-release
    
    if [[ "$ID" != "amzn" ]]; then
        log_error "此脚本仅适用于 Amazon Linux，当前系统: $PRETTY_NAME"
        exit 1
    fi
    
    if [[ "$VERSION_ID" != "2023" ]]; then
        log_warning "此脚本专为 Amazon Linux 2023 设计，当前版本: $PRETTY_NAME"
        read -p "是否继续？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 0
        fi
    fi
    
    log_success "操作系统检查通过: $PRETTY_NAME"
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    
    if ping -c 3 8.8.8.8 &> /dev/null; then
        log_success "网络连接正常"
    else
        log_error "网络连接失败，请检查网络设置"
        exit 1
    fi
}

# 更新系统
update_system() {
    log_info "更新系统包..."
    
    sudo dnf update -y
    sudo dnf install -y epel-release
    
    log_success "系统更新完成"
}

# 安装基础工具
install_basic_tools() {
    log_info "安装基础开发工具..."
    
    sudo dnf groupinstall -y "Development Tools"
    sudo dnf install -y \
        git \
        curl \
        wget \
        unzip \
        zip \
        vim \
        nano \
        htop \
        tree \
        lsof \
        net-tools \
        telnet \
        nc \
        rsync \
        screen \
        tmux \
        openssl \
        openssl-devel \
        ca-certificates
        
    log_success "基础工具安装完成"
}

# 检查并安装 Node.js
install_nodejs() {
    log_info "检查 Node.js..."
    
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log_info "已安装 Node.js: $NODE_VERSION"
        
        # 检查版本是否满足要求 (>= 16)
        NODE_MAJOR=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [[ $NODE_MAJOR -lt 16 ]]; then
            log_warning "Node.js 版本过低，建议升级到 18+"
        else
            log_success "Node.js 版本满足要求"
            return 0
        fi
    fi
    
    log_info "安装 Node.js 18..."
    
    # 安装 Node.js 18
    curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
    sudo dnf install -y nodejs
    
    # 验证安装
    if command -v node &> /dev/null && command -v npm &> /dev/null; then
        log_success "Node.js 安装成功: $(node --version), npm: $(npm --version)"
        
        # 配置npm国内镜像
        npm config set registry https://registry.npmmirror.com
        log_info "已配置npm国内镜像源"
    else
        log_error "Node.js 安装失败"
        exit 1
    fi
}

# 检查并配置 Python
setup_python() {
    log_info "配置 Python 环境..."
    
    # 检查 Python 版本
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        log_info "已安装 Python: $PYTHON_VERSION"
    else
        log_error "Python3 未安装"
        exit 1
    fi
    
    # 安装 Python 开发包和工具
    sudo dnf install -y \
        python3-pip \
        python3-venv \
        python3-devel \
        python3-setuptools \
        python3-wheel
    
    # 升级 pip
    python3 -m pip install --upgrade pip
    
    # 配置pip国内镜像
    mkdir -p ~/.pip
    cat > ~/.pip/pip.conf << EOF
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
EOF
    
    log_success "Python 环境配置完成"
}

# 安装数据库客户端
install_database_clients() {
    log_info "安装数据库客户端..."
    
    # MySQL 客户端和开发包
    sudo dnf install -y \
        mysql \
        mysql-devel \
        mysql-common
    
    # PostgreSQL 客户端和开发包
    sudo dnf install -y \
        postgresql \
        postgresql-devel \
        postgresql-contrib
    
    log_success "数据库客户端安装完成"
}

# 安装和配置 Redis
install_redis() {
    log_info "安装 Redis..."
    
    sudo dnf install -y redis
    
    # 配置 Redis
    sudo systemctl enable redis
    sudo systemctl start redis
    
    # 验证 Redis 安装
    if sudo systemctl is-active redis &> /dev/null; then
        log_success "Redis 安装并启动成功"
    else
        log_warning "Redis 安装完成但启动失败"
    fi
}

# 安装和配置 Nginx
install_nginx() {
    log_info "安装 Nginx..."
    
    sudo dnf install -y nginx
    
    # 启用但不立即启动 Nginx（等待配置）
    sudo systemctl enable nginx
    
    # 创建日志目录
    sudo mkdir -p /var/log/nginx
    sudo chown nginx:nginx /var/log/nginx
    
    log_success "Nginx 安装完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    # 检查 firewalld 是否运行
    if sudo systemctl is-active firewalld &> /dev/null; then
        sudo firewall-cmd --permanent --add-service=http
        sudo firewall-cmd --permanent --add-service=https
        sudo firewall-cmd --permanent --add-port=8000/tcp
        sudo firewall-cmd --permanent --add-port=5000/tcp
        sudo firewall-cmd --reload
        log_success "防火墙规则配置完成"
    else
        log_info "firewalld 未运行，跳过防火墙配置"
    fi
}

# 优化系统参数
optimize_system() {
    log_info "优化系统参数..."
    
    # 网络和性能优化
    sudo tee -a /etc/sysctl.conf > /dev/null << EOF

# CBEC系统优化参数
# 网络优化
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 87380 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_congestion_control = bbr

# 文件描述符限制
fs.file-max = 65536
fs.nr_open = 1048576

# 进程和内存优化
kernel.pid_max = 4194304
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
EOF
    
    # 应用系统参数
    sudo sysctl -p
    
    # 配置用户限制
    sudo tee -a /etc/security/limits.conf > /dev/null << EOF

# CBEC系统用户限制
* soft nofile 65536
* hard nofile 65536
* soft nproc 65536
* hard nproc 65536
EOF
    
    log_success "系统参数优化完成"
}

# 创建项目目录结构
create_project_structure() {
    log_info "创建项目目录结构..."
    
    # 创建应用目录
    sudo mkdir -p /opt/cbec-app
    sudo chown $USER:$USER /opt/cbec-app
    
    # 创建日志目录
    sudo mkdir -p /var/log/cbec
    sudo chown $USER:$USER /var/log/cbec
    
    # 创建上传目录
    sudo mkdir -p /var/www/uploads
    sudo chown $USER:$USER /var/www/uploads
    
    # 创建备份目录
    mkdir -p ~/cbec-backups
    
    log_success "项目目录结构创建完成"
}

# 安装SSL工具
install_ssl_tools() {
    log_info "安装SSL证书工具..."
    
    # 安装 certbot
    sudo dnf install -y certbot python3-certbot-nginx
    
    log_success "SSL工具安装完成"
}

# 创建有用的脚本和别名
create_helper_scripts() {
    log_info "创建辅助脚本和别名..."
    
    # 创建系统监控脚本
    cat > ~/cbec-status.sh << 'EOF'
#!/bin/bash
echo "=== CBEC 系统状态 ==="
echo
echo "系统信息:"
uptime
echo
echo "内存使用:"
free -h
echo
echo "磁盘使用:"
df -h
echo
echo "网络连接:"
ss -tuln | grep -E ':(80|443|5000|8000|3306|5432|6379)'
echo
echo "服务状态:"
for service in nginx redis mysql postgresql; do
    if systemctl list-unit-files | grep -q "^$service.service"; then
        status=$(systemctl is-active $service 2>/dev/null || echo "not-installed")
        echo "$service: $status"
    fi
done
EOF
    chmod +x ~/cbec-status.sh
    
    # 添加有用的别名
    cat >> ~/.bashrc << 'EOF'

# CBEC 项目别名
alias cbec-status='~/cbec-status.sh'
alias cbec-logs='tail -f /var/log/cbec/*.log'
alias cbec-nginx='sudo tail -f /var/log/nginx/access.log'
alias cbec-app='cd /opt/cbec-app'
alias ll='ls -alF'
alias la='ls -A'
alias l='ls -CF'
EOF
    
    log_success "辅助脚本创建完成"
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用情况..."
    
    ports=(80 443 5000 8000 3306 5432 6379)
    
    for port in "${ports[@]}"; do
        if ss -tuln | grep -q ":$port "; then
            log_warning "端口 $port 已被占用"
            ss -tuln | grep ":$port "
        else
            log_info "端口 $port 可用"
        fi
    done
}

# 生成环境报告
generate_report() {
    log_info "生成环境检测报告..."
    
    REPORT_FILE="cbec-environment-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$REPORT_FILE" << EOF
CBEC 环境检测报告
生成时间: $(date)
主机名: $(hostname)
IP地址: $(curl -s ipinfo.io/ip 2>/dev/null || echo "无法获取")

=== 系统信息 ===
$(cat /etc/os-release)

=== 硬件信息 ===
CPU: $(lscpu | grep "Model name" | cut -d: -f2 | xargs)
内存: $(free -h | grep "Mem:" | awk '{print $2}')
磁盘: $(df -h / | tail -1 | awk '{print $2}')

=== 软件版本 ===
Node.js: $(node --version 2>/dev/null || echo "未安装")
npm: $(npm --version 2>/dev/null || echo "未安装")
Python: $(python3 --version 2>/dev/null || echo "未安装")
pip: $(python3 -m pip --version 2>/dev/null || echo "未安装")
Git: $(git --version 2>/dev/null || echo "未安装")
Nginx: $(nginx -v 2>&1 | cut -d: -f2 | xargs || echo "未安装")

=== 服务状态 ===
$(systemctl is-active nginx redis mysql postgresql 2>/dev/null || echo "服务检查失败")

=== 端口状态 ===
$(ss -tuln | grep -E ':(80|443|5000|8000|3306|5432|6379)')

=== 目录结构 ===
/opt/cbec-app: $(ls -la /opt/cbec-app 2>/dev/null || echo "不存在")
/var/log/cbec: $(ls -la /var/log/cbec 2>/dev/null || echo "不存在")
/var/www/uploads: $(ls -la /var/www/uploads 2>/dev/null || echo "不存在")
EOF
    
    log_success "环境报告已生成: $REPORT_FILE"
}

# 主函数
main() {
    echo
    log_info "开始 AWS Linux 2023 CBEC 环境配置..."
    echo
    
    # 环境检查
    check_root
    check_os
    check_network
    
    echo
    log_info "开始安装依赖..."
    echo
    
    # 系统更新和基础安装
    update_system
    install_basic_tools
    
    # 运行时环境
    install_nodejs
    setup_python
    
    # 数据库和缓存
    install_database_clients
    install_redis
    
    # Web服务器
    install_nginx
    
    # 系统配置
    configure_firewall
    optimize_system
    
    # 项目准备
    create_project_structure
    install_ssl_tools
    create_helper_scripts
    
    # 最终检查
    check_ports
    generate_report
    
    echo
    log_success "=== 环境配置完成 ==="
    log_info "请执行以下命令重新加载bash配置:"
    echo "source ~/.bashrc"
    echo
    log_info "有用的命令:"
    echo "cbec-status  - 查看系统状态"
    echo "cbec-app     - 进入应用目录"
    echo "cbec-logs    - 查看应用日志"
    echo
    log_info "下一步："
    echo "1. 重启系统以应用所有配置: sudo reboot"
    echo "2. 克隆CBEC项目到 /opt/cbec-app"
    echo "3. 配置数据库连接"
    echo "4. 运行项目部署脚本"
    echo
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 