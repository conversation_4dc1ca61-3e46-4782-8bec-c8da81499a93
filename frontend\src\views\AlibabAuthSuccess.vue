<template>
  <div class="auth-success-container">
    <div class="success-card">
      <div class="success-icon">
        <i class="el-icon-success" style="font-size: 64px; color: #67C23A;"></i>
      </div>
      
      <h2>阿里国际站授权成功！</h2>
      
      <div class="success-message" v-if="message">
        <p>{{ message }}</p>
      </div>
      
      <div class="account-info" v-if="account">
        <p><strong>关联账号:</strong> {{ account }}</p>
      </div>
      
      <div class="action-buttons">
        <button class="btn btn-primary" @click="goToAuthPage">查看授权状态</button>
        <button class="btn btn-secondary" @click="goToDashboard">返回首页</button>
      </div>
      
      <div class="auto-redirect">
        <p>{{ countdown }} 秒后自动跳转到授权页面...</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AlibabaAuthSuccess',
  data() {
    return {
      message: '',
      account: '',
      countdown: 5,
      timer: null
    }
  },
  
  mounted() {
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search)
    this.message = urlParams.get('message') || '授权成功'
    this.account = urlParams.get('account') || ''
    
    // 开始倒计时
    this.startCountdown()
  },
  
  beforeUnmount() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  
  methods: {
    startCountdown() {
      this.timer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          this.goToAuthPage()
        }
      }, 1000)
    },
    
    goToAuthPage() {
      // 使用完整的路径确保在外网环境下正确跳转
      window.location.href = '/ai-operation/alibaba-auth'
    },
    
    goToDashboard() {
      // 使用完整的路径确保在外网环境下正确跳转
      window.location.href = '/'
    }
  }
}
</script>

<style scoped>
.auth-success-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
}

.success-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.success-icon {
  margin-bottom: 24px;
}

h2 {
  color: #333;
  margin-bottom: 24px;
  font-size: 24px;
}

.success-message {
  margin-bottom: 20px;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #67C23A;
}

.success-message p {
  margin: 0;
  color: #606266;
}

.account-info {
  margin-bottom: 32px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.account-info p {
  margin: 0;
  color: #495057;
  font-size: 16px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s;
  min-width: 120px;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.auto-redirect {
  padding-top: 16px;
  border-top: 1px solid #eee;
  color: #666;
  font-size: 14px;
}
</style> 