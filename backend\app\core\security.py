from datetime import timedelta
from typing import Any, Union, Optional
from jose import jwt, JW<PERSON>rror
from passlib.context import Crypt<PERSON>ontext
from app.core.config import settings
from app.utils.datetime_utils import utc_now

# 密码上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 验证密码
def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

# 获取密码哈希
def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

# 创建访问令牌
def create_access_token(
    subject: Union[str, Any], expires_delta: Optional[timedelta] = None
) -> str:
    # 使用UTC时间避免时区问题
    now = utc_now()
    if expires_delta:
        expire = now + expires_delta
    else:
        expire = now + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

    # 创建JWT载荷
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        "iat": now,  # 添加签发时间
        "type": "access_token"  # 添加token类型
    }
    
    # 使用密钥签名JWT
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm="HS256"
    )
    
    return encoded_jwt

# 解码访问令牌
def decode_access_token(token: str) -> dict:
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
        return payload
    except JWTError as e:
        raise ValueError(f"Token验证失败: {str(e)}") 
 