from pydantic import BaseModel, Field, HttpUrl
from typing import Optional, List, Any
from datetime import datetime


class WordPressSiteBase(BaseModel):
    """WordPress站点基础模式"""
    name: str = Field(..., description="站点名称")
    url: str = Field(..., description="站点URL")
    category: str = Field(..., description="站点分类")
    wp_username: str = Field(..., description="WordPress用户名")
    wp_app_password: str = Field(..., description="WordPress应用程序密码")
    description: Optional[str] = Field(None, description="站点描述")


class WordPressSiteCreate(WordPressSiteBase):
    """WordPress站点创建模式"""
    pass


class WordPressSiteUpdate(BaseModel):
    """WordPress站点更新模式"""
    name: Optional[str] = Field(None, description="站点名称")
    category: Optional[str] = Field(None, description="站点分类")
    wp_username: Optional[str] = Field(None, description="WordPress用户名")
    wp_app_password: Optional[str] = Field(None, description="WordPress应用程序密码")
    description: Optional[str] = Field(None, description="站点描述")
    is_active: Optional[bool] = Field(None, description="是否启用")


class WordPressSiteResponse(WordPressSiteBase):
    """WordPress站点响应模式"""
    id: int
    category: Optional[str] = Field(None, description="站点分类")
    daily_uv: int = Field(default=0, description="当日UV")
    daily_pv: int = Field(default=0, description="当日PV")
    monthly_uv: int = Field(default=0, description="当月UV")
    monthly_pv: int = Field(default=0, description="当月PV")
    total_posts: int = Field(default=0, description="总文章数")
    total_pages: int = Field(default=0, description="总页面数")
    blog_categories: Optional[List[Any]] = Field(None, description="WordPress博客分类")
    blog_tags: Optional[List[Any]] = Field(None, description="WordPress博客标签")
    is_active: bool = Field(default=True, description="是否启用")
    syncing: bool = Field(default=False, description="是否正在同步")
    last_sync_at: Optional[datetime] = Field(None, description="最后同步时间")
    sync_status: str = Field(default="pending", description="同步状态")
    error_message: Optional[str] = Field(None, description="错误信息")
    wordpress_version: Optional[str] = Field(None, description="WordPress版本")
    theme_name: Optional[str] = Field(None, description="主题名称")
    language: Optional[str] = Field(None, description="站点语言")
    timezone: Optional[str] = Field(None, description="时区")
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class WordPressSiteList(BaseModel):
    """WordPress站点列表响应模式"""
    items: List[WordPressSiteResponse]
    total: int
    page: int
    size: int


class WordPressSiteSync(BaseModel):
    """WordPress站点同步结果模式"""
    id: int
    sync_status: str
    wordpress_version: Optional[str] = None
    theme_name: Optional[str] = None
    language: Optional[str] = None
    timezone: Optional[str] = None
    total_posts: int = 0
    total_pages: int = 0
    blog_categories: Optional[List[Any]] = None
    blog_tags: Optional[List[Any]] = None
    error_message: Optional[str] = None
    last_sync_at: datetime


class CategoryCreate(BaseModel):
    """分类创建模式"""
    name: str = Field(..., description="分类名称")


class CategoryUpdate(BaseModel):
    """分类更新模式"""
    oldName: str = Field(..., description="原分类名称")
    newName: str = Field(..., description="新分类名称")


class CategoryDelete(BaseModel):
    """分类删除模式"""
    name: str = Field(..., description="分类名称")


class CategoryResponse(BaseModel):
    """分类操作响应模式"""
    message: str
    success: bool = True
    updated_count: Optional[int] = None
    deleted_placeholder: Optional[int] = None 