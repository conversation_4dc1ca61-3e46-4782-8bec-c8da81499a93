from typing import List, Optional, Dict
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, func
from datetime import date, datetime, timedelta
from decimal import Decimal
import logging

from app.models.alibaba_inquiry import (
    AlibabaTopIndustry,
    AlibabaInquiryPerformance,
    AlibabaDataPeriod,
    AlibabaInquirySummary
)
from app.schemas.alibaba_inquiry import (
    AlibabaTopIndustryCreate,
    AlibabaInquiryPerformanceCreate,
    AlibabaDataPeriodCreate,
    AlibabaInquirySummaryCreate
)
from app.services.alibaba_service import AlibabaService

logger = logging.getLogger(__name__)

class AlibabaInquiryServiceReal:
    """阿里询盘统计服务 - 真实API版本"""
    
    def __init__(self):
        pass

    def get_data_periods(
        self, 
        db: Session, 
        user_id: int, 
        tenant_id: str
    ) -> List[AlibabaDataPeriod]:
        """获取可用数据周期"""
        return db.query(AlibabaDataPeriod).filter(
            and_(
                AlibabaDataPeriod.user_id == user_id,
                AlibabaDataPeriod.tenant_id == tenant_id,
                AlibabaDataPeriod.is_available == True
            )
        ).order_by(desc(AlibabaDataPeriod.end_date)).all()

    def get_top_industries(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        start_date: date,
        end_date: date
    ) -> List[AlibabaTopIndustry]:
        """获取TOP行业列表"""
        return db.query(AlibabaTopIndustry).filter(
            and_(
                AlibabaTopIndustry.user_id == user_id,
                AlibabaTopIndustry.tenant_id == tenant_id,
                AlibabaTopIndustry.data_start_date >= start_date,
                AlibabaTopIndustry.data_end_date <= end_date
            )
        ).order_by(AlibabaTopIndustry.main_category.desc()).all()

    def get_inquiry_performance(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        start_date: date,
        end_date: date,
        industry_id: Optional[str] = None
    ) -> List[AlibabaInquiryPerformance]:
        """获取询盘流量行业表现数据"""
        query = db.query(AlibabaInquiryPerformance).filter(
            and_(
                AlibabaInquiryPerformance.user_id == user_id,
                AlibabaInquiryPerformance.tenant_id == tenant_id,
                AlibabaInquiryPerformance.data_start_date >= start_date,
                AlibabaInquiryPerformance.data_end_date <= end_date
            )
        )
        
        if industry_id:
            query = query.filter(AlibabaInquiryPerformance.industry_id == industry_id)
            
        return query.order_by(desc(AlibabaInquiryPerformance.fb)).all()

    def create_data_period(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        period_data: AlibabaDataPeriodCreate
    ) -> AlibabaDataPeriod:
        """创建数据周期记录"""
        # 检查是否已存在
        existing = db.query(AlibabaDataPeriod).filter(
            and_(
                AlibabaDataPeriod.user_id == user_id,
                AlibabaDataPeriod.tenant_id == tenant_id,
                AlibabaDataPeriod.start_date == period_data.start_date,
                AlibabaDataPeriod.end_date == period_data.end_date
            )
        ).first()
        
        if existing:
            return existing
            
        db_period = AlibabaDataPeriod(
            user_id=user_id,
            tenant_id=tenant_id,
            start_date=period_data.start_date,
            end_date=period_data.end_date,
            is_available=True
        )
        db.add(db_period)
        db.commit()
        db.refresh(db_period)
        return db_period

    def save_top_industries(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        industries_data: List[Dict],
        start_date: date,
        end_date: date
    ) -> List[AlibabaTopIndustry]:
        """保存TOP行业列表"""
        saved_industries = []
        
        for industry_data in industries_data:
            # 检查是否已存在
            existing = db.query(AlibabaTopIndustry).filter(
                and_(
                    AlibabaTopIndustry.user_id == user_id,
                    AlibabaTopIndustry.tenant_id == tenant_id,
                    AlibabaTopIndustry.industry_id == industry_data["industry_id"],
                    AlibabaTopIndustry.data_start_date == start_date,
                    AlibabaTopIndustry.data_end_date == end_date
                )
            ).first()
            
            if existing:
                # 更新现有记录
                existing.industry_desc = industry_data.get("industry_desc", "")
                existing.main_category = industry_data.get("main_category", False)
                saved_industries.append(existing)
            else:
                # 创建新记录
                db_industry = AlibabaTopIndustry(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    industry_id=industry_data["industry_id"],
                    industry_desc=industry_data.get("industry_desc", ""),
                    main_category=industry_data.get("main_category", False),
                    data_start_date=start_date,
                    data_end_date=end_date
                )
                db.add(db_industry)
                saved_industries.append(db_industry)
        
        db.commit()
        return saved_industries

    def save_inquiry_performance(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        performance_data: Dict,
        industry_id: str,
        start_date: date,
        end_date: date
    ) -> AlibabaInquiryPerformance:
        """保存询盘表现数据"""
        # 检查是否已存在
        existing = db.query(AlibabaInquiryPerformance).filter(
            and_(
                AlibabaInquiryPerformance.user_id == user_id,
                AlibabaInquiryPerformance.tenant_id == tenant_id,
                AlibabaInquiryPerformance.industry_id == industry_id,
                AlibabaInquiryPerformance.data_start_date == start_date,
                AlibabaInquiryPerformance.data_end_date == end_date
            )
        ).first()
        
        if existing:
            # 更新现有记录
            existing.industry_desc = performance_data.get("industry_desc", "")
            existing.main_category = performance_data.get("main_category", False)
            existing.clk = performance_data.get("clk", 0)
            existing.clk_rate = performance_data.get("clk_rate", "0")
            existing.fb = performance_data.get("fb", 0)
            existing.imps = performance_data.get("imps", 0)
            existing.reply = performance_data.get("reply", "0")
            existing.visitor = performance_data.get("visitor", 0)
            db_performance = existing
        else:
            # 创建新记录
            db_performance = AlibabaInquiryPerformance(
                user_id=user_id,
                tenant_id=tenant_id,
                industry_id=industry_id,
                industry_desc=performance_data.get("industry_desc", ""),
                main_category=performance_data.get("main_category", False),
                data_start_date=start_date,
                data_end_date=end_date,
                clk=performance_data.get("clk", 0),
                clk_rate=performance_data.get("clk_rate", "0"),
                fb=performance_data.get("fb", 0),
                imps=performance_data.get("imps", 0),
                reply=performance_data.get("reply", "0"),
                visitor=performance_data.get("visitor", 0)
            )
            db.add(db_performance)
        
        db.commit()
        db.refresh(db_performance)
        return db_performance

    async def sync_data_from_alibaba(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        start_date: date,
        end_date: date,
        access_token: str
    ) -> Dict[str, int]:
        """从阿里国际站同步数据 - 真实API版本"""
        try:
            if not access_token:
                raise Exception("访问令牌不能为空")
            
            # 初始化阿里巴巴服务
            alibaba_service = AlibabaService(db)
            
            # 1. 同步数据周期
            period_data = AlibabaDataPeriodCreate(
                start_date=start_date,
                end_date=end_date
            )
            self.create_data_period(db, user_id, tenant_id, period_data)
            
            # 2. 获取并保存TOP行业列表
            logger.info("同步TOP行业列表...")
            try:
                # 调用阿里巴巴API获取行业列表
                industries_response = alibaba_service._call_alibaba_api(
                    access_token,
                    "alibaba.mydata.self.inquiry.industry.get",
                    {
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat()
                    }
                )
                
                if industries_response.get("error_response"):
                    error_msg = industries_response["error_response"].get("msg", "获取行业列表失败")
                    logger.error(f"获取行业列表API错误: {error_msg}")
                    raise Exception(f"获取行业列表失败: {error_msg}")
                
                # 解析行业列表响应
                industries_data = []
                if "alibaba_mydata_self_inquiry_industry_get_response" in industries_response:
                    result = industries_response["alibaba_mydata_self_inquiry_industry_get_response"].get("result", {})
                    industries_list = result.get("industries", [])
                    
                    for industry in industries_list:
                        industries_data.append({
                            "industry_id": str(industry.get("industry_id", "")),
                            "industry_desc": industry.get("industry_desc", ""),
                            "main_category": industry.get("main_category", False)
                        })
                    
                    logger.info(f"获取到 {len(industries_data)} 个行业")
                
                # 保存行业列表
                saved_industries = self.save_top_industries(
                    db, user_id, tenant_id, industries_data, start_date, end_date
                )
                
            except Exception as e:
                logger.error(f"同步行业列表失败: {e}")
                # 使用现有的行业数据
                saved_industries = self.get_top_industries(db, user_id, tenant_id, start_date, end_date)
                if not saved_industries:
                    raise Exception("无法获取行业列表，且本地无现有行业数据")
            
            # 3. 为每个行业获取并保存表现数据
            saved_performance = []
            logger.info(f"开始同步 {len(saved_industries)} 个行业的表现数据...")
            
            for industry in saved_industries:
                try:
                    # 调用阿里巴巴API获取行业表现数据
                    performance_response = alibaba_service._call_alibaba_api(
                        access_token,
                        "alibaba.mydata.self.inquiry.performance.get",
                        {
                            "start_date": start_date.isoformat(),
                            "end_date": end_date.isoformat(),
                            "industry_id": industry.industry_id
                        }
                    )
                    
                    if performance_response.get("error_response"):
                        error_msg = performance_response["error_response"].get("msg", "获取行业表现数据失败")
                        logger.error(f"获取行业 {industry.industry_id} 表现数据API错误: {error_msg}")
                        continue
                    
                    # 解析表现数据响应
                    if "alibaba_mydata_self_inquiry_performance_get_response" in performance_response:
                        result = performance_response["alibaba_mydata_self_inquiry_performance_get_response"].get("result", {})
                        
                        # 处理表现数据
                        performance_data = {
                            "clk": result.get("clk", 0),
                            "clk_rate": str(result.get("clk_rate", 0)),
                            "fb": result.get("fb", 0),
                            "imps": result.get("imps", 0),
                            "reply": str(result.get("reply", 0)),
                            "visitor": result.get("visitor", 0),
                            "industry_desc": industry.industry_desc,
                            "main_category": industry.main_category
                        }
                        
                        performance = self.save_inquiry_performance(
                            db, user_id, tenant_id, performance_data, 
                            industry.industry_id, start_date, end_date
                        )
                        saved_performance.append(performance)
                        
                        logger.info(f"同步行业 {industry.industry_id} 表现数据成功")
                    
                except Exception as e:
                    logger.error(f"同步行业 {industry.industry_id} 表现数据失败: {e}")
                    continue
            
            logger.info(f"询盘统计数据同步完成: {len(saved_industries)} 个行业，{len(saved_performance)} 条表现记录")
            
            return {
                "industries": len(saved_industries),
                "performance_records": len(saved_performance)
            }
            
        except Exception as e:
            logger.error(f"同步阿里巴巴询盘数据失败: {e}")
            raise

    def calculate_summary(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        start_date: date,
        end_date: date,
        summary_type: str = "custom"
    ) -> AlibabaInquirySummary:
        """计算汇总统计"""
        # 获取表现数据
        performance_data = self.get_inquiry_performance(
            db, user_id, tenant_id, start_date, end_date
        )
        
        # 计算汇总指标
        total_industries = len(performance_data)
        main_industries = len([p for p in performance_data if p.main_category])
        total_clk = sum(p.clk for p in performance_data)
        total_fb = sum(p.fb for p in performance_data)
        total_imps = sum(p.imps for p in performance_data)
        total_visitor = sum(p.visitor for p in performance_data)
        
        # 计算平均值
        avg_clk_rate = Decimal("0.00")
        avg_reply_rate = Decimal("0.00")
        
        if performance_data:
            clk_rates = [float(p.clk_rate) for p in performance_data if p.clk_rate and p.clk_rate != "0"]
            reply_rates = [float(p.reply) for p in performance_data if p.reply and p.reply != "0"]
            
            if clk_rates:
                avg_clk_rate = Decimal(str(sum(clk_rates) / len(clk_rates)))
            if reply_rates:
                avg_reply_rate = Decimal(str(sum(reply_rates) / len(reply_rates)))
        
        # 检查是否已存在汇总记录
        existing_summary = db.query(AlibabaInquirySummary).filter(
            and_(
                AlibabaInquirySummary.user_id == user_id,
                AlibabaInquirySummary.tenant_id == tenant_id,
                AlibabaInquirySummary.summary_date == end_date,
                AlibabaInquirySummary.summary_type == summary_type
            )
        ).first()
        
        if existing_summary:
            # 更新现有记录
            existing_summary.total_industries = total_industries
            existing_summary.main_industries = main_industries
            existing_summary.total_clk = total_clk
            existing_summary.total_fb = total_fb
            existing_summary.total_imps = total_imps
            existing_summary.total_visitor = total_visitor
            existing_summary.avg_clk_rate = avg_clk_rate
            existing_summary.avg_reply_rate = avg_reply_rate
            db_summary = existing_summary
        else:
            # 创建新记录
            db_summary = AlibabaInquirySummary(
                user_id=user_id,
                tenant_id=tenant_id,
                summary_date=end_date,
                summary_type=summary_type,
                total_industries=total_industries,
                main_industries=main_industries,
                total_clk=total_clk,
                total_fb=total_fb,
                total_imps=total_imps,
                total_visitor=total_visitor,
                avg_clk_rate=avg_clk_rate,
                avg_reply_rate=avg_reply_rate
            )
            db.add(db_summary)
        
        db.commit()
        db.refresh(db_summary)
        return db_summary 