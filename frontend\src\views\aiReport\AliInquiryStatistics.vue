<template>
  <div class="ali-inquiry-statistics">
    <div class="page-header">
      <h2>阿里询盘统计</h2>
      <p>查看阿里国际站询盘流量和行业表现数据分析</p>
    </div>

    <!-- 操作工具栏 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="24">
        <el-card>
          <div class="toolbar">
            <div class="date-controls">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY年MM月DD日"
                value-format="YYYY-MM-DD"
                @change="handleDateRangeChange"
              />
              <el-select
                v-model="selectedIndustry"
                placeholder="选择行业"
                clearable
                style="margin-left: 10px; width: 200px;"
                @change="loadStatistics"
              >
                <el-option
                  v-for="industry in industries"
                  :key="industry.industry_id"
                  :label="industry.industry_desc"
                  :value="industry.industry_id"
                >
                  <span>{{ industry.industry_desc }}</span>
                  <el-tag v-if="industry.main_category" size="small" type="success" style="margin-left: 8px;">
                    主营
                  </el-tag>
                </el-option>
              </el-select>
            </div>
            <div class="action-buttons">
              <el-button type="primary" @click="loadStatistics" :loading="loading">
                <el-icon><Refresh /></el-icon>
                刷新数据
              </el-button>
              <el-button type="success" @click="showSyncDialog" :loading="syncLoading">
                <el-icon><Download /></el-icon>
                同步数据
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 数据概览卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-number">{{ summary.total_industries }}</div>
          <div class="stat-label">总行业数</div>
          <div class="stat-extra">主营: {{ summary.main_industries }}</div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-number">{{ formatNumber(summary.total_fb) }}</div>
          <div class="stat-label">总询盘数</div>
          <div class="stat-extra">转化率: {{ calculateConversionRate() }}%</div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-number">{{ formatNumber(summary.total_clk) }}</div>
          <div class="stat-label">总点击数</div>
          <div class="stat-extra">点击率: {{ summary.avg_clk_rate }}%</div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <div class="stat-card">
          <div class="stat-number">{{ formatNumber(summary.total_visitor) }}</div>
          <div class="stat-label">总访客数</div>
          <div class="stat-extra">回复率: {{ summary.avg_reply_rate }}%</div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表和表格 -->
    <el-row :gutter="20">
      <el-col :xs="24" :lg="12">
        <el-card>
          <template #header>
            <span>行业表现排行</span>
          </template>
          <div class="chart-container">
            <div v-if="performanceData.length === 0" class="no-data">
              暂无数据
            </div>
            <div v-else class="performance-ranking">
              <div 
                v-for="(item, index) in topPerformanceData" 
                :key="item.industry_id"
                class="ranking-item"
              >
                <div class="rank">{{ index + 1 }}</div>
                <div class="industry-info">
                  <div class="industry-name">
                    {{ item.industry_desc }}
                    <el-tag v-if="item.main_category" size="small" type="success">主营</el-tag>
                  </div>
                  <div class="metrics">
                    <span>询盘: {{ item.fb }}</span>
                    <span>点击: {{ item.clk }}</span>
                    <span>转化率: {{ ((item.fb / item.clk) * 100).toFixed(2) }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :xs="24" :lg="12">
        <el-card>
          <template #header>
            <span>行业分布</span>
          </template>
          <div class="chart-container">
            <div class="industry-distribution">
              <div class="distribution-item">
                <div class="label">主营行业</div>
                <div class="value">{{ summary.main_industries }}</div>
                <div class="percentage">
                  {{ ((summary.main_industries / summary.total_industries) * 100).toFixed(1) }}%
                </div>
              </div>
              <div class="distribution-item">
                <div class="label">其他行业</div>
                <div class="value">{{ summary.total_industries - summary.main_industries }}</div>
                <div class="percentage">
                  {{ (((summary.total_industries - summary.main_industries) / summary.total_industries) * 100).toFixed(1) }}%
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>详细数据</span>
          </template>
          <el-table :data="performanceData" style="width: 100%" v-loading="loading">
            <el-table-column prop="industry_desc" label="行业" width="200">
              <template #default="scope">
                <div>
                  {{ scope.row.industry_desc }}
                  <el-tag v-if="scope.row.main_category" size="small" type="success" style="margin-left: 8px;">
                    主营
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="imps" label="曝光数" width="100" sortable>
              <template #default="scope">
                {{ formatNumber(scope.row.imps) }}
              </template>
            </el-table-column>
            <el-table-column prop="clk" label="点击数" width="100" sortable>
              <template #default="scope">
                {{ formatNumber(scope.row.clk) }}
              </template>
            </el-table-column>
            <el-table-column prop="clk_rate" label="点击率" width="100" sortable>
              <template #default="scope">
                {{ scope.row.clk_rate }}%
              </template>
            </el-table-column>
            <el-table-column prop="visitor" label="访客数" width="100" sortable>
              <template #default="scope">
                {{ formatNumber(scope.row.visitor) }}
              </template>
            </el-table-column>
            <el-table-column prop="fb" label="询盘数" width="100" sortable>
              <template #default="scope">
                {{ formatNumber(scope.row.fb) }}
              </template>
            </el-table-column>
            <el-table-column label="转化率" width="100" sortable>
              <template #default="scope">
                {{ ((scope.row.fb / scope.row.clk) * 100).toFixed(2) }}%
              </template>
            </el-table-column>
            <el-table-column prop="reply" label="回复率" width="100" sortable>
              <template #default="scope">
                {{ scope.row.reply }}%
              </template>
            </el-table-column>
            <el-table-column prop="data_start_date" label="数据周期" min-width="120">
              <template #default="scope">
                {{ scope.row.data_start_date }} ~ {{ scope.row.data_end_date }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 同步数据对话框 -->
    <el-dialog
      v-model="syncDialogVisible"
      title="同步阿里询盘数据"
      width="500px"
    >
      <el-form :model="syncForm" label-width="100px">
        <el-form-item label="数据周期">
          <el-date-picker
            v-model="syncForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY年MM月DD日"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="强制更新">
          <el-switch v-model="syncForm.forceUpdate" />
          <div style="font-size: 12px; color: #909399; margin-top: 4px;">
            开启后将覆盖已有的数据
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="syncDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSync" :loading="syncLoading">
          开始同步
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Download } from '@element-plus/icons-vue'
import axios from 'axios'

export default defineComponent({
  name: 'AliInquiryStatistics',
  components: {
    Refresh,
    Download
  },
  setup() {
    const loading = ref(false)
    const syncLoading = ref(false)
    const syncDialogVisible = ref(false)
    
    // 数据状态
    const dateRange = ref([])
    const selectedIndustry = ref(null)
    const industries = ref([])
    const performanceData = ref([])
    const summary = ref({
      total_industries: 0,
      main_industries: 0,
      total_clk: 0,
      total_fb: 0,
      total_imps: 0,
      total_visitor: 0,
      avg_clk_rate: 0,
      avg_reply_rate: 0
    })
    
    // 同步表单
    const syncForm = ref({
      dateRange: [],
      forceUpdate: false
    })

    // 计算属性
    const topPerformanceData = computed(() => {
      return performanceData.value
        .slice()
        .sort((a, b) => b.fb - a.fb)
        .slice(0, 10)
    })

    // 工具函数
    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }

    const calculateConversionRate = () => {
      if (summary.value.total_clk === 0) return '0.00'
      return ((summary.value.total_fb / summary.value.total_clk) * 100).toFixed(2)
    }

    // API调用函数
    const loadStatistics = async () => {
      if (!dateRange.value || dateRange.value.length !== 2) {
        ElMessage.warning('请选择查询日期范围')
        return
      }

      loading.value = true
      try {
        const params = {
          start_date: dateRange.value[0],
          end_date: dateRange.value[1]
        }
        
        if (selectedIndustry.value) {
          params.industry_id = selectedIndustry.value
        }

        const response = await axios.get('/api/v1/alibaba-inquiry/statistics', {
          params
        })

        const data = response.data
        industries.value = data.top_industries
        performanceData.value = data.performance_data
        if (data.summary) {
          summary.value = data.summary
        }

        ElMessage.success('数据加载成功')
      } catch (error) {
        console.error('加载统计数据失败:', error)
        ElMessage.error(error.response?.data?.detail || '加载数据失败')
      } finally {
        loading.value = false
      }
    }

    const loadDataPeriods = async () => {
      try {
        const response = await axios.get('/api/v1/alibaba-inquiry/data-periods')
        const periods = response.data
        
        if (periods.length > 0) {
          // 默认选择最新的数据周期
          const latestPeriod = periods[0]
          dateRange.value = [latestPeriod.start_date, latestPeriod.end_date]
        }
      } catch (error) {
        console.error('加载数据周期失败:', error)
      }
    }

    const showSyncDialog = () => {
      // 设置默认同步时间为最近30天
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(endDate.getDate() - 30)
      
      syncForm.value.dateRange = [
        startDate.toISOString().split('T')[0],
        endDate.toISOString().split('T')[0]
      ]
      syncForm.value.forceUpdate = false
      syncDialogVisible.value = true
    }

    const handleSync = async () => {
      if (!syncForm.value.dateRange || syncForm.value.dateRange.length !== 2) {
        ElMessage.warning('请选择同步日期范围')
        return
      }

      syncLoading.value = true
      try {
        const response = await axios.post('/api/v1/alibaba-inquiry/sync', {
          date_range: {
            start_date: syncForm.value.dateRange[0],
            end_date: syncForm.value.dateRange[1]
          },
          force_update: syncForm.value.forceUpdate
        })

        ElMessage.success(`数据同步成功！同步了 ${response.data.sync_result.industries} 个行业，${response.data.sync_result.performance_records} 条表现记录`)
        syncDialogVisible.value = false
        
        // 同步完成后重新加载数据
        await loadDataPeriods()
        if (dateRange.value.length === 2) {
          await loadStatistics()
        }
      } catch (error) {
        console.error('同步数据失败:', error)
        ElMessage.error(error.response?.data?.detail || '同步数据失败')
      } finally {
        syncLoading.value = false
      }
    }

    const handleDateRangeChange = (dates) => {
      if (dates && dates.length === 2) {
        loadStatistics()
      }
    }

    // 初始化
    onMounted(async () => {
      await loadDataPeriods()
      if (dateRange.value.length === 2) {
        await loadStatistics()
      }
    })

    return {
      loading,
      syncLoading,
      syncDialogVisible,
      dateRange,
      selectedIndustry,
      industries,
      performanceData,
      summary,
      syncForm,
      topPerformanceData,
      formatNumber,
      calculateConversionRate,
      loadStatistics,
      showSyncDialog,
      handleSync,
      handleDateRangeChange
    }
  }
})
</script>

<style scoped>
.ali-inquiry-statistics {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.date-controls {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.stat-card {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.stat-extra {
  font-size: 12px;
  color: #909399;
}

.chart-container {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-data {
  color: #909399;
  font-size: 14px;
}

.performance-ranking {
  width: 100%;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.ranking-item:last-child {
  border-bottom: none;
}

.rank {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #409EFF;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 12px;
  flex-shrink: 0;
}

.rank:nth-child(1) {
  background: #f56c6c;
}

.rank:nth-child(2) {
  background: #e6a23c;
}

.rank:nth-child(3) {
  background: #67c23a;
}

.industry-info {
  flex: 1;
}

.industry-name {
  font-weight: 500;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.metrics {
  font-size: 12px;
  color: #909399;
  display: flex;
  gap: 12px;
}

.industry-distribution {
  width: 100%;
}

.distribution-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.distribution-item:last-child {
  border-bottom: none;
}

.distribution-item .label {
  font-weight: 500;
  color: #303133;
}

.distribution-item .value {
  font-size: 18px;
  font-weight: 600;
  color: #409EFF;
}

.distribution-item .percentage {
  font-size: 12px;
  color: #909399;
}

@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .date-controls,
  .action-buttons {
    justify-content: center;
  }
}
</style> 