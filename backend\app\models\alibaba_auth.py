from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean

from app.models.base import BaseModel
from app.utils.datetime_utils import utc_now

class AlibabaAuth(BaseModel):
    """阿里国际站授权信息模型"""
    __tablename__ = "alibaba_auth"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True, comment="关联的用户ID")
    tenant_id = Column(String(50), index=True, comment="租户ID")
    
    # 授权信息
    access_token = Column(Text, comment="访问令牌")
    refresh_token = Column(Text, comment="刷新令牌") 
    expires_in = Column(Integer, comment="令牌有效期(秒)")
    refresh_expires_in = Column(Integer, comment="刷新令牌有效期(秒)")
    token_created_at = Column(DateTime(timezone=True), default=utc_now, comment="令牌创建时间")
    
    # 用户信息
    alibaba_account = Column(String(255), comment="阿里巴巴账号")
    country = Column(String(10), comment="国家代码")
    account_platform = Column(String(50), comment="账号平台")
    havana_id = Column(String(100), comment="用户ID (原user_id)")
    
    # 状态信息  
    is_active = Column(Boolean, default=True, comment="是否激活")
    last_refresh_at = Column(DateTime(timezone=True), comment="最后刷新时间")
    
    # 额外信息
    auth_scope = Column(String(500), comment="授权范围")
    code = Column(String(500), comment="授权码(临时)")
    request_id = Column(String(100), comment="请求ID")
    trace_id = Column(String(100), comment="追踪ID")
    
    class Config:
        table = True 