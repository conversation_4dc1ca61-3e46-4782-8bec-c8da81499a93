#!/usr/bin/env python3
"""
测试阿里巴巴ICBU产品列表API调用

使用方法:
python test_icbu_product_list.py
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from app.core.database import SessionLocal
from app.services.alibaba_service import AlibabaService
from app.services.alibaba_product_service_real import AlibabaProductServiceReal

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 测试用的access_token（需要替换为真实的）
TEST_ACCESS_TOKEN = "50000900a25ONSMuApzOPCg6jVchtCtwG9Fey1721d840BOU0EhlSL2grVHUIkb"

async def test_icbu_product_list():
    """测试ICBU产品列表API"""
    db = SessionLocal()
    
    try:
        logger.info("=" * 60)
        logger.info("开始测试阿里巴巴ICBU产品列表API")
        logger.info("=" * 60)
        
        # 初始化服务
        alibaba_service = AlibabaService(db)
        
        logger.info("1. 测试基础API调用...")
        
        # 测试基础调用
        try:
            response = alibaba_service.get_icbu_product_list(
                access_token=TEST_ACCESS_TOKEN,
                page_size=10,
                current_page=1,
                language="ENGLISH"
            )
            
            logger.info("✅ API调用成功")
            logger.info(f"响应数据: {response}")
            
            # 检查响应格式
            if response.get("error_response"):
                logger.error(f"❌ API返回错误: {response['error_response']}")
            elif "alibaba_icbu_product_list_response" in response:
                logger.info("✅ 响应格式正确")
                
                result = response["alibaba_icbu_product_list_response"]
                logger.info(f"总数: {result.get('total_item', 0)}")
                logger.info(f"当前页: {result.get('current_page', 1)}")
                logger.info(f"页大小: {result.get('page_size', 10)}")
                
                products = result.get("products", [])
                if isinstance(products, list):
                    logger.info(f"产品数量: {len(products)}")
                    if len(products) > 0:
                        logger.info("第一个产品信息:")
                        first_product = products[0]
                        for key, value in first_product.items():
                            logger.info(f"  {key}: {value}")
                else:
                    logger.info(f"产品数据: {products}")
            else:
                logger.warning(f"⚠️ 响应格式异常: {response}")
                
        except Exception as e:
            logger.error(f"❌ API调用失败: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
        
        logger.info("\n" + "=" * 60)
        logger.info("2. 测试产品服务层调用...")
        
        # 测试产品服务层
        try:
            product_service = AlibabaProductServiceReal()
            result = await product_service.get_alibaba_product_list(
                db=db,
                user_id=2,  # 测试用户ID
                tenant_id="test_tenant",
                access_token=TEST_ACCESS_TOKEN,
                page_size=10,
                current_page=1,
                sync_to_db=False  # 不同步到数据库
            )
            
            logger.info("✅ 产品服务调用成功")
            logger.info(f"成功状态: {result.get('success', False)}")
            logger.info(f"产品数量: {result.get('total_count', 0)}")
            
            if result.get("error"):
                logger.error(f"❌ 服务层错误: {result['error']}")
            
            if result.get("products"):
                logger.info("产品数据示例:")
                for i, product in enumerate(result["products"][:3]):  # 显示前3个
                    logger.info(f"产品 {i+1}:")
                    logger.info(f"  ID: {product.get('product_id', 'N/A')}")
                    logger.info(f"  名称: {product.get('product_name', 'N/A')}")
                    logger.info(f"  状态: {product.get('product_status', 'N/A')}")
                    logger.info(f"  URL: {product.get('product_url', 'N/A')}")
                    
        except Exception as e:
            logger.error(f"❌ 产品服务调用失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
        
        logger.info("\n" + "=" * 60)
        logger.info("3. 测试不同参数组合...")
        
        # 测试带筛选条件的调用
        test_params = [
            {"status": "approved", "display": "Y"},
            {"page_size": 5, "current_page": 1},
            {"subject": "product"}  # 搜索包含"product"的商品
        ]
        
        for i, params in enumerate(test_params, 1):
            try:
                logger.info(f"测试参数组合 {i}: {params}")
                response = alibaba_service.get_icbu_product_list(
                    access_token=TEST_ACCESS_TOKEN,
                    language="ENGLISH",
                    **params
                )
                
                if response.get("error_response"):
                    logger.error(f"❌ 参数组合 {i} 失败: {response['error_response']}")
                else:
                    logger.info(f"✅ 参数组合 {i} 成功")
                    
            except Exception as e:
                logger.error(f"❌ 参数组合 {i} 异常: {e}")
        
        logger.info("\n" + "=" * 60)
        logger.info("测试完成")
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"测试过程中发生异常: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_icbu_product_list()) 