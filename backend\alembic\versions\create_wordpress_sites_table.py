"""create wordpress sites table

Revision ID: create_wordpress_sites
Revises: create_ai_tables
Create Date: 2024-12-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import text


# revision identifiers, used by Alembic.
revision = 'create_wordpress_sites'
down_revision = 'create_ai_tables'
branch_labels = None
depends_on = None


def upgrade():
    # 创建WordPress站点表
    op.execute(text("""
    CREATE TABLE `wordpress_sites` (
      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
      `name` varchar(255) NOT NULL COMMENT '站点名称',
      `url` varchar(255) NOT NULL COMMENT '站点URL',
      `category` varchar(100) DEFAULT NULL COMMENT '站点分类',
      
      `wp_username` varchar(255) NOT NULL COMMENT 'WordPress用户名',
      `wp_app_password` varchar(255) NOT NULL COMMENT 'WordPress应用程序密码',
      
      `daily_uv` bigint(20) DEFAULT 0 COMMENT '当日UV(独立访客)',
      `daily_pv` bigint(20) DEFAULT 0 COMMENT '当日PV(页面浏览量)',
      `monthly_uv` bigint(20) DEFAULT 0 COMMENT '当月UV',
      `monthly_pv` bigint(20) DEFAULT 0 COMMENT '当月PV',
      `total_posts` int(11) DEFAULT 0 COMMENT '总文章数',
      `total_pages` int(11) DEFAULT 0 COMMENT '总页面数',
      
      `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
      `last_sync_at` timestamp NULL DEFAULT NULL COMMENT '最后同步时间',
      `sync_status` varchar(50) DEFAULT 'pending' COMMENT '同步状态',
      `error_message` text DEFAULT NULL COMMENT '错误信息',
      
      `description` text DEFAULT NULL COMMENT '站点描述',
      `wordpress_version` varchar(50) DEFAULT NULL COMMENT 'WordPress版本',
      `theme_name` varchar(255) DEFAULT NULL COMMENT '主题名称',
      `language` varchar(10) DEFAULT NULL COMMENT '站点语言',
      `timezone` varchar(50) DEFAULT NULL COMMENT '时区',
      
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      
      PRIMARY KEY (`id`),
      UNIQUE KEY `idx_url` (`url`),
      KEY `idx_name` (`name`),
      KEY `idx_category` (`category`),
      KEY `idx_is_active` (`is_active`),
      KEY `idx_sync_status` (`sync_status`),
      KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='WordPress站点表'
    """))
    
    # 创建视图：WordPress站点统计汇总
    op.execute(text("""
    CREATE VIEW `v_wordpress_sites_summary` AS
    SELECT 
      COUNT(*) as `total_sites`,
      COUNT(CASE WHEN `is_active` = 1 THEN 1 END) as `active_sites`,
      COUNT(CASE WHEN `sync_status` = 'success' THEN 1 END) as `synced_sites`,
      COUNT(CASE WHEN `sync_status` = 'failed' THEN 1 END) as `failed_sites`,
      SUM(`total_posts`) as `total_posts_count`,
      SUM(`total_pages`) as `total_pages_count`,
      SUM(`daily_uv`) as `total_daily_uv`,
      SUM(`daily_pv`) as `total_daily_pv`,
      COUNT(DISTINCT `category`) as `categories_count`
    FROM `wordpress_sites`
    """))


def downgrade():
    # 删除视图
    op.execute(text("DROP VIEW IF EXISTS `v_wordpress_sites_summary`"))
    
    # 删除WordPress站点表
    op.execute(text("DROP TABLE IF EXISTS `wordpress_sites`")) 