from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional
import asyncio
from datetime import datetime
from ....db.session import get_db
from ....models.ai_config import AIConfig
from ....models.ai_article import AIArticle, ArticleStatus
from ....schemas.ai_article import (
    AIArticleCreate, 
    AIArticleUpdate, 
    AIArticleResponse, 
    AIArticleList,
    DIFYWorkflowRequest
)
from ....api.deps import get_current_user
from ....models.user import User
from ....services.dify_service import DIFYService
import os
import uuid
from app.utils.datetime_utils import utc_now, to_iso_string

router = APIRouter()


@router.get("/", response_model=AIArticleList)
async def get_ai_articles(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    status: Optional[str] = Query(None, description="文章状态筛选"),
    keywords: Optional[str] = Query(None, description="关键词搜索"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取AI文章列表"""
    query = db.query(AIArticle)
    
    # 筛选条件
    if status:
        query = query.filter(AIArticle.status == status)
    
    if keywords:
        query = query.filter(AIArticle.keywords.contains(keywords))
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * size
    items = query.order_by(AIArticle.created_at.desc()).offset(offset).limit(size).all()
    
    return AIArticleList(
        items=items,
        total=total,
        page=page,
        size=size
    )


@router.get("/{article_id}", response_model=AIArticleResponse)
async def get_ai_article(
    article_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取单个AI文章"""
    article = db.query(AIArticle).filter(AIArticle.id == article_id).first()
    if not article:
        raise HTTPException(status_code=404, detail="文章记录不存在")
    return article


@router.post("/generate", response_model=AIArticleResponse)
async def generate_article(
    request: DIFYWorkflowRequest,
    ai_config_id: Optional[int] = Query(None, description="AI配置ID，不提供则使用默认配置"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """生成AI文章 - 用户接口版本"""
    return await _generate_article_internal(request, ai_config_id, db)


async def _generate_article_internal(
    request: DIFYWorkflowRequest,
    ai_config_id: Optional[int] = None,
    db: Session = None
):
    """生成AI文章 - 内部实现，可用于定时任务"""

    # 获取AI配置
    if ai_config_id:
        ai_config = db.query(AIConfig).filter(
            AIConfig.id == ai_config_id,
            AIConfig.is_active == True
        ).first()
        if not ai_config:
            raise HTTPException(status_code=404, detail="AI配置不存在或未启用")
    else:
        # 获取默认的DIFY配置
        ai_config = db.query(AIConfig).filter(
            AIConfig.service_type == "DIFY",
            AIConfig.is_active == True
        ).first()
        if not ai_config:
            raise HTTPException(status_code=400, detail="未找到可用的DIFY配置")

    # 创建文章记录
    article_data = AIArticleCreate(
        keywords=request.keywords,
        wordpress_url=request.wordpress_url,
        type=request.type,
        tags=request.tags,
        model=request.model,
        image_url=request.image_url,
        ai_config_id=ai_config.id
    )

    article = AIArticle(**article_data.dict())
    article.status = "generating"
    db.add(article)
    db.commit()

    # 获取文章ID，避免刷新带来的枚举值问题
    article_id = article.id

    # 启动异步生成文章任务（不等待完成）
    print(f"准备启动异步任务，文章ID: {article_id}")
    asyncio.create_task(
        _generate_article_async(article_id, ai_config, request)
    )
    print(f"异步任务已启动，立即返回响应")

    # 手动构造返回对象，包含所有必需字段
    return {
        "id": article_id,
        "keywords": article.keywords,
        "wordpress_url": article.wordpress_url,
        "type": article.type,
        "tags": article.tags,
        "model": article.model,
        "image_url": article.image_url,
        "title": None,
        "article_url": None,
        "article_id": None,
        "featured_image_id": None,
        "status": "generating",
        "error_message": None,
        "result_text": None,
        "workflow_run_id": None,
        "task_id": None,
        "ai_config_id": article.ai_config_id,
        "created_at": article.created_at,
        "updated_at": article.updated_at
    }


async def _generate_article_async(
    article_id: int,
    ai_config: AIConfig,
    request: DIFYWorkflowRequest
):
    """异步生成文章 - 完整streaming模式，在后台执行"""
    print(f"🚀 异步任务开始执行，文章ID: {article_id}")
    
    from ....db.session import SessionLocal
    
    db = SessionLocal()
    try:
        print(f"查询文章记录，ID: {article_id}")
        article = db.query(AIArticle).filter(AIArticle.id == article_id).first()
        if not article:
            print(f"文章记录不存在: {article_id}")
            return
        
        print(f"文章记录找到: {article.id}, 状态: {article.status}")
        
        # 初始化DIFY服务
        dify_service = DIFYService(
            api_key=ai_config.api_key,
            base_url=ai_config.base_url or "http://ai.yufeiind.cn:81/v1"
        )
        
        # 处理图片上传
        image_file_id = None
        if request.image_url:
            # 这里可以添加图片下载和上传到DIFY的逻辑
            pass
        
        # 创建状态回调函数，实时更新文章状态
        def status_callback(status, message):
            """实时更新文章状态的回调函数"""
            try:
                # 重新查询文章记录
                current_article = db.query(AIArticle).filter(AIArticle.id == article_id).first()
                if current_article:
                    # 更新状态信息
                    if status == "running":
                        current_article.status = "generating"
                        if "工作流已启动:" in message:
                            # 提取workflow_run_id
                            workflow_id = message.split(":")[-1].strip()
                            current_article.workflow_run_id = workflow_id
                    elif status == "processing":
                        current_article.status = "generating"
                        # 可以将详细进度信息存储到error_message字段作为临时状态
                        current_article.error_message = f"进度: {message}"
                    elif status == "completed":
                        # 完成状态会在主流程中处理，这里只记录
                        print(f"📝 状态回调收到完成信号: {message}")
                    
                    db.commit()
                    print(f"📝 状态已更新: {status} - {message}")
            except Exception as e:
                print(f"❌ 状态回调更新失败: {str(e)}")
        
        # 执行DIFY工作流（异步streaming模式，在后台完成）
        print(f"🔄 启动DIFY工作流，文章ID: {article_id}")
        
        # 修改run_workflow_async方法以支持状态回调
        workflow_result = await dify_service.run_workflow_async(
            keywords=request.keywords,
            wordpress_url=request.wordpress_url,
            type_param=request.type,
            tags=request.tags,
            model=request.model if request.model and request.model.strip() else None,
            image_file_id=image_file_id,
            wp_user=request.wp_user,
            wp_pwd=request.wp_pwd,
            response_mode="streaming",  # 使用streaming模式，完整等待
            status_callback=status_callback  # 传递状态回调
        )
        
        print(f"工作流执行结果: {workflow_result}")
        
        # 保存工作流ID
        workflow_run_id = workflow_result.get("workflow_run_id")
        task_id = workflow_result.get("task_id")
        
        if workflow_run_id:
            article.workflow_run_id = workflow_run_id
        if task_id:
            article.task_id = task_id
        
        # 根据执行结果更新文章状态
        if workflow_result.get("status") == "completed":
            # 工作流成功完成
            workflow_data = workflow_result.get("data", {})
            workflow_status = workflow_data.get("status")
            
            if workflow_status == "succeeded":
                # 处理成功结果
                outputs = workflow_data.get("outputs") or {}
                result_text = outputs.get("result", "")
                
                if result_text:
                    # 保存原始结果
                    article.result_text = result_text
                    
                    # 解析结果文本
                    parsed = dify_service.parse_result(result_text)
                    print(f"解析结果: {parsed}")
                    
                    if parsed["success"]:
                        # 解析成功，更新文章信息
                        article.status = "published"
                        article.title = parsed["title"]
                        article.article_url = parsed["url"]
                        article.article_id = parsed["article_id"]
                        article.featured_image_id = parsed["featured_image_id"]
                        article.error_message = None
                        print(f"✅ 文章发布成功: {article.title}")
                    else:
                        # 解析失败
                        article.status = "failed"
                        article.error_message = parsed["message"]
                        print(f"❌ 结果解析失败: {parsed['message']}")
                else:
                    # 没有返回内容
                    article.status = "success"
                    article.error_message = "工作流执行成功，但未返回文章内容"
                    print(f"⚠️ 工作流成功但无内容")
                    
            elif workflow_status == "failed":
                # 工作流执行失败
                article.status = "failed"
                article.error_message = workflow_data.get("error", "DIFY工作流执行失败")
                print(f"❌ 工作流执行失败: {article.error_message}")
                
            else:
                # 其他状态
                article.status = "failed"
                article.error_message = f"工作流状态异常: {workflow_status}"
                print(f"❌ 工作流状态异常: {workflow_status}")
                
        elif workflow_result.get("status") == "started":
            # 工作流启动但连接中断
            article.status = "failed"
            article.error_message = workflow_result.get("message", "工作流启动后连接中断")
            print(f"⚠️ 工作流连接中断")
            
        else:
            # 执行失败
            article.status = "failed"
            article.error_message = workflow_result.get("message", "工作流执行失败")
            print(f"❌ 工作流执行失败: {article.error_message}")
        
        # 提交最终状态
        db.commit()
        print(f"✅ 文章状态已更新: {article.status}")

        # 如果文章发布成功，检查是否有关联的定时任务并触发事件
        if article.status == "published":
            try:
                await _trigger_scheduled_task_events(article_id, db)
            except Exception as e:
                print(f"[EVENT] 触发事件时发生错误: {e}")
                import traceback
                traceback.print_exc()

        return
        
    except Exception as e:
        # 更新错误状态
        print(f"异步生成文章时发生异常: {str(e)}")
        try:
            # 重新查询文章记录，确保article是有效的对象
            article = db.query(AIArticle).filter(AIArticle.id == article_id).first()
            if article:
                article.status = "failed"
                article.error_message = f"生成过程中发生错误: {str(e)}"
                db.commit()
        except Exception as commit_error:
            print(f"更新错误状态时发生异常: {str(commit_error)}")
    finally:
        db.close()


async def _trigger_scheduled_task_events(article_id: int, db: Session):
    """当文章发布成功时，检查是否有关联的定时任务并触发事件"""
    try:
        # 查找关联的定时任务
        from ....models.scheduled_publish import ScheduledPublishTask, ScheduledPublishPlan, TaskQueue

        task = db.query(ScheduledPublishTask).filter(
            ScheduledPublishTask.ai_article_id == article_id
        ).first()

        if not task:
            print(f"[EVENT] 文章 {article_id} 不是定时任务，跳过事件触发")
            return

        print(f"[EVENT] 发现定时任务 {task.id} 关联文章 {article_id}，开始触发事件")

        # 更新任务状态
        original_status = task.status
        task.status = "success"
        task.completion_time = utc_now()

        # 更新队列状态
        queue_item = db.query(TaskQueue).filter(TaskQueue.task_id == task.id).first()
        if queue_item:
            queue_item.status = "completed"
            print(f"[EVENT] 队列项 {queue_item.id} 状态已更新为 completed")

        # 更新计划统计
        plan = db.query(ScheduledPublishPlan).filter(ScheduledPublishPlan.id == task.plan_id).first()
        if plan:
            # 重新计算统计
            from ....services.scheduled_publish_service import ScheduledPublishService
            service = ScheduledPublishService(db)
            service._update_plan_statistics(plan)
            print(f"[EVENT] 计划 {plan.id} 统计已更新")

        db.commit()
        print(f"[EVENT] 任务 {task.id} 状态已更新为 success")

        # 任务完成记录
        print(f"[TASK_COMPLETED] 任务 {task.id} 已完成")
        print(f"[TASK_COMPLETED] 计划 {task.plan_id} 统计已更新")
        print(f"[TASK_COMPLETED] 队列状态已更新")
        print(f"[TASK_COMPLETED] 前端可通过'刷新数据'按钮获取最新状态")

    except Exception as e:
        print(f"[EVENT] ❌ 触发定时任务事件失败: {e}")
        import traceback
        traceback.print_exc()


@router.post("/upload-image")
async def upload_image(
    file: UploadFile = File(...),
    ai_config_id: Optional[int] = Query(None, description="AI配置ID"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """上传图片到DIFY"""
    
    # 验证文件类型
    if not file.content_type or not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="仅支持图片文件")
    
    # 获取AI配置
    if ai_config_id:
        ai_config = db.query(AIConfig).filter(
            AIConfig.id == ai_config_id,
            AIConfig.is_active == True
        ).first()
        if not ai_config:
            raise HTTPException(status_code=404, detail="AI配置不存在或未启用")
    else:
        ai_config = db.query(AIConfig).filter(
            AIConfig.service_type == "DIFY",
            AIConfig.is_active == True
        ).first()
        if not ai_config:
            raise HTTPException(status_code=400, detail="未找到可用的DIFY配置")
    
    try:
        # 读取文件内容
        file_content = await file.read()
        
        # 初始化DIFY服务
        dify_service = DIFYService(
            api_key=ai_config.api_key,
            base_url=ai_config.base_url or "http://ai.yufeiind.cn:81/v1"
        )
        
        # 上传到DIFY
        file_id = await dify_service.upload_file(
            file_content=file_content,
            filename=file.filename,
            file_type="image"
        )
        
        if file_id:
            return {
                "status": "success",
                "file_id": file_id,
                "filename": file.filename,
                "message": "图片上传成功"
            }
        else:
            raise HTTPException(status_code=500, detail="图片上传失败")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"上传过程中发生错误: {str(e)}")


@router.put("/{article_id}", response_model=AIArticleResponse)
async def update_ai_article(
    article_id: int,
    article_data: AIArticleUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新AI文章"""
    article = db.query(AIArticle).filter(AIArticle.id == article_id).first()
    if not article:
        raise HTTPException(status_code=404, detail="文章记录不存在")
    
    # 更新字段
    update_data = article_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(article, field, value)
    
    db.commit()
    db.refresh(article)
    return article


@router.delete("/{article_id}")
async def delete_ai_article(
    article_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除AI文章记录"""
    article = db.query(AIArticle).filter(AIArticle.id == article_id).first()
    if not article:
        raise HTTPException(status_code=404, detail="文章记录不存在")
    
    db.delete(article)
    db.commit()
    return {"message": "文章记录已删除"}


@router.get("/{article_id}/status")
async def get_article_status(
    article_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取文章生成状态"""
    article = db.query(AIArticle).filter(AIArticle.id == article_id).first()
    if not article:
        raise HTTPException(status_code=404, detail="文章记录不存在")
    
    return {
        "id": article.id,
        "status": article.status,
        "title": article.title,
        "article_url": article.article_url,
        "error_message": article.error_message,
        "workflow_run_id": article.workflow_run_id,
        "task_id": article.task_id,
        "created_at": article.created_at,
        "updated_at": article.updated_at
    }


# 刷新状态API已移除，因为现在使用完整streaming模式，工作流完成后会直接更新状态 