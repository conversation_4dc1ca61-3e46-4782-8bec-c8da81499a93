-- 为keyword_import_tasks表的status字段添加cancelled状态
-- 此脚本修改status枚举类型，添加cancelled选项

USE cbec;

-- 1. 检查当前表结构
SELECT 
    COLUMN_NAME,
    COLUMN_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'keyword_import_tasks' 
  AND COLUMN_NAME = 'status';

-- 2. 修改status字段，添加cancelled状态
ALTER TABLE keyword_import_tasks 
MODIFY COLUMN status ENUM('pending','processing','completed','failed','cancelled') 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci 
DEFAULT 'pending' 
COMMENT '任务状态';

-- 3. 验证修改结果
SELECT 
    COLUMN_NAME,
    COLUMN_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'keyword_import_tasks' 
  AND COLUMN_NAME = 'status';

-- 4. 显示表结构确认
DESCRIBE keyword_import_tasks;

-- 5. 检查现有数据
SELECT 
    status,
    COUNT(*) as count
FROM keyword_import_tasks 
GROUP BY status;

-- 6. 测试插入cancelled状态（可选）
-- INSERT INTO keyword_import_tasks (task_id, file_name, file_path, status) 
-- VALUES ('test-cancelled', 'test.csv', '/tmp/test.csv', 'cancelled');

-- 7. 验证cancelled状态可以正常使用
-- SELECT * FROM keyword_import_tasks WHERE status = 'cancelled';
