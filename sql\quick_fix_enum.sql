-- 快速修复枚举值 - 使用DROP和CREATE方式
USE cbec;

-- 禁用外键检查和安全模式
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_SAFE_UPDATES = 0;

-- 备份数据（如果有的话）
CREATE TABLE IF NOT EXISTS temp_keyword_data AS SELECT * FROM keyword_library WHERE 1=0;
CREATE TABLE IF NOT EXISTS temp_history_data AS SELECT * FROM keyword_update_history WHERE 1=0;
CREATE TABLE IF NOT EXISTS temp_tasks_data AS SELECT * FROM keyword_import_tasks WHERE 1=0;

-- 如果表中有数据，先备份
INSERT INTO temp_keyword_data SELECT * FROM keyword_library;
INSERT INTO temp_history_data SELECT * FROM keyword_update_history;
INSERT INTO temp_tasks_data SELECT * FROM keyword_import_tasks;

-- 删除原表
DROP TABLE IF EXISTS keyword_update_history;
DROP TABLE IF EXISTS keyword_library;
DROP TABLE IF EXISTS keyword_import_tasks;

-- 重新创建表（使用正确的枚举值）
CREATE TABLE `keyword_library` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `keyword_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关键词名称',
  `avg_monthly_searches` bigint DEFAULT NULL COMMENT '平均每月搜索量（过去12个月）',
  `monthly_searches` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '大致每月搜索量（JSON格式存储每月数据）',
  `competition_level` enum('LOW','MEDIUM','HIGH','UNSPECIFIED') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'UNSPECIFIED' COMMENT '竞争级别',
  `competition_index` decimal(5,2) DEFAULT NULL COMMENT '竞争指数（0-100）',
  `low_bid_micros` bigint DEFAULT NULL COMMENT '出价第20百分位（微货币单位）',
  `high_bid_micros` bigint DEFAULT NULL COMMENT '出价第80百分位（微货币单位）',
  `currency_code` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'CNY' COMMENT '货币代码',
  `language_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'zh-CN' COMMENT '语言代码',
  `location_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地理位置ID列表（逗号分隔）',
  `update_method` enum('GOOGLE_ADS_API','MANUAL','BATCH_IMPORT') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'MANUAL' COMMENT '更新方式',
  `tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签（逗号分隔）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_keyword_name` (`keyword_name`) USING BTREE,
  KEY `idx_keyword_name` (`keyword_name`) USING BTREE,
  KEY `idx_competition_level` (`competition_level`) USING BTREE,
  KEY `idx_avg_monthly_searches` (`avg_monthly_searches`) USING BTREE,
  KEY `idx_update_method` (`update_method`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  KEY `idx_updated_at` (`updated_at`) USING BTREE,
  FULLTEXT KEY `idx_keyword_tags_fulltext` (`keyword_name`,`tags`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关键词库表';

CREATE TABLE `keyword_update_history` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `keyword_id` int NOT NULL COMMENT '关键词ID',
  `update_method` enum('GOOGLE_ADS_API','MANUAL','BATCH_IMPORT') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新方式',
  `old_data` json DEFAULT NULL COMMENT '更新前的数据（JSON格式）',
  `new_data` json DEFAULT NULL COMMENT '更新后的数据（JSON格式）',
  `batch_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '批量操作ID',
  `operator_id` int DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作人姓名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_keyword_id` (`keyword_id`) USING BTREE,
  KEY `idx_update_method` (`update_method`) USING BTREE,
  KEY `idx_batch_id` (`batch_id`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE,
  CONSTRAINT `keyword_update_history_ibfk_1` FOREIGN KEY (`keyword_id`) REFERENCES `keyword_library` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关键词更新历史表';

CREATE TABLE `keyword_import_tasks` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务ID',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件名',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  `total_count` int DEFAULT '0' COMMENT '总记录数',
  `success_count` int DEFAULT '0' COMMENT '成功数',
  `failed_count` int DEFAULT '0' COMMENT '失败数',
  `status` enum('PENDING','PROCESSING','COMPLETED','FAILED') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'PENDING' COMMENT '任务状态',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `result_file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '结果文件路径',
  `operator_id` int DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作人姓名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_task_id` (`task_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_created_at` (`created_at`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关键词批量导入任务表';

-- 恢复数据（需要处理枚举值转换）
-- 注意：这里需要将旧的小写枚举值转换为新的大写枚举值
INSERT INTO keyword_library 
SELECT 
  id, keyword_name, avg_monthly_searches, monthly_searches, competition_level,
  competition_index, low_bid_micros, high_bid_micros, currency_code, language_code, location_ids,
  CASE 
    WHEN update_method = 'google_ads_api' THEN 'GOOGLE_ADS_API'
    WHEN update_method = 'manual' THEN 'MANUAL' 
    WHEN update_method = 'batch_import' THEN 'BATCH_IMPORT'
    ELSE 'MANUAL'
  END as update_method,
  tags, created_at, updated_at
FROM temp_keyword_data;

INSERT INTO keyword_update_history 
SELECT 
  id, keyword_id,
  CASE 
    WHEN update_method = 'google_ads_api' THEN 'GOOGLE_ADS_API'
    WHEN update_method = 'manual' THEN 'MANUAL'
    WHEN update_method = 'batch_import' THEN 'BATCH_IMPORT'
    ELSE 'MANUAL'
  END as update_method,
  old_data, new_data, batch_id, operator_id, operator_name, created_at, updated_at
FROM temp_history_data;

INSERT INTO keyword_import_tasks 
SELECT 
  id, task_id, file_name, file_path, total_count, success_count, failed_count,
  CASE 
    WHEN status = 'pending' THEN 'PENDING'
    WHEN status = 'processing' THEN 'PROCESSING'
    WHEN status = 'completed' THEN 'COMPLETED'
    WHEN status = 'failed' THEN 'FAILED'
    ELSE 'PENDING'
  END as status,
  error_message, result_file_path, operator_id, operator_name, created_at, updated_at
FROM temp_tasks_data;

-- 清理临时表
DROP TABLE IF EXISTS temp_keyword_data;
DROP TABLE IF EXISTS temp_history_data;
DROP TABLE IF EXISTS temp_tasks_data;

-- 启用外键检查和安全模式
SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;

-- 验证结果
SELECT COUNT(*) as keyword_count FROM keyword_library;
SELECT COUNT(*) as history_count FROM keyword_update_history;
SELECT COUNT(*) as tasks_count FROM keyword_import_tasks; 