/*
 Navicat Premium Dump SQL

 Source Server         : LP-MYSQL
 Source Server Type    : MySQL
 Source Server Version : 90001 (9.0.1)
 Source Host           : **************:13306
 Source Schema         : cbec

 Target Server Type    : MySQL
 Target Server Version : 90001 (9.0.1)
 File Encoding         : 65001

 Date: 29/05/2025 10:57:39
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ai_articles
-- ----------------------------
DROP TABLE IF EXISTS `ai_articles`;
CREATE TABLE `ai_articles`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `keywords` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关键词',
  `wordpress_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'WordPress站点URL',
  `type` int NOT NULL COMMENT '博客分类ID',
  `model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'AI模型，WanX或Jimeng',
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '上传的图片URL',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '生成的文章标题',
  `article_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '发布后的文章链接',
  `article_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'WordPress文章ID',
  `featured_image_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '特色图片ID',
  `status` enum('pending','generating','success','failed','published') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'pending' COMMENT '文章状态',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `result_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '完整返回结果',
  `workflow_run_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'DIFY工作流运行ID',
  `task_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'DIFY任务ID',
  `ai_config_id` int NULL DEFAULT NULL COMMENT 'AI配置ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_keywords`(`keywords`(191) ASC) USING BTREE,
  INDEX `idx_wordpress_url`(`wordpress_url` ASC) USING BTREE,
  INDEX `idx_ai_config_id`(`ai_config_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_workflow_run_id`(`workflow_run_id` ASC) USING BTREE,
  INDEX `idx_ai_articles_status_created`(`status` ASC, `created_at` ASC) USING BTREE,
  INDEX `idx_ai_articles_wordpress_status`(`wordpress_url` ASC, `status` ASC) USING BTREE,
  FULLTEXT INDEX `idx_ai_articles_keywords_fulltext`(`keywords`),
  CONSTRAINT `fk_ai_articles_ai_config` FOREIGN KEY (`ai_config_id`) REFERENCES `ai_configs` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 82 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI文章表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for ai_configs
-- ----------------------------
DROP TABLE IF EXISTS `ai_configs`;
CREATE TABLE `ai_configs`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置名称',
  `service_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '服务类型，如DIFY',
  `api_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'API密钥',
  `base_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'API基础URL',
  `model_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模型名称',
  `max_tokens` int NULL DEFAULT 4000 COMMENT '最大token数',
  `temperature` decimal(3, 2) NULL DEFAULT 0.70 COMMENT '创造性参数',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用，1=启用，0=禁用',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '配置描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_service_type`(`service_type` ASC) USING BTREE,
  INDEX `idx_is_active`(`is_active` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_ai_configs_name`(`name` ASC) USING BTREE,
  INDEX `idx_ai_configs_service_active`(`service_type` ASC, `is_active` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for alibaba_auth
-- ----------------------------
DROP TABLE IF EXISTS `alibaba_auth`;
CREATE TABLE `alibaba_auth`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '关联的用户ID',
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户ID',
  `access_token` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '访问令牌',
  `refresh_token` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '刷新令牌',
  `expires_in` int NULL DEFAULT NULL COMMENT '令牌有效期(秒)',
  `refresh_expires_in` int NULL DEFAULT NULL COMMENT '刷新令牌有效期(秒)',
  `token_created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '令牌创建时间',
  `alibaba_account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '阿里巴巴账号',
  `country` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '国家代码',
  `account_platform` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '账号平台',
  `havana_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '用户ID (原user_id)',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否激活',
  `last_refresh_at` timestamp NULL DEFAULT NULL COMMENT '最后刷新时间',
  `auth_scope` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '授权范围',
  `code` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '授权码(临时)',
  `request_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '请求ID',
  `trace_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '追踪ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_tenant_id`(`tenant_id` ASC) USING BTREE,
  INDEX `idx_user_tenant`(`user_id` ASC, `tenant_id` ASC) USING BTREE,
  CONSTRAINT `alibaba_auth_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '阿里国际站授权信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for alibaba_data_periods
-- ----------------------------
DROP TABLE IF EXISTS `alibaba_data_periods`;
CREATE TABLE `alibaba_data_periods`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `user_id` int NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `is_available` tinyint(1) NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_tenant_user_period`(`tenant_id` ASC, `user_id` ASC, `start_date` ASC, `end_date` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_tenant_user`(`tenant_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_date_range`(`start_date` ASC, `end_date` ASC) USING BTREE,
  CONSTRAINT `alibaba_data_periods_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `alibaba_data_periods_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 79 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '阿里数据周期信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for alibaba_inquiry_performance
-- ----------------------------
DROP TABLE IF EXISTS `alibaba_inquiry_performance`;
CREATE TABLE `alibaba_inquiry_performance`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `user_id` int NOT NULL,
  `industry_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `industry_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `main_category` tinyint(1) NULL DEFAULT 0,
  `data_start_date` date NOT NULL,
  `data_end_date` date NOT NULL,
  `clk` bigint NULL DEFAULT 0 COMMENT '点击数',
  `clk_rate` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '点击率',
  `fb` bigint NULL DEFAULT 0 COMMENT '反馈（询盘）',
  `imps` bigint NULL DEFAULT 0 COMMENT '曝光数',
  `reply` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最近30天询盘一次回复率',
  `visitor` bigint NULL DEFAULT 0 COMMENT '访客数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_tenant_user_industry_date`(`tenant_id` ASC, `user_id` ASC, `industry_id` ASC, `data_start_date` ASC, `data_end_date` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_tenant_user`(`tenant_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_industry_id`(`industry_id` ASC) USING BTREE,
  INDEX `idx_date_range`(`data_start_date` ASC, `data_end_date` ASC) USING BTREE,
  CONSTRAINT `alibaba_inquiry_performance_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `alibaba_inquiry_performance_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 76 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '阿里询盘流量行业表现数据' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for alibaba_inquiry_summary
-- ----------------------------
DROP TABLE IF EXISTS `alibaba_inquiry_summary`;
CREATE TABLE `alibaba_inquiry_summary`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `user_id` int NOT NULL,
  `summary_date` date NOT NULL,
  `summary_type` enum('daily','weekly','monthly','custom','dashboard','quarterly','yearly') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '汇总类型：daily-日汇总，weekly-周汇总，monthly-月汇总，custom-自定义时间范围，dashboard-仪表板，quarterly-季度汇总，yearly-年汇总',
  `total_industries` int NULL DEFAULT 0 COMMENT '总行业数',
  `main_industries` int NULL DEFAULT 0 COMMENT '主营行业数',
  `total_clk` bigint NULL DEFAULT 0 COMMENT '总点击数',
  `total_fb` bigint NULL DEFAULT 0 COMMENT '总询盘数',
  `total_imps` bigint NULL DEFAULT 0 COMMENT '总曝光数',
  `total_visitor` bigint NULL DEFAULT 0 COMMENT '总访客数',
  `avg_clk_rate` decimal(5, 2) NULL DEFAULT 0.00 COMMENT '平均点击率',
  `avg_reply_rate` decimal(5, 2) NULL DEFAULT 0.00 COMMENT '平均回复率',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_tenant_user_date_type`(`tenant_id` ASC, `user_id` ASC, `summary_date` ASC, `summary_type` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_tenant_user`(`tenant_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_summary_date`(`summary_date` ASC) USING BTREE,
  INDEX `idx_summary_type`(`summary_type` ASC) USING BTREE,
  CONSTRAINT `alibaba_inquiry_summary_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `alibaba_inquiry_summary_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '阿里询盘统计汇总数据' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for alibaba_keyword_date_periods
-- ----------------------------
DROP TABLE IF EXISTS `alibaba_keyword_date_periods`;
CREATE TABLE `alibaba_keyword_date_periods`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户ID',
  `user_id` int NOT NULL COMMENT '关联的用户ID',
  `period_type` enum('week','month') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '周期类型',
  `start_date` date NOT NULL COMMENT '周期开始日期',
  `end_date` date NOT NULL COMMENT '周期结束日期',
  `is_available` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否可用',
  `is_current` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为当前周期',
  `last_updated` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_period`(`user_id` ASC, `period_type` ASC, `start_date` ASC, `end_date` ASC) USING BTREE,
  INDEX `idx_tenant_user`(`tenant_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_period_type`(`period_type` ASC) USING BTREE,
  INDEX `idx_date_range`(`start_date` ASC, `end_date` ASC) USING BTREE,
  INDEX `idx_is_available`(`is_available` ASC) USING BTREE,
  INDEX `idx_is_current`(`is_current` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '阿里巴巴关键词数据可用时间周期表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for alibaba_keyword_performance
-- ----------------------------
DROP TABLE IF EXISTS `alibaba_keyword_performance`;
CREATE TABLE `alibaba_keyword_performance`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户ID',
  `user_id` int NOT NULL COMMENT '关联的用户ID',
  `keyword` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关键词',
  `statistics_type` enum('week','month') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '统计周期类型',
  `start_date` date NOT NULL COMMENT '统计开始日期',
  `end_date` date NOT NULL COMMENT '统计结束日期',
  `sum_show_cnt` bigint NOT NULL DEFAULT 0 COMMENT '曝光量',
  `sum_click_cnt` bigint NOT NULL DEFAULT 0 COMMENT '点击量',
  `ctr` decimal(8, 4) NOT NULL DEFAULT 0.0000 COMMENT '点击率',
  `search_pv_index` bigint NOT NULL DEFAULT 0 COMMENT '搜索热度',
  `gs_tp_member_set_cnt` bigint NOT NULL DEFAULT 0 COMMENT '卖家竞争度',
  `avg_sum_show_cnt` bigint NOT NULL DEFAULT 0 COMMENT 'Top10平均曝光量',
  `avg_sum_click_cnt` bigint NOT NULL DEFAULT 0 COMMENT 'Top10平均点击量',
  `sum_p4p_show_cnt` bigint NOT NULL DEFAULT 0 COMMENT '直通车曝光量',
  `sum_p4p_click_cnt` bigint NOT NULL DEFAULT 0 COMMENT '直通车点击量',
  `is_p4p_kw` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为P4P推广关键词',
  `keywords_in_use` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已设置为关键词',
  `keywords_viewed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有曝光',
  `sync_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据同步时间',
  `data_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'alibaba_api' COMMENT '数据来源',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_keyword_period`(`keyword` ASC, `statistics_type` ASC, `start_date` ASC, `end_date` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_tenant_user`(`tenant_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_keyword`(`keyword` ASC) USING BTREE,
  INDEX `idx_statistics_type`(`statistics_type` ASC) USING BTREE,
  INDEX `idx_date_range`(`start_date` ASC, `end_date` ASC) USING BTREE,
  INDEX `idx_is_p4p`(`is_p4p_kw` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 214 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '阿里巴巴关键词表现数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for alibaba_keyword_summary
-- ----------------------------
DROP TABLE IF EXISTS `alibaba_keyword_summary`;
CREATE TABLE `alibaba_keyword_summary`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户ID',
  `user_id` int NOT NULL COMMENT '关联的用户ID',
  `summary_date` date NOT NULL COMMENT '汇总日期',
  `summary_type` enum('weekly','monthly','quarterly','yearly') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '汇总类型',
  `total_keywords` int NOT NULL DEFAULT 0 COMMENT '关键词总数',
  `active_keywords` int NOT NULL DEFAULT 0 COMMENT '有效关键词数',
  `p4p_keywords` int NOT NULL DEFAULT 0 COMMENT 'P4P推广关键词数',
  `total_show` bigint NOT NULL DEFAULT 0 COMMENT '总曝光量',
  `total_click` bigint NOT NULL DEFAULT 0 COMMENT '总点击量',
  `avg_ctr` decimal(8, 4) NOT NULL DEFAULT 0.0000 COMMENT '平均点击率',
  `total_search_pv_index` bigint NOT NULL DEFAULT 0 COMMENT '总搜索热度',
  `total_p4p_show` bigint NOT NULL DEFAULT 0 COMMENT 'P4P总曝光量',
  `total_p4p_click` bigint NOT NULL DEFAULT 0 COMMENT 'P4P总点击量',
  `p4p_ctr` decimal(8, 4) NOT NULL DEFAULT 0.0000 COMMENT 'P4P平均点击率',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_summary`(`user_id` ASC, `summary_date` ASC, `summary_type` ASC) USING BTREE,
  INDEX `idx_tenant_user`(`tenant_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_summary_date`(`summary_date` ASC) USING BTREE,
  INDEX `idx_summary_type`(`summary_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '阿里巴巴关键词统计汇总表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for alibaba_keyword_trends
-- ----------------------------
DROP TABLE IF EXISTS `alibaba_keyword_trends`;
CREATE TABLE `alibaba_keyword_trends`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户ID',
  `user_id` int NOT NULL COMMENT '关联的用户ID',
  `keyword` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关键词',
  `trend_date` date NOT NULL COMMENT '趋势日期',
  `daily_show` bigint NOT NULL DEFAULT 0 COMMENT '当日曝光量',
  `daily_click` bigint NOT NULL DEFAULT 0 COMMENT '当日点击量',
  `daily_ctr` decimal(8, 4) NOT NULL DEFAULT 0.0000 COMMENT '当日点击率',
  `search_heat_index` bigint NOT NULL DEFAULT 0 COMMENT '搜索热度指数',
  `show_trend` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '曝光趋势',
  `click_trend` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '点击趋势',
  `ctr_trend` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '点击率趋势',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_keyword_date`(`keyword` ASC, `trend_date` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_tenant_user`(`tenant_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_keyword`(`keyword` ASC) USING BTREE,
  INDEX `idx_trend_date`(`trend_date` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '阿里巴巴关键词趋势数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for alibaba_product_date_ranges
-- ----------------------------
DROP TABLE IF EXISTS `alibaba_product_date_ranges`;
CREATE TABLE `alibaba_product_date_ranges`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户ID',
  `user_id` int NOT NULL COMMENT '关联的用户ID',
  `statistics_type` enum('day','week','month') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '统计周期类型',
  `start_date` date NOT NULL COMMENT '可用数据开始日期',
  `end_date` date NOT NULL COMMENT '可用数据结束日期',
  `last_updated` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否有效',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_stat_type`(`user_id` ASC, `statistics_type` ASC) USING BTREE,
  INDEX `idx_tenant_user`(`tenant_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_statistics_type`(`statistics_type` ASC) USING BTREE,
  INDEX `idx_is_active`(`is_active` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '阿里巴巴产品数据可用时间范围表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for alibaba_product_keyword_effects
-- ----------------------------
DROP TABLE IF EXISTS `alibaba_product_keyword_effects`;
CREATE TABLE `alibaba_product_keyword_effects`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户ID',
  `user_id` int NOT NULL COMMENT '关联的用户ID',
  `product_performance_id` int NOT NULL COMMENT '关联的产品表现记录ID',
  `keyword` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关键词',
  `keyword_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关键词类型',
  `keyword_impression` bigint NOT NULL DEFAULT 0 COMMENT '关键词曝光量',
  `keyword_click` bigint NOT NULL DEFAULT 0 COMMENT '关键词点击量',
  `keyword_ctr` decimal(8, 4) NOT NULL DEFAULT 0.0000 COMMENT '关键词点击率',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_tenant_user`(`tenant_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_product_performance`(`product_performance_id` ASC) USING BTREE,
  INDEX `idx_keyword`(`keyword` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '阿里巴巴产品关键词效果表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for alibaba_product_performance
-- ----------------------------
DROP TABLE IF EXISTS `alibaba_product_performance`;
CREATE TABLE `alibaba_product_performance`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户ID',
  `user_id` int NOT NULL COMMENT '关联的用户ID',
  `product_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品ID',
  `product_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '产品名称',
  `statistics_type` enum('day','week','month') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '统计周期类型',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `impression` bigint NOT NULL DEFAULT 0 COMMENT '曝光量',
  `click` bigint NOT NULL DEFAULT 0 COMMENT '点击量',
  `visitor` bigint NOT NULL DEFAULT 0 COMMENT '访客数',
  `order_count` bigint NOT NULL DEFAULT 0 COMMENT '订单数',
  `bookmark` bigint NOT NULL DEFAULT 0 COMMENT '收藏数',
  `compare` bigint NOT NULL DEFAULT 0 COMMENT '对比数',
  `share` bigint NOT NULL DEFAULT 0 COMMENT '分享数',
  `feedback` bigint NOT NULL DEFAULT 0 COMMENT '反馈数',
  `ctr` decimal(8, 4) NOT NULL DEFAULT 0.0000 COMMENT '点击率',
  `conversion_rate` decimal(8, 4) NOT NULL DEFAULT 0.0000 COMMENT '转化率',
  `sync_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据同步时间',
  `data_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'alibaba_api' COMMENT '数据来源',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_product_stat`(`product_id` ASC, `statistics_type` ASC, `stat_date` ASC) USING BTREE,
  INDEX `idx_tenant_user`(`tenant_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_stat_date`(`stat_date` ASC) USING BTREE,
  INDEX `idx_statistics_type`(`statistics_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 163 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '阿里巴巴产品表现数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for alibaba_products
-- ----------------------------
DROP TABLE IF EXISTS `alibaba_products`;
CREATE TABLE `alibaba_products`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '租户ID',
  `user_id` int NOT NULL COMMENT '关联的用户ID',
  `product_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品ID',
  `product_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '产品名称',
  `product_title` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '产品标题',
  `product_category` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '产品分类',
  `product_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '产品状态',
  `product_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '产品链接',
  `main_image_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '主图链接',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '产品描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `sync_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后同步时间',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否有效',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_tenant_user`(`tenant_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_is_active`(`is_active` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 72 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '阿里巴巴产品基础信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for alibaba_top_industries
-- ----------------------------
DROP TABLE IF EXISTS `alibaba_top_industries`;
CREATE TABLE `alibaba_top_industries`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `user_id` int NOT NULL,
  `industry_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `industry_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `main_category` tinyint(1) NULL DEFAULT 0,
  `data_start_date` date NOT NULL,
  `data_end_date` date NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_tenant_user`(`tenant_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_industry_id`(`industry_id` ASC) USING BTREE,
  INDEX `idx_date_range`(`data_start_date` ASC, `data_end_date` ASC) USING BTREE,
  CONSTRAINT `alibaba_top_industries_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `alibaba_top_industries_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 76 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '阿里TOP行业列表数据' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for crawl_result
-- ----------------------------
DROP TABLE IF EXISTS `crawl_result`;
CREATE TABLE `crawl_result`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` int NOT NULL,
  `data` json NOT NULL,
  `crawled_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `task_id`(`task_id` ASC) USING BTREE,
  INDEX `tenant_id`(`tenant_id` ASC) USING BTREE,
  CONSTRAINT `crawl_result_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `crawl_task` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `crawl_result_ibfk_2` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for crawl_task
-- ----------------------------
DROP TABLE IF EXISTS `crawl_task`;
CREATE TABLE `crawl_task`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'pending',
  `schedule_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `schedule_config` json NULL,
  `last_run_at` timestamp NULL DEFAULT NULL,
  `created_by` int NOT NULL,
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `created_by`(`created_by` ASC) USING BTREE,
  INDEX `tenant_id`(`tenant_id` ASC) USING BTREE,
  CONSTRAINT `crawl_task_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `crawl_task_ibfk_2` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for customer
-- ----------------------------
DROP TABLE IF EXISTS `customer`;
CREATE TABLE `customer`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `country` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `source` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'lead',
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `tenant_id`(`tenant_id` ASC) USING BTREE,
  CONSTRAINT `customer_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for google_ads_config
-- ----------------------------
DROP TABLE IF EXISTS `google_ads_config`;
CREATE TABLE `google_ads_config`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置名称',
  `customer_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户ID',
  `developer_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '开发者令牌',
  `client_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端ID',
  `client_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端密钥',
  `refresh_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '刷新令牌',
  `login_customer_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '登录客户ID',
  `oauth_state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'OAuth状态参数',
  `authorization_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'pending' COMMENT '授权状态: pending, authorized, expired, failed',
  `last_auth_time` timestamp NULL DEFAULT NULL COMMENT '最后授权时间',
  `access_token` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '访问令牌（临时存储）',
  `token_expiry` timestamp NULL DEFAULT NULL COMMENT '令牌过期时间',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否激活',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_config_name`(`config_name` ASC) USING BTREE,
  INDEX `idx_customer_id`(`customer_id` ASC) USING BTREE,
  INDEX `idx_is_active`(`is_active` ASC) USING BTREE,
  INDEX `idx_authorization_status`(`authorization_status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'Google Ads API配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for inquiry
-- ----------------------------
DROP TABLE IF EXISTS `inquiry`;
CREATE TABLE `inquiry`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `customer_id` int NULL DEFAULT NULL,
  `product_id` int NULL DEFAULT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'new',
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `customer_id`(`customer_id` ASC) USING BTREE,
  INDEX `product_id`(`product_id` ASC) USING BTREE,
  INDEX `tenant_id`(`tenant_id` ASC) USING BTREE,
  CONSTRAINT `inquiry_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `inquiry_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `inquiry_ibfk_3` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for keyword_import_tasks
-- ----------------------------
DROP TABLE IF EXISTS `keyword_import_tasks`;
CREATE TABLE `keyword_import_tasks`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务ID',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件名',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  `total_count` int NULL DEFAULT 0 COMMENT '总记录数',
  `success_count` int NULL DEFAULT 0 COMMENT '成功数',
  `failed_count` int NULL DEFAULT 0 COMMENT '失败数',
  `status` enum('pending','processing','completed','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'pending' COMMENT '任务状态',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `result_file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '结果文件路径',
  `operator_id` int NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_task_id`(`task_id` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '关键词批量导入任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for keyword_library
-- ----------------------------
DROP TABLE IF EXISTS `keyword_library`;
CREATE TABLE `keyword_library`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `keyword_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关键词名称',
  `avg_monthly_searches` bigint NULL DEFAULT NULL COMMENT '平均每月搜索量（过去12个月）',
  `monthly_searches` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '大致每月搜索量（JSON格式存储每月数据）',
  `competition_level` enum('low','medium','high','unspecified') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'unspecified' COMMENT '竞争级别',
  `competition_index` decimal(5, 2) NULL DEFAULT NULL COMMENT '竞争指数（0-100）',
  `low_bid_micros` bigint NULL DEFAULT NULL COMMENT '出价第20百分位（微货币单位）',
  `high_bid_micros` bigint NULL DEFAULT NULL COMMENT '出价第80百分位（微货币单位）',
  `currency_code` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'CNY' COMMENT '货币代码',
  `language_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'zh-CN' COMMENT '语言代码',
  `location_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '地理位置ID列表（逗号分隔）',
  `update_method` enum('google_ads_api','manual','batch_import') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'manual' COMMENT '更新方式',
  `tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标签（逗号分隔）',
  `category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关键词分类/组名称',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_keyword_name`(`keyword_name` ASC) USING BTREE,
  INDEX `idx_keyword_name`(`keyword_name` ASC) USING BTREE,
  INDEX `idx_competition_level`(`competition_level` ASC) USING BTREE,
  INDEX `idx_avg_monthly_searches`(`avg_monthly_searches` ASC) USING BTREE,
  INDEX `idx_update_method`(`update_method` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  INDEX `idx_updated_at`(`updated_at` ASC) USING BTREE,
  INDEX `idx_category`(`category` ASC) USING BTREE,
  FULLTEXT INDEX `idx_keyword_tags_category_fulltext`(`keyword_name`, `tags`, `category`)
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '关键词库表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for keyword_library_backup
-- ----------------------------
DROP TABLE IF EXISTS `keyword_library_backup`;
CREATE TABLE `keyword_library_backup`  (
  `id` int NOT NULL DEFAULT 0 COMMENT '主键ID',
  `keyword_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关键词名称',
  `avg_monthly_searches` bigint NULL DEFAULT NULL COMMENT '平均每月搜索量（过去12个月）',
  `monthly_searches` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '大致每月搜索量（JSON格式存储每月数据）',
  `competition_level` enum('low','medium','high','unspecified') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'unspecified' COMMENT '竞争级别',
  `competition_index` decimal(5, 2) NULL DEFAULT NULL COMMENT '竞争指数（0-100）',
  `low_bid_micros` bigint NULL DEFAULT NULL COMMENT '出价第20百分位（微货币单位）',
  `high_bid_micros` bigint NULL DEFAULT NULL COMMENT '出价第80百分位（微货币单位）',
  `currency_code` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'CNY' COMMENT '货币代码',
  `language_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'zh-CN' COMMENT '语言代码',
  `location_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '地理位置ID列表（逗号分隔）',
  `update_method` enum('google_ads_api','manual','batch_import') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'manual' COMMENT '更新方式',
  `tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标签（逗号分隔）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for keyword_update_history
-- ----------------------------
DROP TABLE IF EXISTS `keyword_update_history`;
CREATE TABLE `keyword_update_history`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `keyword_id` int NOT NULL COMMENT '关键词ID',
  `update_method` enum('google_ads_api','manual','batch_import') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新方式',
  `old_data` json NULL COMMENT '更新前的数据（JSON格式）',
  `new_data` json NULL COMMENT '更新后的数据（JSON格式）',
  `batch_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '批量操作ID',
  `operator_id` int NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_keyword_id`(`keyword_id` ASC) USING BTREE,
  INDEX `idx_update_method`(`update_method` ASC) USING BTREE,
  INDEX `idx_batch_id`(`batch_id` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE,
  CONSTRAINT `keyword_update_history_ibfk_1` FOREIGN KEY (`keyword_id`) REFERENCES `keyword_library` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '关键词更新历史表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for keyword_update_history_backup
-- ----------------------------
DROP TABLE IF EXISTS `keyword_update_history_backup`;
CREATE TABLE `keyword_update_history_backup`  (
  `id` int NOT NULL DEFAULT 0 COMMENT '主键ID',
  `keyword_id` int NOT NULL COMMENT '关键词ID',
  `update_method` enum('google_ads_api','manual','batch_import') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新方式',
  `old_data` json NULL COMMENT '更新前的数据（JSON格式）',
  `new_data` json NULL COMMENT '更新后的数据（JSON格式）',
  `batch_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '批量操作ID',
  `operator_id` int NULL DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product
-- ----------------------------
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `price` float NULL DEFAULT NULL,
  `sku` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `images` json NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'active',
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `tenant_id`(`tenant_id` ASC) USING BTREE,
  CONSTRAINT `product_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `permissions` json NOT NULL,
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `tenant_id`(`tenant_id` ASC) USING BTREE,
  CONSTRAINT `role_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for seo_content
-- ----------------------------
DROP TABLE IF EXISTS `seo_content`;
CREATE TABLE `seo_content`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `host_id` int NULL DEFAULT NULL,
  `post_id` int NULL DEFAULT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'draft',
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `host_id`(`host_id` ASC) USING BTREE,
  INDEX `tenant_id`(`tenant_id` ASC) USING BTREE,
  CONSTRAINT `seo_content_ibfk_1` FOREIGN KEY (`host_id`) REFERENCES `wordpress_host` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `seo_content_ibfk_2` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for subscription
-- ----------------------------
DROP TABLE IF EXISTS `subscription`;
CREATE TABLE `subscription`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `price` float NOT NULL DEFAULT 0,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `features` json NOT NULL,
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `tenant_id`(`tenant_id` ASC) USING BTREE,
  CONSTRAINT `subscription_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tenant
-- ----------------------------
DROP TABLE IF EXISTS `tenant`;
CREATE TABLE `tenant`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `is_active` tinyint(1) NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tenant_subscription
-- ----------------------------
DROP TABLE IF EXISTS `tenant_subscription`;
CREATE TABLE `tenant_subscription`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `subscription_id` int NOT NULL,
  `is_active` tinyint(1) NULL DEFAULT 1,
  `start_date` timestamp NOT NULL,
  `end_date` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `tenant_id`(`tenant_id` ASC) USING BTREE,
  INDEX `subscription_id`(`subscription_id` ASC) USING BTREE,
  CONSTRAINT `tenant_subscription_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `tenant_subscription_ibfk_2` FOREIGN KEY (`subscription_id`) REFERENCES `subscription` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `hashed_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `full_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `is_active` tinyint(1) NULL DEFAULT 1,
  `is_superuser` tinyint(1) NULL DEFAULT 0,
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `role_id` int NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `email`(`email` ASC) USING BTREE,
  INDEX `tenant_id`(`tenant_id` ASC) USING BTREE,
  INDEX `role_id`(`role_id` ASC) USING BTREE,
  CONSTRAINT `user_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `user_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wordpress_host
-- ----------------------------
DROP TABLE IF EXISTS `wordpress_host`;
CREATE TABLE `wordpress_host`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `api_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'active',
  `tenant_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `tenant_id`(`tenant_id` ASC) USING BTREE,
  CONSTRAINT `wordpress_host_ibfk_1` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wordpress_sites
-- ----------------------------
DROP TABLE IF EXISTS `wordpress_sites`;
CREATE TABLE `wordpress_sites`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '站点名称',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '站点URL',
  `category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '站点分类',
  `wp_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'WordPress用户名',
  `wp_app_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'WordPress应用程序密码',
  `blog_categories` json NULL COMMENT 'WordPress博客分类信息(JSON格式)',
  `daily_uv` bigint NULL DEFAULT 0 COMMENT '当日UV(独立访客)',
  `daily_pv` bigint NULL DEFAULT 0 COMMENT '当日PV(页面浏览量)',
  `monthly_uv` bigint NULL DEFAULT 0 COMMENT '当月UV',
  `monthly_pv` bigint NULL DEFAULT 0 COMMENT '当月PV',
  `total_posts` int NULL DEFAULT 0 COMMENT '总文章数',
  `total_pages` int NULL DEFAULT 0 COMMENT '总页面数',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `last_sync_at` timestamp NULL DEFAULT NULL COMMENT '最后同步时间',
  `sync_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'pending' COMMENT '同步状态',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '站点描述',
  `wordpress_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'WordPress版本',
  `theme_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '主题名称',
  `language` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '站点语言',
  `timezone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '时区',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_url`(`url` ASC) USING BTREE,
  INDEX `idx_name`(`name` ASC) USING BTREE,
  INDEX `idx_category`(`category` ASC) USING BTREE,
  INDEX `idx_is_active`(`is_active` ASC) USING BTREE,
  INDEX `idx_sync_status`(`sync_status` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'WordPress站点表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- View structure for v_active_ai_configs
-- ----------------------------
DROP VIEW IF EXISTS `v_active_ai_configs`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_active_ai_configs` AS select `ai_configs`.`id` AS `id`,`ai_configs`.`name` AS `name`,`ai_configs`.`service_type` AS `service_type`,`ai_configs`.`api_key` AS `api_key`,`ai_configs`.`base_url` AS `base_url`,`ai_configs`.`model_name` AS `model_name`,`ai_configs`.`max_tokens` AS `max_tokens`,`ai_configs`.`temperature` AS `temperature`,`ai_configs`.`description` AS `description`,`ai_configs`.`created_at` AS `created_at`,`ai_configs`.`updated_at` AS `updated_at` from `ai_configs` where (`ai_configs`.`is_active` = 1) order by `ai_configs`.`created_at` desc;

-- ----------------------------
-- View structure for v_ai_article_stats
-- ----------------------------
DROP VIEW IF EXISTS `v_ai_article_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_ai_article_stats` AS select `ai_articles`.`status` AS `status`,count(0) AS `count`,cast(`ai_articles`.`created_at` as date) AS `date` from `ai_articles` group by `ai_articles`.`status`,cast(`ai_articles`.`created_at` as date) order by `date` desc,`ai_articles`.`status`;

-- ----------------------------
-- View structure for v_ai_config_usage
-- ----------------------------
DROP VIEW IF EXISTS `v_ai_config_usage`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_ai_config_usage` AS select `ac`.`id` AS `id`,`ac`.`name` AS `name`,`ac`.`service_type` AS `service_type`,count(`aa`.`id`) AS `usage_count`,count((case when (`aa`.`status` = 'published') then 1 end)) AS `success_count`,count((case when (`aa`.`status` = 'failed') then 1 end)) AS `failed_count` from (`ai_configs` `ac` left join `ai_articles` `aa` on((`ac`.`id` = `aa`.`ai_config_id`))) where (`ac`.`is_active` = 1) group by `ac`.`id`,`ac`.`name`,`ac`.`service_type` order by `usage_count` desc;

-- ----------------------------
-- View structure for v_keyword_stats
-- ----------------------------
DROP VIEW IF EXISTS `v_keyword_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_keyword_stats` AS select `keyword_library`.`update_method` AS `update_method`,count(0) AS `total_count`,avg(`keyword_library`.`avg_monthly_searches`) AS `avg_searches`,min(`keyword_library`.`avg_monthly_searches`) AS `min_searches`,max(`keyword_library`.`avg_monthly_searches`) AS `max_searches`,count((case when (`keyword_library`.`competition_level` = 'LOW') then 1 end)) AS `low_competition_count`,count((case when (`keyword_library`.`competition_level` = 'MEDIUM') then 1 end)) AS `medium_competition_count`,count((case when (`keyword_library`.`competition_level` = 'HIGH') then 1 end)) AS `high_competition_count` from `keyword_library` group by `keyword_library`.`update_method`;

-- ----------------------------
-- View structure for v_popular_keywords
-- ----------------------------
DROP VIEW IF EXISTS `v_popular_keywords`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_popular_keywords` AS select `keyword_library`.`id` AS `id`,`keyword_library`.`keyword_name` AS `keyword_name`,`keyword_library`.`avg_monthly_searches` AS `avg_monthly_searches`,`keyword_library`.`competition_level` AS `competition_level`,`keyword_library`.`competition_index` AS `competition_index`,`keyword_library`.`tags` AS `tags`,`keyword_library`.`updated_at` AS `updated_at` from `keyword_library` where (`keyword_library`.`avg_monthly_searches` is not null) order by `keyword_library`.`avg_monthly_searches` desc limit 100;

-- ----------------------------
-- View structure for v_wordpress_sites_summary
-- ----------------------------
DROP VIEW IF EXISTS `v_wordpress_sites_summary`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_wordpress_sites_summary` AS select count(0) AS `total_sites`,count((case when (`wordpress_sites`.`is_active` = 1) then 1 end)) AS `active_sites`,count((case when (`wordpress_sites`.`sync_status` = 'success') then 1 end)) AS `synced_sites`,count((case when (`wordpress_sites`.`sync_status` = 'failed') then 1 end)) AS `failed_sites`,sum(`wordpress_sites`.`total_posts`) AS `total_posts_count`,sum(`wordpress_sites`.`total_pages`) AS `total_pages_count`,sum(`wordpress_sites`.`daily_uv`) AS `total_daily_uv`,sum(`wordpress_sites`.`daily_pv`) AS `total_daily_pv`,count(distinct `wordpress_sites`.`category`) AS `categories_count` from `wordpress_sites`;

SET FOREIGN_KEY_CHECKS = 1;
