#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AICBEC项目环境监测和依赖安装脚本
功能：
1. 检查系统环境（Python、Node.js、MySQL等）
2. 检查项目依赖是否已安装
3. 自动安装缺失的依赖
4. 生成环境报告
5. 错误诊断和建议
"""

import sys
import os
import platform
import subprocess
import json
import time
import shutil
from pathlib import Path
from datetime import datetime
import urllib.request
import ssl

class ColorText:
    """彩色文本输出类"""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    
    @staticmethod
    def colorize(text, color):
        return f"{color}{text}{ColorText.ENDC}"

class EnvironmentMonitor:
    """环境监测类"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.backend_path = self.project_root / "backend"
        # 修改前端路径为项目根目录的上级目录
        self.frontend_path = self.project_root.parent
        self.logs_path = self.project_root / "logs"
        self.logs_path.mkdir(exist_ok=True)
        
        self.log_file = self.logs_path / f"env_monitor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "system_info": {},
            "python_env": {},
            "node_env": {},
            "dependencies": {},
            "issues": [],
            "recommendations": []
        }
    
    def log(self, message, level="INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] [{level}] {message}"
        print(log_message)
        
        with open(self.log_file, "a", encoding="utf-8") as f:
            f.write(log_message + "\n")
    
    def run_command(self, command, capture_output=True, timeout=30):
        """执行命令并返回结果"""
        try:
            if isinstance(command, str):
                command = command.split()
            
            result = subprocess.run(
                command, 
                capture_output=capture_output, 
                text=True, 
                timeout=timeout,
                encoding='utf-8',
                errors='ignore'  # 忽略编码错误
            )
            return result
        except subprocess.TimeoutExpired:
            self.log(f"命令执行超时: {' '.join(command)}", "ERROR")
            return None
        except Exception as e:
            self.log(f"命令执行失败: {' '.join(command)}, 错误: {str(e)}", "ERROR")
            return None
    
    def check_system_info(self):
        """检查系统信息"""
        self.log("=" * 60)
        self.log("开始系统环境检查")
        self.log("=" * 60)
        
        system_info = {
            "platform": platform.platform(),
            "system": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
            "python_implementation": platform.python_implementation()
        }
        
        self.results["system_info"] = system_info
        
        self.log(f"操作系统: {system_info['platform']}")
        self.log(f"Python版本: {system_info['python_version']}")
        self.log(f"处理器架构: {system_info['machine']}")
        
        return system_info
    
    def check_python_environment(self):
        """检查Python环境"""
        self.log("\n" + "=" * 40)
        self.log("检查Python环境")
        self.log("=" * 40)
        
        python_env = {
            "version": sys.version_info,
            "executable": sys.executable,
            "path": sys.path,
            "pip_available": False,
            "venv_active": False,
            "venv_path": None,
            "venv_python": None,  # 新增：虚拟环境中的Python路径
            "packages": {}
        }
        
        # 检查Python版本
        if sys.version_info >= (3, 8):
            self.log(ColorText.colorize(f"✓ Python版本 {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} 符合要求", ColorText.OKGREEN))
        else:
            self.log(ColorText.colorize(f"✗ Python版本过低: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}, 需要3.8+", ColorText.FAIL))
            self.results["issues"].append("Python版本过低，需要3.8或更高版本")
        
        # 检查pip
        pip_result = self.run_command([sys.executable, "-m", "pip", "--version"])
        if pip_result and pip_result.returncode == 0:
            python_env["pip_available"] = True
            self.log(ColorText.colorize("✓ pip可用", ColorText.OKGREEN))
        else:
            self.log(ColorText.colorize("✗ pip不可用", ColorText.FAIL))
            self.results["issues"].append("pip不可用")
        
        # 检查虚拟环境
        venv_active = False
        venv_path = None
        venv_python = None
        
        # 方法1: 检查是否在虚拟环境中运行
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            venv_active = True
            venv_path = sys.prefix
            venv_python = sys.executable
            self.log(ColorText.colorize(f"✓ 虚拟环境已激活: {venv_path}", ColorText.OKGREEN))
        else:
            # 方法2: 检查项目目录中是否存在虚拟环境
            possible_venv_paths = [
                self.project_root / "venv",  # 当前项目目录
                self.project_root / "env",
                self.project_root.parent / "venv",  # 上级目录 (D:\LP01\AI\AICBEC\venv)
                self.project_root.parent / "env"
            ]
            
            for venv_dir in possible_venv_paths:
                if venv_dir.exists():
                    venv_path = str(venv_dir)
                    # 检查虚拟环境中的Python可执行文件
                    potential_python = venv_dir / "Scripts" / "python.exe"
                    if potential_python.exists():
                        venv_python = str(potential_python)
                        self.log(ColorText.colorize(f"✓ 发现虚拟环境: {venv_path}", ColorText.OKGREEN))
                        self.log(ColorText.colorize(f"  虚拟环境Python: {venv_python}", ColorText.OKBLUE))
                        # 测试虚拟环境Python是否可用
                        test_result = self.run_command([venv_python, "--version"])
                        if test_result and test_result.returncode == 0:
                            self.log(ColorText.colorize(f"  ✓ 虚拟环境Python可用: {test_result.stdout.strip()}", ColorText.OKGREEN))
                        else:
                            self.log(ColorText.colorize(f"  ⚠ 虚拟环境Python不可用", ColorText.WARNING))
                            venv_python = None
                    else:
                        self.log(ColorText.colorize(f"⚠ 发现虚拟环境但Python不存在: {venv_path}", ColorText.WARNING))
                    break
            
            if not venv_path:
                self.log(ColorText.colorize("⚠ 未检测到虚拟环境", ColorText.WARNING))
                self.results["recommendations"].append("建议创建并使用虚拟环境")
            elif venv_python and not venv_active:
                self.results["recommendations"].append(f"建议激活虚拟环境: {venv_path}")
        
        python_env["venv_active"] = venv_active
        python_env["venv_path"] = venv_path
        python_env["venv_python"] = venv_python
        
        self.results["python_env"] = python_env
        return python_env
    
    def check_node_environment(self):
        """检查Node.js环境"""
        self.log("\n" + "=" * 40)
        self.log("检查Node.js环境")
        self.log("=" * 40)
        
        node_env = {
            "node_available": False,
            "npm_available": False,
            "node_version": None,
            "npm_version": None,
            "npm_path": None
        }
        
        # 检查Node.js
        node_result = self.run_command(["node", "--version"])
        if node_result and node_result.returncode == 0:
            node_env["node_available"] = True
            node_env["node_version"] = node_result.stdout.strip()
            self.log(ColorText.colorize(f"✓ Node.js {node_env['node_version']} 可用", ColorText.OKGREEN))
        else:
            self.log(ColorText.colorize("✗ Node.js未安装", ColorText.FAIL))
            self.results["issues"].append("Node.js未安装")
        
        # 检查npm - 多种方式尝试
        npm_commands_to_try = [
            ["npm", "--version"],
            ["npm.cmd", "--version"],
            ["npx.cmd", "npm", "--version"]
        ]
        
        # 在Windows上添加完整路径尝试
        if platform.system() == "Windows":
            possible_npm_paths = [
                "C:\\Program Files\\nodejs\\npm.cmd",
                "C:\\Program Files (x86)\\nodejs\\npm.cmd",
                "C:\\nodejs\\npm.cmd"
            ]
            for npm_path in possible_npm_paths:
                if Path(npm_path).exists():
                    npm_commands_to_try.insert(0, [npm_path, "--version"])
                    break
        
        npm_version = None
        npm_path = None
        
        for cmd in npm_commands_to_try:
            try:
                result = self.run_command(cmd)
                if result and result.returncode == 0:
                    npm_version = result.stdout.strip()
                    npm_path = cmd[0]
                    break
            except Exception as e:
                continue
        
        if npm_version:
            node_env["npm_available"] = True
            node_env["npm_version"] = npm_version
            node_env["npm_path"] = npm_path
            self.log(ColorText.colorize(f"✓ npm {npm_version} 可用 (路径: {npm_path})", ColorText.OKGREEN))
        else:
            self.log(ColorText.colorize("✗ npm不可用", ColorText.FAIL))
            self.results["issues"].append("npm不可用")
            
            # 添加诊断信息
            if node_env["node_available"]:
                self.log(ColorText.colorize("ℹ Node.js可用但npm不可用，可能是PATH配置问题", ColorText.OKCYAN))
                self.results["recommendations"].append("检查npm的PATH环境变量配置")
        
        self.results["node_env"] = node_env
        return node_env
    
    def check_database_connectivity(self):
        """检查数据库连接"""
        self.log("\n" + "=" * 40)
        self.log("检查数据库连接")
        self.log("=" * 40)
        
        try:
            import pymysql
            self.log(ColorText.colorize("✓ PyMySQL模块可用", ColorText.OKGREEN))
            
            # 这里可以添加实际的数据库连接测试
            # 但需要从配置文件读取数据库信息
            self.log(ColorText.colorize("ℹ 数据库连接测试需要配置信息", ColorText.OKCYAN))
            
        except ImportError:
            self.log(ColorText.colorize("✗ PyMySQL模块未安装", ColorText.FAIL))
            self.results["issues"].append("PyMySQL模块未安装")
    
    def check_backend_dependencies(self):
        """检查后端Python依赖"""
        self.log("\n" + "=" * 40)
        self.log("检查后端Python依赖")
        self.log("=" * 40)
        
        # 获取Python环境信息
        python_env = self.results.get("python_env", {})
        
        # 确定使用哪个Python环境
        python_executable = None
        if python_env.get("venv_python"):
            # 优先使用虚拟环境的Python
            python_executable = python_env["venv_python"]
            self.log(ColorText.colorize(f"使用虚拟环境Python: {python_executable}", ColorText.OKBLUE))
        elif python_env.get("venv_active"):
            # 如果当前在虚拟环境中
            python_executable = sys.executable
            self.log(ColorText.colorize(f"当前虚拟环境已激活: {python_executable}", ColorText.OKBLUE))
        else:
            # 使用系统Python
            python_executable = sys.executable
            self.log(ColorText.colorize(f"使用系统Python: {python_executable}", ColorText.WARNING))
            self.results["recommendations"].append("建议在虚拟环境中安装依赖")
        
        backend_deps = {
            "python_executable": python_executable,
            "requirements_file": None,
            "total_packages": 0,
            "installed_packages": 0,
            "missing_packages": [],
            "installed_list": []
        }
        
        # 检查requirements.txt文件
        requirements_file = self.backend_path / "requirements.txt"
        if not requirements_file.exists():
            self.log(ColorText.colorize("✗ requirements.txt文件不存在", ColorText.FAIL))
            self.results["issues"].append("后端requirements.txt文件缺失")
            self.results["backend_deps"] = backend_deps
            return backend_deps
        
        backend_deps["requirements_file"] = str(requirements_file)
        
        # 读取requirements.txt
        try:
            with open(requirements_file, 'r', encoding='utf-8') as f:
                requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
            
            backend_deps["total_packages"] = len(requirements)
            self.log(f"requirements.txt中共有 {len(requirements)} 个依赖包")
            
            # 检查每个包
            for requirement in requirements:
                # 处理包名（去除版本号）
                package_name = requirement.split('==')[0].split('>=')[0].split('<=')[0].split('~=')[0].split('>')[0].split('<')[0].strip()
                
                # 使用指定的Python检查包
                result = self.run_command([python_executable, "-m", "pip", "show", package_name])
                if result and result.returncode == 0 and result.stdout:
                    backend_deps["installed_packages"] += 1
                    backend_deps["installed_list"].append(package_name)
                    
                    # 提取版本信息
                    version = "unknown"
                    try:
                        for line in result.stdout.split('\n'):
                            if line.startswith('Version:'):
                                version = line.split(':', 1)[1].strip()
                                break
                    except Exception:
                        version = "unknown"
                    
                    self.log(ColorText.colorize(f"  ✓ {package_name} ({version})", ColorText.OKGREEN))
                else:
                    backend_deps["missing_packages"].append(requirement)
                    self.log(ColorText.colorize(f"  ✗ {package_name}", ColorText.FAIL))
            
            # 总结
            if backend_deps["missing_packages"]:
                self.log(ColorText.colorize(f"\n⚠ 后端依赖检查完成: {backend_deps['installed_packages']}/{backend_deps['total_packages']} 已安装", ColorText.WARNING))
                self.log(ColorText.colorize(f"缺失的包: {', '.join([pkg.split('==')[0] for pkg in backend_deps['missing_packages']])}", ColorText.FAIL))
                self.results["issues"].append(f"缺失 {len(backend_deps['missing_packages'])} 个后端依赖包")
            else:
                self.log(ColorText.colorize(f"\n✓ 后端依赖检查完成: 所有 {backend_deps['total_packages']} 个包均已安装", ColorText.OKGREEN))
                
        except Exception as e:
            self.log(ColorText.colorize(f"✗ 读取requirements.txt失败: {e}", ColorText.FAIL))
            self.results["issues"].append("无法读取requirements.txt文件")
        
        self.results["backend_deps"] = backend_deps
        return backend_deps
    
    def check_frontend_dependencies(self):
        """检查前端依赖"""
        self.log("\n" + "=" * 40)
        self.log("检查前端Node.js依赖")
        self.log("=" * 40)
        
        package_json = self.frontend_path / "package.json"
        if not package_json.exists():
            self.log(ColorText.colorize("✗ package.json文件不存在", ColorText.FAIL))
            self.results["issues"].append("前端package.json文件缺失")
            return
        
        node_modules = self.frontend_path / "node_modules"
        if not node_modules.exists():
            self.log(ColorText.colorize("✗ node_modules目录不存在", ColorText.WARNING))
            self.results["issues"].append("前端依赖未安装")
            return ["需要运行 npm install"]
        
        # 读取package.json
        with open(package_json, 'r', encoding='utf-8') as f:
            package_data = json.load(f)
        
        dependencies = package_data.get('dependencies', {})
        dev_dependencies = package_data.get('devDependencies', {})
        all_deps = {**dependencies, **dev_dependencies}
        
        self.log(f"检查 {len(all_deps)} 个前端依赖包...")
        
        missing_packages = []
        installed_packages = {}
        
        for package_name in all_deps:
            package_path = node_modules / package_name
            if package_path.exists():
                # 尝试读取版本信息
                try:
                    pkg_json = package_path / "package.json"
                    if pkg_json.exists():
                        with open(pkg_json, 'r', encoding='utf-8') as f:
                            pkg_data = json.load(f)
                            version = pkg_data.get('version', 'unknown')
                            installed_packages[package_name] = version
                            self.log(f"✓ {package_name} ({version})")
                except:
                    installed_packages[package_name] = 'unknown'
                    self.log(f"✓ {package_name} (版本未知)")
            else:
                missing_packages.append(package_name)
                self.log(ColorText.colorize(f"✗ {package_name} 未安装", ColorText.FAIL))
        
        self.results["dependencies"]["frontend"] = {
            "installed": installed_packages,
            "missing": missing_packages,
            "total_required": len(all_deps)
        }
        
        if missing_packages:
            self.results["issues"].append(f"缺失 {len(missing_packages)} 个前端依赖包")
        
        return missing_packages
    
    def install_backend_dependencies(self):
        """安装后端依赖"""
        self.log("\n" + "=" * 40)
        self.log("安装后端依赖")
        self.log("=" * 40)
        
        # 获取后端依赖信息
        backend_deps = self.results.get("backend_deps", {})
        if not backend_deps.get("missing_packages"):
            self.log(ColorText.colorize("✓ 所有后端依赖都已安装", ColorText.OKGREEN))
            return True
        
        # 获取Python可执行文件路径
        python_executable = backend_deps.get("python_executable")
        if not python_executable:
            self.log(ColorText.colorize("✗ 未找到Python可执行文件", ColorText.FAIL))
            return False
        
        requirements_file = backend_deps.get("requirements_file")
        if not requirements_file:
            self.log(ColorText.colorize("✗ requirements.txt文件不存在", ColorText.FAIL))
            return False
        
        self.log(f"使用Python: {python_executable}")
        self.log(f"安装来源: {requirements_file}")
        self.log(f"待安装包数量: {len(backend_deps['missing_packages'])}")
        
        try:
            # 首先升级pip
            self.log(ColorText.colorize("正在升级pip...", ColorText.OKBLUE))
            pip_upgrade_result = self.run_command([
                python_executable, "-m", "pip", "install", "--upgrade", "pip"
            ], timeout=120)
            
            if pip_upgrade_result and pip_upgrade_result.returncode == 0:
                self.log(ColorText.colorize("✓ pip升级成功", ColorText.OKGREEN))
            else:
                self.log(ColorText.colorize("⚠ pip升级失败，继续安装依赖", ColorText.WARNING))
            
            # 使用requirements.txt安装所有依赖
            self.log(ColorText.colorize("正在安装后端依赖...", ColorText.OKBLUE))
            install_result = self.run_command([
                python_executable, "-m", "pip", "install", "-r", requirements_file
            ], timeout=300)
            
            if install_result and install_result.returncode == 0:
                self.log(ColorText.colorize("✓ 后端依赖安装成功", ColorText.OKGREEN))
                
                # 重新检查安装结果
                self.log(ColorText.colorize("验证安装结果...", ColorText.OKBLUE))
                success_count = 0
                for requirement in backend_deps['missing_packages']:
                    package_name = requirement.split('==')[0].split('>=')[0].split('<=')[0].split('~=')[0].split('>')[0].split('<')[0].strip()
                    
                    verify_result = self.run_command([python_executable, "-m", "pip", "show", package_name])
                    if verify_result and verify_result.returncode == 0:
                        success_count += 1
                        self.log(ColorText.colorize(f"  ✓ {package_name} 验证成功", ColorText.OKGREEN))
                    else:
                        self.log(ColorText.colorize(f"  ✗ {package_name} 验证失败", ColorText.FAIL))
                
                self.log(ColorText.colorize(f"✓ 成功安装 {success_count}/{len(backend_deps['missing_packages'])} 个包", ColorText.OKGREEN))
                return success_count == len(backend_deps['missing_packages'])
                
            else:
                self.log(ColorText.colorize("✗ 后端依赖安装失败", ColorText.FAIL))
                if install_result and install_result.stderr:
                    self.log(ColorText.colorize(f"错误信息: {install_result.stderr}", ColorText.FAIL))
                return False
                
        except Exception as e:
            self.log(ColorText.colorize(f"✗ 安装过程出现异常: {e}", ColorText.FAIL))
            return False
    
    def install_frontend_dependencies(self):
        """安装前端依赖"""
        self.log("\n" + "=" * 40)
        self.log("安装前端依赖")
        self.log("=" * 40)
        
        if not (self.frontend_path / "package.json").exists():
            self.log(ColorText.colorize("✗ package.json不存在，无法安装依赖", ColorText.FAIL))
            return False
        
        # 获取npm命令
        node_env = self.results.get("node_env", {})
        npm_path = node_env.get("npm_path")
        
        if not npm_path:
            # 如果还没有检测过，先检测npm
            self.check_node_environment()
            node_env = self.results.get("node_env", {})
            npm_path = node_env.get("npm_path")
        
        if not npm_path or not node_env.get("npm_available"):
            self.log(ColorText.colorize("✗ npm不可用，无法安装前端依赖", ColorText.FAIL))
            return False
        
        # 确定npm命令，添加 --legacy-peer-deps 参数
        npm_cmd = [npm_path, "install", "--legacy-peer-deps"]
        
        # 切换到前端目录并运行npm install
        self.log(f"使用npm路径: {npm_path}")
        self.log("运行 npm install...")
        
        # 保存当前目录
        original_cwd = os.getcwd()
        try:
            # 切换到前端目录
            os.chdir(self.frontend_path)
            
            result = self.run_command(
                npm_cmd, 
                timeout=600  # 10分钟超时
            )
            
            if result and result.returncode == 0:
                self.log(ColorText.colorize("✓ 前端依赖安装成功", ColorText.OKGREEN))
                return True
            else:
                self.log(ColorText.colorize("✗ 前端依赖安装失败", ColorText.FAIL))
                if result:
                    self.log(f"错误信息: {result.stderr}")
                return False
        finally:
            # 恢复原始目录
            os.chdir(original_cwd)
    
    def generate_report(self):
        """生成环境报告"""
        self.log("\n" + "=" * 60)
        self.log("环境检查报告")
        self.log("=" * 60)
        
        # 基本信息
        self.log(f"检查时间: {self.results['timestamp']}")
        self.log(f"Python版本: {self.results['python_env'].get('version', 'Unknown')}")
        self.log(f"操作系统: {self.results['system_info'].get('platform', 'Unknown')}")
        
        # 依赖情况
        backend_deps = self.results["dependencies"].get("backend", {})
        frontend_deps = self.results["dependencies"].get("frontend", {})
        
        if backend_deps:
            total_backend = backend_deps.get("total_required", 0)
            installed_backend = len(backend_deps.get("installed", {}))
            missing_backend = len(backend_deps.get("missing", []))
            self.log(f"后端依赖: {installed_backend}/{total_backend} 已安装, {missing_backend} 缺失")
        
        if frontend_deps:
            total_frontend = frontend_deps.get("total_required", 0)
            installed_frontend = len(frontend_deps.get("installed", {}))
            missing_frontend = len(frontend_deps.get("missing", []))
            self.log(f"前端依赖: {installed_frontend}/{total_frontend} 已安装, {missing_frontend} 缺失")
        
        # 问题和建议
        if self.results["issues"]:
            self.log(f"\n发现 {len(self.results['issues'])} 个问题:")
            for i, issue in enumerate(self.results["issues"], 1):
                self.log(f"  {i}. {issue}")
        
        if self.results["recommendations"]:
            self.log(f"\n建议 ({len(self.results['recommendations'])} 项):")
            for i, rec in enumerate(self.results["recommendations"], 1):
                self.log(f"  {i}. {rec}")
        
        # 保存详细报告到JSON文件
        report_file = self.logs_path / f"env_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2, default=str)
        
        self.log(f"\n详细报告已保存到: {report_file}")
        
        return self.results
    
    def run_full_check(self, install_missing=True):
        """运行完整的环境检查"""
        self.log("开始完整环境检查...")
        
        # 系统信息检查
        self.check_system_info()
        
        # Python环境检查
        self.check_python_environment()
        
        # Node.js环境检查
        self.check_node_environment()
        
        # 数据库连接检查
        self.check_database_connectivity()
        
        # 检查后端依赖
        self.log("\n" + "🔍 检查后端依赖...")
        backend_deps = self.check_backend_dependencies()
        
        # 检查前端依赖
        self.log("\n" + "🔍 检查前端依赖...")
        missing_frontend = self.check_frontend_dependencies()
        
        # 安装缺失的依赖（如果不是仅检查模式）
        if install_missing:
            if backend_deps.get("missing_packages"):
                self.install_backend_dependencies()
            
            if missing_frontend:
                self.install_frontend_dependencies()
        
        # 生成报告
        self.generate_report()
        
        return self.results

def main():
    """主函数"""
    print(ColorText.colorize("=" * 80, ColorText.HEADER))
    print(ColorText.colorize("AICBEC项目环境监测和依赖安装脚本", ColorText.HEADER))
    print(ColorText.colorize("=" * 80, ColorText.HEADER))
    
    # 检查是否在项目根目录
    script_path = Path(__file__).parent.parent
    frontend_path = script_path.parent
    
    if not (script_path / "backend").exists():
        print(ColorText.colorize("✗ 未找到backend目录，请确保在正确的项目目录运行此脚本", ColorText.FAIL))
        sys.exit(1)
        
    if not (frontend_path / "package.json").exists():
        print(ColorText.colorize("✗ 未找到前端package.json文件，请确保前端依赖在正确位置", ColorText.FAIL))
        sys.exit(1)
    
    monitor = EnvironmentMonitor()
    
    # 解析命令行参数
    install_missing = True
    if len(sys.argv) > 1 and sys.argv[1] == "--check-only":
        install_missing = False
        print("仅检查模式，不会安装缺失的依赖")
    
    try:
        results = monitor.run_full_check(install_missing)
        
        # 判断整体状态
        if results["issues"]:
            print(ColorText.colorize(f"\n⚠ 发现 {len(results['issues'])} 个问题需要处理", ColorText.WARNING))
            sys.exit(1)
        else:
            print(ColorText.colorize("\n✓ 环境检查完成，一切正常！", ColorText.OKGREEN))
            sys.exit(0)
            
    except KeyboardInterrupt:
        print(ColorText.colorize("\n用户中断操作", ColorText.WARNING))
        sys.exit(1)
    except Exception as e:
        print(ColorText.colorize(f"\n脚本执行出错: {str(e)}", ColorText.FAIL))
        sys.exit(1)

if __name__ == "__main__":
    main() 