# CBEC - Cross-Border E-Commerce Intelligence Management System

## 📋 Project Overview

CBEC (Cross-Border E-Commerce) is a modern cross-border e-commerce management system based on Vue.js + FastAPI, focusing on providing comprehensive intelligent management solutions for cross-border e-commerce enterprises.

### 🎯 Core Features

- **Product Management**: Multi-platform product information management, supporting Amazon, eBay, Shopify, etc.
- **Inventory Management**: Real-time inventory monitoring, automatic restock alerts
- **Order Processing**: Automated order processing, multi-channel order integration
- **Keyword Research**: Integrated with Google Ads API, providing professional keyword analysis
- **Data Analytics**: Sales data analysis, market trend prediction
- **Multi-language Support**: Supporting Chinese, English, and other languages

## 🏗️ System Architecture

### Technology Stack

**Frontend**
- Vue.js 3 + Composition API
- Element Plus UI Component Library
- TypeScript
- Vite Build Tool
- Axios HTTP Client

**Backend**
- FastAPI (Python 3.9+)
- SQLAlchemy ORM
- PostgreSQL Database
- Redis Cache
- Google Ads API v19 Integration

**Deployment**
- Docker Containerization
- Nginx Reverse Proxy
- Kubernetes Cluster (Optional)

### Architecture Diagram

```
User Interface Layer (Vue.js)
      ↓
API Gateway Layer (Nginx)
      ↓
Business Logic Layer (FastAPI)
      ↓
Data Access Layer (SQLAlchemy + Redis)
      ↓
Data Storage Layer (PostgreSQL + MinIO)
```

## 🚀 Quick Start

### System Requirements

- Node.js 16+
- Python 3.9+
- PostgreSQL 13+
- Redis 6+
- Docker (Optional)

### Installation Steps

1. **Clone Repository**
```bash
git clone https://github.com/your-username/cbec.git
cd cbec
```

2. **Frontend Setup**
```bash
cd frontend
npm install
npm run dev
```

3. **Backend Setup**
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload
```

4. **Database Initialization**
```bash
# In backend directory
python -m alembic upgrade head
```

### Docker Deployment

```bash
# In project root directory
docker-compose up -d
```

## 🔑 Google Ads Integration

### Feature Highlights

- **OAuth2.0 Authentication**: Secure Google account authorization
- **Keyword Research**: Keyword suggestions based on Google Ads API
- **Competition Analysis**: Keyword competition and bidding analysis
- **Search Volume Prediction**: Historical search volume data and trend analysis
- **Intelligent Scoring**: Multi-dimensional keyword scoring algorithm

### Configuration Steps

1. **Get Google Ads API Credentials**
   - Visit [Google Ads API Documentation](https://developers.google.com/google-ads/api)
   - Create developer account and apply for API access
   - Obtain client ID, client secret, and developer token

2. **Configure Environment Variables**
```bash
# .env file
GOOGLE_ADS_DEVELOPER_TOKEN=your_developer_token
GOOGLE_ADS_CLIENT_ID=your_client_id
GOOGLE_ADS_CLIENT_SECRET=your_client_secret
```

3. **Authorization Flow**
   - Add Google Ads configuration in the system
   - Complete OAuth2.0 authorization flow
   - Start using keyword research functionality

### API Usage Example

```python
# Keyword research API call
response = requests.post('http://localhost:8000/api/v1/keyword-library/import/google-ads/1', 
    json={
        "seed_keywords": ["cross-border e-commerce", "amazon fba"],
        "language_code": "en-US",
        "location_ids": [2840],
        "page_size": 100
    }
)
```

## 📚 Documentation

- [System Design Document](docs/google_ads_integration_design_en.md)
- [Architecture Diagrams](docs/architecture_diagrams_en.md)
- [API Documentation](http://localhost:8000/docs) (Available after starting backend service)
- [User Manual](docs/user_manual_en.md)

## 🔧 Development Guide

### Project Structure

```
cbec/
├── frontend/              # Vue.js Frontend Application
│   ├── src/
│   │   ├── views/         # Page Components
│   │   ├── components/    # Common Components
│   │   ├── services/      # API Services
│   │   └── utils/         # Utility Functions
│   └── package.json
├── backend/               # FastAPI Backend Application
│   ├── app/
│   │   ├── api/           # API Routes
│   │   ├── models/        # Data Models
│   │   ├── services/      # Business Logic
│   │   └── utils/         # Utility Functions
│   └── requirements.txt
├── docs/                  # Project Documentation
├── docker-compose.yml     # Docker Compose File
└── README.md
```

### Development Standards

- **Code Style**: Follow PEP8 (Python) and ESLint (JavaScript)
- **Commit Messages**: Use [Conventional Commits](https://www.conventionalcommits.org/)
- **Branching Strategy**: Git Flow workflow
- **Test Coverage**: Unit test coverage > 80%

### Contributing Guide

1. Fork this project
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add some amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Create Pull Request

## 🧪 Testing

### Running Tests

```bash
# Frontend tests
cd frontend
npm run test

# Backend tests
cd backend
pytest

# Coverage report
pytest --cov=app tests/
```

### Testing Strategy

- **Unit Testing**: 70% coverage
- **Integration Testing**: 20% coverage
- **End-to-End Testing**: 10% coverage

## 📊 Monitoring and Logging

### Monitoring Metrics

- **System Metrics**: CPU, memory, disk usage
- **Application Metrics**: API response time, error rate
- **Business Metrics**: Keyword query count, user activity

### Log Management

- **Log Levels**: ERROR, WARN, INFO, DEBUG
- **Log Format**: Structured JSON format
- **Log Collection**: ELK Stack (Elasticsearch + Logstash + Kibana)

## 🔒 Security Considerations

### Authentication and Authorization

- JWT Token authentication
- OAuth2.0 standard
- RBAC access control
- API rate limiting

### Data Protection

- HTTPS encrypted transmission
- Sensitive data encrypted storage
- Regular security audits
- Data backup and recovery

## 🌍 Multi-language Support

Currently supported languages:
- 🇨🇳 Simplified Chinese
- 🇺🇸 English
- 🇯🇵 Japanese (Planned)
- 🇰🇷 Korean (Planned)

## 📈 Roadmap

### v1.0 (Current Version)
- [x] Basic product management
- [x] Order processing system
- [x] Google Ads API integration
- [x] Keyword research functionality

### v1.1 (Next Version)
- [ ] Multi-platform inventory synchronization
- [ ] Intelligent pricing strategies
- [ ] Advanced data analytics
- [ ] Mobile application

### v2.0 (Future Version)
- [ ] AI-driven market analysis
- [ ] Automated advertising campaigns
- [ ] Supply chain management
- [ ] Multi-tenant support

## 📞 Support and Contact

- **Issue Reports**: [GitHub Issues](https://github.com/your-username/cbec/issues)
- **Feature Requests**: [GitHub Discussions](https://github.com/your-username/cbec/discussions)
- **Technical Community**: [Join our Slack](https://join.slack.com/...)
- **Email Contact**: <EMAIL>

## 📄 License

This project is licensed under the [MIT License](LICENSE).

## 🙏 Acknowledgments

Thanks to the following open source projects and services:

- [Vue.js](https://vuejs.org/) - Progressive JavaScript Framework
- [FastAPI](https://fastapi.tiangolo.com/) - Modern Python Web Framework
- [Element Plus](https://element-plus.org/) - Vue 3 Component Library
- [Google Ads API](https://developers.google.com/google-ads/api) - Advertising Data API
- [PostgreSQL](https://www.postgresql.org/) - Powerful Open Source Database

---

⭐ **If this project helps you, please give us a Star!**

[![GitHub stars](https://img.shields.io/github/stars/your-username/cbec.svg?style=social)](https://github.com/your-username/cbec/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/your-username/cbec.svg?style=social)](https://github.com/your-username/cbec/network)
[![GitHub issues](https://img.shields.io/github/issues/your-username/cbec.svg)](https://github.com/your-username/cbec/issues)
[![GitHub license](https://img.shields.io/github/license/your-username/cbec.svg)](https://github.com/your-username/cbec/blob/main/LICENSE) 