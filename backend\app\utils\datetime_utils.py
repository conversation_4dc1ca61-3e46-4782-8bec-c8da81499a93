"""
统一的时间处理工具函数

采用 UTC + 前端本地化 的最主流方案：
- 后端统一使用UTC时间存储和传输
- 前端根据环境变量配置的时区自动显示本地时间
- 数据库存储UTC时间，保证数据一致性
- 支持全局时区配置，修改环境变量即可改变整个系统的时区显示
"""

import os
from datetime import datetime, timezone, timedelta
from typing import Optional, Union


def get_system_timezone() -> timezone:
    """
    从环境变量获取系统时区配置
    
    环境变量 SYSTEM_TIMEZONE_OFFSET 设置时区偏移（小时）
    默认为 +8 (中国时区)
    
    Returns:
        timezone: 系统配置的时区
    """
    try:
        offset_hours = int(os.getenv('SYSTEM_TIMEZONE_OFFSET', '8'))
        return timezone(timedelta(hours=offset_hours))
    except (ValueError, TypeError):
        # 如果环境变量无效，默认使用中国时区
        return timezone(timedelta(hours=8))


def utc_now() -> datetime:
    """
    获取当前UTC时间（带时区信息）
    
    替代已废弃的 datetime.utcnow()
    这是系统中所有时间的标准来源
    
    Returns:
        datetime: 当前UTC时间，带时区信息
    """
    return datetime.now(timezone.utc)


def to_utc(dt: Optional[datetime]) -> Optional[datetime]:
    """
    将任意时间转换为UTC时间
    
    Args:
        dt: 要转换的时间
        
    Returns:
        datetime: UTC时间，带时区信息；如果输入为None则返回None
    """
    if dt is None:
        return None
        
    if dt.tzinfo is None:
        # 如果是naive datetime，假设为系统时区时间
        system_tz = get_system_timezone()
        dt = dt.replace(tzinfo=system_tz)
    
    # 转换为UTC
    return dt.astimezone(timezone.utc)


def to_iso_string(dt: Optional[datetime]) -> Optional[str]:
    """
    将datetime转换为ISO格式字符串（UTC时间）
    
    这是API返回给前端的标准格式
    
    Args:
        dt: 要转换的时间
        
    Returns:
        str: ISO格式的UTC时间字符串，如 "2024-01-01T10:00:00Z"
             如果输入为None则返回None
    """
    if dt is None:
        return None
        
    # 确保是UTC时间
    utc_dt = to_utc(dt)
    
    # 返回ISO格式字符串，以Z结尾表示UTC
    return utc_dt.strftime('%Y-%m-%dT%H:%M:%SZ')


def parse_iso_string(iso_string: Optional[str]) -> Optional[datetime]:
    """
    解析ISO格式时间字符串为UTC datetime
    
    Args:
        iso_string: ISO格式时间字符串
        
    Returns:
        datetime: UTC时间，带时区信息；如果输入为None或空则返回None
    """
    if not iso_string:
        return None
        
    try:
        if iso_string.endswith('Z'):
            # UTC时间
            return datetime.fromisoformat(iso_string.replace('Z', '+00:00'))
        elif '+' in iso_string or iso_string.count('-') > 2:
            # 包含时区信息的时间
            return datetime.fromisoformat(iso_string).astimezone(timezone.utc)
        else:
            # 无时区信息，假设为系统时区
            dt = datetime.fromisoformat(iso_string)
            system_tz = get_system_timezone()
            return dt.replace(tzinfo=system_tz).astimezone(timezone.utc)
    except ValueError as e:
        raise ValueError(f"无法解析ISO时间字符串: {iso_string}, 错误: {e}")


def from_system_timezone(dt: Union[datetime, str]) -> Optional[datetime]:
    """
    将系统时区时间转换为UTC时间
    
    Args:
        dt: 系统时区时间（datetime对象或字符串）
        
    Returns:
        datetime: UTC时间，带时区信息
    """
    if dt is None:
        return None
        
    # 如果是字符串，先解析为datetime
    if isinstance(dt, str):
        try:
            if 'T' in dt:
                dt = datetime.fromisoformat(dt)
            else:
                dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
        except ValueError as e:
            raise ValueError(f"无法解析时间字符串: {dt}, 错误: {e}")
    
    # 如果是naive datetime，假设为系统时区时间
    if dt.tzinfo is None:
        system_tz = get_system_timezone()
        dt = dt.replace(tzinfo=system_tz)
    
    # 转换为UTC
    return dt.astimezone(timezone.utc)


def format_for_db() -> datetime:
    """
    获取用于数据库存储的当前时间（UTC）
    
    Returns:
        datetime: 当前UTC时间，用于数据库存储
    """
    return utc_now()


def format_for_log(dt: Optional[datetime] = None) -> str:
    """
    格式化时间用于日志显示（系统时区）
    
    Args:
        dt: 要格式化的时间，默认为当前时间
        
    Returns:
        str: 格式化的系统时区时间字符串
    """
    if dt is None:
        dt = utc_now()
    
    # 转换为系统时区
    system_tz = get_system_timezone()
    local_time = dt.astimezone(system_tz)
    
    # 获取时区名称
    offset_hours = int(os.getenv('SYSTEM_TIMEZONE_OFFSET', '8'))
    tz_name = f"UTC{offset_hours:+d}"
    
    return local_time.strftime(f'%Y-%m-%d %H:%M:%S {tz_name}')


def get_system_timezone_info() -> dict:
    """
    获取系统时区配置信息
    
    Returns:
        dict: 包含时区配置的字典
    """
    offset_hours = int(os.getenv('SYSTEM_TIMEZONE_OFFSET', '8'))
    system_tz = get_system_timezone()
    
    return {
        'timezone_offset': offset_hours,
        'timezone_name': f"UTC{offset_hours:+d}",
        'timezone_object': system_tz,
        'current_utc': utc_now().isoformat(),
        'current_local': utc_now().astimezone(system_tz).isoformat()
    }


# 兼容性函数（逐步替换旧代码）
def get_current_time() -> datetime:
    """
    获取当前时间（UTC）
    
    兼容性函数，建议使用 utc_now()
    """
    return utc_now()


def convert_to_utc(dt: datetime) -> datetime:
    """
    转换为UTC时间
    
    兼容性函数，建议使用 to_utc()
    """
    return to_utc(dt)


# 常用时区对象
UTC = timezone.utc
SYSTEM_TZ = get_system_timezone()


# 导出主要函数
__all__ = [
    'utc_now', 'to_utc', 'to_iso_string', 'parse_iso_string',
    'from_system_timezone', 'format_for_db', 'format_for_log',
    'get_system_timezone', 'get_system_timezone_info',
    'UTC', 'SYSTEM_TZ'
]
