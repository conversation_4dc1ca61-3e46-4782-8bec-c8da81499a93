#!/usr/bin/env python3
"""
测试阿里巴巴API时间戳重试机制

使用方法:
python test_timestamp_retry.py
"""

import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from app.core.database import SessionLocal
    from app.services.alibaba_service import AlibabaService
    from app.services.alibaba_product_service_real import AlibabaProductServiceReal
    import logging

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    def test_timestamp_retry():
        """测试时间戳重试机制"""
        logger.info("=" * 60)
        logger.info("开始测试阿里巴巴API时间戳重试机制")
        logger.info("=" * 60)
        
        db = SessionLocal()
        
        try:
            # 创建服务实例
            alibaba_service = AlibabaService(db)
            
            # 测试用的access_token
            test_access_token = "50000900a25ONSMuApzOPCg6jVchtCtwG9Fey1721d840BOU0EhlSL2grVHUIkb"
            
            # 测试API调用（将自动尝试多种时间戳格式）
            api_params = {
                "language": "ENGLISH",
                "page_size": 10,
                "current_page": 1
            }
            
            response = alibaba_service._call_alibaba_api(
                test_access_token,
                "alibaba.icbu.product.list",
                api_params
            )
            
            logger.info("✅ API调用成功!")
            logger.info(f"响应: {response}")
            
        except Exception as e:
            logger.error(f"❌ API调用失败: {e}")
            logger.error(f"错误类型: {type(e).__name__}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
        finally:
            db.close()
        
        logger.info("=" * 60)
        logger.info("测试完成")
        logger.info("=" * 60)

    if __name__ == "__main__":
        test_timestamp_retry()

except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在正确的环境中运行此脚本") 