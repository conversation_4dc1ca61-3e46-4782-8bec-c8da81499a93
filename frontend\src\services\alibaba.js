import request from '../utils/request'

// 产品表现API
export const productPerformanceApi = {
  // 获取产品表现数据
  getPerformanceData(params) {
    return request({
      url: '/v1/alibaba-product/performance',
      method: 'get',
      params: params
    })
  },

  // 获取可用时间范围
  getDateRange(statisticsType) {
    return request({
      url: '/v1/alibaba-product/date-range',
      method: 'get',
      params: { statistics_type: statisticsType }
    })
  },

  // 获取产品列表
  getProductList() {
    return request({
      url: '/v1/alibaba-product/products',
      method: 'get'
    })
  },

  // 同步产品表现数据
  syncData(params) {
    return request({
      url: '/v1/alibaba-product/sync',
      method: 'post',
      params: params
    })
  }
}

// 关键词表现API
export const keywordPerformanceApi = {
  // 获取可用数据周期
  getDatePeriods(statisticsType) {
    return request({
      url: '/v1/alibaba-keyword/date-periods',
      method: 'get',
      params: { statistics_type: statisticsType }
    })
  },

  // 获取周统计数据
  getWeeklyData(params) {
    return request({
      url: '/v1/alibaba-keyword/weekly',
      method: 'get',
      params: params
    })
  },

  // 获取月统计数据
  getMonthlyData(params) {
    return request({
      url: '/v1/alibaba-keyword/monthly',
      method: 'get',
      params: params
    })
  },

  // 获取关键词汇总数据
  getSummary(params) {
    return request({
      url: '/v1/alibaba-keyword/summary',
      method: 'get',
      params: params
    })
  },

  // 同步关键词表现数据
  syncData(params) {
    return request({
      url: '/v1/alibaba-keyword/sync',
      method: 'post',
      params: {
        statistics_type: params.statisticsType,
        start_date: params.dateRange[0],
        end_date: params.dateRange[1],
        force_update: params.forceUpdate
      }
    })
  }
}

// 通用阿里巴巴API
export const alibabaApi = {
  // 获取授权状态
  getAuthStatus() {
    return request({
      url: '/v1/alibaba/auth/status',
      method: 'get'
    })
  },

  // 刷新授权令牌
  refreshToken() {
    return request({
      url: '/v1/alibaba/auth/refresh',
      method: 'post'
    })
  }
}

// 商品管理API
export const productsApi = {
  // 获取商品列表
  getProductList(params) {
    return request({
      url: '/v1/alibaba-product/list',
      method: 'get',
      params: params
    })
  },

  // 搜索商品
  searchProducts(params) {
    return request({
      url: '/v1/alibaba-product/list',
      method: 'get',
      params: {
        ...params,
        sync_to_db: false // 搜索时不同步到数据库
      }
    })
  }
} 