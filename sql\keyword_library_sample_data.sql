-- 关键词库示例数据插入脚本
-- 展示如何使用新的关键词字段结构

-- 插入示例关键词数据
INSERT INTO `keyword_library` (
    `keyword_name`,
    `intent`,
    `volume`,
    `trend`,
    `keyword_difficulty`,
    `cpc_usd`,
    `competitive_density`,
    `serp_features`,
    `number_of_results`,
    `competition_level`,
    `category`,
    `tags`,
    `update_method`
) VALUES 
(
    'claw machine',
    'Informational, Commercial',
    40500,
    '[0.54,0.54,0.44,0.66,0.81,0.81,0.66,1.00,0.66,0.81,0.66,0.81]',
    49,
    0.78,
    1,
    '["Sitelinks", "Reviews", "Image", "Video", "People also ask", "Related searches", "Popular products", "Things to know"]',
    74200000,
    'medium',
    '游戏设备',
    '娃娃机,抓娃娃,游戏机',
    'manual'
),
(
    'best smartphone 2024',
    'Commercial',
    125000,
    '[0.45,0.52,0.61,0.78,0.85,0.92,0.88,0.95,0.89,0.82,0.76,0.71]',
    72,
    1.25,
    1,
    '["Sitelinks", "Reviews", "Image", "Video", "Popular products", "Top stories"]',
    156000000,
    'high',
    '电子产品',
    '智能手机,手机推荐,2024',
    'manual'
),
(
    'how to cook pasta',
    'Informational',
    89000,
    '[0.62,0.58,0.55,0.59,0.64,0.68,0.72,0.75,0.71,0.67,0.63,0.60]',
    35,
    0.45,
    0,
    '["Image", "Video", "People also ask", "Related searches", "Recipe"]',
    45800000,
    'low',
    '烹饪',
    '意大利面,烹饪教程,食谱',
    'manual'
),
(
    'buy running shoes online',
    'Transactional',
    67000,
    '[0.58,0.61,0.65,0.69,0.73,0.77,0.81,0.85,0.82,0.78,0.74,0.70]',
    58,
    1.89,
    1,
    '["Sitelinks", "Popular products", "Shopping", "Image", "Reviews"]',
    23400000,
    'high',
    '运动用品',
    '跑鞋,在线购买,运动鞋',
    'manual'
),
(
    'weather forecast',
    'Navigational',
    234000,
    '[0.75,0.78,0.82,0.85,0.88,0.91,0.89,0.86,0.83,0.80,0.77,0.74]',
    25,
    0.12,
    0,
    '["Weather", "Map", "Related searches", "Local results"]',
    892000000,
    'low',
    '天气',
    '天气预报,气象,预报',
    'manual'
);

-- 验证插入的数据
SELECT 
    keyword_name,
    intent,
    volume,
    keyword_difficulty,
    cpc_usd,
    competitive_density,
    JSON_LENGTH(serp_features) as serp_features_count,
    number_of_results,
    competition_level,
    category
FROM keyword_library 
WHERE keyword_name IN ('claw machine', 'best smartphone 2024', 'how to cook pasta', 'buy running shoes online', 'weather forecast')
ORDER BY volume DESC;
