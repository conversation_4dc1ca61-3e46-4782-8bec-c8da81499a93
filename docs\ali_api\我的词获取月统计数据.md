# 我的词获取月统计数据

我的词-获取月统计数据

GET/POST

alibaba.mydata.self.keyword.effect.month.get

描述：获取数据管家我的词月统计数据

## 参数

|  名称  |  类型  |  是否必须  |  描述  |
| --- | --- | --- | --- |
|  properties  |  Object  |  否  |  查询选项  |
|  is\_p4p  |  String  |  否  |  是否是外贸直通车推广词。可选值：ALL-所有；YES-只选择外贸直通车推广词；NO-排除外贸直通车推广词  |
|  keyword  |  String  |  否  |  要查询的关键词，不填时查收所有词  |
|  keywords\_in\_use  |  String  |  否  |  是否已设置为关键词。可选值：ALL-所有；YES-设置为关键词；NO-没有设置为关键词  |
|  keywords\_viewed  |  String  |  否  |  是否有曝光。可选值：ALL-所有；YES-有曝光；NO-没有曝光  |
|  limit  |  Number  |  否  |  返回数据分页大小  |
|  offset  |  Number  |  否  |  返回数据分页偏移  |
|  order\_by\_field  |  String  |  否  |  排序字段，sumShowCnt-曝光量；sumClickCnt-点击量；ctr-点击率；sumP4pShowCnt-外贸直通车曝光；sumP4pClickCnt-外贸直通车点击；searchPvIndex-搜索热度；gsTpMemberSetCnt-卖家竞争度；avgSumShowCnt-Top10平均曝光；avgSumClickCnt-Top10平均点击；  |
|  order\_by\_mode  |  String  |  否  |  排序方式，可选值：asc,desc  |

## 响应参数

|  名称  |  类型  |  描述  |
| --- | --- | --- |
|  result  |  Object  |  我的词查询结果  |
|  effects  |  Object\[\]  |  词效果  |
|  avg\_sum\_click\_cnt  |  Number  |  Top10平均点击  |
|  avg\_sum\_show\_cnt  |  Number  |  Top10平均曝光  |
|  ctr  |  String  |  点击率，1代表100%  |
|  gs\_tp\_member\_set\_cnt  |  Number  |  卖家竞争度  |
|  is\_p4p\_kw  |  Boolean  |  是否为外贸直通车推广词（离线）  |
|  keyword  |  String  |  关键词  |
|  search\_pv\_index  |  Number  |  搜索热度  |
|  sum\_click\_cnt  |  Number  |  点击量  |
|  sum\_p4p\_click\_cnt  |  Number  |  外贸直通车点击  |
|  sum\_p4p\_show\_cnt  |  Number  |  外贸直通车曝光  |
|  sum\_show\_cnt  |  Number  |  曝光量  |
|  total\_count  |  Number  |  记录总数  |

## 错误码

|  错误码  |  错误信息  |  解决方案  |
| --- | --- | --- |
|  没有数据  |  |  |

GET/POSTalibaba.mydata.self.keyword.effect.month.get

*   PYTHON
    

```PYTHON
client = iop.IopClient(url, appkey ,appSecret)
request = iop.IopRequest('alibaba.mydata.self.keyword.effect.month.get')
request.add_api_param('properties', '{\"keywords_in_use\":\"ALL\",\"keywords_viewed\":\"ALL\",\"offset\":\"0\",\"order_by_mode\":\"desc\",\"limit\":\"10\",\"is_p4p\":\"ALL\",\"keyword\":\"mp3\",\"order_by_field\":\"sumShowCnt\"}')
response = client.execute(request, access_token)
print(response.type)
print(response.body)

```

*   非精简返回
    

---
---
```json
{
  "code": "0",
  "alibaba_mydata_self_keyword_effect_month_get_response": {
    "result": {
      "effects": [
        {
          "ctr": "0.123",
          "avg_sum_click_cnt": "1",
          "sum_p4p_click_cnt": "1",
          "search_pv_index": "1",
          "avg_sum_show_cnt": "1",
          "sum_show_cnt": "1",
          "is_p4p_kw": "true",
          "keyword": "mp3",
          "sum_click_cnt": "1",
          "sum_p4p_show_cnt": "1",
          "gs_tp_member_set_cnt": "1"
        }
      ],
      "total_count": "100"
    }
  },
  "request_id": "0ba2887315178178017221014"
```