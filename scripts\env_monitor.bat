@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: =============================================================================
:: AICBEC项目环境监测和依赖安装脚本 (Windows批处理版本)
:: =============================================================================

echo ================================================================================
echo                        AICBEC项目环境监测和依赖安装工具
echo ================================================================================
echo.

:: 检查是否在正确的目录
if not exist "%~dp0..\backend" (
    echo [错误] 未找到backend目录，请确保在项目根目录的scripts文件夹中运行此脚本
    pause
    exit /b 1
)

if not exist "%~dp0..\..\package.json" (
    echo [错误] 未找到前端package.json文件，请确保前端依赖在正确位置
    pause
    exit /b 1
)

:: 切换到项目根目录
cd /d "%~dp0.."

echo [信息] 当前工作目录: %CD%
echo.

:: 显示菜单
:menu
echo 请选择要执行的操作:
echo.
echo 1. 完整环境检查并自动安装缺失依赖
echo 2. 仅检查环境状态（不安装依赖）
echo 3. 仅安装后端Python依赖
echo 4. 仅安装前端Node.js依赖
echo 5. 虚拟环境管理
echo 6. 查看最近的环境报告
echo 7. npm诊断和修复
echo 8. 清理临时文件和日志
echo 0. 退出
echo.
set /p choice=请输入选项 (0-8): 

if "%choice%"=="1" goto full_check
if "%choice%"=="2" goto check_only
if "%choice%"=="3" goto backend_only
if "%choice%"=="4" goto frontend_only
if "%choice%"=="5" goto venv_management
if "%choice%"=="6" goto view_reports
if "%choice%"=="7" goto npm_fix
if "%choice%"=="8" goto cleanup
if "%choice%"=="0" goto exit
echo [错误] 无效选择，请重新输入
goto menu

:full_check
echo.
echo ================================================================================
echo                           开始完整环境检查和依赖安装
echo ================================================================================
echo.
python scripts\env_monitor_and_install.py
goto end

:check_only
echo.
echo ================================================================================
echo                             开始环境检查（仅检查模式）
echo ================================================================================
echo.
python scripts\env_monitor_and_install.py --check-only
goto end

:backend_only
echo.
echo ================================================================================
echo                              安装后端Python依赖
echo ================================================================================
echo.
cd backend

:: 检查虚拟环境，先检查上级目录，再检查当前目录
if exist "..\venv\Scripts\activate.bat" (
    echo [信息] 检测到上级目录虚拟环境，正在激活...
    call ..\venv\Scripts\activate.bat
) else if exist "venv\Scripts\activate.bat" (
    echo [信息] 检测到当前目录虚拟环境，正在激活...
    call venv\Scripts\activate.bat
) else (
    echo [警告] 未检测到虚拟环境，建议在虚拟环境中运行
)

echo [信息] 安装Python依赖包...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt
if %errorlevel% equ 0 (
    echo [成功] 后端依赖安装完成
) else (
    echo [错误] 后端依赖安装失败
)
cd ..
goto end

:frontend_only
echo.
echo ================================================================================
echo                             安装前端Node.js依赖
echo ================================================================================
echo.
cd ..
if not exist "package.json" (
    echo [错误] 未找到package.json文件
    cd CBEC
    goto end
)
echo [信息] 安装Node.js依赖包...
"C:\Program Files\nodejs\npm.cmd" install --legacy-peer-deps
if %errorlevel% equ 0 (
    echo [成功] 前端依赖安装完成
) else (
    echo [错误] 前端依赖安装失败
)
cd CBEC
goto end

:venv_management
echo.
echo ================================================================================
echo                                虚拟环境管理
echo ================================================================================
echo.
echo 请选择要执行的操作:
echo.
echo 1. 创建虚拟环境
echo 2. 激活虚拟环境
echo 3. 退出
echo.
set /p venv_choice=请输入选项 (1-3): 

if "%venv_choice%"=="1" goto create_venv
if "%venv_choice%"=="2" goto activate_venv
if "%venv_choice%"=="3" goto end
echo [错误] 无效选择，请重新输入
goto venv_management

:create_venv
echo.
echo ================================================================================
echo                                创建虚拟环境
echo ================================================================================
echo.
cd ..
if exist "venv" (
    echo [信息] 虚拟环境已存在
) else (
    echo [信息] 在 %CD% 创建虚拟环境...
    python -m venv venv
    if %errorlevel% equ 0 (
        echo [成功] 虚拟环境创建完成: %CD%\venv
    ) else (
        echo [错误] 虚拟环境创建失败
    )
)
cd CBEC
goto end

:activate_venv
echo.
echo ================================================================================
echo                                激活虚拟环境
echo ================================================================================
echo.
if exist "..\venv\Scripts\activate.bat" (
    echo [信息] 激活上级目录的虚拟环境...
    call ..\venv\Scripts\activate.bat
    echo [信息] 虚拟环境已激活，请在新的命令行窗口中使用
) else if exist "backend\venv\Scripts\activate.bat" (
    echo [信息] 激活backend目录的虚拟环境...
    call backend\venv\Scripts\activate.bat
    echo [信息] 虚拟环境已激活，请在新的命令行窗口中使用
) else (
    echo [错误] 未找到虚拟环境，请先创建虚拟环境
)
goto end

:view_reports
echo.
echo ================================================================================
echo                                查看环境报告
echo ================================================================================
echo.
if exist "logs" (
    echo 最近的环境报告:
    echo.
    dir logs\env_report_*.json /b /o-d 2>nul | findstr /r "env_report_.*\.json" >nul
    if %errorlevel% equ 0 (
        for /f %%i in ('dir logs\env_report_*.json /b /o-d 2^>nul ^| findstr /r "env_report_.*\.json"') do (
            echo 报告文件: logs\%%i
            set latest_report=logs\%%i
            goto show_report
        )
        :show_report
        echo.
        set /p view_choice=是否查看最新报告的详细内容？ (y/n): 
        if /i "!view_choice!"=="y" (
            if exist "!latest_report!" (
                type "!latest_report!"
            )
        )
    ) else (
        echo 未找到环境报告文件
    )
    
    echo.
    echo 日志文件:
    dir logs\env_monitor_*.log /b /o-d 2>nul | findstr /r "env_monitor_.*\.log" >nul
    if %errorlevel% equ 0 (
        for /f "tokens=1" %%i in ('dir logs\env_monitor_*.log /b /o-d 2^>nul ^| findstr /r "env_monitor_.*\.log"') do (
            echo 日志文件: logs\%%i
            goto :break_loop
        )
        :break_loop
    ) else (
        echo 未找到日志文件
    )
) else (
    echo logs目录不存在，还没有生成过报告
)
echo.
pause
goto menu

:npm_fix
echo.
echo ================================================================================
echo                                 npm诊断和修复
echo ================================================================================
echo.
cd ..
if not exist "package.json" (
    echo [错误] 未找到package.json文件
    cd CBEC
    goto end
)
echo [信息] 运行npm诊断和修复...
npm run diagnose
if %errorlevel% equ 0 (
    echo [成功] npm诊断和修复完成
) else (
    echo [错误] npm诊断和修复失败
)
cd CBEC
goto end

:cleanup
echo.
echo ================================================================================
echo                                清理临时文件
echo ================================================================================
echo.
set /p confirm=确定要清理所有日志文件和临时文件吗？ (y/n): 
if /i "%confirm%"=="y" (
    if exist "logs" (
        echo 清理logs目录...
        del /q logs\env_monitor_*.log 2>nul
        del /q logs\env_report_*.json 2>nul
        echo 清理完成
    )
    
    if exist "temp" (
        echo 清理temp目录...
        rmdir /s /q temp 2>nul
        echo 清理完成
    )
    
    echo 清理Python缓存...
    for /r . %%i in (__pycache__) do (
        if exist "%%i" rmdir /s /q "%%i" 2>nul
    )
    
    echo 清理完成！
) else (
    echo 取消清理操作
)
echo.
pause
goto menu

:end
echo.
echo ================================================================================
echo                                  操作完成
echo ================================================================================
echo.
set /p continue=按Enter返回主菜单，或输入'q'退出: 
if /i "%continue%"=="q" goto exit
goto menu

:exit
echo.
echo 感谢使用AICBEC环境监测工具！
echo.
pause
exit /b 0 