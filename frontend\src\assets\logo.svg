<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#409EFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#66B1FF;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 外圈 -->
  <circle cx="20" cy="20" r="18" fill="url(#logoGradient)" opacity="0.1"/>
  
  <!-- 主体图标 -->
  <g transform="translate(8, 8)">
    <!-- 数据连线 -->
    <path d="M2 6L8 12L14 8L22 14" stroke="url(#logoGradient)" stroke-width="2" fill="none" stroke-linecap="round"/>
    
    <!-- 数据点 -->
    <circle cx="2" cy="6" r="2" fill="url(#logoGradient)"/>
    <circle cx="8" cy="12" r="2" fill="url(#logoGradient)"/>
    <circle cx="14" cy="8" r="2" fill="url(#logoGradient)"/>
    <circle cx="22" cy="14" r="2" fill="url(#logoGradient)"/>
    
    <!-- AI符号 -->
    <text x="6" y="20" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="url(#logoGradient)">AI</text>
  </g>
</svg> 