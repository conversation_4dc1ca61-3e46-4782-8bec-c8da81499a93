# AI外贸运营系统环境配置文件
# 请将此文件重命名为 .env 并放置在 CBEC 目录下

# 基本配置
PROJECT_NAME=AI Foreign Trade Operation System
API_V1_STR=/api/v1

# 安全配置
SECRET_KEY=c2fc5f09e96dfc9b4d756eca0d7b3008a3544aefaa9ab1b1e02204a56c6baa3f
ACCESS_TOKEN_EXPIRE_MINUTES=10080

# 数据库配置
DATABASE_URL=mysql+pymysql://root:S3rSTRBm3dBw8EB7@**************:13306/CBEC

# 后端服务配置
BACKEND_HOST=0.0.0.0
BACKEND_PORT=5000
BACKEND_DEBUG=true
BACKEND_RELOAD=true

# 前端服务配置
FRONTEND_HOST=0.0.0.0
FRONTEND_PORT=8080
FRONTEND_URL=https://1088442hg3xl8.vicp.fun

# CORS配置 - 更新为新域名并放宽限制用于调试
CORS_ORIGINS=http://localhost:8080,http://localhost:3000,http://127.0.0.1:8080,http://127.0.0.1:3000,https://1088442hg3xl8.vicp.fun,http://1088442hg3xl8.vicp.fun,http://wf00659340.gnway.cc:8000,https://wf00659340.gnway.cc:8000,*

# 多租户配置
MULTI_TENANT_ENABLED=true
DEFAULT_TENANT_ID=default

# 生产环境配置
PRODUCTION=false

# 日志配置
LOG_LEVEL=INFO

# 阿里国际站API配置 - 更新回调地址为新域名
ALIBABA_APP_KEY=502750
ALIBABA_APP_SECRET=a7cd4a24d3081fd89ae6233f127ba461
ALIBABA_OAUTH_URL=https://open-api.alibaba.com/oauth/authorize
ALIBABA_TOKEN_URL=https://open-api.alibaba.com/rest/auth/token/create
ALIBABA_REFRESH_TOKEN_URL=https://open-api.alibaba.com/rest/auth/token/refresh
ALIBABA_REDIRECT_URI=https://1088442hg3xl8.vicp.fun/api/v1/alibaba/callback

# 文件存储配置
UPLOAD_DIR=uploads 