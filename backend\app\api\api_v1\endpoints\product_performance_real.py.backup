from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import date, datetime, timedelta
import logging

from app.api import deps
from app.models.user import User
from app.models.alibaba_auth import Ali<PERSON>baAuth
from app.services.alibaba_product_service_real import AlibabaProductServiceReal
from app.services.alibaba_product_service import AlibabaProductService
from app.core.config import settings

router = APIRouter()
logger = logging.getLogger(__name__)

# 根据配置选择使用真实API还是模拟数据
if settings.ALIBABA_USE_REAL_API:
    product_service = AlibabaProductServiceReal()
    logger.info("使用真实阿里巴巴API服务")
else:
    product_service = AlibabaProductService()
    logger.info("使用模拟数据服务")

@router.get("/performance")
def get_product_performance(
    statistics_type: str = Query(..., description="统计周期类型"),
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    product_ids: Optional[str] = Query(None, description="产品ID列表，逗号分隔"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取产品表现数据
    """
    try:
        # 解析产品ID列表
        product_id_list = None
        if product_ids:
            product_id_list = [pid.strip() for pid in product_ids.split(",") if pid.strip()]
        
        # 从本地数据库获取产品表现数据
        performance_data = product_service.get_product_performance(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            statistics_type=statistics_type,
            start_date=start_date,
            end_date=end_date,
            product_ids=product_id_list
        )
        
        return {
            "code": 200,
            "data": performance_data,
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取产品表现数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取产品表现数据失败")

@router.get("/date-range")
def get_product_performance_date_range(
    statistics_type: Optional[str] = Query("day", description="统计周期类型"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取产品表现数据的可用时间范围
    """
    try:
        # 从本地数据库获取时间范围
        date_range = product_service.get_date_range(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            statistics_type=statistics_type
        )
        
        return {
            "code": 200,
            "data": date_range,
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取产品表现时间范围失败: {e}")
        raise HTTPException(status_code=500, detail="获取产品表现时间范围失败")

@router.get("/products")
def get_product_list(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取产品列表
    """
    try:
        products = product_service.get_available_products(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id
        )
        
        return {
            "code": 200,
            "data": products,
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取产品列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取产品列表失败")

@router.post("/sync")
async def sync_product_data(
    statistics_type: str = Query(..., description="统计周期类型"),
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    product_ids: Optional[str] = Query(None, description="产品ID列表，逗号分隔"),
    force_update: bool = Query(False, description="是否强制更新"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    从阿里国际站同步产品表现数据
    """
    try:
        # 检查用户是否有阿里国际站授权
        auth_record = db.query(AlibabaAuth).filter(
            AlibabaAuth.user_id == current_user.id,
            AlibabaAuth.tenant_id == current_user.tenant_id,
            AlibabaAuth.is_active == True
        ).first()
        
        if not auth_record:
            raise HTTPException(
                status_code=400, 
                detail="请先完成阿里国际站授权"
            )
        
        # 检查token是否过期
        token_expires_at = auth_record.token_created_at + timedelta(seconds=auth_record.expires_in)
        if datetime.now() > token_expires_at:
            raise HTTPException(
                status_code=400,
                detail="阿里国际站访问令牌已过期，请重新授权"
            )
        
        # 解析产品ID列表
        product_id_list = None
        if product_ids:
            product_id_list = [pid.strip() for pid in product_ids.split(",") if pid.strip()]
        
        # 同步数据
        result = await product_service.sync_data_from_alibaba(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            statistics_type=statistics_type,
            start_date=start_date,
            end_date=end_date,
            product_ids=product_id_list,
            access_token=auth_record.access_token
        )
        
        logger.info(f"用户 {current_user.id} 同步产品表现数据成功: {result}")
        
        return {
            "code": 200,
            "data": result,
            "message": "数据同步成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步产品表现数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"同步数据失败: {str(e)}")

@router.post("/sync-date-range")
async def sync_date_range(
    statistics_type: str = Query(..., description="统计周期类型"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    从阿里国际站同步可用时间范围
    """
    try:
        # 检查用户授权
        auth_record = db.query(AlibabaAuth).filter(
            AlibabaAuth.user_id == current_user.id,
            AlibabaAuth.tenant_id == current_user.tenant_id,
            AlibabaAuth.is_active == True
        ).first()
        
        if not auth_record:
            raise HTTPException(status_code=400, detail="请先完成阿里国际站授权")
        
        # 检查token是否过期
        token_expires_at = auth_record.token_created_at + timedelta(seconds=auth_record.expires_in)
        if datetime.now() > token_expires_at:
            raise HTTPException(status_code=400, detail="阿里国际站访问令牌已过期，请重新授权")
        
        # 如果使用真实API，同步时间范围
        if settings.ALIBABA_USE_REAL_API and hasattr(product_service, 'sync_date_range_from_alibaba'):
            result = await product_service.sync_date_range_from_alibaba(
                db=db,
                user_id=current_user.id,
                tenant_id=current_user.tenant_id,
                statistics_type=statistics_type,
                access_token=auth_record.access_token
            )
            
            return {
                "code": 200,
                "data": result,
                "message": "时间范围同步成功"
            }
        else:
            return {
                "code": 200,
                "data": {},
                "message": "模拟模式下无需同步时间范围"
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步时间范围失败: {e}")
        raise HTTPException(status_code=500, detail=f"同步时间范围失败: {str(e)}") 