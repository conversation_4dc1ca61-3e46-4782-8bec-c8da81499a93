from sqlalchemy import Colum<PERSON>, Inte<PERSON>, String, BigInteger, Enum, Text, JSON, ForeignKey, Boolean
from sqlalchemy.types import DECIMAL, DateTime, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
import enum
from .base import Base
from app.utils.datetime_utils import utc_now

class CompetitionLevel(enum.Enum):
    low = "low"
    medium = "medium"
    high = "high"
    unspecified = "unspecified"

class UpdateMethod(enum.Enum):
    google_ads_api = "google_ads_api"
    manual = "manual"
    batch_import = "batch_import"
    pytrends = "pytrends"

class ImportTaskStatus(enum.Enum):
    pending = "pending"
    processing = "processing"
    completed = "completed"
    failed = "failed"
    cancelled = "cancelled"

class PyTrendsTaskStatus(enum.Enum):
    pending = "pending"
    running = "running"
    completed = "completed"
    failed = "failed"
    cancelled = "cancelled"

class KeywordLibrary(Base):
    __tablename__ = "keyword_library"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    keyword_name = Column(String(255), unique=True, nullable=False, index=True)

    # 新的关键词数据字段
    intent = Column(String(100), nullable=True, comment="关键词意图（Informational, Commercial等）")
    volume = Column(BigInteger, nullable=True, index=True, comment="搜索量")
    trend = Column(Text, nullable=True, comment="趋势数据（12个月的趋势值数组，JSON格式）")
    keyword_difficulty = Column(Integer, nullable=True, comment="关键词难度（0-100）")
    cpc_usd = Column(DECIMAL(10, 2), nullable=True, comment="每次点击费用（美元）")
    competitive_density = Column(DECIMAL(3, 2), nullable=True, comment="竞争密度（0-1）")
    serp_features = Column(Text, nullable=True, comment="SERP特征（JSON数组格式）")
    number_of_results = Column(BigInteger, nullable=True, comment="搜索结果数量")

    # 保留原有字段以兼容现有数据
    avg_monthly_searches = Column(BigInteger, nullable=True, index=True)
    monthly_searches = Column(String(500), nullable=True)  # JSON string
    competition_level = Column(Enum(CompetitionLevel), default=CompetitionLevel.unspecified, index=True)
    competition_index = Column(DECIMAL(5, 2), nullable=True)
    trend_interest = Column(DECIMAL(5, 2), nullable=True, index=True, comment="Google Trends兴趣度（0-100）")
    trend_growth_rate = Column(DECIMAL(5, 2), nullable=True, comment="趋势增长率（%）")
    trend_region = Column(String(10), nullable=True, index=True, comment="趋势地区代码")
    trend_timeframe = Column(String(20), default="today 12-m", comment="趋势时间范围")
    trend_data = Column(Text, nullable=True, comment="趋势数据（JSON格式）")
    low_bid_micros = Column(BigInteger, nullable=True)
    high_bid_micros = Column(BigInteger, nullable=True)
    currency_code = Column(String(3), default="CNY")
    language_code = Column(String(10), default="zh-CN")
    location_ids = Column(String(255), nullable=True)
    update_method = Column(Enum(UpdateMethod), nullable=False, default=UpdateMethod.manual, index=True)
    category = Column(String(100), nullable=True, index=True)
    tags = Column(String(500), nullable=True)
    # 时间戳 - 使用UTC时间统一方案
    created_at = Column(DateTime(timezone=True), nullable=False, default=utc_now)
    updated_at = Column(DateTime(timezone=True), nullable=True, onupdate=utc_now)
    
    # 关系
    update_history = relationship("KeywordUpdateHistory", back_populates="keyword", cascade="all, delete-orphan")

class GoogleAdsConfig(Base):
    __tablename__ = "google_ads_config"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    config_name = Column(String(100), unique=True, nullable=False)
    customer_id = Column(String(20), nullable=False, index=True)
    developer_token = Column(String(255), nullable=False)
    api_key = Column(String(255), nullable=True)  # Google API Key
    client_id = Column(String(255), nullable=False)
    client_secret = Column(String(255), nullable=False)
    refresh_token = Column(String(255), nullable=True)
    login_customer_id = Column(String(20), nullable=True)
    oauth_state = Column(String(255), nullable=True)
    authorization_status = Column(String(20), default="pending")
    last_auth_time = Column(DateTime(timezone=True), nullable=True)
    access_token = Column(String(500), nullable=True)
    token_expiry = Column(DateTime(timezone=True), nullable=True)
    # 代理服务器配置
    use_proxy = Column(Boolean, default=False)
    proxy_host = Column(String(255), nullable=True)
    proxy_port = Column(Integer, nullable=True)
    proxy_username = Column(String(255), nullable=True)
    proxy_password = Column(String(255), nullable=True)
    proxy_type = Column(String(10), default="http")  # http, https, socks5
    is_active = Column(Boolean, default=True, index=True)
    created_at = Column(DateTime(timezone=True), default=utc_now)
    updated_at = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now)

class PyTrendsConfig(Base):
    __tablename__ = "pytrends_config"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    config_name = Column(String(100), unique=True, nullable=False)
    language = Column(String(5), default="zh-CN", comment="语言设置")
    timezone = Column(Integer, default=480, comment="时区偏移（分钟）")
    geo_location = Column(String(5), default="CN", comment="地理位置代码")
    default_timeframe = Column(String(20), default="today 12-m", comment="默认时间范围")
    max_keywords_per_batch = Column(Integer, default=5, comment="每批最大关键词数")
    request_delay = Column(Integer, default=2, comment="请求延迟（秒）")
    retry_attempts = Column(Integer, default=3, comment="重试次数")
    use_proxy = Column(Boolean, default=False, comment="是否使用代理")
    proxy_host = Column(String(255), nullable=True, comment="代理主机")
    proxy_port = Column(Integer, nullable=True, comment="代理端口")
    proxy_username = Column(String(255), nullable=True, comment="代理用户名")
    proxy_password = Column(String(255), nullable=True, comment="代理密码")
    proxy_type = Column(String(10), default="http", comment="代理类型")
    is_active = Column(Boolean, default=True, index=True)
    created_at = Column(DateTime(timezone=True), default=utc_now)
    updated_at = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now)

class PyTrendsTaskHistory(Base):
    __tablename__ = "pytrends_task_history"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    task_id = Column(String(50), unique=True, nullable=False)
    config_id = Column(Integer, ForeignKey("pytrends_config.id"), nullable=False, index=True)
    seed_keywords = Column(Text, nullable=False, comment="种子关键词列表（JSON）")
    search_parameters = Column(Text, nullable=True, comment="搜索参数（JSON）")
    status = Column(Enum(PyTrendsTaskStatus), default=PyTrendsTaskStatus.pending, index=True)
    progress = Column(Integer, default=0, comment="进度百分比")
    total_keywords = Column(Integer, default=0, comment="总关键词数")
    processed_keywords = Column(Integer, default=0, comment="已处理关键词数")
    success_keywords = Column(Integer, default=0, comment="成功关键词数")
    failed_keywords = Column(Integer, default=0, comment="失败关键词数")
    error_message = Column(Text, nullable=True, comment="错误信息")
    result_summary = Column(Text, nullable=True, comment="结果摘要（JSON）")
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    created_at = Column(DateTime(timezone=True), default=utc_now, index=True)
    updated_at = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now)
    
    # 关系
    config = relationship("PyTrendsConfig")

class KeywordUpdateHistory(Base):
    __tablename__ = "keyword_update_history"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    keyword_id = Column(Integer, ForeignKey("keyword_library.id"), nullable=False, index=True)
    update_method = Column(Enum(UpdateMethod), nullable=False, index=True)
    old_data = Column(JSON, nullable=True)
    new_data = Column(JSON, nullable=True)
    batch_id = Column(String(100), nullable=True, index=True)
    operator_id = Column(Integer, nullable=True)
    operator_name = Column(String(100), nullable=True)
    created_at = Column(DateTime(timezone=True), default=utc_now, index=True)
    
    # 关系
    keyword = relationship("KeywordLibrary", back_populates="update_history")

class KeywordImportTask(Base):
    __tablename__ = "keyword_import_tasks"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    task_id = Column(String(100), unique=True, nullable=False)
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    total_count = Column(Integer, default=0)
    success_count = Column(Integer, default=0)
    failed_count = Column(Integer, default=0)
    progress = Column(Integer, default=0, comment="进度百分比")
    status = Column(Enum(ImportTaskStatus), default=ImportTaskStatus.pending, index=True)
    error_message = Column(Text, nullable=True)
    result_file_path = Column(String(500), nullable=True)
    operator_id = Column(Integer, nullable=True)
    operator_name = Column(String(100), nullable=True)
    created_at = Column(DateTime(timezone=True), default=utc_now, index=True)
    updated_at = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now) 