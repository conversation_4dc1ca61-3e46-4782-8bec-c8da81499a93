#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python版本兼容性检查脚本
检查当前Python版本是否与项目依赖兼容
"""

import sys
import platform
import subprocess

def check_python_version():
    """检查Python版本"""
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version >= (3, 13):
        print("✓ 检测到Python 3.13+")
        return "3.13+"
    elif python_version >= (3, 8):
        print("✓ 检测到Python 3.8-3.12")
        return "3.8-3.12"
    else:
        print("✗ Python版本过低，需要3.8或更高版本")
        return "low"

def get_compatible_pandas_version(python_version_category):
    """根据Python版本获取兼容的pandas版本"""
    if python_version_category == "3.13+":
        return "pandas>=2.1.4", "需要pandas 2.1.4+支持Python 3.13"
    elif python_version_category == "3.8-3.12":
        return "pandas>=2.0.0", "可以使用pandas 2.0.0+"
    else:
        return None, "Python版本不支持"

def check_platform():
    """检查操作系统平台"""
    system = platform.system()
    machine = platform.machine()
    print(f"操作系统: {system} {machine}")
    
    if system == "Windows":
        return "windows"
    else:
        return "other"

def main():
    print("=" * 50)
    print("Python兼容性检查")
    print("=" * 50)
    
    # 检查Python版本
    python_category = check_python_version()
    
    # 检查平台
    platform_type = check_platform()
    
    # 获取建议的pandas版本
    pandas_version, note = get_compatible_pandas_version(python_category)
    
    print("\n" + "=" * 50)
    print("兼容性分析结果")
    print("=" * 50)
    
    if pandas_version:
        print(f"✓ 建议pandas版本: {pandas_version}")
        print(f"  说明: {note}")
        
        if python_category == "3.13+":
            print("\n⚠ Python 3.13兼容性注意事项:")
            print("  1. 需要使用pandas 2.1.4或更高版本")
            print("  2. 在Windows上可能需要Visual C++ Build Tools")
            print("  3. 建议使用预编译的wheel包")
            
            if platform_type == "windows":
                print("\n💡 Windows用户建议:")
                print("  1. 安装Microsoft Visual C++ Build Tools")
                print("     下载: https://visualstudio.microsoft.com/visual-cpp-build-tools/")
                print("  2. 或使用conda安装: conda install pandas")
                print("  3. 或使用预编译wheel: pip install --only-binary=all pandas")
    else:
        print(f"✗ {note}")
        print("  请升级Python到3.8或更高版本")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main() 