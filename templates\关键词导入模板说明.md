# 关键词库导入模板说明

## 模板文件
- **中文模板**: `keyword_import_template.csv`
- **英文模板**: `keyword_import_template_en.csv`

## 字段说明

### 必填字段（标红显示）
| 字段名 | 英文字段名 | 数据类型 | 说明 | 示例 |
|--------|------------|----------|------|------|
| **关键词名** | **keyword_name** | 文本 | 关键词名称，必须唯一 | claw machine |

### 新增字段（推荐填写）
| 字段名 | 英文字段名 | 数据类型 | 说明 | 示例 |
|--------|------------|----------|------|------|
| 意图 | intent | 文本 | 关键词意图类型 | Informational, Commercial, Transactional, Navigational |
| 搜索量 | volume | 整数 | 月搜索量 | 40500 |
| 趋势 | trend | JSON数组 | 12个月趋势数据（0-1范围） | [0.54,0.54,0.44,0.66,0.81,0.81,0.66,1.00,0.66,0.81,0.66,0.81] |
| 关键词难度 | keyword_difficulty | 整数 | 关键词难度（0-100） | 49 |
| CPC (USD) | cpc_usd | 小数 | 每次点击费用（美元） | 0.78 |
| 竞争密度 | competitive_density | 整数 | 竞争密度（0-1） | 1 |
| SERP特征 | serp_features | JSON数组 | SERP特征列表 | ["Sitelinks", "Reviews", "Image"] |
| 搜索结果数 | number_of_results | 整数 | 搜索结果总数 | 74200000 |

### 兼容字段（可选）
| 字段名 | 英文字段名 | 数据类型 | 说明 | 示例 |
|--------|------------|----------|------|------|
| 平均每月搜索量 | avg_monthly_searches | 整数 | 过去12个月平均搜索量 | 40500 |
| 竞争级别 | competition_level | 枚举 | 竞争级别 | low, medium, high |
| 竞争指数 | competition_index | 小数 | 竞争指数（0-100） | 65.5 |
| 出价第20百分位 | low_bid_micros | 整数 | 低出价（微货币单位） | 780000 |
| 出价第80百分位 | high_bid_micros | 整数 | 高出价（微货币单位） | 1200000 |
| 货币代码 | currency_code | 文本 | 货币代码 | USD, CNY |
| 语言代码 | language_code | 文本 | 语言代码 | en-US, zh-CN |
| 分类 | category | 文本 | 关键词分类 | 游戏设备 |
| 标签 | tags | 文本 | 标签（逗号分隔） | 娃娃机,游戏 |

## 数据格式要求

### 1. 趋势数据格式
```json
[0.54,0.54,0.44,0.66,0.81,0.81,0.66,1.00,0.66,0.81,0.66,0.81]
```
- 必须是12个数值的数组
- 数值范围：0-1
- 表示相对趋势强度

### 2. SERP特征格式
```json
["Sitelinks", "Reviews", "Image", "Video", "People also ask"]
```
- 常见SERP特征：
  - Sitelinks（站点链接）
  - Reviews（评论）
  - Image（图片）
  - Video（视频）
  - People also ask（相关问题）
  - Related searches（相关搜索）
  - Popular products（热门产品）
  - Shopping（购物）
  - Local results（本地结果）

### 3. 竞争级别枚举值
- `low` 或 `低`
- `medium` 或 `中`
- `high` 或 `高`

### 4. 意图类型
- `Informational`（信息型）
- `Commercial`（商业型）
- `Transactional`（交易型）
- `Navigational`（导航型）

## 导入注意事项

1. **文件格式**：支持 CSV 和 Excel (.xlsx, .xls) 格式
2. **文件大小**：不超过 10MB
3. **记录数量**：建议单次导入不超过 1000 条记录
4. **字符编码**：CSV 文件请使用 UTF-8 编码
5. **必填字段**：只有"关键词名"是必填的，其他字段都是可选的
6. **重复处理**：相同关键词名会被跳过，不会重复导入

## 示例数据

参考模板文件中的示例数据，确保数据格式正确。

## 错误处理

导入过程中如果某行数据有问题，会跳过该行并在导入结果中显示错误信息，不会影响其他正确数据的导入。
