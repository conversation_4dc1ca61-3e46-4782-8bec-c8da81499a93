import json
import uuid
import logging
import pandas as pd
import asyncio
import threading
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from fastapi import HTTPException, UploadFile
from datetime import datetime, timedelta
from decimal import Decimal
import math

from ..models.keyword_library import (
    KeywordLibrary, GoogleAdsConfig, KeywordUpdateHistory, 
    KeywordImportTask, CompetitionLevel, UpdateMethod, ImportTaskStatus,
    PyTrendsConfig, PyTrendsTaskHistory, PyTrendsTaskStatus  # 添加PyTrends模型
)
from ..schemas.keyword_library import (
    KeywordLibraryCreate, KeywordLibraryUpdate, KeywordSearchRequest,
    GoogleAdsKeywordRequest, KeywordBatchCreate,
    PyTrendsKeywordRequest  # 添加PyTrends请求schema
)

# 可选导入 Google Ads 服务
try:
    from .google_ads_service import GoogleAdsService, GOOGLE_ADS_AVAILABLE
except ImportError:
    GoogleAdsService = None
    GOOGLE_ADS_AVAILABLE = False

# 可选导入 TrendsPy 服务（替代PyTrends）
try:
    from .trendspy_service import TrendsPyService
    TRENDSPY_AVAILABLE = True
except ImportError:
    TrendsPyService = None
    TRENDSPY_AVAILABLE = False

# 保留PyTrends作为备用
try:
    from .pytrends_service import PyTrendsService, PYTRENDS_AVAILABLE
except ImportError:
    PyTrendsService = None
    PYTRENDS_AVAILABLE = False

logger = logging.getLogger(__name__)

def safe_json_serialize(obj):
    """安全地序列化对象为JSON兼容格式"""
    if obj is None:
        return None
    elif isinstance(obj, Decimal):
        return float(obj)
    elif isinstance(obj, datetime):
        return obj.isoformat()
    elif hasattr(obj, 'value'):  # 枚举类型
        return obj.value
    elif isinstance(obj, (int, float, str, bool)):
        return obj
    elif isinstance(obj, (list, tuple)):
        return [safe_json_serialize(item) for item in obj]
    elif isinstance(obj, dict):
        return {key: safe_json_serialize(value) for key, value in obj.items()}
    else:
        # 对于其他对象，尝试转换为字符串
        return str(obj)

class KeywordLibraryService:
    # 类级别的停止事件字典，用于管理所有导入任务的停止信号
    _stop_events = {}
    _stop_events_lock = threading.Lock()

    def __init__(self, db: Session):
        self.db = db
    
    def create_keyword(
        self, 
        keyword_data: KeywordLibraryCreate,
        operator_id: Optional[int] = None,
        operator_name: Optional[str] = None
    ) -> KeywordLibrary:
        """创建关键词"""
        try:
            # 检查关键词是否已存在
            existing = self.db.query(KeywordLibrary).filter(
                KeywordLibrary.keyword_name == keyword_data.keyword_name
            ).first()
            
            if existing:
                raise HTTPException(status_code=400, detail=f"关键词 '{keyword_data.keyword_name}' 已存在")
            
            # 创建关键词
            db_keyword = KeywordLibrary(**keyword_data.model_dump())
            self.db.add(db_keyword)
            self.db.flush()

            # 记录创建历史
            self._create_history_record(
                keyword_id=db_keyword.id,
                update_method=keyword_data.update_method,
                old_data=None,
                new_data=keyword_data.model_dump(),
                operator_id=operator_id,
                operator_name=operator_name
            )
            
            self.db.commit()
            self.db.refresh(db_keyword)
            return db_keyword
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建关键词失败: {e}")
            raise
    
    def update_keyword(
        self,
        keyword_id: int,
        keyword_data: KeywordLibraryUpdate,
        operator_id: Optional[int] = None,
        operator_name: Optional[str] = None
    ) -> KeywordLibrary:
        """更新关键词"""
        try:
            # 获取现有关键词
            keyword = self.db.query(KeywordLibrary).filter(
                KeywordLibrary.id == keyword_id
            ).first()
            
            if not keyword:
                raise HTTPException(status_code=404, detail="关键词不存在")
            
            # 保存旧数据
            old_data = {
                "keyword_name": keyword.keyword_name,
                "avg_monthly_searches": keyword.avg_monthly_searches,
                "monthly_searches": keyword.monthly_searches,
                "competition_level": keyword.competition_level.value if keyword.competition_level else None,
                "competition_index": float(keyword.competition_index) if keyword.competition_index else None,
                "low_bid_micros": keyword.low_bid_micros,
                "high_bid_micros": keyword.high_bid_micros,
                "currency_code": keyword.currency_code,
                "language_code": keyword.language_code,
                "location_ids": keyword.location_ids,
                "update_method": keyword.update_method.value if keyword.update_method else None,
                "tags": keyword.tags
            }
            
            # 更新字段
            update_data = keyword_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(keyword, field, value)
            
            self.db.flush()
            
            # 记录更新历史
            self._create_history_record(
                keyword_id=keyword.id,
                update_method=keyword_data.update_method or keyword.update_method,
                old_data=old_data,
                new_data=update_data,
                operator_id=operator_id,
                operator_name=operator_name
            )
            
            self.db.commit()
            self.db.refresh(keyword)
            return keyword
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新关键词失败: {e}")
            raise
    
    def delete_keyword(self, keyword_id: int) -> bool:
        """删除关键词"""
        try:
            keyword = self.db.query(KeywordLibrary).filter(
                KeywordLibrary.id == keyword_id
            ).first()

            if not keyword:
                raise HTTPException(status_code=404, detail="关键词不存在")

            self.db.delete(keyword)
            self.db.commit()
            return True

        except Exception as e:
            self.db.rollback()
            logger.error(f"删除关键词失败: {e}")
            raise

    def batch_delete_keywords(self, keyword_ids: list[int]) -> dict:
        """批量删除关键词"""
        try:
            deleted_count = 0
            failed_count = 0
            failed_keywords = []

            for keyword_id in keyword_ids:
                try:
                    keyword = self.db.query(KeywordLibrary).filter(
                        KeywordLibrary.id == keyword_id
                    ).first()

                    if keyword:
                        self.db.delete(keyword)
                        deleted_count += 1
                    else:
                        failed_count += 1
                        failed_keywords.append({
                            "id": keyword_id,
                            "error": "关键词不存在"
                        })

                except Exception as e:
                    failed_count += 1
                    failed_keywords.append({
                        "id": keyword_id,
                        "error": str(e)
                    })
                    logger.error(f"删除关键词 {keyword_id} 失败: {e}")

            self.db.commit()

            return {
                "deleted_count": deleted_count,
                "failed_count": failed_count,
                "failed_keywords": failed_keywords
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"批量删除关键词失败: {e}")
            raise HTTPException(status_code=500, detail="批量删除关键词失败")
    
    def get_keyword(self, keyword_id: int) -> KeywordLibrary:
        """获取单个关键词"""
        keyword = self.db.query(KeywordLibrary).filter(
            KeywordLibrary.id == keyword_id
        ).first()
        
        if not keyword:
            raise HTTPException(status_code=404, detail="关键词不存在")
        
        return keyword
    
    def search_keywords(self, search_request: KeywordSearchRequest) -> Tuple[List[KeywordLibrary], int]:
        """搜索关键词"""
        # 基础查询，排除占位符关键词
        query = self.db.query(KeywordLibrary).filter(
            ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
        )
        
        # 关键词搜索
        if search_request.keyword:
            query = query.filter(
                or_(
                    KeywordLibrary.keyword_name.contains(search_request.keyword),
                    KeywordLibrary.tags.contains(search_request.keyword)
                )
            )
        
        # 标签过滤
        if search_request.tags:
            tag_conditions = []
            for tag in search_request.tags:
                tag_conditions.append(KeywordLibrary.tags.contains(tag))
            query = query.filter(or_(*tag_conditions))
        
        # 分类过滤
        if search_request.category:
            if search_request.category == "uncategorized":
                # 查询未分类的关键词（category为null或空字符串）
                query = query.filter(
                    or_(
                        KeywordLibrary.category.is_(None),
                        KeywordLibrary.category == ""
                    )
                )
            else:
                query = query.filter(KeywordLibrary.category == search_request.category)
        
        # 竞争级别过滤
        if search_request.competition_level:
            query = query.filter(KeywordLibrary.competition_level == search_request.competition_level)
        
        # 地理位置过滤
        if search_request.location_ids:
            query = query.filter(KeywordLibrary.location_ids.contains(search_request.location_ids))
        
        # 更新方式过滤
        if search_request.update_method:
            query = query.filter(KeywordLibrary.update_method == search_request.update_method)
        
        # 搜索量范围过滤（兼容旧字段）
        if search_request.min_searches is not None:
            query = query.filter(KeywordLibrary.avg_monthly_searches >= search_request.min_searches)

        if search_request.max_searches is not None:
            query = query.filter(KeywordLibrary.avg_monthly_searches <= search_request.max_searches)

        # 新字段过滤
        # 意图过滤
        if search_request.intent:
            query = query.filter(KeywordLibrary.intent.contains(search_request.intent))

        # 新搜索量范围过滤
        if search_request.min_volume is not None:
            query = query.filter(KeywordLibrary.volume >= search_request.min_volume)

        if search_request.max_volume is not None:
            query = query.filter(KeywordLibrary.volume <= search_request.max_volume)

        # 关键词难度过滤
        if search_request.min_difficulty is not None:
            query = query.filter(KeywordLibrary.keyword_difficulty >= search_request.min_difficulty)

        if search_request.max_difficulty is not None:
            query = query.filter(KeywordLibrary.keyword_difficulty <= search_request.max_difficulty)

        # CPC范围过滤
        if search_request.min_cpc is not None:
            query = query.filter(KeywordLibrary.cpc_usd >= search_request.min_cpc)

        if search_request.max_cpc is not None:
            query = query.filter(KeywordLibrary.cpc_usd <= search_request.max_cpc)

        # 竞争密度过滤
        if search_request.competitive_density is not None:
            query = query.filter(KeywordLibrary.competitive_density == search_request.competitive_density)
        
        # 获取总数
        total = query.count()
        
        # 排序
        if search_request.sort_by == "avg_monthly_searches":
            order_column = KeywordLibrary.avg_monthly_searches
        elif search_request.sort_by == "volume":
            order_column = KeywordLibrary.volume
        elif search_request.sort_by == "keyword_difficulty":
            order_column = KeywordLibrary.keyword_difficulty
        elif search_request.sort_by == "cpc_usd":
            order_column = KeywordLibrary.cpc_usd
        elif search_request.sort_by == "competitive_density":
            order_column = KeywordLibrary.competitive_density
        elif search_request.sort_by == "number_of_results":
            order_column = KeywordLibrary.number_of_results
        elif search_request.sort_by == "competition_index":
            order_column = KeywordLibrary.competition_index
        elif search_request.sort_by == "keyword_name":
            order_column = KeywordLibrary.keyword_name
        elif search_request.sort_by == "created_at":
            order_column = KeywordLibrary.created_at
        else:
            order_column = KeywordLibrary.updated_at
        
        if search_request.sort_order == "asc":
            query = query.order_by(asc(order_column))
        else:
            query = query.order_by(desc(order_column))
        
        # 分页
        offset = (search_request.page - 1) * search_request.page_size
        keywords = query.offset(offset).limit(search_request.page_size).all()
        
        return keywords, total
    
    def get_categories(self) -> List[str]:
        """获取所有分类列表"""
        try:
            # 查询所有非空的分类
            categories = self.db.query(KeywordLibrary.category).filter(
                KeywordLibrary.category.isnot(None),
                KeywordLibrary.category != ""
            ).distinct().all()
            
            # 提取分类名称并排序
            category_list = [cat[0] for cat in categories if cat[0]]
            category_list.sort()
            
            return category_list
        except Exception as e:
            logger.error(f"获取分类列表失败: {e}")
            return []
    
    def get_categories_stats(self) -> Dict[str, int]:
        """获取每个分类的关键词数量统计"""
        try:
            # 查询每个分类的关键词数量，排除占位符关键词
            stats = self.db.query(
                KeywordLibrary.category,
                func.count(KeywordLibrary.id).label('count')
            ).filter(
                KeywordLibrary.category.isnot(None),
                KeywordLibrary.category != "",
                ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
            ).group_by(KeywordLibrary.category).all()
            
            # 转换为字典格式
            result = {}
            for category, count in stats:
                if category:
                    result[category] = count
            
            # 统计未分类的关键词数量
            uncategorized_count = self.db.query(func.count(KeywordLibrary.id)).filter(
                or_(
                    KeywordLibrary.category.is_(None),
                    KeywordLibrary.category == ""
                ),
                ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
            ).scalar()
            
            if uncategorized_count > 0:
                result["uncategorized"] = uncategorized_count
            
            return result
        except Exception as e:
            logger.error(f"获取分类统计失败: {e}")
            return {}
    
    def get_keywords_stats(self) -> Dict[str, Any]:
        """获取关键词统计信息"""
        try:
            # 总数统计，排除占位符关键词
            total_keywords = self.db.query(func.count(KeywordLibrary.id)).filter(
                ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
            ).scalar()
            
            # 按更新方式统计，排除占位符关键词
            by_update_method = {}
            for method in UpdateMethod:
                count = self.db.query(func.count(KeywordLibrary.id)).filter(
                    KeywordLibrary.update_method == method,
                    ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
                ).scalar()
                by_update_method[method.value] = count
            
            # 按竞争级别统计，排除占位符关键词
            by_competition_level = {}
            for level in CompetitionLevel:
                count = self.db.query(func.count(KeywordLibrary.id)).filter(
                    KeywordLibrary.competition_level == level,
                    ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
                ).scalar()
                by_competition_level[level.value] = count
            
            # 平均搜索量，优先使用新的volume字段，排除占位符关键词
            avg_searches = self.db.query(
                func.avg(
                    func.coalesce(KeywordLibrary.volume, KeywordLibrary.avg_monthly_searches)
                )
            ).filter(
                or_(
                    KeywordLibrary.volume.isnot(None),
                    KeywordLibrary.avg_monthly_searches.isnot(None)
                ),
                ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
            ).scalar()

            # 热门关键词（前10），优先使用新的volume字段，排除占位符关键词
            top_keywords = self.db.query(KeywordLibrary).filter(
                or_(
                    KeywordLibrary.volume.isnot(None),
                    KeywordLibrary.avg_monthly_searches.isnot(None)
                ),
                ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
            ).order_by(
                desc(func.coalesce(KeywordLibrary.volume, KeywordLibrary.avg_monthly_searches))
            ).limit(10).all()

            # 新字段统计
            # 意图分布统计
            intent_stats = {}
            intent_results = self.db.query(
                KeywordLibrary.intent,
                func.count(KeywordLibrary.id).label('count')
            ).filter(
                KeywordLibrary.intent.isnot(None),
                ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
            ).group_by(KeywordLibrary.intent).all()

            for intent, count in intent_results:
                intent_stats[intent] = count

            # 关键词难度分布
            difficulty_ranges = {
                'easy': (0, 30),
                'medium': (31, 60),
                'hard': (61, 100)
            }
            difficulty_stats = {}
            for level, (min_val, max_val) in difficulty_ranges.items():
                count = self.db.query(KeywordLibrary).filter(
                    KeywordLibrary.keyword_difficulty >= min_val,
                    KeywordLibrary.keyword_difficulty <= max_val,
                    ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
                ).count()
                difficulty_stats[level] = count

            # 平均CPC
            avg_cpc = self.db.query(func.avg(KeywordLibrary.cpc_usd)).filter(
                KeywordLibrary.cpc_usd.isnot(None),
                ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
            ).scalar()
            
            return {
                "total_keywords": total_keywords,
                "by_update_method": by_update_method,
                "by_competition_level": by_competition_level,
                "avg_monthly_searches": float(avg_searches) if avg_searches else None,
                "top_keywords": top_keywords,
                # 新字段统计
                "by_intent": intent_stats,
                "by_difficulty": difficulty_stats,
                "avg_cpc_usd": float(avg_cpc) if avg_cpc else None
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            raise
    
    def batch_create_keywords(
        self,
        batch_data: KeywordBatchCreate
    ) -> Dict[str, Any]:
        """批量创建关键词"""
        batch_id = batch_data.batch_id or str(uuid.uuid4())
        success_keywords = []
        failed_keywords = []
        
        try:
            for keyword_data in batch_data.keywords:
                try:
                    # 检查是否已存在
                    existing = self.db.query(KeywordLibrary).filter(
                        KeywordLibrary.keyword_name == keyword_data.keyword_name
                    ).first()
                    
                    if existing:
                        # 更新现有关键词
                        update_data = KeywordLibraryUpdate(**keyword_data.model_dump())
                        updated_keyword = self.update_keyword(
                            existing.id,
                            update_data,
                            batch_data.operator_id,
                            batch_data.operator_name
                        )
                        success_keywords.append(updated_keyword)
                    else:
                        # 创建新关键词
                        new_keyword = self.create_keyword(
                            keyword_data,
                            batch_data.operator_id,
                            batch_data.operator_name
                        )
                        success_keywords.append(new_keyword)
                        
                except Exception as e:
                    failed_keywords.append({
                        "keyword_name": keyword_data.keyword_name,
                        "error": str(e)
                    })
                    logger.error(f"批量创建关键词失败 '{keyword_data.keyword_name}': {e}")
            
            return {
                "total_count": len(batch_data.keywords),
                "success_count": len(success_keywords),
                "failed_count": len(failed_keywords),
                "batch_id": batch_id,
                "success_keywords": success_keywords,
                "failed_keywords": failed_keywords
            }
            
        except Exception as e:
            logger.error(f"批量创建关键词失败: {e}")
            raise
    
    def import_keywords_from_google_ads(
        self,
        config_id: int,
        request: GoogleAdsKeywordRequest,
        operator_id: Optional[int] = None,
        operator_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """从 Google Ads API 导入关键词"""
        # 检查 Google Ads SDK 是否可用
        if not GOOGLE_ADS_AVAILABLE:
            raise HTTPException(
                status_code=503,
                detail="Google Ads SDK 未安装。请安装: pip install google-ads"
            )
        
        try:
            # 获取配置
            config = self.db.query(GoogleAdsConfig).filter(
                GoogleAdsConfig.id == config_id,
                GoogleAdsConfig.is_active == True
            ).first()
            
            if not config:
                raise HTTPException(status_code=404, detail="Google Ads 配置不存在或未激活")
            
            # 创建 Google Ads 服务
            ads_config = {
                "developer_token": config.developer_token,
                "client_id": config.client_id,
                "client_secret": config.client_secret,
                "refresh_token": config.refresh_token,
                "customer_id": config.customer_id,
                "login_customer_id": config.login_customer_id,
                "api_key": config.api_key
            }
            
            # 准备代理配置
            proxy_config = None
            if config.use_proxy and config.proxy_host:
                proxy_config = {
                    'use_proxy': config.use_proxy,
                    'proxy_host': config.proxy_host,
                    'proxy_port': config.proxy_port,
                    'proxy_username': config.proxy_username,
                    'proxy_password': config.proxy_password,
                    'proxy_type': config.proxy_type or 'http'
                }
            
            google_ads_service = GoogleAdsService(ads_config, proxy_config)
            
            # 生成关键词提示
            keywords_data = google_ads_service.generate_keyword_ideas(
                seed_keywords=request.seed_keywords,
                url=request.url,
                language_code=request.language_code,
                location_ids=request.location_ids,
                include_adult_keywords=request.include_adult_keywords,
                page_size=request.page_size
            )
            
            # 转换为创建请求
            keyword_creates = []
            for kw_data in keywords_data:
                # 将Google Ads数据映射到新字段结构
                keyword_create = KeywordLibraryCreate(
                    keyword_name=kw_data["keyword_name"],
                    # 新字段映射
                    intent="Commercial",  # Google Ads关键词通常是商业意图
                    volume=kw_data["avg_monthly_searches"],  # 映射到新的volume字段
                    cpc_usd=kw_data.get("low_bid_micros", 0) / 1000000 if kw_data.get("low_bid_micros") else None,  # 转换为美元
                    competitive_density=1 if kw_data["competition_level"] == "HIGH" else 0,  # 简单映射
                    # 原有字段（兼容）
                    avg_monthly_searches=kw_data["avg_monthly_searches"],
                    monthly_searches=kw_data["monthly_searches"],
                    competition_level=CompetitionLevel(kw_data["competition_level"]),
                    competition_index=kw_data["competition_index"],
                    low_bid_micros=kw_data["low_bid_micros"],
                    high_bid_micros=kw_data["high_bid_micros"],
                    currency_code=kw_data["currency_code"],
                    language_code=request.language_code,
                    location_ids=",".join(map(str, request.location_ids)),
                    update_method=UpdateMethod.GOOGLE_ADS_API,
                    tags="google_ads_import",
                    category="Google Ads导入"
                )
                keyword_creates.append(keyword_create)
            
            # 批量创建
            batch_data = KeywordBatchCreate(
                keywords=keyword_creates,
                operator_id=operator_id,
                operator_name=operator_name
            )
            
            result = self.batch_create_keywords(batch_data)
            
            logger.info(f"从 Google Ads 导入完成: 成功 {result['success_count']}, 失败 {result['failed_count']}")
            return result
            
        except Exception as e:
            logger.error(f"从 Google Ads 导入关键词失败: {e}")
            raise
    
    def import_keywords_from_file(
        self,
        file: UploadFile,
        operator_id: Optional[int] = None,
        operator_name: Optional[str] = None
    ) -> str:
        """从文件导入关键词（异步任务）"""
        try:
            # 创建导入任务
            task_id = str(uuid.uuid4())
            file_path = f"uploads/keywords/{task_id}_{file.filename}"

            # 保存文件
            import os
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, "wb") as buffer:
                content = file.file.read()
                buffer.write(content)

            # 创建任务记录
            import_task = KeywordImportTask(
                task_id=task_id,
                file_name=file.filename,
                file_path=file_path,
                status=ImportTaskStatus.pending,
                operator_id=operator_id,
                operator_name=operator_name
            )

            self.db.add(import_task)
            self.db.commit()

            # 为任务创建停止事件
            self._create_stop_event(task_id)

            # 启动异步处理（使用线程处理文件导入）
            thread = threading.Thread(
                target=self._process_import_file_sync,
                args=(task_id,)
            )
            thread.daemon = True
            thread.start()

            return task_id

        except Exception as e:
            logger.error(f"创建导入任务失败: {e}")
            raise

    def import_keywords_from_semrush(
        self,
        file: UploadFile,
        operator_id: Optional[int] = None,
        operator_name: Optional[str] = None,
        category: Optional[str] = None,
        country: Optional[str] = "global"
    ) -> str:
        """从Semrush文件导入关键词（异步任务）"""
        try:
            # 创建导入任务
            task_id = str(uuid.uuid4())
            file_path = f"uploads/keywords/semrush_{task_id}_{file.filename}"

            # 保存文件
            import os
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, "wb") as buffer:
                content = file.file.read()
                buffer.write(content)

            # 创建任务记录
            import_task = KeywordImportTask(
                task_id=task_id,
                file_name=file.filename,
                file_path=file_path,
                status=ImportTaskStatus.pending,
                operator_id=operator_id,
                operator_name=operator_name
            )

            self.db.add(import_task)
            self.db.commit()

            # 为任务创建停止事件
            self._create_stop_event(task_id)

            # 启动异步处理（使用线程处理Semrush导入）
            thread = threading.Thread(
                target=self._process_semrush_import_file_sync,
                args=(task_id, category, country)
            )
            thread.daemon = True
            thread.start()

            return task_id

        except Exception as e:
            logger.error(f"创建Semrush导入任务失败: {e}")
            raise
    
    @classmethod
    def _create_stop_event(cls, task_id: str) -> threading.Event:
        """为任务创建停止事件"""
        with cls._stop_events_lock:
            stop_event = threading.Event()
            cls._stop_events[task_id] = stop_event
            return stop_event

    @classmethod
    def _get_stop_event(cls, task_id: str) -> Optional[threading.Event]:
        """获取任务的停止事件"""
        with cls._stop_events_lock:
            return cls._stop_events.get(task_id)

    @classmethod
    def _remove_stop_event(cls, task_id: str):
        """移除任务的停止事件"""
        with cls._stop_events_lock:
            cls._stop_events.pop(task_id, None)

    @classmethod
    def _signal_stop(cls, task_id: str):
        """发送停止信号"""
        with cls._stop_events_lock:
            stop_event = cls._stop_events.get(task_id)
            if stop_event:
                stop_event.set()
                logger.info(f"已发送停止信号给任务: {task_id}")

    def _check_should_stop(self, task_id: str) -> bool:
        """检查是否应该停止（非阻塞）"""
        stop_event = self._get_stop_event(task_id)
        return stop_event and stop_event.is_set()

    def _process_import_file_sync(self, task_id: str):
        """处理文件导入（同步，在线程中运行）"""
        # 创建新的数据库会话（线程安全）
        from ..db.session import SessionLocal
        db = SessionLocal()

        try:
            # 获取任务
            task = db.query(KeywordImportTask).filter(
                KeywordImportTask.task_id == task_id
            ).first()

            if not task:
                return

            # 更新状态为处理中
            task.status = ImportTaskStatus.processing
            task.progress = 5  # 开始处理
            db.commit()
            
            # 读取文件 - 使用分块读取大文件
            try:
                if task.file_path.endswith('.csv'):
                    # 对于大文件，使用chunksize参数分块读取
                    df = pd.read_csv(task.file_path, encoding='utf-8')
                elif task.file_path.endswith('.xlsx'):
                    df = pd.read_excel(task.file_path, engine='openpyxl')
                else:
                    raise ValueError("不支持的文件格式，请使用 CSV 或 Excel 文件")
            except MemoryError:
                raise ValueError("文件过大，无法加载到内存。请尝试分割文件或联系管理员。")
            except Exception as e:
                raise ValueError(f"文件读取失败: {str(e)}")

            logger.info(f"读取到 {len(df)} 行数据")

            # 更新进度和总数
            task.progress = 10  # 文件读取完成
            task.total_count = len(df)  # 设置总数
            db.commit()

            # 处理数据 - 分批处理
            success_count = 0
            failed_count = 0
            updated_count = 0
            total_rows = len(df)
            batch_size = 500  # 增加批次大小到500条记录，提高处理速度

            logger.info(f"开始处理标准导入数据，总计 {total_rows} 条记录，分批处理，每批 {batch_size} 条")

            for batch_start in range(0, total_rows, batch_size):
                # 检查停止信号
                if self._check_should_stop(task_id):
                    logger.info(f"导入任务收到停止信号: {task_id}")
                    self._remove_stop_event(task_id)
                    return

                batch_end = min(batch_start + batch_size, total_rows)
                batch_df = df.iloc[batch_start:batch_end]

                logger.info(f"处理批次 {batch_start + 1}-{batch_end}/{total_rows}")

                # 批量处理当前批次
                batch_keywords = []
                batch_updates = []
                batch_keyword_names = []

                # 首先收集所有关键词数据
                batch_keyword_data = []
                for index, row in batch_df.iterrows():
                    try:
                        keyword_data = KeywordLibraryCreate(
                            keyword_name=str(row.get('关键词名', row.get('keyword_name', ''))).strip(),
                            # 新字段
                            intent=str(row.get('意图', row.get('intent', ''))).strip() or None,
                            volume=self._safe_int(row.get('搜索量', row.get('volume'))),
                            trend=str(row.get('趋势', row.get('trend', ''))).strip() or None,
                            keyword_difficulty=self._safe_int(row.get('关键词难度', row.get('keyword_difficulty'))),
                            cpc_usd=self._safe_float(row.get('CPC (USD)', row.get('cpc_usd'))),
                            competitive_density=self._safe_int(row.get('竞争密度', row.get('competitive_density'))),
                            serp_features=str(row.get('SERP特征', row.get('serp_features', ''))).strip() or None,
                            number_of_results=self._safe_int(row.get('搜索结果数', row.get('number_of_results'))),
                            # 原有字段（兼容）
                            avg_monthly_searches=self._safe_int(row.get('平均每月搜索量', row.get('avg_monthly_searches'))),
                            competition_level=self._parse_competition_level(row.get('竞争级别', row.get('competition_level'))),
                            competition_index=self._safe_float(row.get('竞争指数', row.get('competition_index'))),
                            low_bid_micros=self._safe_int(row.get('出价第20百分位', row.get('low_bid_micros'))),
                            high_bid_micros=self._safe_int(row.get('出价第80百分位', row.get('high_bid_micros'))),
                            currency_code=str(row.get('货币代码', row.get('currency_code', 'CNY'))).strip(),
                            language_code=str(row.get('语言代码', row.get('language_code', 'zh-CN'))).strip(),
                            update_method=UpdateMethod.batch_import,
                            tags=str(row.get('标签', row.get('tags', ''))).strip(),
                            category=str(row.get('分类', row.get('category', ''))).strip() or None
                        )
                        batch_keyword_data.append(keyword_data)
                        batch_keyword_names.append(keyword_data.keyword_name)

                    except Exception as e:
                        failed_count += 1
                        keyword_name = row.get('关键词名', row.get('keyword_name', 'unknown'))
                        logger.error(f"处理第 {index + 1} 行失败 (关键词: {keyword_name}): {e}")

                # 批量查询现有关键词（考虑关键词名和国家）
                existing_keywords = {}
                if batch_keyword_data:
                    # 构建查询条件：收集所有关键词名和对应的国家
                    keyword_location_pairs = []
                    for keyword_data in batch_keyword_data:
                        location_key = keyword_data.location_ids or 'global'
                        keyword_location_pairs.append((keyword_data.keyword_name, location_key))

                    # 简化查询：先查询所有相关关键词，然后在内存中过滤
                    keyword_names = [pair[0] for pair in keyword_location_pairs]
                    if keyword_names:
                        existing_results = db.query(KeywordLibrary).filter(
                            KeywordLibrary.keyword_name.in_(keyword_names)
                        ).all()

                        # 按关键词名+国家建立索引
                        for kw in existing_results:
                            location_key = kw.location_ids or 'global'
                            key = f"{kw.keyword_name}_{location_key}"
                            existing_keywords[key] = kw

                # 分类处理新增和更新
                for keyword_data in batch_keyword_data:
                    # 构建查找键：关键词名_国家（标准导入通常没有location_ids，使用global）
                    location_key = keyword_data.location_ids or 'global'
                    lookup_key = f"{keyword_data.keyword_name}_{location_key}"

                    if lookup_key in existing_keywords:
                        # 准备更新数据
                        existing_keyword = existing_keywords[lookup_key]
                        update_data = self._convert_create_to_update(keyword_data)
                        batch_updates.append((existing_keyword.id, update_data))
                    else:
                        # 准备创建数据
                        batch_keywords.append(keyword_data)

                # 在数据库操作前检查停止信号
                if self._check_should_stop(task_id):
                    logger.info(f"导入任务在数据库操作前收到停止信号: {task_id}")
                    self._remove_stop_event(task_id)
                    return

                # 批量执行数据库操作
                try:
                    # 真正的批量插入新关键词
                    if batch_keywords:
                        keyword_objects = []
                        for keyword_data in batch_keywords:

                            try:
                                keyword_obj = KeywordLibrary(**keyword_data.model_dump())
                                # 注意：created_by和created_by_name字段不存在于模型中，跳过设置
                                keyword_objects.append(keyword_obj)
                            except Exception as e:
                                failed_count += 1
                                logger.error(f"准备关键词对象失败: {keyword_data.keyword_name}, 错误: {e}")

                        if keyword_objects:
                            try:
                                db.bulk_save_objects(keyword_objects)
                                success_count += len(keyword_objects)
                            except Exception as e:
                                # 批量插入失败，尝试逐个插入
                                logger.warning(f"批量插入失败，尝试逐个插入: {e}")
                                db.rollback()

                                for keyword_obj in keyword_objects:

                                    try:
                                        db.add(keyword_obj)
                                        db.commit()
                                        success_count += 1
                                    except Exception as single_error:
                                        failed_count += 1
                                        db.rollback()
                                        logger.error(f"插入关键词失败: {keyword_obj.keyword_name}, 错误: {single_error}")

                    # 批量更新现有关键词
                    if batch_updates:
                        for keyword_id, update_data in batch_updates:

                            try:
                                # 只更新有效字段，不包含不存在的字段
                                update_dict = update_data.dict(exclude_unset=True)
                                db.query(KeywordLibrary).filter(KeywordLibrary.id == keyword_id).update(update_dict)
                                updated_count += 1
                            except Exception as e:
                                failed_count += 1
                                logger.error(f"更新关键词失败: ID {keyword_id}, 错误: {e}")

                    # 提交当前批次
                    db.commit()

                    # 更新进度和计数
                    progress = int((batch_end / total_rows) * 90)  # 90%为处理完成，留10%给最终操作
                    task.progress = progress
                    task.success_count = success_count
                    task.failed_count = failed_count
                    # 在处理过程中也记录更新统计
                    task.error_message = f"新增: {success_count}, 更新: {updated_count}, 失败: {failed_count}"
                    db.commit()

                    logger.info(f"批次 {batch_start + 1}-{batch_end} 处理完成，进度: {progress}%")

                except Exception as e:
                    logger.error(f"批次处理失败: {e}")
                    db.rollback()
                    failed_count += len(batch_keywords) + len(batch_updates)
            
            # 更新任务状态
            task.total_count = len(df)
            task.success_count = success_count
            task.failed_count = failed_count
            task.progress = 100  # 完成
            task.status = ImportTaskStatus.completed

            # 在error_message中记录更新统计（包含所有情况）
            task.error_message = f"新增: {success_count}, 更新: {updated_count}, 失败: {failed_count}"

            db.commit()

            logger.info(f"文件导入完成: {task_id}, 新增: {success_count}, 更新: {updated_count}, 失败: {failed_count}")

        except Exception as e:
            # 更新任务状态为失败
            try:
                task = db.query(KeywordImportTask).filter(
                    KeywordImportTask.task_id == task_id
                ).first()
                if task:
                    task.status = ImportTaskStatus.failed
                    task.error_message = str(e)
                    db.commit()
            except Exception as update_error:
                logger.error(f"更新标准导入任务状态失败: {update_error}")

            logger.error(f"文件导入失败: {task_id}, 错误: {e}")
        finally:
            # 清理停止事件
            self._remove_stop_event(task_id)
            db.close()

    def _process_semrush_import_file_sync(self, task_id: str, category: Optional[str] = None, country: Optional[str] = "global"):
        """处理Semrush文件导入（同步，在线程中运行）"""
        # 创建新的数据库会话（线程安全）
        from ..db.session import SessionLocal
        db = SessionLocal()

        try:
            # 获取任务
            task = db.query(KeywordImportTask).filter(
                KeywordImportTask.task_id == task_id
            ).first()

            if not task:
                return

            # 更新状态为处理中
            task.status = ImportTaskStatus.processing
            task.progress = 5  # 开始处理
            db.commit()

            # 读取文件 - 使用分块读取大文件
            try:
                if task.file_path.endswith('.csv'):
                    df = pd.read_csv(task.file_path, encoding='utf-8')
                elif task.file_path.endswith('.xlsx'):
                    df = pd.read_excel(task.file_path, engine='openpyxl')
                else:
                    raise ValueError("不支持的文件格式，请使用 CSV 或 Excel 文件")
            except MemoryError:
                raise ValueError("文件过大，无法加载到内存。请尝试分割文件或联系管理员。")
            except Exception as e:
                raise ValueError(f"文件读取失败: {str(e)}")

            logger.info(f"读取到 {len(df)} 行数据")

            # 更新总数
            task.total_count = len(df)
            task.progress = 10  # 文件读取完成
            db.commit()

            # 检查是否有数据
            if len(df) == 0:
                raise ValueError("文件中没有数据行")

            # 检查必要的列是否存在
            required_columns = ['Keyword']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"文件缺少必要的列: {missing_columns}")

            # 从文件名解析信息
            file_info = self._parse_semrush_filename(task.file_name)

            # 处理数据 - 分批处理以提高性能和稳定性
            success_count = 0
            failed_count = 0
            updated_count = 0
            total_rows = len(df)
            batch_size = 500  # 增加批次大小到500条记录，提高处理速度

            logger.info(f"开始处理Semrush数据，总计 {total_rows} 条记录，分批处理，每批 {batch_size} 条")

            for batch_start in range(0, total_rows, batch_size):
                # 检查停止信号
                if self._check_should_stop(task_id):
                    logger.info(f"Semrush导入任务收到停止信号: {task_id}")
                    self._remove_stop_event(task_id)
                    return

                batch_end = min(batch_start + batch_size, total_rows)
                batch_df = df.iloc[batch_start:batch_end]

                logger.info(f"处理批次 {batch_start + 1}-{batch_end}/{total_rows}")

                # 批量处理当前批次
                batch_keywords = []
                batch_updates = []
                batch_keyword_names = []

                # 首先收集所有关键词数据
                batch_keyword_data = []
                for index, row in batch_df.iterrows():
                    try:
                        keyword_data = self._map_semrush_data_to_keyword(row, file_info, category, country)
                        batch_keyword_data.append(keyword_data)
                        batch_keyword_names.append(keyword_data.keyword_name)

                    except Exception as e:
                        failed_count += 1
                        keyword_name = row.get('Keyword', 'unknown')
                        logger.error(f"处理Semrush第 {index + 1} 行失败 (关键词: {keyword_name}): {e}")

                # 批量查询现有关键词（考虑关键词名和国家）
                existing_keywords = {}
                if batch_keyword_data:
                    # 构建查询条件：收集所有关键词名和对应的国家
                    keyword_location_pairs = []
                    for keyword_data in batch_keyword_data:
                        location_key = keyword_data.location_ids or country or 'global'
                        keyword_location_pairs.append((keyword_data.keyword_name, location_key))

                    # 简化查询：先查询所有相关关键词，然后在内存中过滤
                    keyword_names = [pair[0] for pair in keyword_location_pairs]
                    if keyword_names:
                        existing_results = db.query(KeywordLibrary).filter(
                            KeywordLibrary.keyword_name.in_(keyword_names)
                        ).all()

                        # 按关键词名+国家建立索引
                        for kw in existing_results:
                            location_key = kw.location_ids or 'global'
                            key = f"{kw.keyword_name}_{location_key}"
                            existing_keywords[key] = kw

                        logger.info(f"批次 {batch_start + 1}-{batch_end}: 找到 {len(existing_results)} 个现有关键词")

                # 分类处理新增和更新
                for keyword_data in batch_keyword_data:
                    # 构建查找键：关键词名_国家
                    location_key = keyword_data.location_ids or country or 'global'
                    lookup_key = f"{keyword_data.keyword_name}_{location_key}"

                    if lookup_key in existing_keywords:
                        # 准备更新数据
                        existing_keyword = existing_keywords[lookup_key]
                        update_data = self._convert_create_to_update(keyword_data)
                        batch_updates.append((existing_keyword.id, update_data))
                    else:
                        # 准备创建数据
                        batch_keywords.append(keyword_data)

                logger.info(f"批次 {batch_start + 1}-{batch_end}: 新增 {len(batch_keywords)} 个，更新 {len(batch_updates)} 个")

                # 在数据库操作前检查停止信号
                if self._check_should_stop(task_id):
                    logger.info(f"Semrush导入任务在数据库操作前收到停止信号: {task_id}")
                    self._remove_stop_event(task_id)
                    return

                # 批量执行数据库操作
                try:
                    # 真正的批量插入新关键词
                    if batch_keywords:
                        keyword_objects = []
                        for keyword_data in batch_keywords:

                            try:
                                keyword_obj = KeywordLibrary(**keyword_data.model_dump())
                                # 注意：created_by和created_by_name字段不存在于模型中，跳过设置
                                keyword_objects.append(keyword_obj)
                            except Exception as e:
                                failed_count += 1
                                logger.error(f"准备关键词对象失败: {keyword_data.keyword_name}, 错误: {e}")

                        if keyword_objects:
                            try:
                                db.bulk_save_objects(keyword_objects)
                                success_count += len(keyword_objects)
                            except Exception as e:
                                # 批量插入失败，尝试逐个插入
                                logger.warning(f"批量插入失败，尝试逐个插入: {e}")
                                db.rollback()

                                for keyword_obj in keyword_objects:
                                    # 在逐个插入时也检查取消状态
                                    task = db.query(KeywordImportTask).filter(
                                        KeywordImportTask.task_id == task_id
                                    ).first()
                                    if task and task.status == ImportTaskStatus.cancelled:
                                        logger.info(f"Semrush导入任务在逐个插入中被取消: {task_id}")
                                        return

                                    try:
                                        db.add(keyword_obj)
                                        db.commit()
                                        success_count += 1
                                    except Exception as single_error:
                                        failed_count += 1
                                        db.rollback()
                                        logger.error(f"插入关键词失败: {keyword_obj.keyword_name}, 错误: {single_error}")

                    # 批量更新现有关键词
                    if batch_updates:
                        for keyword_id, update_data in batch_updates:
                            # 在每次更新前检查取消状态
                            task = db.query(KeywordImportTask).filter(
                                KeywordImportTask.task_id == task_id
                            ).first()
                            if task and task.status == ImportTaskStatus.cancelled:
                                logger.info(f"Semrush导入任务在更新中被取消: {task_id}")
                                return

                            try:
                                # 只更新有效字段，不包含不存在的字段
                                update_dict = update_data.dict(exclude_unset=True)
                                db.query(KeywordLibrary).filter(KeywordLibrary.id == keyword_id).update(update_dict)
                                updated_count += 1
                            except Exception as e:
                                failed_count += 1
                                logger.error(f"更新关键词失败: ID {keyword_id}, 错误: {e}")

                    # 提交当前批次
                    db.commit()

                    # 更新进度和计数
                    progress = int((batch_end / total_rows) * 90)  # 90%为处理完成，留10%给最终操作
                    task.progress = progress
                    task.success_count = success_count
                    task.failed_count = failed_count
                    # 在处理过程中也记录更新统计
                    task.error_message = f"新增: {success_count}, 更新: {updated_count}, 失败: {failed_count}"
                    db.commit()

                    logger.info(f"批次 {batch_start + 1}-{batch_end} 处理完成，进度: {progress}%")

                except Exception as e:
                    logger.error(f"批次处理失败: {e}")
                    db.rollback()
                    failed_count += len(batch_keywords) + len(batch_updates)

            # 更新任务状态
            task.total_count = len(df)
            task.success_count = success_count
            task.failed_count = failed_count
            task.progress = 100  # 完成
            task.status = ImportTaskStatus.completed

            # 在error_message中记录更新统计（包含所有情况）
            task.error_message = f"新增: {success_count}, 更新: {updated_count}, 失败: {failed_count}"

            db.commit()

            logger.info(f"Semrush文件导入完成: {task_id}, 新增: {success_count}, 更新: {updated_count}, 失败: {failed_count}")

        except Exception as e:
            # 更新任务状态为失败
            try:
                task = db.query(KeywordImportTask).filter(
                    KeywordImportTask.task_id == task_id
                ).first()
                if task:
                    task.status = ImportTaskStatus.failed
                    task.error_message = str(e)
                    db.commit()
            except Exception as update_error:
                logger.error(f"更新Semrush任务状态失败: {update_error}")

            logger.error(f"Semrush文件导入失败: {task_id}, 错误: {e}")
        finally:
            # 清理停止事件
            self._remove_stop_event(task_id)
            db.close()

    def get_import_task(self, task_id: str) -> KeywordImportTask:
        """获取导入任务状态"""
        task = self.db.query(KeywordImportTask).filter(
            KeywordImportTask.task_id == task_id
        ).first()

        if not task:
            raise HTTPException(status_code=404, detail="导入任务不存在")

        return task

    def cancel_import_task(self, task_id: str) -> dict:
        """取消导入任务"""
        task = self.db.query(KeywordImportTask).filter(
            KeywordImportTask.task_id == task_id
        ).first()

        if not task:
            raise HTTPException(status_code=404, detail="导入任务不存在")

        if task.status in [ImportTaskStatus.completed, ImportTaskStatus.failed, ImportTaskStatus.cancelled]:
            raise HTTPException(status_code=400, detail="任务已完成或已取消，无法取消")

        # 发送停止信号给处理线程
        self._signal_stop(task_id)

        # 更新任务状态为已取消
        task.status = ImportTaskStatus.cancelled
        task.error_message = "用户手动取消"
        self.db.commit()

        logger.info(f"导入任务已取消: {task_id}")
        return {"message": "导入任务已取消", "task_id": task_id}
    
    def get_keyword_history(self, keyword_id: int) -> List[KeywordUpdateHistory]:
        """获取关键词更新历史"""
        history = self.db.query(KeywordUpdateHistory).filter(
            KeywordUpdateHistory.keyword_id == keyword_id
        ).order_by(desc(KeywordUpdateHistory.created_at)).all()
        
        return history
    
    def _create_history_record(
        self,
        keyword_id: int,
        update_method: UpdateMethod,
        old_data: Optional[Dict[str, Any]],
        new_data: Optional[Dict[str, Any]],
        operator_id: Optional[int] = None,
        operator_name: Optional[str] = None,
        batch_id: Optional[str] = None
    ):
        """创建历史记录"""
        from app.utils.datetime_utils import utc_now

        history = KeywordUpdateHistory(
            keyword_id=keyword_id,
            update_method=update_method,
            old_data=old_data,
            new_data=new_data,
            batch_id=batch_id,
            operator_id=operator_id,
            operator_name=operator_name
            # 注意：不需要手动设置 created_at，模型会自动使用 utc_now()
        )
        self.db.add(history)
    
    def _safe_int(self, value) -> Optional[int]:
        """安全转换为整数"""
        if pd.isna(value) or value == '' or value is None:
            return None
        try:
            return int(float(value))
        except:
            return None
    
    def _safe_float(self, value) -> Optional[float]:
        """安全转换为浮点数"""
        if pd.isna(value) or value == '' or value is None:
            return None
        try:
            return float(value)
        except:
            return None
    
    def _parse_competition_level(self, value) -> CompetitionLevel:
        """解析竞争级别"""
        if pd.isna(value) or value == '' or value is None:
            return CompetitionLevel.unspecified
        
        value_str = str(value).upper().strip()
        competition_map = {
            'LOW': CompetitionLevel.low,
            'MEDIUM': CompetitionLevel.medium,
            'HIGH': CompetitionLevel.high,
            '低': CompetitionLevel.low,
            '中': CompetitionLevel.medium,
            '高': CompetitionLevel.high,
            '低竞争': CompetitionLevel.low,
            '中竞争': CompetitionLevel.medium,
            '高竞争': CompetitionLevel.high
        }
        
        return competition_map.get(value_str, CompetitionLevel.unspecified)

    def _parse_semrush_filename(self, filename: str) -> Dict[str, str]:
        """解析Semrush文件名获取信息

        文件名格式示例: claw-machine_broad-match_us_2025-06-06.xlsx
        """
        try:
            # 移除文件扩展名
            name_without_ext = filename.rsplit('.', 1)[0]

            # 按下划线分割
            parts = name_without_ext.split('_')

            file_info = {
                'keyword_base': '',
                'match_type': '',
                'country': '',
                'date': '',
                'category': 'Semrush导入'
            }

            if len(parts) >= 1:
                file_info['keyword_base'] = parts[0].replace('-', ' ')
            if len(parts) >= 2:
                file_info['match_type'] = parts[1]
            if len(parts) >= 3:
                file_info['country'] = parts[2].upper()
            if len(parts) >= 4:
                file_info['date'] = parts[3]

            # 设置分类
            if file_info['keyword_base']:
                file_info['category'] = f"Semrush-{file_info['keyword_base']}"
            else:
                file_info['category'] = "Semrush导入"

            return file_info

        except Exception as e:
            logger.warning(f"解析Semrush文件名失败: {filename}, 错误: {e}")
            return {
                'keyword_base': '',
                'match_type': 'broad',
                'country': 'US',
                'date': '',
                'category': 'Semrush导入'
            }

    def _map_semrush_data_to_keyword(self, row, file_info: Dict[str, str], category: Optional[str] = None, country: Optional[str] = "global") -> KeywordLibraryCreate:
        """将Semrush数据映射到关键词创建对象"""

        # 获取关键词名称（Semrush标准字段名）
        keyword_name = row.get('Keyword')
        if not keyword_name or pd.isna(keyword_name):
            raise ValueError("未找到Keyword字段")

        # 获取意图信息
        intent_value = row.get('Intent', '')
        if pd.isna(intent_value):
            intent_value = ''

        # 处理多重意图（如"Informational, Commercial"）
        if intent_value and ',' in str(intent_value):
            # 取第一个意图作为主要意图
            intent_value = str(intent_value).split(',')[0].strip()

        # 获取趋势数据并转换为JSON格式
        trend_value = row.get('Trend', '')
        trend_json = self._parse_semrush_trend_data(trend_value)

        # 获取SERP特征并转换为JSON格式
        serp_features_value = row.get('SERP Features', '')
        serp_features_json = self._parse_semrush_serp_features(serp_features_value)

        # 获取竞争密度
        competitive_density = self._parse_semrush_competitive_density(row.get('Competitive Density'))

        # 映射数据
        try:
            keyword_data = KeywordLibraryCreate(
                keyword_name=str(keyword_name).strip(),

                # 新字段映射
                intent=str(intent_value).strip() if intent_value else "Commercial",  # 默认商业意图
                volume=self._safe_int(row.get('Volume')),
                keyword_difficulty=self._safe_keyword_difficulty(row.get('Keyword Difficulty')),
                cpc_usd=self._safe_cpc(row.get('CPC (USD)')),
                competitive_density=competitive_density,
                number_of_results=self._safe_int(row.get('Number of Results')),
                serp_features=serp_features_json,
                trend=trend_json,

                # 兼容字段
                avg_monthly_searches=self._safe_int(row.get('Volume')),
                competition_level=self._parse_competition_level_from_density(row.get('Competitive Density')),
                currency_code="USD",
                language_code="en-US" if country == 'us' else "zh-CN",
                update_method=UpdateMethod.batch_import,
                category=category or file_info['category'] or "Semrush导入",  # 优先使用用户选择的分类
                tags=f"semrush,{file_info['match_type']},{country},{file_info['date']}",
                location_ids=country if country != "global" else None  # 设置国家信息
            )
        except Exception as e:
            logger.error(f"创建KeywordLibraryCreate对象失败: {keyword_name}, 错误: {e}")
            raise ValueError(f"数据映射失败: {e}")

        return keyword_data

    def _get_field_value(self, row, field_names: List[str]):
        """从行数据中获取字段值，支持多个可能的字段名"""
        for field_name in field_names:
            if field_name in row and pd.notna(row[field_name]):
                return row[field_name]
        return None

    def _determine_intent(self, row, field_mapping: Dict, file_info: Dict[str, str]) -> str:
        """确定关键词意图"""
        # 首先尝试从数据中获取意图
        intent_value = self._get_field_value(row, field_mapping['intent'])
        if intent_value:
            return str(intent_value).strip()

        # 根据文件信息和关键词特征推断意图
        keyword = str(self._get_field_value(row, field_mapping['keyword'])).lower()

        # 商业意图关键词
        commercial_indicators = ['buy', 'purchase', 'price', 'cost', 'cheap', 'best', 'review', 'compare']
        if any(indicator in keyword for indicator in commercial_indicators):
            return "Commercial"

        # 信息意图关键词
        info_indicators = ['how', 'what', 'why', 'when', 'where', 'guide', 'tutorial', 'tips']
        if any(indicator in keyword for indicator in info_indicators):
            return "Informational"

        # 导航意图关键词
        nav_indicators = ['login', 'website', 'official', 'site']
        if any(indicator in keyword for indicator in nav_indicators):
            return "Navigational"

        # 默认为商业意图（Semrush数据通常偏向商业）
        return "Commercial"

    def _parse_competitive_density(self, value) -> Optional[int]:
        """解析竞争密度值"""
        if pd.isna(value) or value == '' or value is None:
            return None

        try:
            # 如果是百分比格式，转换为0-1范围
            if isinstance(value, str) and '%' in value:
                percent_value = float(value.replace('%', ''))
                return 1 if percent_value > 50 else 0

            # 如果是小数，直接使用
            float_value = float(value)
            if 0 <= float_value <= 1:
                return int(float_value)
            elif float_value > 1:
                # 如果大于1，假设是百分比
                return 1 if float_value > 50 else 0

            return 0
        except:
            return None

    def _parse_competition_level_from_density(self, value) -> CompetitionLevel:
        """根据竞争密度解析竞争级别"""
        if pd.isna(value) or value == '' or value is None:
            return CompetitionLevel.unspecified

        try:
            if isinstance(value, str) and '%' in value:
                percent_value = float(value.replace('%', ''))
                if percent_value < 30:
                    return CompetitionLevel.low
                elif percent_value < 70:
                    return CompetitionLevel.medium
                else:
                    return CompetitionLevel.high

            float_value = float(value)
            if float_value < 0.3:
                return CompetitionLevel.low
            elif float_value < 0.7:
                return CompetitionLevel.medium
            else:
                return CompetitionLevel.high

        except:
            return CompetitionLevel.unspecified

    def _parse_serp_features(self, value) -> Optional[str]:
        """解析SERP特征"""
        if pd.isna(value) or value == '' or value is None:
            return None

        try:
            # 如果已经是JSON格式，直接返回
            if isinstance(value, str) and (value.startswith('[') or value.startswith('{')):
                return value

            # 如果是逗号分隔的字符串，转换为JSON数组
            if isinstance(value, str):
                features = [f.strip() for f in value.split(',') if f.strip()]
                return json.dumps(features, ensure_ascii=False)

            return None
        except:
            return None

    def _parse_trend_data(self, value) -> Optional[str]:
        """解析趋势数据"""
        if pd.isna(value) or value == '' or value is None:
            return None

        try:
            # 如果已经是JSON格式，直接返回
            if isinstance(value, str) and value.startswith('['):
                return value

            # 如果是单个数值，创建12个月的平均趋势
            if isinstance(value, (int, float)):
                normalized_value = min(1.0, max(0.0, float(value) / 100))
                trend_array = [normalized_value] * 12
                return json.dumps(trend_array)

            return None
        except:
            return None

    def _parse_semrush_trend_data(self, value) -> Optional[str]:
        """解析Semrush趋势数据"""
        if pd.isna(value) or value == '' or value is None:
            return None

        try:
            # Semrush趋势数据格式：0.54,0.54,0.44,0.66,0.81,0.81,0.66,1.00,0.66,0.81,0.66,0.81
            if isinstance(value, str):
                # 移除引号
                trend_str = value.strip('"').strip("'")
                # 分割并转换为浮点数
                trend_values = [float(x.strip()) for x in trend_str.split(',') if x.strip()]

                # 确保有12个值（如果不足12个，用最后一个值填充）
                while len(trend_values) < 12:
                    if trend_values:
                        trend_values.append(trend_values[-1])
                    else:
                        trend_values.append(0.5)  # 默认值

                # 如果超过12个值，只取前12个
                if len(trend_values) > 12:
                    trend_values = trend_values[:12]

                # 只取前12个值
                trend_values = trend_values[:12]

                return json.dumps(trend_values)

            return None
        except Exception as e:
            logger.warning(f"解析Semrush趋势数据失败: {value}, 错误: {e}")
            return None

    def _parse_semrush_serp_features(self, value) -> Optional[str]:
        """解析Semrush SERP特征数据"""
        if pd.isna(value) or value == '' or value is None:
            return None

        try:
            # Semrush SERP特征格式：Sitelinks, Reviews, Image, Video, People also ask, Related searches, Popular products, Things to know
            if isinstance(value, str):
                # 移除外层引号
                features_str = value.strip('"').strip("'")
                # 分割并清理
                features = [f.strip() for f in features_str.split(',') if f.strip()]

                return json.dumps(features, ensure_ascii=False)

            return None
        except Exception as e:
            logger.warning(f"解析Semrush SERP特征失败: {value}, 错误: {e}")
            return None

    def _parse_semrush_competitive_density(self, value) -> Optional[float]:
        """解析Semrush竞争密度"""
        if pd.isna(value) or value == '' or value is None:
            return None

        try:
            # Semrush竞争密度通常是0-1的小数
            float_value = float(value)

            # 确保值在0-1范围内
            if float_value > 1:
                # 如果大于1，可能是百分比格式，除以100
                float_value = float_value / 100

            # 限制在0-1范围内，保留两位小数
            float_value = max(0.0, min(1.0, float_value))

            # 返回保留两位小数的浮点数
            return round(float_value, 2)

        except Exception as e:
            logger.warning(f"解析Semrush竞争密度失败: {value}, 错误: {e}")
            return None

    def _convert_create_to_update(self, create_data: KeywordLibraryCreate) -> KeywordLibraryUpdate:
        """将KeywordLibraryCreate转换为KeywordLibraryUpdate"""
        from ..schemas.keyword_library import KeywordLibraryUpdate

        # 获取create_data的所有字段
        create_dict = create_data.model_dump()

        # 移除不能更新的字段（如果有的话）
        # keyword_name通常不允许更新，但这里我们保留它以防需要

        return KeywordLibraryUpdate(**create_dict)

    def _safe_keyword_difficulty(self, value) -> Optional[int]:
        """安全转换关键词难度（0-100）"""
        if pd.isna(value) or value == '' or value is None:
            return None

        try:
            int_value = int(float(value))
            # 限制在0-100范围内
            return max(0, min(100, int_value))
        except:
            return None

    def _safe_cpc(self, value) -> Optional[float]:
        """安全转换CPC值（>=0）"""
        if pd.isna(value) or value == '' or value is None:
            return None

        try:
            float_value = float(value)
            # 确保非负数
            return max(0.0, float_value)
        except:
            return None
    
    def import_keywords_from_pytrends(
        self,
        config_id: int,
        request: PyTrendsKeywordRequest,
        operator_id: Optional[int] = None,
        operator_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """从 TrendsPy 导入关键词（替代PyTrends）- 异步处理版本"""
        # 检查 TrendsPy 是否可用
        if not TRENDSPY_AVAILABLE:
            raise HTTPException(
                status_code=503,
                detail="TrendsPy 未安装。请安装: pip install trendspy"
            )
        
        try:
            # 获取配置
            config = self.db.query(PyTrendsConfig).filter(
                PyTrendsConfig.id == config_id,
                PyTrendsConfig.is_active == True
            ).first()
            
            if not config:
                raise HTTPException(status_code=404, detail="PyTrends 配置不存在或未激活")
            
            # 创建任务ID和任务记录
            from app.utils.datetime_utils import utc_now
            task_id = str(uuid.uuid4())
            task_history = PyTrendsTaskHistory(
                task_id=task_id,
                config_id=config_id,
                seed_keywords=json.dumps(request.seed_keywords, ensure_ascii=False),
                search_parameters=json.dumps({
                    "timeframe": request.timeframe,
                    "geo_location": request.geo_location,
                    "category": request.category,
                    "include_related": request.include_related,
                    "include_rising": request.include_rising,
                    "service": "trendspy"
                }, ensure_ascii=False),
                status=PyTrendsTaskStatus.running,
                progress=0,
                total_keywords=0,
                processed_keywords=0,
                success_keywords=0,
                failed_keywords=0,
                started_at=utc_now()
            )
            
            self.db.add(task_history)
            self.db.commit()
            
            # 启动异步处理 - 使用线程池而不是asyncio.create_task
            thread = threading.Thread(
                target=self._process_pytrends_import_sync,
                args=(task_id, config_id, request, operator_id, operator_name)
            )
            thread.daemon = True
            thread.start()
            
            logger.info(f"PyTrends导入任务已启动: {task_id}")
            
            # 立即返回任务ID
            return {
                "task_id": task_id,
                "message": "导入任务已启动，请使用task_id查询进度",
                "status": "started"
            }
            
        except Exception as e:
            logger.error(f"启动 TrendsPy 导入任务失败: {e}")
            raise
    
    def _process_pytrends_import_sync(
        self,
        task_id: str,
        config_id: int,
        request: PyTrendsKeywordRequest,
        operator_id: Optional[int] = None,
        operator_name: Optional[str] = None
    ):
        """同步处理PyTrends导入任务（在单独线程中运行）"""
        # 创建新的数据库会话（线程安全）
        from ..db.session import SessionLocal
        db = SessionLocal()
        
        try:
            # 获取任务记录
            task = db.query(PyTrendsTaskHistory).filter(
                PyTrendsTaskHistory.task_id == task_id
            ).first()
            
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
            
            # 获取配置
            config = db.query(PyTrendsConfig).filter(
                PyTrendsConfig.id == config_id,
                PyTrendsConfig.is_active == True
            ).first()
            
            if not config:
                from app.utils.datetime_utils import utc_now
                task.status = PyTrendsTaskStatus.failed
                task.error_message = "PyTrends 配置不存在或未激活"
                task.completed_at = utc_now()
                db.commit()
                return
            
            logger.info(f"开始处理PyTrends导入任务: {task_id}")
            
            # 更新任务状态
            task.status = PyTrendsTaskStatus.running
            task.progress = 10
            db.commit()
            
            # 创建 TrendsPy 服务配置
            trendspy_config = {
                "language": config.language,
                "timezone": config.timezone,
                "geo_location": config.geo_location,
                "default_timeframe": config.default_timeframe,
                "max_keywords_per_batch": config.max_keywords_per_batch,
                "request_delay": config.request_delay,
                "retry_attempts": config.retry_attempts
            }
            
            # 准备代理配置
            proxy_config = None
            if config.use_proxy and config.proxy_host:
                proxy_config = {
                    'use_proxy': config.use_proxy,
                    'proxy_host': config.proxy_host,
                    'proxy_port': config.proxy_port,
                    'proxy_username': config.proxy_username,
                    'proxy_password': config.proxy_password,
                    'proxy_type': config.proxy_type or 'http'
                }
            
            # 使用TrendsPy服务替代PyTrends
            trendspy_service = TrendsPyService(trendspy_config, proxy_config)
            logger.info("使用TrendsPy服务替代PyTrends进行关键词导入")
            
            # 更新进度
            task.progress = 30
            db.commit()
            
            # 生成关键词建议
            keywords_suggestions = trendspy_service.generate_keyword_ideas(
                seed_keywords=request.seed_keywords,
                timeframe=request.timeframe,
                geo_location=request.geo_location,
                include_related=request.include_related,
                include_rising=request.include_rising
            )
            
            # 更新进度
            task.progress = 60
            task.total_keywords = len(keywords_suggestions)
            db.commit()
            
            # 转换TrendsPy数据为关键词创建格式
            keyword_creates = []
            for suggestion in keywords_suggestions:
                try:
                    # 处理relevance_score，确保在0-100范围内
                    raw_score = suggestion.get("relevance_score", 0)
                    # 如果分数超过100，进行标准化处理
                    if raw_score > 100:
                        # 使用分段线性映射：
                        # 100-1000: 映射到70-90
                        # 1000-10000: 映射到90-99
                        # >10000: 固定为99
                        if raw_score <= 1000:
                            normalized_score = int(70 + (raw_score - 100) / 900 * 20)
                        elif raw_score <= 10000:
                            normalized_score = int(90 + (raw_score - 1000) / 9000 * 9)
                        else:
                            normalized_score = 99
                    else:
                        normalized_score = min(100, max(0, int(raw_score)))
                    
                    keyword_create = KeywordLibraryCreate(
                        keyword_name=suggestion["keyword"],
                        avg_monthly_searches=None,  # TrendsPy不直接提供搜索量
                        monthly_searches=None,
                        competition_level=CompetitionLevel.unspecified,  # TrendsPy不直接提供竞争级别
                        competition_index=None,
                        # TrendsPy特有字段映射 - 使用标准化后的分数
                        trend_interest=normalized_score,
                        trend_growth_rate=None,
                        trend_region=request.geo_location,
                        trend_timeframe=request.timeframe,
                        trend_data=json.dumps({
                            "source_keyword": suggestion.get("source_keyword"),
                            "type": suggestion.get("type"),
                            "category": suggestion.get("category"),
                            "topic_type": suggestion.get("topic_type"),
                            "topic_mid": suggestion.get("topic_mid"),
                            "original_score": raw_score  # 保存原始分数用于参考
                        }, ensure_ascii=False),
                        low_bid_micros=None,
                        high_bid_micros=None,
                        currency_code="USD",
                        language_code=config.language or "en-US",
                        location_ids=request.geo_location,
                        update_method=UpdateMethod.pytrends,  # 保持原有枚举值
                        tags=f"trendspy_import,{suggestion.get('category', '')},score_{normalized_score}",
                        category=request.category or "TrendsPy导入"  # 使用用户指定的分类
                    )
                    keyword_creates.append(keyword_create)
                except Exception as e:
                    logger.warning(f"转换关键词建议失败: {suggestion.get('keyword', 'unknown')}, 错误: {e}")
                    continue
            
            if not keyword_creates:
                logger.warning("没有成功转换的关键词建议")
                from app.utils.datetime_utils import utc_now
                task.status = PyTrendsTaskStatus.completed
                task.progress = 100
                task.processed_keywords = 0
                task.success_keywords = 0
                task.failed_keywords = 0
                task.completed_at = utc_now()
                task.result_summary = json.dumps({
                    "total_count": 0,
                    "success_count": 0,
                    "failed_count": 0,
                    "message": "没有获取到有效的关键词建议"
                }, ensure_ascii=False)
                db.commit()
                return
            
            # 更新进度
            task.progress = 80
            task.processed_keywords = len(keyword_creates)
            db.commit()
            
            # 使用新的数据库会话批量创建关键词
            keyword_service = KeywordLibraryService(db)
            batch_data = KeywordBatchCreate(
                keywords=keyword_creates,
                operator_id=operator_id,
                operator_name=operator_name
            )
            
            result = keyword_service.batch_create_keywords(batch_data)
            
            # 创建可序列化的结果摘要（排除SQLAlchemy对象）
            serializable_result = {
                "total_count": result["total_count"],
                "success_count": result["success_count"],
                "failed_count": result["failed_count"],
                "batch_id": result["batch_id"],
                "success_keywords": [
                    {
                        "id": safe_json_serialize(kw.id),
                        "keyword_name": safe_json_serialize(kw.keyword_name),
                        "avg_monthly_searches": safe_json_serialize(kw.avg_monthly_searches),
                        "competition_level": safe_json_serialize(kw.competition_level),
                        "trend_interest": safe_json_serialize(kw.trend_interest),
                        "trend_growth_rate": safe_json_serialize(kw.trend_growth_rate),
                        "competition_index": safe_json_serialize(kw.competition_index),
                        "created_at": safe_json_serialize(kw.created_at)
                    }
                    for kw in result["success_keywords"]
                ],
                "failed_keywords": result["failed_keywords"]
            }
            
            # 更新任务状态为完成
            from app.utils.datetime_utils import utc_now
            task.status = PyTrendsTaskStatus.completed
            task.progress = 100
            task.success_keywords = result['success_count']
            task.failed_keywords = result['failed_count']
            task.result_summary = json.dumps(serializable_result, ensure_ascii=False)
            task.completed_at = utc_now()
            
            db.commit()
            
            logger.info(f"TrendsPy导入任务完成: {task_id}, 成功: {result['success_count']}, 失败: {result['failed_count']}")
            
        except Exception as e:
            logger.error(f"处理TrendsPy导入任务失败: {task_id}, 错误: {e}")
            # 更新任务状态为失败
            try:
                task = db.query(PyTrendsTaskHistory).filter(
                    PyTrendsTaskHistory.task_id == task_id
                ).first()
                if task:
                    from app.utils.datetime_utils import utc_now
                    task.status = PyTrendsTaskStatus.failed
                    task.error_message = str(e)
                    task.completed_at = utc_now()
                    db.commit()
            except Exception as update_error:
                logger.error(f"更新任务状态失败: {update_error}")
        finally:
            db.close()
    
    def get_pytrends_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取PyTrends导入任务状态"""
        try:
            task = self.db.query(PyTrendsTaskHistory).filter(
                PyTrendsTaskHistory.task_id == task_id
            ).first()
            
            if not task:
                raise HTTPException(status_code=404, detail="任务不存在")
            
            return {
                "task_id": task.task_id,
                "status": task.status.value if task.status else "unknown",
                "progress": task.progress or 0,
                "total_keywords": task.total_keywords or 0,
                "processed_keywords": task.processed_keywords or 0,
                "success_keywords": task.success_keywords or 0,
                "failed_keywords": task.failed_keywords or 0,
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "error_message": task.error_message,
                "result_summary": json.loads(task.result_summary) if task.result_summary else None
            }
            
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            raise

    def create_category(self, name: str) -> Dict[str, Any]:
        """创建新分类（通过创建占位符关键词实现）"""
        try:
            # 验证分类名称
            if not name or not name.strip():
                raise HTTPException(status_code=400, detail="分类名称不能为空")
            
            name = name.strip()
            
            # 检查分类是否已存在
            existing_categories = self.get_categories()
            if name in existing_categories:
                raise HTTPException(status_code=400, detail="分类名称已存在")
            
            # 创建一个占位符关键词来确保分类存在
            placeholder_keyword = KeywordLibrary(
                keyword_name=f"_category_placeholder_{name}",
                avg_monthly_searches=0,
                competition_level=CompetitionLevel.unspecified,
                competition_index=0.0,
                low_bid_micros=0,
                high_bid_micros=0,
                currency_code="USD",
                language_code="en-US",
                location_ids="",
                update_method=UpdateMethod.manual,
                tags=f"category_placeholder,{name}",
                category=name
            )
            
            self.db.add(placeholder_keyword)
            self.db.commit()
            
            return {
                "message": f"分类 '{name}' 创建成功", 
                "name": name,
                "success": True
            }
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建分类失败: {e}")
            raise HTTPException(status_code=500, detail="创建分类失败")

    def update_category(self, old_name: str, new_name: str) -> Dict[str, Any]:
        """更新分类名称"""
        try:
            # 验证参数
            if not old_name or not old_name.strip():
                raise HTTPException(status_code=400, detail="原分类名称不能为空")
            if not new_name or not new_name.strip():
                raise HTTPException(status_code=400, detail="新分类名称不能为空")
            
            old_name = old_name.strip()
            new_name = new_name.strip()
            
            if old_name == new_name:
                return {"message": "分类名称没有变化", "updated_count": 0}
            
            # 检查原分类是否存在
            existing_categories = self.get_categories()
            if old_name not in existing_categories:
                raise HTTPException(status_code=404, detail="原分类不存在")
            
            # 检查新分类名称是否已存在
            if new_name in existing_categories:
                raise HTTPException(status_code=400, detail="新分类名称已存在")
            
            # 更新所有使用该分类的关键词
            updated_count = self.db.query(KeywordLibrary).filter(
                KeywordLibrary.category == old_name
            ).update({"category": new_name})
            
            self.db.commit()
            
            return {
                "message": f"成功将分类 '{old_name}' 更新为 '{new_name}'",
                "updated_count": updated_count
            }
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新分类失败: {e}")
            raise HTTPException(status_code=500, detail="更新分类失败")

    def delete_category(self, name: str) -> Dict[str, Any]:
        """删除分类（将使用该分类的关键词设为未分类，并删除占位符关键词）"""
        try:
            # 验证参数
            if not name or not name.strip():
                raise HTTPException(status_code=400, detail="分类名称不能为空")

            name = name.strip()

            # 检查分类是否存在
            existing_categories = self.get_categories()
            if name not in existing_categories:
                raise HTTPException(status_code=404, detail="分类不存在")

            # 将使用该分类的真实关键词设为 None（未分类）
            updated_count = self.db.query(KeywordLibrary).filter(
                KeywordLibrary.category == name,
                ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
            ).update({"category": None}, synchronize_session=False)

            # 删除占位符关键词
            deleted_placeholder = self.db.query(KeywordLibrary).filter(
                KeywordLibrary.category == name,
                KeywordLibrary.keyword_name.startswith("_category_placeholder_")
            ).delete(synchronize_session=False)

            self.db.commit()

            return {
                "message": f"成功删除分类 '{name}'，{updated_count} 个关键词已设为未分类",
                "updated_count": updated_count,
                "deleted_placeholder": deleted_placeholder
            }

        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除分类失败: {e}")
            raise HTTPException(status_code=500, detail="删除分类失败")

    def clear_uncategorized_keywords(self) -> Dict[str, Any]:
        """清空未分类关键词（删除所有category为None或空的关键词，排除占位符关键词）"""
        try:
            # 查询未分类的真实关键词（排除占位符关键词）
            uncategorized_keywords = self.db.query(KeywordLibrary).filter(
                or_(
                    KeywordLibrary.category.is_(None),
                    KeywordLibrary.category == ""
                ),
                ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
            ).all()

            if not uncategorized_keywords:
                return {
                    "message": "未分类中没有关键词",
                    "deleted_count": 0
                }

            # 记录要删除的关键词数量
            deleted_count = len(uncategorized_keywords)

            # 删除未分类的关键词
            self.db.query(KeywordLibrary).filter(
                or_(
                    KeywordLibrary.category.is_(None),
                    KeywordLibrary.category == ""
                ),
                ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
            ).delete(synchronize_session=False)

            self.db.commit()

            return {
                "message": f"成功清空未分类关键词，共删除 {deleted_count} 个关键词",
                "deleted_count": deleted_count
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"清空未分类关键词失败: {e}")
            raise HTTPException(status_code=500, detail="清空未分类关键词失败")

    def batch_update_category(self, keyword_ids: List[int], category: str) -> Dict[str, Any]:
        """批量更新关键词分类"""
        try:
            # 验证参数
            if not keyword_ids:
                raise HTTPException(status_code=400, detail="关键词ID列表不能为空")
            if not category or not category.strip():
                raise HTTPException(status_code=400, detail="分类名称不能为空")
            
            category = category.strip()
            
            # 查询存在的关键词
            existing_keywords = self.db.query(KeywordLibrary).filter(
                KeywordLibrary.id.in_(keyword_ids)
            ).all()
            
            if not existing_keywords:
                raise HTTPException(status_code=404, detail="没有找到指定的关键词")
            
            existing_ids = [kw.id for kw in existing_keywords]
            not_found_ids = [kid for kid in keyword_ids if kid not in existing_ids]
            
            # 批量更新分类
            updated_count = self.db.query(KeywordLibrary).filter(
                KeywordLibrary.id.in_(existing_ids)
            ).update({"category": category}, synchronize_session=False)
            
            self.db.commit()
            
            result = {
                "message": f"成功更新 {updated_count} 个关键词的分类为 '{category}'",
                "success": True,
                "updated_count": updated_count,
                "keyword_ids": existing_ids
            }
            
            if not_found_ids:
                result["message"] += f"，{len(not_found_ids)} 个关键词ID不存在"
                result["not_found_ids"] = not_found_ids
            
            return result
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"批量更新分类失败: {e}")
            raise HTTPException(status_code=500, detail="批量更新分类失败") 