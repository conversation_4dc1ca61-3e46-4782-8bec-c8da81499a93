<template>
  <el-dialog
    v-model="dialogVisible"
    title="PyTrends 关键词导入"
    width="800px"
    :close-on-click-modal="false"
    @closed="handleDialogClosed"
  >
    <div class="pytrends-import-container">
      <!-- 配置选择区域 -->
      <div class="config-section">
        <div class="section-header">
          <el-icon class="header-icon"><Setting /></el-icon>
          <span>PyTrends 配置</span>
          <el-button 
            type="primary" 
            link 
            @click="showConfigDialog = true"
            size="small"
          >
            <el-icon><Plus /></el-icon>
            管理配置
          </el-button>
        </div>
        
        <div class="config-selection">
          <el-select 
            v-model="selectedConfigId" 
            placeholder="请选择 PyTrends 配置"
            style="width: 100%;"
            :loading="configsLoading"
            @change="handleConfigChange"
          >
            <el-option
              v-for="config in configList"
              :key="config.id"
              :label="config.config_name"
              :value="config.id"
            >
              <div class="config-option">
                <span>{{ config.config_name }}</span>
                <el-tag 
                  v-if="config.is_active" 
                  type="success" 
                  size="small"
                  style="margin-left: 10px;"
                >
                  活跃
                </el-tag>
              </div>
            </el-option>
          </el-select>
          
          <div class="config-actions" v-if="selectedConfig">
            <el-button 
              type="primary" 
              link 
              @click="testConnection" 
              :loading="testing"
              size="small"
            >
              <el-icon><Link /></el-icon>
              测试连接
            </el-button>
          </div>
        </div>
        
        <!-- 配置信息显示 -->
        <div class="config-info" v-if="selectedConfig">
          <el-descriptions :column="2" size="small" border>
            <el-descriptions-item label="语言">{{ selectedConfig.language }}</el-descriptions-item>
            <el-descriptions-item label="地理位置">{{ selectedConfig.geo_location }}</el-descriptions-item>
            <el-descriptions-item label="默认时间范围">{{ selectedConfig.default_timeframe }}</el-descriptions-item>
            <el-descriptions-item label="批次大小">{{ selectedConfig.max_keywords_per_batch }} 个/批</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <!-- 导入参数设置 -->
      <div class="import-section">
        <div class="section-header">
          <el-icon class="header-icon"><Search /></el-icon>
          <span>导入参数</span>
        </div>
        
        <el-form :model="importForm" :rules="rules" ref="formRef" label-width="120px">
          <el-form-item label="种子关键词" prop="seed_keywords" required>
            <el-input
              v-model="seedKeywordsText"
              type="textarea"
              :rows="3"
              placeholder="请输入种子关键词，每行一个或用逗号分隔（最多5个）"
              show-word-limit
              maxlength="500"
            />
            <div class="help-text">
              <small class="text-muted">
                输入相关的种子关键词，PyTrends 会基于这些词生成趋势数据和相关关键词
              </small>
            </div>
          </el-form-item>

          <el-form-item label="时间范围" prop="timeframe">
            <el-select v-model="importForm.timeframe" placeholder="选择时间范围">
              <el-option label="过去12个月" value="today 12-m" />
              <el-option label="过去5年" value="today 5-y" />
              <el-option label="过去3个月" value="today 3-m" />
              <el-option label="过去30天" value="today 1-m" />
              <el-option label="过去7天" value="now 7-d" />
              <el-option label="过去1天" value="now 1-d" />
              <el-option label="过去4小时" value="now 4-H" />
              <el-option label="过去1小时" value="now 1-H" />
            </el-select>
          </el-form-item>

          <el-form-item label="地理位置" prop="geo_location">
            <el-select v-model="importForm.geo_location" placeholder="选择地理位置">
              <el-option label="全球" value="" />
              <el-option label="中国" value="CN" />
              <el-option label="美国" value="US" />
              <el-option label="英国" value="GB" />
              <el-option label="德国" value="DE" />
              <el-option label="法国" value="FR" />
              <el-option label="日本" value="JP" />
              <el-option label="韩国" value="KR" />
              <el-option label="澳大利亚" value="AU" />
              <el-option label="加拿大" value="CA" />
            </el-select>
          </el-form-item>

          <el-form-item label="分类" prop="category">
            <el-autocomplete
              v-model="importForm.category"
              placeholder="选择现有分类或输入新分类名称"
              :fetch-suggestions="queryCategories"
              :trigger-on-focus="true"
              clearable
              style="width: 100%;"
              @select="handleCategorySelect"
              value-key="value"
            >
              <template #default="{ item }">
                <div class="category-item">
                  <span class="category-name">{{ item.value }}</span>
                  <span class="category-count" v-if="item.count">({{ item.count }})</span>
                </div>
              </template>
              <template #suffix>
                <el-icon class="el-input__icon"><Search /></el-icon>
              </template>
            </el-autocomplete>
            <div class="help-text">
              <small class="text-muted">
                选择现有分类或输入新分类名称。显示分类后的数字为该分类下的关键词数量。
              </small>
            </div>
          </el-form-item>

          <el-form-item label="包含相关查询">
            <el-switch 
              v-model="importForm.include_related" 
              active-text="是" 
              inactive-text="否"
            />
          </el-form-item>

          <el-form-item label="包含上升查询">
            <el-switch 
              v-model="importForm.include_rising" 
              active-text="是" 
              inactive-text="否"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 导入进度 -->
      <div class="progress-section" v-if="importProgress.show">
        <div class="section-header">
          <el-icon class="header-icon"><Loading /></el-icon>
          <span>导入进度</span>
        </div>
        
        <el-progress
          :percentage="importProgress.percentage"
          :status="importProgress.status"
          stroke-width="8"
        />
        <p class="progress-text">{{ importProgress.text }}</p>
        
        <!-- 导入结果 -->
        <div class="import-result" v-if="importProgress.result">
          <el-alert
            :title="`导入完成！成功: ${importProgress.result.success_count}, 失败: ${importProgress.result.failed_count}`"
            type="success"
            :closable="false"
            show-icon
          />
          
          <div class="result-details" v-if="importProgress.result.failed_count > 0">
            <el-collapse>
              <el-collapse-item title="查看失败详情" name="failed">
                <ul class="failed-list">
                  <li v-for="(fail, index) in importProgress.result.failed_keywords" :key="index">
                    {{ fail.keyword || fail.error }}
                  </li>
                </ul>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>
    </div>

    <!-- 对话框按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="startImport" 
          :loading="importing"
          :disabled="!selectedConfigId || !importForm.seed_keywords?.length"
        >
          <el-icon><Download /></el-icon>
          开始导入
        </el-button>
      </div>
    </template>

    <!-- PyTrends 配置管理对话框 -->
    <PyTrendsConfigDialog
      v-model:visible="showConfigDialog"
      @success="loadConfigs"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Setting, Plus, Link, Search, Loading, Download
} from '@element-plus/icons-vue'
import { pytrendsService, pyTrendsConfigService, keywordLibraryService } from '@/services/keyword'
import PyTrendsConfigDialog from './PyTrendsConfigDialog.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const formRef = ref()
const importing = ref(false)
const testing = ref(false)
const configList = ref([])
const selectedConfigId = ref(null)
const showConfigDialog = ref(false)
const seedKeywordsText = ref('')
const configsLoading = ref(false)
const allCategories = ref([]) // 所有现有分类

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const selectedConfig = computed(() => {
  return configList.value.find(config => config.id === selectedConfigId.value)
})

// 导入表单
const importForm = reactive({
  seed_keywords: [],
  timeframe: 'today 1-m', // 默认过去30天
  geo_location: 'GB', // 默认英国
  category: '', // 改为字符串类型，支持自定义分类
  include_related: true,
  include_rising: true
})

// 导入进度
const importProgress = reactive({
  show: false,
  percentage: 0,
  status: '',
  text: '',
  result: null
})

// 表单验证规则
const rules = {
  seed_keywords: [
    { required: true, message: '请输入种子关键词', trigger: 'blur' }
  ]
}

// 监听种子关键词文本变化
watch(seedKeywordsText, (newValue) => {
  if (newValue) {
    // 支持逗号分隔和换行分隔
    const keywords = newValue
      .split(/[,\n]/)
      .map(k => k.trim())
      .filter(k => k.length > 0)
      .slice(0, 5) // PyTrends最多支持5个关键词
    
    importForm.seed_keywords = keywords
  } else {
    importForm.seed_keywords = []
  }
})

// 生命周期
onMounted(() => {
  loadConfigs()
  loadCategories() // 加载现有分类
})

// 方法
const loadConfigs = async () => {
  try {
    configsLoading.value = true
    configList.value = await pyTrendsConfigService.getConfigs()
    
    // 如果没有选中的配置且有配置可用，自动选择第一个
    if (!selectedConfigId.value && configList.value.length > 0) {
      selectedConfigId.value = configList.value[0].id
    }
  } catch (error) {
    console.error('加载配置失败:', error)
    ElMessage.error('加载配置失败')
  } finally {
    configsLoading.value = false
  }
}

const loadCategories = async () => {
  try {
    const categories = await keywordLibraryService.getCategories()
    const categoriesStats = await keywordLibraryService.getCategoriesStats()
    
    allCategories.value = categories.map(cat => ({
      value: cat,
      count: categoriesStats[cat] || 0
    }))
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const queryCategories = (queryString, cb) => {
  const results = queryString
    ? allCategories.value.filter(item => 
        item.value.toLowerCase().includes(queryString.toLowerCase())
      )
    : allCategories.value
  cb(results)
}

const handleCategorySelect = (item) => {
  importForm.category = item.value
}

const handleConfigChange = () => {
  // 配置变更时的处理
  console.log('选择的配置ID:', selectedConfigId.value)
}

const testConnection = async () => {
  if (!selectedConfigId.value) {
    ElMessage.warning('请先选择配置')
    return
  }

  try {
    testing.value = true
    const result = await pyTrendsConfigService.testConnection(selectedConfigId.value)
    
    if (result.success) {
      ElMessage.success('连接测试成功')
    } else {
      ElMessage.error(`连接测试失败: ${result.message}`)
    }
  } catch (error) {
    console.error('测试连接失败:', error)
    ElMessage.error('测试连接失败')
  } finally {
    testing.value = false
  }
}

const startImport = async () => {
  if (!selectedConfigId.value) {
    ElMessage.warning('请选择 PyTrends 配置')
    return
  }

  if (!importForm.seed_keywords || importForm.seed_keywords.length === 0) {
    ElMessage.warning('请输入种子关键词')
    return
  }

  if (importForm.seed_keywords.length > 5) {
    ElMessage.warning('PyTrends 最多支持5个种子关键词')
    return
  }

  try {
    importing.value = true
    
    // 显示进度
    importProgress.show = true
    importProgress.percentage = 0
    importProgress.status = ''
    importProgress.text = '启动导入任务...'
    importProgress.result = null

    let result = null

    try {
      // 执行导入
      result = await pytrendsService.importFromPyTrends(
        selectedConfigId.value,
        importForm,
        {
          operator_id: null,
          operator_name: 'PyTrends导入'
        }
      )
    } catch (error) {
      // 检查是否是超时错误
      if (error.message && error.message.includes('timeout')) {
        // 超时了，但任务可能已经在后台启动
        console.log('请求超时，但任务可能已在后台启动，开始检查任务状态...')
        
        // 设置一个假的任务ID用于监控（实际应该从后端改进返回机制）
        importProgress.text = '请求超时，正在检查后台任务状态...'
        importProgress.percentage = 10
        
        // 延迟5秒后开始检查是否有新的任务
        setTimeout(() => {
          checkRecentTasks()
        }, 5000)
        
        ElMessage.warning('请求超时，但任务可能已在后台启动，请稍后查看关键词库')
        return
      } else {
        throw error
      }
    }

    // 如果成功获得响应
    if (result) {
      // 如果返回任务ID，开始监控进度
      if (result.task_id) {
        importProgress.text = '任务已启动，正在后台处理...'
        importProgress.percentage = 10
        
        await monitorTaskProgress(result.task_id)
      } else {
        // 同步处理完成
        importProgress.percentage = 100
        importProgress.status = 'success'
        importProgress.text = '导入完成'
        importProgress.result = result

        ElMessage.success(`导入完成！成功: ${result.success_count}, 失败: ${result.failed_count}`)
        
        // 3秒后自动关闭对话框
        setTimeout(() => {
          if (result.success_count > 0) {
            dialogVisible.value = false
            emit('success')
          }
        }, 3000)
      }
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error(`导入失败: ${error.response?.data?.detail || error.message}`)
    importProgress.status = 'exception'
    importProgress.text = '导入失败'
  } finally {
    importing.value = false
  }
}

// 监控任务进度
const monitorTaskProgress = async (taskId) => {
  const maxAttempts = 120 // 最多监控10分钟（每5秒检查一次）
  let attempts = 0
  
  const checkProgress = async () => {
    try {
      const status = await pytrendsService.getTaskStatus(taskId)
      
      importProgress.percentage = status.progress || 0
      
      switch (status.status) {
        case 'processing':
        case 'running':
          importProgress.text = `正在处理... (${status.processed_keywords || 0}/${status.total_keywords || 0})`
          importProgress.status = ''
          break
          
        case 'completed':
          importProgress.percentage = 100
          importProgress.status = 'success'
          importProgress.text = '导入完成'
          importProgress.result = status.result_summary
          
          ElMessage.success(`导入完成！成功: ${status.success_keywords}, 失败: ${status.failed_keywords}`)
          
          // 3秒后自动关闭对话框
          setTimeout(() => {
            if (status.success_keywords > 0) {
              dialogVisible.value = false
              emit('success')
            }
          }, 3000)
          
          return // 完成，停止监控
          
        case 'failed':
          importProgress.status = 'exception'
          importProgress.text = '导入失败'
          ElMessage.error(`导入失败: ${status.error_message || '未知错误'}`)
          return // 失败，停止监控
          
        default:
          importProgress.text = '任务状态未知'
      }
      
      attempts++
      if (attempts < maxAttempts && (status.status === 'processing' || status.status === 'running')) {
        // 5秒后再次检查
        setTimeout(checkProgress, 5000)
      } else if (attempts >= maxAttempts) {
        importProgress.status = 'exception'
        importProgress.text = '任务超时，请稍后查看结果'
        ElMessage.warning('任务处理时间较长，请稍后在关键词库中查看导入结果')
      }
    } catch (error) {
      console.error('获取任务状态失败:', error)
      importProgress.status = 'exception'
      importProgress.text = '无法获取任务状态'
      ElMessage.error('无法获取任务状态')
    }
  }
  
  // 开始检查进度
  setTimeout(checkProgress, 2000) // 2秒后开始第一次检查
}

// 检查最近的任务（用于超时后的恢复）
const checkRecentTasks = async () => {
  try {
    // 这里应该调用一个API来获取当前配置的最近任务
    // 由于当前后端可能没有这个API，我们先使用一个简化的方法
    importProgress.text = '正在查找相关任务...'
    
    // 显示提示信息，建议用户手动检查
    ElMessage.info('由于请求超时，请稍后在关键词库中查看导入结果，或重新尝试导入')
    
    // 设置一个合理的状态
    importProgress.status = 'warning'
    importProgress.text = '请求超时，请手动检查导入结果'
    importProgress.percentage = 50
  } catch (error) {
    console.error('检查最近任务失败:', error)
    importProgress.status = 'exception'
    importProgress.text = '无法检查任务状态'
  }
}

const handleDialogClosed = () => {
  // 重置表单和进度
  importProgress.show = false
  importProgress.percentage = 0
  importProgress.status = ''
  importProgress.text = ''
  importProgress.result = null
  
  seedKeywordsText.value = ''
  importForm.seed_keywords = []
  importForm.timeframe = 'today 1-m'
  importForm.geo_location = 'GB'
  importForm.category = ''
  importForm.include_related = true
  importForm.include_rising = true
}
</script>

<style scoped>
.pytrends-import-container {
  max-height: 600px;
  overflow-y: auto;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.header-icon {
  margin-right: 8px;
  color: #409eff;
}

.config-section,
.import-section,
.progress-section {
  margin-bottom: 24px;
}

.config-selection {
  margin-bottom: 16px;
}

.config-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.config-actions {
  margin-top: 12px;
  text-align: right;
}

.config-info {
  margin-top: 16px;
}

.help-text {
  margin-top: 4px;
}

.text-muted {
  color: #909399;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.category-name {
  flex: 1;
}

.category-count {
  color: #909399;
  font-size: 12px;
  margin-left: 8px;
}

.progress-text {
  text-align: center;
  margin: 16px 0;
  color: #606266;
}

.import-result {
  margin-top: 16px;
}

.result-details {
  margin-top: 12px;
}

.failed-list {
  max-height: 200px;
  overflow-y: auto;
  margin: 0;
  padding-left: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>