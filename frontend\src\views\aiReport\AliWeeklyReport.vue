<template>
  <div class="ali-weekly-report">
    <div class="page-header">
      <h2>阿里周报</h2>
      <p>查看阿里国际站每周运营数据报表</p>
    </div>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>本周概览</span>
              <el-date-picker
                v-model="selectedWeek"
                type="week"
                placeholder="选择周"
                format="YYYY年第WW周"
                @change="handleWeekChange"
              />
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="6">
              <div class="stat-card">
                <div class="stat-number">{{ weeklyStats.inquiries }}</div>
                <div class="stat-label">本周询盘</div>
                <div class="stat-change positive">+{{ weeklyStats.inquiriesGrowth }}%</div>
                <div class="stat-compare">较上周</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="stat-card">
                <div class="stat-number">{{ weeklyStats.views }}</div>
                <div class="stat-label">商品浏览量</div>
                <div class="stat-change positive">+{{ weeklyStats.viewsGrowth }}%</div>
                <div class="stat-compare">较上周</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="stat-card">
                <div class="stat-number">{{ weeklyStats.customers }}</div>
                <div class="stat-label">新增客户</div>
                <div class="stat-change positive">+{{ weeklyStats.customersGrowth }}%</div>
                <div class="stat-compare">较上周</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="stat-card">
                <div class="stat-number">{{ weeklyStats.orders }}</div>
                <div class="stat-label">成交订单</div>
                <div class="stat-change positive">+{{ weeklyStats.ordersGrowth }}%</div>
                <div class="stat-compare">较上周</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>每日趋势分析</span>
          </template>
          <div class="trend-chart">
            <div class="chart-container">
              <div class="y-axis">
                <div class="y-label">询盘数</div>
                <div class="y-values">
                  <span>50</span>
                  <span>40</span>
                  <span>30</span>
                  <span>20</span>
                  <span>10</span>
                  <span>0</span>
                </div>
              </div>
              <div class="chart-area">
                <div class="bars-container">
                  <div v-for="(day, index) in weeklyTrends" :key="index" class="bar-group">
                    <div class="bar inquiries" :style="{ height: (day.inquiries / 50 * 100) + '%' }"></div>
                    <div class="bar views" :style="{ height: (day.views / 1000 * 100) + '%' }"></div>
                    <div class="day-label">{{ day.day }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="chart-legend">
              <div class="legend-item">
                <span class="legend-color inquiries"></span>
                <span>询盘数</span>
              </div>
              <div class="legend-item">
                <span class="legend-color views"></span>
                <span>浏览量(千)</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <span>客户地区分布</span>
          </template>
          <div class="region-stats">
            <div v-for="region in customerRegions" :key="region.name" class="region-item">
              <div class="region-info">
                <div class="region-name">{{ region.name }}</div>
                <div class="region-percent">{{ region.percent }}%</div>
              </div>
              <div class="region-bar">
                <div class="region-progress" :style="{ width: region.percent + '%' }"></div>
              </div>
              <div class="region-count">{{ region.count }} 客户</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <span>本周重要事件</span>
          </template>
          <div class="events-timeline">
            <div v-for="event in weeklyEvents" :key="event.id" class="event-item">
              <div class="event-date">{{ event.date }}</div>
              <div class="event-content">
                <div class="event-title">{{ event.title }}</div>
                <div class="event-description">{{ event.description }}</div>
                <el-tag :type="getEventTagType(event.type)" size="small">{{ event.type }}</el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>商品表现排行</span>
          </template>
          <el-table :data="productPerformance" style="width: 100%">
            <el-table-column type="index" label="排名" width="80" :index="index => index + 1"/>
            <el-table-column prop="name" label="商品名称" min-width="200"/>
            <el-table-column prop="views" label="浏览量" width="120" sortable/>
            <el-table-column prop="inquiries" label="询盘数" width="120" sortable/>
            <el-table-column prop="conversion" label="转化率" width="120">
              <template #default="scope">
                <span>{{ scope.row.conversion }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="trend" label="趋势" width="120">
              <template #default="scope">
                <el-tag :type="scope.row.trend === '上升' ? 'success' : scope.row.trend === '下降' ? 'danger' : 'info'">
                  {{ scope.row.trend }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted } from 'vue'

export default defineComponent({
  name: 'AliWeeklyReport',
  setup() {
    const selectedWeek = ref(new Date())
    
    const weeklyStats = ref({
      inquiries: 168,
      inquiriesGrowth: 15.2,
      views: 8456,
      viewsGrowth: 12.8,
      customers: 42,
      customersGrowth: 18.5,
      orders: 28,
      ordersGrowth: 22.3
    })

    const weeklyTrends = ref([
      { day: '周一', inquiries: 18, views: 980 },
      { day: '周二', inquiries: 25, views: 1200 },
      { day: '周三', inquiries: 32, views: 1450 },
      { day: '周四', inquiries: 28, views: 1320 },
      { day: '周五', inquiries: 35, views: 1680 },
      { day: '周六', inquiries: 20, views: 1100 },
      { day: '周日', inquiries: 10, views: 726 }
    ])

    const customerRegions = ref([
      { name: '北美', percent: 35, count: 58 },
      { name: '欧洲', percent: 28, count: 46 },
      { name: '东南亚', percent: 20, count: 33 },
      { name: '中东', percent: 10, count: 16 },
      { name: '其他', percent: 7, count: 12 }
    ])

    const weeklyEvents = ref([
      {
        id: 1,
        date: '周一',
        title: '大客户签约',
        description: '美国Fortune 500公司签订年度采购合同',
        type: '重要合作'
      },
      {
        id: 2,
        date: '周三',
        title: '产品推广活动',
        description: '新品蓝牙耳机推广活动上线，效果显著',
        type: '营销活动'
      },
      {
        id: 3,
        date: '周五',
        title: '客户满意度调研',
        description: '完成本周客户满意度调研，平均分4.8/5.0',
        type: '客户服务'
      }
    ])

    const productPerformance = ref([
      { name: '智能手机支架', views: 1256, inquiries: 78, conversion: 6.2, trend: '上升' },
      { name: '蓝牙耳机', views: 1180, inquiries: 65, conversion: 5.5, trend: '上升' },
      { name: '充电宝', views: 980, inquiries: 52, conversion: 5.3, trend: '稳定' },
      { name: '手机壳', views: 856, inquiries: 38, conversion: 4.4, trend: '下降' },
      { name: '数据线', views: 720, inquiries: 32, conversion: 4.4, trend: '稳定' }
    ])

    const handleWeekChange = (week) => {
      console.log('选择周:', week)
      // 这里可以根据周重新加载数据
    }

    const getEventTagType = (type) => {
      const typeMap = {
        重要合作: 'success',
        营销活动: 'warning',
        客户服务: 'info'
      }
      return typeMap[type] || 'info'
    }

    onMounted(() => {
      // 初始化加载数据
    })

    return {
      selectedWeek,
      weeklyStats,
      weeklyTrends,
      customerRegions,
      weeklyEvents,
      productPerformance,
      handleWeekChange,
      getEventTagType
    }
  }
})
</script>

<style scoped>
.ali-weekly-report {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  margin-bottom: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  margin-bottom: 4px;
  opacity: 0.9;
}

.stat-change {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 2px;
}

.stat-change.positive {
  color: #a8ff88;
}

.stat-compare {
  font-size: 10px;
  opacity: 0.8;
}

.trend-chart {
  padding: 20px;
}

.chart-container {
  display: flex;
  height: 250px;
  margin-bottom: 20px;
}

.y-axis {
  display: flex;
  flex-direction: column;
  width: 60px;
  margin-right: 20px;
}

.y-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 10px;
}

.y-values {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  font-size: 10px;
  color: #909399;
}

.chart-area {
  flex: 1;
  position: relative;
}

.bars-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 100%;
  padding-bottom: 30px;
}

.bar-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  margin: 0 5px;
}

.bar {
  width: 20px;
  margin: 0 2px;
  border-radius: 2px 2px 0 0;
  position: relative;
}

.bar.inquiries {
  background: #409EFF;
}

.bar.views {
  background: #67C23A;
}

.day-label {
  margin-top: 10px;
  font-size: 12px;
  color: #606266;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #606266;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 6px;
}

.legend-color.inquiries {
  background: #409EFF;
}

.legend-color.views {
  background: #67C23A;
}

.region-stats {
  padding: 10px 0;
}

.region-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.region-info {
  width: 80px;
  margin-right: 15px;
}

.region-name {
  font-size: 14px;
  color: #303133;
}

.region-percent {
  font-size: 12px;
  color: #909399;
}

.region-bar {
  flex: 1;
  height: 8px;
  background: #f0f2f5;
  border-radius: 4px;
  margin-right: 15px;
  overflow: hidden;
}

.region-progress {
  height: 100%;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.region-count {
  width: 60px;
  font-size: 12px;
  color: #606266;
  text-align: right;
}

.events-timeline {
  max-height: 300px;
  overflow-y: auto;
}

.event-item {
  display: flex;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f2f5;
}

.event-item:last-child {
  border-bottom: none;
}

.event-date {
  width: 50px;
  font-size: 12px;
  color: #909399;
  margin-right: 15px;
}

.event-content {
  flex: 1;
}

.event-title {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  margin-bottom: 5px;
}

.event-description {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.4;
}
</style> 