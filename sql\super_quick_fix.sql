-- 超快速修复 - 直接清空重建表结构
USE cbec;

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 直接删除表
DROP TABLE IF EXISTS keyword_update_history;
DROP TABLE IF EXISTS keyword_library;
DROP TABLE IF EXISTS keyword_import_tasks;

-- 快速重建表
CREATE TABLE keyword_library (
  id int NOT NULL AUTO_INCREMENT,
  keyword_name varchar(255) NOT NULL,
  avg_monthly_searches bigint DEFAULT NULL,
  monthly_searches varchar(500) DEFAULT NULL,
  competition_level enum('LOW','MEDIUM','HIGH','UNSPECIFIED') DEFAULT 'UNSPECIFIED',
  competition_index decimal(5,2) DEFAULT NULL,
  low_bid_micros bigint DEFAULT NULL,
  high_bid_micros bigint DEFAULT NULL,
  currency_code varchar(3) DEFAULT 'CNY',
  language_code varchar(10) DEFAULT 'zh-CN',
  location_ids varchar(255) DEFAULT NULL,
  update_method enum('GOOGLE_ADS_API','MANUAL','BATCH_IMPORT') NOT NULL DEFAULT 'MANUAL',
  tags varchar(500) DEFAULT NULL,
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY uk_keyword_name (keyword_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE keyword_update_history (
  id int NOT NULL AUTO_INCREMENT,
  keyword_id int NOT NULL,
  update_method enum('GOOGLE_ADS_API','MANUAL','BATCH_IMPORT') NOT NULL,
  old_data json DEFAULT NULL,
  new_data json DEFAULT NULL,
  batch_id varchar(100) DEFAULT NULL,
  operator_id int DEFAULT NULL,
  operator_name varchar(100) DEFAULT NULL,
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY idx_keyword_id (keyword_id),
  CONSTRAINT keyword_update_history_ibfk_1 FOREIGN KEY (keyword_id) REFERENCES keyword_library (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE keyword_import_tasks (
  id int NOT NULL AUTO_INCREMENT,
  task_id varchar(100) NOT NULL,
  file_name varchar(255) NOT NULL,
  file_path varchar(500) NOT NULL,
  total_count int DEFAULT 0,
  success_count int DEFAULT 0,
  failed_count int DEFAULT 0,
  status enum('PENDING','PROCESSING','COMPLETED','FAILED') DEFAULT 'PENDING',
  error_message text,
  result_file_path varchar(500) DEFAULT NULL,
  operator_id int DEFAULT NULL,
  operator_name varchar(100) DEFAULT NULL,
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY uk_task_id (task_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

SET FOREIGN_KEY_CHECKS = 1;

SELECT 'Tables recreated successfully' as result; 