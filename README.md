# CBEC - 跨境电商智能化管理系统

## 📋 项目概述

CBEC（Cross-Border E-Commerce）是一个基于Vue.js + FastAPI的现代化跨境电商管理系统，专注于为跨境电商企业提供全方位的智能化管理解决方案。

### 🎯 核心功能

- **产品管理**: 多平台产品信息管理，支持Amazon、eBay、Shopify等
- **库存管理**: 实时库存监控，自动补货提醒
- **订单处理**: 订单自动化处理，多渠道订单整合
- **关键词研究**: 集成Google Ads API，提供专业的关键词分析
- **数据分析**: 销售数据分析，市场趋势预测
- **多语言支持**: 支持中文、英文等多种语言

## 🏗️ 系统架构

### 技术栈

**前端**
- Vue.js 3 + Composition API
- Element Plus UI组件库
- TypeScript
- Vite构建工具
- Axios HTTP客户端

**后端**
- FastAPI (Python 3.9+)
- SQLAlchemy ORM
- PostgreSQL数据库
- Redis缓存
- Google Ads API v19集成

**部署**
- Docker容器化
- Nginx反向代理
- Kubernetes集群（可选）

### 架构图

```
用户界面层 (Vue.js)
      ↓
API网关层 (Nginx)
      ↓
业务逻辑层 (FastAPI)
      ↓
数据访问层 (SQLAlchemy + Redis)
      ↓
数据存储层 (PostgreSQL + MinIO)
```

## 🚀 快速开始

### 环境要求

- Node.js 16+
- Python 3.9+
- PostgreSQL 13+
- Redis 6+
- Docker (可选)

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-username/cbec.git
cd cbec
```

2. **前端环境设置**
```bash
cd frontend
npm install
npm run dev
```

3. **后端环境设置**
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload
```

4. **数据库初始化**
```bash
# 在backend目录下
python -m alembic upgrade head
```

### Docker部署

```bash
# 在项目根目录下
docker-compose up -d
```

## 🔑 Google Ads集成

### 功能特性

- **OAuth2.0认证**: 安全的Google账户授权
- **关键词研究**: 基于Google Ads API的关键词建议
- **竞争分析**: 关键词竞争度和出价分析
- **搜索量预测**: 历史搜索量数据和趋势分析
- **智能评分**: 基于多维度的关键词评分算法

### 配置步骤

1. **获取Google Ads API凭据**
   - 访问[Google Ads API文档](https://developers.google.com/google-ads/api)
   - 创建开发者账户并申请API访问权限
   - 获取客户端ID、客户端密钥和开发者令牌

2. **配置环境变量**
```bash
# .env文件
GOOGLE_ADS_DEVELOPER_TOKEN=your_developer_token
GOOGLE_ADS_CLIENT_ID=your_client_id
GOOGLE_ADS_CLIENT_SECRET=your_client_secret
```

3. **授权流程**
   - 在系统中添加Google Ads配置
   - 完成OAuth2.0授权流程
   - 开始使用关键词研究功能

### API使用示例

```python
# 关键词研究API调用
response = requests.post('http://localhost:8000/api/v1/keyword-library/import/google-ads/1', 
    json={
        "seed_keywords": ["跨境电商", "亚马逊FBA"],
        "language_code": "zh-CN",
        "location_ids": [2156],
        "page_size": 100
    }
)
```

## 📚 文档

- [系统设计文档](docs/google_ads_integration_design.md)
- [架构图详解](docs/architecture_diagrams.md)
- [API文档](http://localhost:8000/docs) (启动后端服务后访问)
- [用户手册](docs/user_manual.md)

## 🔧 开发指南

### 项目结构

```
cbec/
├── frontend/              # Vue.js前端应用
│   ├── src/
│   │   ├── views/         # 页面组件
│   │   ├── components/    # 公共组件
│   │   ├── services/      # API服务
│   │   └── utils/         # 工具函数
│   └── package.json
├── backend/               # FastAPI后端应用
│   ├── app/
│   │   ├── api/           # API路由
│   │   ├── models/        # 数据模型
│   │   ├── services/      # 业务逻辑
│   │   └── utils/         # 工具函数
│   └── requirements.txt
├── docs/                  # 项目文档
├── docker-compose.yml     # Docker编排文件
└── README.md
```

### 开发规范

- **代码风格**: 遵循PEP8 (Python) 和 ESLint (JavaScript)
- **提交信息**: 使用[约定式提交](https://www.conventionalcommits.org/)
- **分支策略**: Git Flow工作流
- **测试覆盖**: 单元测试覆盖率 > 80%

### 贡献指南

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 🧪 测试

### 运行测试

```bash
# 前端测试
cd frontend
npm run test

# 后端测试
cd backend
pytest

# 覆盖率报告
pytest --cov=app tests/
```

### 测试策略

- **单元测试**: 70%覆盖率
- **集成测试**: 20%覆盖率
- **端到端测试**: 10%覆盖率

## 📊 监控和日志

### 监控指标

- **系统指标**: CPU、内存、磁盘使用率
- **应用指标**: API响应时间、错误率
- **业务指标**: 关键词查询次数、用户活跃度

### 日志管理

- **日志级别**: ERROR、WARN、INFO、DEBUG
- **日志格式**: 结构化JSON格式
- **日志收集**: ELK Stack (Elasticsearch + Logstash + Kibana)

## 🔒 安全考虑

### 认证授权

- JWT Token认证
- OAuth2.0标准
- RBAC权限控制
- API限流保护

### 数据保护

- HTTPS加密传输
- 敏感数据加密存储
- 定期安全审计
- 数据备份和恢复

## 🌍 多语言支持

当前支持的语言：
- 🇨🇳 简体中文
- 🇺🇸 English
- 🇯🇵 日本語 (计划中)
- 🇰🇷 한국어 (计划中)

## 📈 路线图

### v1.0 (当前版本)
- [x] 基础产品管理
- [x] 订单处理系统
- [x] Google Ads API集成
- [x] 关键词研究功能

### v1.1 (下一版本)
- [ ] 多平台库存同步
- [ ] 智能定价策略
- [ ] 高级数据分析
- [ ] 移动端应用

### v2.0 (未来版本)
- [ ] AI驱动的市场分析
- [ ] 自动化广告投放
- [ ] 供应链管理
- [ ] 多租户支持

## 📞 支持与联系

- **问题反馈**: [GitHub Issues](https://github.com/your-username/cbec/issues)
- **功能建议**: [GitHub Discussions](https://github.com/your-username/cbec/discussions)
- **技术交流**: [加入我们的Slack](https://join.slack.com/...)
- **邮件联系**: <EMAIL>

## 📄 许可证

本项目基于 [MIT License](LICENSE) 开源协议。

## 🙏 致谢

感谢以下开源项目和服务：

- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [FastAPI](https://fastapi.tiangolo.com/) - 现代Python Web框架
- [Element Plus](https://element-plus.org/) - Vue 3组件库
- [Google Ads API](https://developers.google.com/google-ads/api) - 广告数据API
- [PostgreSQL](https://www.postgresql.org/) - 强大的开源数据库

---

⭐ **如果这个项目对您有帮助，请给我们一个Star！**

[![GitHub stars](https://img.shields.io/github/stars/your-username/cbec.svg?style=social)](https://github.com/your-username/cbec/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/your-username/cbec.svg?style=social)](https://github.com/your-username/cbec/network)
[![GitHub issues](https://img.shields.io/github/issues/your-username/cbec.svg)](https://github.com/your-username/cbec/issues)
[![GitHub license](https://img.shields.io/github/license/your-username/cbec.svg)](https://github.com/your-username/cbec/blob/main/LICENSE) 