from fastapi import APIRouter, Depends, HTTPException, File, UploadFile, BackgroundTasks, Request, Form
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import math
import logging
import urllib.parse
import os

logger = logging.getLogger(__name__)

from ..db.session import get_db
from ..schemas.keyword_library import (
    KeywordLibraryCreate, KeywordLibraryUpdate, KeywordLibraryResponse,
    KeywordSearchRequest, KeywordPageResponse, KeywordStatsResponse,
    KeywordImportTaskCreate, KeywordImportTaskResponse, KeywordUpdateHistoryResponse,
    GoogleAdsConfigCreate, GoogleAdsConfigUpdate, GoogleAdsConfigResponse,
    GoogleAdsKeywordRequest, KeywordBatchCreate, KeywordBatchResponse,
    PyTrendsConfigCreate, PyTrendsConfigUpdate, PyTrendsConfigResponse,
    PyTrendsKeywordRequest, PyTrendsTaskResponse, PyTrendsTaskStartResponse,
    KeywordCategoryCreate, KeywordCategoryUpdate, KeywordCategoryDelete, KeywordCategoryResponse,
    KeywordBatchUpdateCategory, KeywordBatchUpdateCategoryResponse
)
from ..services.keyword_library_service import KeywordLibraryService
from ..services.pytrends_config_service import PyTrendsConfigService
from ..models.keyword_library import GoogleAdsConfig, PyTrendsConfig
from ..core.config import settings
from app.utils.datetime_utils import utc_now, to_iso_string

router = APIRouter(
    prefix="/keyword-library",
    tags=["关键词库"]
)

# 关键词 CRUD 操作
@router.post("/keywords", response_model=KeywordLibraryResponse, summary="创建关键词")
def create_keyword(
    keyword_data: KeywordLibraryCreate,
    db: Session = Depends(get_db),
    operator_id: Optional[int] = None,
    operator_name: Optional[str] = None
):
    """创建单个关键词"""
    service = KeywordLibraryService(db)
    return service.create_keyword(keyword_data, operator_id, operator_name)

@router.get("/keywords/{keyword_id}", response_model=KeywordLibraryResponse, summary="获取关键词详情")
def get_keyword(keyword_id: int, db: Session = Depends(get_db)):
    """获取单个关键词详情"""
    service = KeywordLibraryService(db)
    return service.get_keyword(keyword_id)

@router.put("/keywords/{keyword_id}", response_model=KeywordLibraryResponse, summary="更新关键词")
def update_keyword(
    keyword_id: int,
    keyword_data: KeywordLibraryUpdate,
    db: Session = Depends(get_db),
    operator_id: Optional[int] = None,
    operator_name: Optional[str] = None
):
    """更新关键词信息"""
    service = KeywordLibraryService(db)
    return service.update_keyword(keyword_id, keyword_data, operator_id, operator_name)

@router.delete("/keywords/{keyword_id}", summary="删除关键词")
def delete_keyword(keyword_id: int, db: Session = Depends(get_db)):
    """删除关键词"""
    service = KeywordLibraryService(db)
    result = service.delete_keyword(keyword_id)
    return {"success": result, "message": "关键词删除成功"}

@router.post("/keywords/batch-delete", summary="批量删除关键词")
def batch_delete_keywords(
    request: dict,
    db: Session = Depends(get_db)
):
    """批量删除关键词"""
    keyword_ids = request.get("keyword_ids", [])
    if not keyword_ids:
        raise HTTPException(status_code=400, detail="关键词ID列表不能为空")

    service = KeywordLibraryService(db)
    result = service.batch_delete_keywords(keyword_ids)
    return {
        "success": True,
        "message": f"成功删除 {result['deleted_count']} 个关键词",
        "deleted_count": result['deleted_count'],
        "failed_count": result['failed_count'],
        "failed_keywords": result.get('failed_keywords', [])
    }

# 搜索和分页
@router.post("/keywords/search", response_model=KeywordPageResponse, summary="搜索关键词")
def search_keywords(
    search_request: KeywordSearchRequest,
    db: Session = Depends(get_db)
):
    """搜索关键词，支持多种过滤条件和分页"""
    service = KeywordLibraryService(db)
    keywords, total = service.search_keywords(search_request)
    
    total_pages = math.ceil(total / search_request.page_size)
    
    return KeywordPageResponse(
        items=keywords,
        total=total,
        page=search_request.page,
        page_size=search_request.page_size,
        total_pages=total_pages
    )

# 获取分类列表
@router.get("/categories", summary="获取分类列表")
def get_categories(db: Session = Depends(get_db)):
    """获取所有分类列表"""
    service = KeywordLibraryService(db)
    categories = service.get_categories()
    return categories

# 获取分类统计
@router.get("/categories/stats", summary="获取分类统计数据")
def get_categories_stats(db: Session = Depends(get_db)):
    """获取每个分类的关键词数量统计"""
    service = KeywordLibraryService(db)
    return service.get_categories_stats()

# 分类管理 CRUD 操作
@router.post("/categories", summary="创建分类", response_model=KeywordCategoryResponse)
def create_category(
    category_data: KeywordCategoryCreate,
    db: Session = Depends(get_db)
):
    """创建新分类"""
    service = KeywordLibraryService(db)
    return service.create_category(category_data.name)

@router.put("/categories", summary="更新分类", response_model=KeywordCategoryResponse)
def update_category(
    category_data: KeywordCategoryUpdate,
    db: Session = Depends(get_db)
):
    """更新分类名称"""
    service = KeywordLibraryService(db)
    return service.update_category(category_data.oldName, category_data.newName)

@router.delete("/categories", summary="删除分类", response_model=KeywordCategoryResponse)
def delete_category(
    category_data: KeywordCategoryDelete,
    db: Session = Depends(get_db)
):
    """删除分类"""
    service = KeywordLibraryService(db)
    return service.delete_category(category_data.name)

@router.delete("/categories/uncategorized", summary="清空未分类关键词")
def clear_uncategorized_keywords(
    db: Session = Depends(get_db)
):
    """清空未分类中的所有关键词"""
    service = KeywordLibraryService(db)
    return service.clear_uncategorized_keywords()

# 统计信息
@router.get("/stats", response_model=KeywordStatsResponse, summary="获取关键词统计")
def get_keywords_stats(db: Session = Depends(get_db)):
    """获取关键词库统计信息"""
    service = KeywordLibraryService(db)
    return service.get_keywords_stats()

# 批量操作
@router.post("/keywords/batch", response_model=KeywordBatchResponse, summary="批量创建关键词")
def batch_create_keywords(
    batch_data: KeywordBatchCreate,
    db: Session = Depends(get_db)
):
    """批量创建或更新关键词"""
    service = KeywordLibraryService(db)
    return service.batch_create_keywords(batch_data)

@router.post("/keywords/batch-update-category", response_model=KeywordBatchUpdateCategoryResponse, summary="批量更新关键词分类")
def batch_update_category(
    update_data: KeywordBatchUpdateCategory,
    db: Session = Depends(get_db)
):
    """批量更新关键词分类"""
    service = KeywordLibraryService(db)
    return service.batch_update_category(update_data.keyword_ids, update_data.category)

# Google Ads API 集成
@router.post("/import/google-ads/{config_id}", response_model=KeywordBatchResponse, summary="从Google Ads导入")
def import_from_google_ads(
    config_id: int,
    request: GoogleAdsKeywordRequest,
    db: Session = Depends(get_db),
    operator_id: Optional[int] = None,
    operator_name: Optional[str] = None
):
    """从 Google Ads API 导入关键词"""
    service = KeywordLibraryService(db)
    return service.import_keywords_from_google_ads(config_id, request, operator_id, operator_name)

# 文件导入
@router.post("/import/file", summary="从文件导入关键词")
def import_from_file(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    operator_id: Optional[int] = None,
    operator_name: Optional[str] = None
):
    """
    从文件导入关键词（支持 CSV 和 Excel）

    文件格式要求：
    - CSV 或 Excel 格式
    - 必须包含 '关键词名' 或 'keyword_name' 列
    - 新字段列：意图/intent、搜索量/volume、趋势/trend、关键词难度/keyword_difficulty、
      CPC (USD)/cpc_usd、竞争密度/competitive_density、SERP特征/serp_features、搜索结果数/number_of_results
    - 兼容字段列：平均每月搜索量、竞争级别、竞争指数、出价第20百分位、出价第80百分位、标签、分类等
    """
    # 验证文件类型
    if not file.filename.endswith(('.csv', '.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="不支持的文件格式，请上传 CSV 或 Excel 文件")

    service = KeywordLibraryService(db)
    task_id = service.import_keywords_from_file(file, operator_id, operator_name)

    return {
        "task_id": task_id,
        "message": "文件上传成功，正在后台处理，请使用 task_id 查询进度"
    }

# Semrush文件导入
@router.post("/import/semrush", summary="从Semrush文件导入关键词")
def import_from_semrush(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    category: Optional[str] = Form(None),
    country: Optional[str] = Form("global"),
    operator_id: Optional[int] = Form(None),
    operator_name: Optional[str] = Form(None)
):
    """
    从Semrush文件导入关键词（支持 CSV 和 Excel）

    Semrush文件格式特点：
    - 支持Semrush导出的CSV和Excel格式
    - 自动识别Semrush字段结构
    - 智能映射到系统字段
    - 支持文件名格式：keyword_broad-match_country_date.xlsx
    """
    # 验证文件类型
    if not file.filename.endswith(('.csv', '.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="不支持的文件格式，请上传 CSV 或 Excel 文件")

    service = KeywordLibraryService(db)
    task_id = service.import_keywords_from_semrush(
        file,
        operator_id,
        operator_name,
        category=category,
        country=country
    )

    return {
        "task_id": task_id,
        "message": "Semrush文件上传成功，正在后台处理，请使用 task_id 查询进度"
    }

@router.get("/import/tasks/{task_id}", response_model=KeywordImportTaskResponse, summary="查询导入任务状态")
def get_import_task_status(task_id: str, db: Session = Depends(get_db)):
    """查询文件导入任务状态"""
    service = KeywordLibraryService(db)
    return service.get_import_task(task_id)

@router.post("/import/tasks/{task_id}/cancel", summary="取消导入任务")
def cancel_import_task(task_id: str, db: Session = Depends(get_db)):
    """取消导入任务"""
    service = KeywordLibraryService(db)
    return service.cancel_import_task(task_id)

# 模板下载
@router.get("/import/template/{template_type}", summary="下载导入模板")
def download_import_template(template_type: str):
    """
    下载关键词导入模板文件

    template_type: 模板类型
    - csv: 中文CSV模板
    - csv_en: 英文CSV模板
    """
    template_files = {
        "csv": "keyword_import_template.csv",
        "csv_en": "keyword_import_template_en.csv"
    }

    if template_type not in template_files:
        raise HTTPException(status_code=400, detail="不支持的模板类型")

    template_filename = template_files[template_type]

    # 获取项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
    template_path = os.path.join(project_root, "templates", template_filename)

    logger.info(f"尝试访问模板文件: {template_path}")
    logger.info(f"文件是否存在: {os.path.exists(template_path)}")

    if not os.path.exists(template_path):
        raise HTTPException(status_code=404, detail=f"模板文件不存在: {template_path}")

    # 设置下载文件名
    download_filename = "关键词导入模板.csv" if template_type == "csv" else "keyword_import_template.csv"

    return FileResponse(
        path=template_path,
        filename=download_filename,
        media_type="text/csv"
    )

# 历史记录
@router.get("/keywords/{keyword_id}/history", response_model=List[KeywordUpdateHistoryResponse], summary="获取关键词更新历史")
def get_keyword_history(keyword_id: int, db: Session = Depends(get_db)):
    """获取关键词的更新历史记录"""
    service = KeywordLibraryService(db)
    return service.get_keyword_history(keyword_id)

# Google Ads 配置管理
@router.post("/google-ads-configs", response_model=GoogleAdsConfigResponse, summary="创建Google Ads配置")
def create_google_ads_config(config_data: GoogleAdsConfigCreate, db: Session = Depends(get_db)):
    """创建 Google Ads API 配置"""
    try:
        # 检查配置名是否已存在
        existing = db.query(GoogleAdsConfig).filter(
            GoogleAdsConfig.config_name == config_data.config_name
        ).first()
        
        if existing:
            raise HTTPException(status_code=400, detail="配置名已存在")
        
        # 手动创建配置对象，设置OAuth相关字段的默认值
        db_config = GoogleAdsConfig(
            config_name=config_data.config_name,
            customer_id=config_data.customer_id,
            developer_token=config_data.developer_token,
            client_id=config_data.client_id,
            client_secret=config_data.client_secret,
            refresh_token=config_data.refresh_token,
            login_customer_id=config_data.login_customer_id,
            is_active=config_data.is_active,
            # OAuth相关字段设置默认值
            oauth_state=None,
            authorization_status="pending",
            last_auth_time=None,
            access_token=None,
            token_expiry=None
        )
        
        db.add(db_config)
        db.commit()
        db.refresh(db_config)
        return db_config
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建配置失败: {str(e)}")

@router.get("/google-ads-configs", response_model=List[GoogleAdsConfigResponse], summary="获取Google Ads配置列表")
def get_google_ads_configs(
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """获取 Google Ads API 配置列表"""
    query = db.query(GoogleAdsConfig)
    if active_only:
        query = query.filter(GoogleAdsConfig.is_active == True)
    
    return query.order_by(GoogleAdsConfig.created_at.desc()).all()

@router.get("/google-ads-configs/{config_id}", response_model=GoogleAdsConfigResponse, summary="获取Google Ads配置详情")
def get_google_ads_config(config_id: int, db: Session = Depends(get_db)):
    """获取 Google Ads API 配置详情"""
    config = db.query(GoogleAdsConfig).filter(GoogleAdsConfig.id == config_id).first()
    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")
    return config

@router.put("/google-ads-configs/{config_id}", response_model=GoogleAdsConfigResponse, summary="更新Google Ads配置")
def update_google_ads_config(
    config_id: int,
    config_data: GoogleAdsConfigUpdate,
    db: Session = Depends(get_db)
):
    """更新 Google Ads API 配置"""
    try:
        config = db.query(GoogleAdsConfig).filter(GoogleAdsConfig.id == config_id).first()
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        update_data = config_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(config, field, value)
        
        db.commit()
        db.refresh(config)
        return config
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")

@router.delete("/google-ads-configs/{config_id}", summary="删除Google Ads配置")
def delete_google_ads_config(config_id: int, db: Session = Depends(get_db)):
    """删除 Google Ads API 配置"""
    try:
        config = db.query(GoogleAdsConfig).filter(GoogleAdsConfig.id == config_id).first()
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        db.delete(config)
        db.commit()
        return {"success": True, "message": "配置删除成功"}
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除配置失败: {str(e)}")

@router.post("/google-ads-configs/{config_id}/test", summary="测试Google Ads连接")
def test_google_ads_connection(config_id: int, db: Session = Depends(get_db)):
    """测试 Google Ads API 连接"""
    try:
        config = db.query(GoogleAdsConfig).filter(GoogleAdsConfig.id == config_id).first()
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        from ..services.google_ads_service import GoogleAdsService
        
        ads_config = {
            "developer_token": config.developer_token,
            "client_id": config.client_id,
            "client_secret": config.client_secret,
            "refresh_token": config.refresh_token,
            "customer_id": config.customer_id,
            "login_customer_id": config.login_customer_id,
            "api_key": config.api_key
        }
        
        # 准备代理配置
        proxy_config = None
        if config.use_proxy and config.proxy_host:
            proxy_config = {
                'use_proxy': config.use_proxy,
                'proxy_host': config.proxy_host,
                'proxy_port': config.proxy_port,
                'proxy_username': config.proxy_username,
                'proxy_password': config.proxy_password,
                'proxy_type': config.proxy_type or 'http'
            }
        
        service = GoogleAdsService(ads_config, proxy_config)
        is_connected = service.test_connection()
        
        return {
            "success": is_connected,
            "message": "连接成功" if is_connected else "连接失败，请检查配置"
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"连接测试失败: {str(e)}"
        }

# OAuth授权相关API
@router.post("/google-ads-configs/{config_id}/oauth/authorize", summary="启动OAuth授权")
def start_oauth_authorization(config_id: int, request: Request, db: Session = Depends(get_db)):
    """启动Google Ads OAuth授权流程"""
    try:
        config = db.query(GoogleAdsConfig).filter(GoogleAdsConfig.id == config_id).first()
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        from ..services.google_oauth_service import GoogleOAuthService
        
        # 使用配置文件中的域名，如果没有配置则尝试从请求头获取
        if settings.FRONTEND_URL and settings.FRONTEND_URL != "http://localhost:8080":
            base_url = settings.FRONTEND_URL
        else:
            # 获取真实的域名和协议（回退方案）
            real_scheme = request.headers.get("X-Forwarded-Proto") or request.url.scheme
            real_host = (
                request.headers.get("X-Forwarded-Host") or 
                request.headers.get("Host") or 
                request.url.netloc
            )
            base_url = f"{real_scheme}://{real_host}"
        
        # 记录调试信息
        logger.info(f"Request URL: {request.url}")
        logger.info(f"Headers: X-Forwarded-Proto={request.headers.get('X-Forwarded-Proto')}, "
                   f"X-Forwarded-Host={request.headers.get('X-Forwarded-Host')}, "
                   f"Host={request.headers.get('Host')}")
        logger.info(f"Settings FRONTEND_URL: {settings.FRONTEND_URL}")
        logger.info(f"Using base_url: {base_url}")
        
        # 动态构建回调URL - 使用统一的回调地址
        redirect_uri = f"{base_url}/api/v1/keyword-library/google-ads-configs/oauth/callback"
        
        logger.info(f"Generated redirect_uri: {redirect_uri}")
        
        # 准备代理配置
        proxy_config = None
        if config.use_proxy and config.proxy_host:
            proxy_config = {
                'use_proxy': config.use_proxy,
                'proxy_host': config.proxy_host,
                'proxy_port': config.proxy_port,
                'proxy_username': config.proxy_username,
                'proxy_password': config.proxy_password,
                'proxy_type': config.proxy_type or 'http'
            }
            logger.info(f"使用代理配置: {config.proxy_type}://{config.proxy_host}:{config.proxy_port}")
        
        # 创建OAuth服务
        oauth_service = GoogleOAuthService(
            client_id=config.client_id,
            client_secret=config.client_secret,
            redirect_uri=redirect_uri,
            proxy_config=proxy_config
        )
        
        # 生成授权URL，在state中包含config_id
        authorization_url, original_state = oauth_service.generate_authorization_url()
        
        # 修改state格式：config_id:原始state
        combined_state = f"{config_id}:{original_state}"
        
        # 替换URL中的state参数
        parsed_url = urllib.parse.urlparse(authorization_url)
        query_params = urllib.parse.parse_qs(parsed_url.query)
        query_params['state'] = [combined_state]
        new_query = urllib.parse.urlencode(query_params, doseq=True)
        new_authorization_url = urllib.parse.urlunparse((
            parsed_url.scheme, parsed_url.netloc, parsed_url.path,
            parsed_url.params, new_query, parsed_url.fragment
        ))
        
        # 保存state到数据库
        config.oauth_state = combined_state
        config.authorization_status = "pending"
        db.commit()
        
        return {
            "authorization_url": new_authorization_url,
            "state": combined_state,
            "redirect_uri": redirect_uri,  # 返回实际使用的回调URL供调试
            "message": "请在浏览器中完成授权"
        }
        
    except Exception as e:
        logger.error(f"启动OAuth授权失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动授权失败: {str(e)}")

@router.get("/google-ads-configs/oauth/callback", summary="OAuth授权回调")
def oauth_callback(
    request: Request,
    code: str = None,
    state: str = None,
    error: str = None,
    db: Session = Depends(get_db)
):
    """处理Google OAuth授权回调"""
    # 记录详细的调试信息
    logger.info(f"OAuth回调接收到的参数: code={code}, state={state}, error={error}")
    logger.info(f"请求URL: {request.url}")
    logger.info(f"查询参数: {dict(request.query_params)}")
    
    config = None  # 初始化config变量
    try:
        # 从state中解析config_id
        if not state or ":" not in state:
            raise HTTPException(status_code=400, detail="无效的state参数格式")
        
        config_id_str, original_state = state.split(":", 1)
        try:
            config_id = int(config_id_str)
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的配置ID")
        
        config = db.query(GoogleAdsConfig).filter(GoogleAdsConfig.id == config_id).first()
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        # 检查是否有错误
        if error:
            config.authorization_status = "failed"
            db.commit()
            return {"error": f"授权失败: {error}"}
        
        # 验证state参数
        if not state or state != config.oauth_state:
            config.authorization_status = "failed"
            db.commit()
            raise HTTPException(status_code=400, detail="无效的state参数")
        
        # 检查授权码
        if not code:
            config.authorization_status = "failed"
            db.commit()
            raise HTTPException(status_code=400, detail="缺少授权码")
        
        from ..services.google_oauth_service import GoogleOAuthService
        from datetime import datetime, timedelta
        
        # 使用配置文件中的域名，如果没有配置则尝试从请求头获取
        if settings.FRONTEND_URL and settings.FRONTEND_URL != "http://localhost:8080":
            base_url = settings.FRONTEND_URL
        else:
            # 获取真实的域名和协议（回退方案）
            real_scheme = request.headers.get("X-Forwarded-Proto") or request.url.scheme
            real_host = (
                request.headers.get("X-Forwarded-Host") or 
                request.headers.get("Host") or 
                request.url.netloc
            )
            base_url = f"{real_scheme}://{real_host}"
        
        # 动态构建回调URL - 使用统一的回调地址
        redirect_uri = f"{base_url}/api/v1/keyword-library/google-ads-configs/oauth/callback"
        
        # 准备代理配置
        proxy_config = None
        if config.use_proxy and config.proxy_host:
            proxy_config = {
                'use_proxy': config.use_proxy,
                'proxy_host': config.proxy_host,
                'proxy_port': config.proxy_port,
                'proxy_username': config.proxy_username,
                'proxy_password': config.proxy_password,
                'proxy_type': config.proxy_type or 'http'
            }
        
        # 创建OAuth服务
        oauth_service = GoogleOAuthService(
            client_id=config.client_id,
            client_secret=config.client_secret,
            redirect_uri=redirect_uri,
            proxy_config=proxy_config
        )
        
        # 交换授权码获取令牌，使用原始state
        tokens = oauth_service.exchange_code_for_tokens(code, original_state)
        
        # 更新配置信息
        config.refresh_token = tokens["refresh_token"]
        config.access_token = tokens["access_token"]
        config.authorization_status = "authorized"
        config.last_auth_time = utc_now()
        
        # 设置令牌过期时间（通常为1小时）
        if tokens.get("expiry"):
            from dateutil.parser import parse
            config.token_expiry = parse(tokens["expiry"])
        else:
            config.token_expiry = utc_now() + timedelta(hours=1)
        
        # 清除OAuth状态
        config.oauth_state = None
        
        db.commit()
        
        # 返回成功页面
        return {
            "success": True,
            "message": "授权成功！您可以关闭此窗口。",
            "redirect_url": f"{base_url}/keyword-library?oauth_success=1"
        }
        
    except HTTPException:
        # 如果是HTTPException，重新抛出
        if config:
            config.authorization_status = "failed"
            db.commit()
        raise
    except Exception as e:
        logger.error(f"OAuth回调处理失败: {e}")
        if config:
            config.authorization_status = "failed"
            db.commit()
        raise HTTPException(status_code=500, detail=f"授权处理失败: {str(e)}")

@router.get("/google-ads-configs/{config_id}/oauth/status", summary="获取OAuth授权状态")
def get_oauth_status(config_id: int, db: Session = Depends(get_db)):
    """获取OAuth授权状态"""
    try:
        config = db.query(GoogleAdsConfig).filter(GoogleAdsConfig.id == config_id).first()
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        from datetime import datetime
        
        # 检查令牌是否过期
        is_token_expired = False
        if config.token_expiry:
            is_token_expired = config.token_expiry < utc_now()
        
        return {
            "config_id": config_id,
            "authorization_status": config.authorization_status,
            "has_refresh_token": bool(config.refresh_token),
            "last_auth_time": config.last_auth_time.isoformat() if config.last_auth_time else None,
            "token_expired": is_token_expired,
            "token_expiry": config.token_expiry.isoformat() if config.token_expiry else None
        }
        
    except Exception as e:
        logger.error(f"获取OAuth状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.post("/google-ads-configs/{config_id}/oauth/refresh", summary="刷新访问令牌")
def refresh_oauth_token(config_id: int, db: Session = Depends(get_db)):
    """刷新OAuth访问令牌"""
    try:
        config = db.query(GoogleAdsConfig).filter(GoogleAdsConfig.id == config_id).first()
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        if not config.refresh_token:
            raise HTTPException(status_code=400, detail="没有刷新令牌，请重新授权")
        
        from ..services.google_oauth_service import GoogleOAuthService
        from datetime import datetime, timedelta
        
        # 准备代理配置
        proxy_config = None
        if config.use_proxy and config.proxy_host:
            proxy_config = {
                'use_proxy': config.use_proxy,
                'proxy_host': config.proxy_host,
                'proxy_port': config.proxy_port,
                'proxy_username': config.proxy_username,
                'proxy_password': config.proxy_password,
                'proxy_type': config.proxy_type or 'http'
            }
        
        # 创建OAuth服务
        oauth_service = GoogleOAuthService(
            client_id=config.client_id,
            client_secret=config.client_secret,
            proxy_config=proxy_config
        )
        
        # 刷新访问令牌
        tokens = oauth_service.refresh_access_token(config.refresh_token)
        
        # 更新配置信息
        config.access_token = tokens["access_token"]
        config.authorization_status = "authorized"
        
        # 更新令牌过期时间
        if tokens.get("expiry"):
            from dateutil.parser import parse
            config.token_expiry = parse(tokens["expiry"])
        else:
            config.token_expiry = utc_now() + timedelta(hours=1)
        
        db.commit()
        
        return {
            "success": True,
            "message": "令牌刷新成功",
            "token_expiry": config.token_expiry.isoformat()
        }
        
    except Exception as e:
        logger.error(f"刷新令牌失败: {e}")
        config.authorization_status = "expired"
        db.commit()
        raise HTTPException(status_code=500, detail=f"刷新令牌失败: {str(e)}")

# 导出功能
@router.get("/export", summary="导出关键词库")
def export_keywords(
    format: str = "csv",  # csv 或 excel
    keyword: Optional[str] = None,
    country: Optional[str] = None,  # 添加country参数
    competition: Optional[str] = None,  # 前端发送的competition字段
    competition_level: Optional[str] = None,  # 备选参数
    category: Optional[str] = None,  # 添加分类参数
    tags: Optional[str] = None,
    sort_by: str = "updated_at",
    sort_order: str = "desc",
    db: Session = Depends(get_db)
):
    """
    导出关键词库数据
    支持 CSV 和 Excel 格式
    可以根据搜索条件导出筛选后的数据
    """
    try:
        # 检查pandas是否可用
        try:
            import pandas as pd
            from fastapi.responses import StreamingResponse
            import io
        except ImportError:
            raise HTTPException(
                status_code=500, 
                detail="导出功能需要安装pandas和openpyxl。请运行: pip install pandas openpyxl"
            )
        
        # 直接构建数据库查询，不使用KeywordSearchRequest来避免page_size限制
        from ..models.keyword_library import KeywordLibrary, CompetitionLevel as ModelCompetitionLevel
        from sqlalchemy import or_, desc, asc
        
        query = db.query(KeywordLibrary)
        
        # 关键词搜索
        if keyword:
            query = query.filter(
                or_(
                    KeywordLibrary.keyword_name.contains(keyword),
                    KeywordLibrary.tags.contains(keyword)
                )
            )
        
        # 竞争级别过滤
        competition_value = competition_level or competition
        if competition_value:
            try:
                comp_level = ModelCompetitionLevel(competition_value)
                query = query.filter(KeywordLibrary.competition_level == comp_level)
            except ValueError:
                pass  # 忽略无效的竞争级别值
        
        # 地理位置过滤
        if country:
            query = query.filter(KeywordLibrary.location_ids.contains(country))
        
        # 分类过滤
        if category:
            query = query.filter(KeywordLibrary.category == category)
        
        # 标签过滤
        if tags:
            tags_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
            if tags_list:
                tag_conditions = []
                for tag in tags_list:
                    tag_conditions.append(KeywordLibrary.tags.contains(tag))
                query = query.filter(or_(*tag_conditions))
        
        # 排序
        if sort_by == "avg_monthly_searches":
            order_column = KeywordLibrary.avg_monthly_searches
        elif sort_by == "competition_index":
            order_column = KeywordLibrary.competition_index
        elif sort_by == "keyword_name":
            order_column = KeywordLibrary.keyword_name
        elif sort_by == "created_at":
            order_column = KeywordLibrary.created_at
        else:
            order_column = KeywordLibrary.updated_at
        
        if sort_order == "asc":
            query = query.order_by(asc(order_column))
        else:
            query = query.order_by(desc(order_column))
        
        # 获取所有符合条件的关键词（不限制数量）
        keywords = query.all()
        
        # 准备数据
        data = []
        for keyword in keywords:
            data.append({
                "关键词名": keyword.keyword_name or "",
                # 新字段
                "意图": keyword.intent or "",
                "搜索量": keyword.volume or 0,
                "趋势": keyword.trend or "",
                "关键词难度": keyword.keyword_difficulty or 0,
                "CPC (USD)": keyword.cpc_usd or 0,
                "竞争密度": keyword.competitive_density or 0,
                "SERP特征": keyword.serp_features or "",
                "搜索结果数": keyword.number_of_results or 0,
                # 兼容字段
                "平均每月搜索量": keyword.avg_monthly_searches or 0,
                "竞争级别": keyword.competition_level.value if keyword.competition_level else "",
                "竞争指数": keyword.competition_index or 0,
                "出价第20百分位": (keyword.low_bid_micros / 1000000) if keyword.low_bid_micros else 0,
                "出价第80百分位": (keyword.high_bid_micros / 1000000) if keyword.high_bid_micros else 0,
                "货币代码": keyword.currency_code or "",
                "语言代码": keyword.language_code or "",
                "地理位置ID": keyword.location_ids or "",
                "更新方式": keyword.update_method.value if keyword.update_method else "",
                "分类": keyword.category or "",
                "标签": keyword.tags or "",
                "创建时间": keyword.created_at.strftime("%Y-%m-%d %H:%M:%S") if keyword.created_at else "",
                "更新时间": keyword.updated_at.strftime("%Y-%m-%d %H:%M:%S") if keyword.updated_at else ""
            })
        
        # 如果没有数据，创建空的DataFrame
        if not data:
            data = [{
                "关键词名": "",
                # 新字段
                "意图": "",
                "搜索量": 0,
                "趋势": "",
                "关键词难度": 0,
                "CPC (USD)": 0,
                "竞争密度": 0,
                "SERP特征": "",
                "搜索结果数": 0,
                # 兼容字段
                "平均每月搜索量": 0,
                "竞争级别": "",
                "竞争指数": 0,
                "出价第20百分位": 0,
                "出价第80百分位": 0,
                "货币代码": "",
                "语言代码": "",
                "地理位置ID": "",
                "更新方式": "",
                "分类": "",
                "标签": "",
                "创建时间": "",
                "更新时间": ""
            }]
        
        df = pd.DataFrame(data)
        
        if format.lower() == "excel":
            # 导出 Excel
            try:
                import openpyxl
                output = io.BytesIO()
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name='关键词库')
                output.seek(0)
                
                return StreamingResponse(
                    io.BytesIO(output.read()),
                    media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    headers={"Content-Disposition": "attachment; filename=keywords.xlsx"}
                )
            except ImportError:
                raise HTTPException(
                    status_code=500,
                    detail="Excel导出需要安装openpyxl。请运行: pip install openpyxl"
                )
        else:
            # 导出 CSV
            output = io.StringIO()
            df.to_csv(output, index=False, encoding='utf-8-sig')
            output.seek(0)
            
            return StreamingResponse(
                io.BytesIO(output.getvalue().encode('utf-8-sig')),
                media_type="text/csv",
                headers={"Content-Disposition": "attachment; filename=keywords.csv"}
            )
            
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()  # 打印详细错误信息
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

# PyTrends API 集成
@router.post("/import/pytrends/{config_id}", response_model=PyTrendsTaskStartResponse, summary="从PyTrends导入")
def import_from_pytrends(
    config_id: int,
    request: PyTrendsKeywordRequest,
    db: Session = Depends(get_db),
    operator_id: Optional[int] = None,
    operator_name: Optional[str] = None
):
    """从 PyTrends 导入关键词"""
    service = KeywordLibraryService(db)
    return service.import_keywords_from_pytrends(config_id, request, operator_id, operator_name)

@router.get("/pytrends-tasks/{task_id}", summary="获取PyTrends任务状态")
def get_pytrends_task_status(
    task_id: str,
    db: Session = Depends(get_db)
):
    """获取PyTrends导入任务状态"""
    service = KeywordLibraryService(db)
    return service.get_pytrends_task_status(task_id)

# PyTrends 配置管理
@router.post("/pytrends-configs", response_model=PyTrendsConfigResponse, summary="创建PyTrends配置")
def create_pytrends_config(config_data: PyTrendsConfigCreate, db: Session = Depends(get_db)):
    """创建 PyTrends 配置"""
    service = PyTrendsConfigService(db)
    return service.create_config(config_data)

@router.get("/pytrends-configs", response_model=List[PyTrendsConfigResponse], summary="获取PyTrends配置列表")
def get_pytrends_configs(
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """获取PyTrends配置列表"""
    service = PyTrendsConfigService(db)
    return service.get_configs(active_only)

@router.get("/pytrends-configs/{config_id}", response_model=PyTrendsConfigResponse, summary="获取PyTrends配置详情")
def get_pytrends_config(config_id: int, db: Session = Depends(get_db)):
    """获取PyTrends配置详情"""
    service = PyTrendsConfigService(db)
    return service.get_config(config_id)

@router.put("/pytrends-configs/{config_id}", response_model=PyTrendsConfigResponse, summary="更新PyTrends配置")
def update_pytrends_config(
    config_id: int,
    config_data: PyTrendsConfigUpdate,
    db: Session = Depends(get_db)
):
    """更新PyTrends配置"""
    service = PyTrendsConfigService(db)
    return service.update_config(config_id, config_data)

@router.delete("/pytrends-configs/{config_id}", summary="删除PyTrends配置")
def delete_pytrends_config(config_id: int, db: Session = Depends(get_db)):
    """删除PyTrends配置"""
    service = PyTrendsConfigService(db)
    result = service.delete_config(config_id)
    return {"success": result, "message": "配置删除成功"}

@router.post("/pytrends-configs/{config_id}/test", summary="测试PyTrends连接")
def test_pytrends_connection(config_id: int, db: Session = Depends(get_db)):
    """测试PyTrends连接"""
    service = PyTrendsConfigService(db)
    return service.test_connection(config_id)

@router.post("/pytrends-configs/{config_id}/toggle", response_model=PyTrendsConfigResponse, summary="切换PyTrends配置状态")
def toggle_pytrends_config_status(config_id: int, db: Session = Depends(get_db)):
    """切换PyTrends配置的激活状态"""
    service = PyTrendsConfigService(db)
    return service.toggle_active_status(config_id)

@router.get("/pytrends-configs/{config_id}/stats", summary="获取PyTrends配置统计")
def get_pytrends_config_stats(config_id: int, db: Session = Depends(get_db)):
    """获取PyTrends配置的使用统计"""
    service = PyTrendsConfigService(db)
    return service.get_config_stats(config_id) 