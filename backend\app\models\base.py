from sqlalchemy import Column, DateTime, func, <PERSON><PERSON>an, Foreign<PERSON>ey, Integer, String, Float, Text, JSON
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from app.utils.datetime_utils import utc_now

class CustomBase:
    """
    数据库模型基类

    统一使用UTC时间存储：
    - created_at: 创建时间（UTC）
    - updated_at: 更新时间（UTC）
    - 所有时间字段都带时区信息
    """

    @declared_attr
    def __tablename__(cls):
        return cls.__name__.lower()

    # 所有表的通用列
    id = Column(Integer, primary_key=True, index=True)
    created_at = Column(DateTime(timezone=True), nullable=False, default=utc_now)
    updated_at = Column(DateTime(timezone=True), nullable=True, onupdate=utc_now)

Base = declarative_base(cls=CustomBase)

# 为了兼容性，提供BaseModel别名
BaseModel = Base 