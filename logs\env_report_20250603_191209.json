{"timestamp": "2025-06-03T19:11:53.483555", "system_info": {"platform": "Windows-10-10.0.19045-SP0", "system": "Windows", "release": "10", "version": "10.0.19045", "machine": "AMD64", "processor": "Intel64 Family 6 Model 165 Stepping 3, GenuineIntel", "python_version": "3.13.3", "python_implementation": "CPython"}, "python_env": {"version": [3, 13, 3, "final", 0], "executable": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe", "path": ["D:\\AI\\AICBEC\\CBEC\\scripts", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages"], "pip_available": true, "venv_active": false, "venv_path": "D:\\AI\\AICBEC\\venv", "venv_python": "D:\\AI\\AICBEC\\venv\\Scripts\\python.exe", "packages": {}}, "node_env": {"node_available": true, "npm_available": true, "node_version": "v22.14.0", "npm_version": "10.9.2", "npm_path": "C:\\Program Files\\nodejs\\npm.cmd"}, "dependencies": {}, "issues": ["前端依赖未安装"], "recommendations": ["建议激活虚拟环境: D:\\AI\\AICBEC\\venv"], "backend_deps": {"python_executable": "D:\\AI\\AICBEC\\venv\\Scripts\\python.exe", "requirements_file": "D:\\AI\\AICBEC\\CBEC\\backend\\requirements.txt", "total_packages": 27, "installed_packages": 27, "missing_packages": [], "installed_list": ["<PERSON><PERSON><PERSON>", "u<PERSON><PERSON>", "sqlalchemy", "pydantic", "python-jose", "passlib", "bcrypt", "alembic", "python-multipart", "pydantic-settings", "pymysql", "requests", "httpx", "beautifulsoup4", "pandas", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seaborn", "google-ads", "google-api-python-client", "google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "python-dateutil", "openpyxl", "xlsxwriter", "pytrends", "trendspy"]}}