<template>
  <div class="my-sites" :class="{ 'sidebar-collapsed': isCollapse }">
    <!-- 主要内容区域 - 左右布局 -->
    <div class="main-content">
      <!-- 左侧添加表单区域 - 隐藏 -->
      <div class="form-section hidden">
      </div>

      <!-- 中间分类筛选栏 -->
      <div class="category-filter-section">
        <el-card class="category-filter-card">
          <template #header>
            <div class="category-filter-header">
              <el-icon class="header-icon"><Collection /></el-icon>
              <span>分类导航</span>
            </div>
          </template>
          
          <!-- 分类搜索 -->
          <div class="category-search">
            <div class="search-with-sort">
            <el-input
              v-model="categorySearchKeyword"
              placeholder="搜索分类"
              clearable
              size="small"
              @input="handleCategorySearch"
              @clear="handleCategorySearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
              <el-dropdown @command="handleCategorySortChange" trigger="click">
                <el-button size="small" class="sort-button">
                  <el-icon><Sort /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="name-asc">按名称A-Z</el-dropdown-item>
                    <el-dropdown-item command="name-desc">按名称Z-A</el-dropdown-item>
                    <el-dropdown-item command="count-desc">按数量降序</el-dropdown-item>
                    <el-dropdown-item command="count-asc">按数量升序</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        
          <!-- 分类列表 -->
          <div class="category-list">
            <!-- 显示"全部"选项 -->
            <div
              class="category-item all-category"
              :class="{ 'active': searchForm.category === '' }"
              @click="handleCategoryFilter('')"
              @contextmenu.prevent="showCategoryContextMenu($event, '')"
            >
              <div class="category-content">
                <el-icon class="category-icon"><Grid /></el-icon>
                <span class="category-text">全部分类</span>
              </div>
              <span class="category-count">
                ({{ summary.total_sites || 0 }})
              </span>
            </div>

            <!-- 其他分类 -->
            <div
              v-for="category in paginatedCategories"
              :key="category"
              class="category-item"
              :class="{ 'active': searchForm.category === category }"
              @click="handleCategoryFilter(category)"
              @contextmenu.prevent="showCategoryContextMenu($event, category)"
            >
              <div class="category-content">
                <el-icon class="category-icon"><Folder /></el-icon>
                <span class="category-text">{{ category && String(category).trim() ? String(category).trim() : '未分类' }}</span>
              </div>
              <span class="category-count" v-if="categoryCounts[category]">
                ({{ categoryCounts[category] }})
              </span>
            </div>

            <!-- 显示"未分类"选项 - 放在最下方 -->
            <div
              class="category-item uncategorized-item"
              :class="{ 'active': searchForm.category === 'uncategorized' }"
              @click="handleCategoryFilter('uncategorized')"
              @contextmenu.prevent="showCategoryContextMenu($event, 'uncategorized')"
            >
              <div class="category-content">
                <el-icon class="category-icon"><FolderOpened /></el-icon>
                <span class="category-text">未分类</span>
              </div>
              <span class="category-count" v-if="categoryCounts['uncategorized']">
                ({{ categoryCounts['uncategorized'] }})
              </span>
            </div>
          </div>
            
          <!-- 分类分页 -->
          <div class="category-pagination" v-if="filteredCategories.length > categoryPageSize">
            <el-pagination
              v-model:current-page="categoryPagination.page"
              :page-size="categoryPageSize"
              :total="filteredCategories.length"
              layout="prev, pager, next"
              small
              @current-change="handleCategoryPageChange"
            />
          </div>
            
          <!-- 分类管理按钮 -->
          <div class="category-actions">
            <el-button 
              type="primary" 
              size="small" 
              @click="showAddCategoryDialog"
              :icon="Plus"
            >
              添加
            </el-button>
            <el-button 
                size="small"
              @click="showEditCategoryDialog"
              :icon="Edit"
              :disabled="!searchForm.category || searchForm.category === ''"
            >
              修改
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="confirmDeleteCategory"
              :icon="Delete"
              :disabled="!searchForm.category || searchForm.category === ''"
              class="delete-btn"
            >
              删除
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 右侧站点列表区域 -->
      <div class="list-section">
        <el-card class="list-card">
          <template #header>
            <div class="content-header">
              <div class="list-header">
                <el-icon class="header-icon"><Compass /></el-icon>
                <span>站点管理</span>
              </div>
              
              <!-- 统计栏移到这里 -->
              <div class="header-stats">
                <div class="stat-item">
                  <div class="stat-number">{{ summary.total_sites || 0 }}</div>
                  <div class="stat-label">总站点数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ summary.active_sites || 0 }}</div>
                  <div class="stat-label">活跃站点</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ formatNumber(summary.total_daily_uv) }}</div>
                  <div class="stat-label">今日总UV</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ formatNumber(summary.total_daily_pv) }}</div>
                  <div class="stat-label">今日总PV</div>
                </div>
              </div>
              
              <div class="search-filters">
                <el-input
                  v-model="searchForm.name"
                  placeholder="搜索站点名称"
                  style="width: 200px; margin-right: 10px;"
                  @input="handleSearch"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                
                <el-select
                  v-model="searchForm.is_active"
                  placeholder="状态"
                  style="width: 120px; margin-right: 10px;"
                  @change="loadSites"
                  clearable
                >
                  <el-option label="启用" :value="true" />
                  <el-option label="禁用" :value="false" />
                </el-select>
                
                <el-button @click="refreshAll" :loading="refreshing" :icon="Refresh">
                  刷新数据
                </el-button>
                
                <el-button 
                  type="success" 
                  @click="syncAllSites" 
                  :loading="batchSyncing" 
                  :icon="Refresh"
                  class="sync-all-button"
                  :class="{ 'syncing': isSyncing }"
                  :disabled="isSyncing"
                >
                  一键同步
                </el-button>
                
                <el-button type="primary" @click="handleQuickAddSite" :icon="Plus">
                  添加站点
                </el-button>
              </div>
            </div>
          </template>

          <!-- 站点列表 -->
          <el-table
            :data="sites"
            style="width: 100%"
            v-loading="loading"
            row-key="id"
          >
            <el-table-column prop="name" label="站点名称" min-width="120">
              <template #default="{ row }">
                <div class="site-name">
                  <el-link :href="row.url" target="_blank" type="primary">
                    {{ row.name }}
                  </el-link>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column prop="category" label="站点分类" width="100">
              <template #default="{ row }">
                <el-tag v-if="row.category" size="small" type="primary" class="site-category-tag">
                  {{ row.category }}
                </el-tag>
                <span v-else class="no-category">未分类</span>
              </template>
            </el-table-column>
            
            <el-table-column prop="url" label="站点URL" min-width="150">
              <template #default="{ row }">
                <el-link :href="row.url" target="_blank" type="primary">
                  {{ row.url }}
                </el-link>
              </template>
            </el-table-column>
            
            <el-table-column label="当日数据" width="100">
              <template #default="{ row }">
                <div class="stats-cell">
                  <div>UV: {{ formatNumber(row.daily_uv) }}</div>
                  <div>PV: {{ formatNumber(row.daily_pv) }}</div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="当月数据" width="100">
              <template #default="{ row }">
                <div class="stats-cell">
                  <div>UV: {{ formatNumber(row.monthly_uv) }}</div>
                  <div>PV: {{ formatNumber(row.monthly_pv) }}</div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="内容统计" width="100">
              <template #default="{ row }">
                <div class="stats-cell">
                  <div>文章: {{ row.total_posts }}</div>
                  <div>页面: {{ row.total_pages }}</div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="站点信息" width="120">
              <template #default="{ row }">
                <div class="site-info">
                  <div v-if="row.wordpress_version">
                    <el-tag size="small">WP {{ row.wordpress_version }}</el-tag>
                  </div>
                  <div v-if="row.theme_name" class="theme-info">
                    主题: {{ row.theme_name }}
                  </div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="博客分类" width="140">
              <template #default="{ row }">
                <div class="blog-categories">
                  <div v-if="row.blog_categories && row.blog_categories.length > 0" class="categories-summary">
                    <span class="count-text">共{{ row.blog_categories.length }}个分类</span>
                    <el-button
                      size="small"
                      type="text"
                      @click="showCategoryDetails(row)"
                      class="view-button"
                    >
                      查看
                    </el-button>
                  </div>
                  <div v-else class="no-categories">
                    <el-text type="info" size="small">暂无分类</el-text>
                  </div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="博客标签" width="140">
              <template #default="{ row }">
                <div class="blog-tags">
                  <div v-if="row.blog_tags && row.blog_tags.length > 0" class="tags-summary">
                    <span class="count-text">共{{ row.blog_tags.length }}个标签</span>
                    <el-button
                      size="small"
                      type="text"
                      @click="showTagDetails(row)"
                      class="view-button"
                    >
                      查看
                    </el-button>
                  </div>
                  <div v-else class="no-tags">
                    <el-text type="info" size="small">暂无标签</el-text>
                  </div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="同步状态" width="120">
              <template #default="{ row }">
                <div class="sync-status">
                  <el-tag
                    :type="getSyncStatusType(row.sync_status)"
                    size="small"
                  >
                    {{ getSyncStatusText(row.sync_status) }}
                  </el-tag>
                  <div v-if="row.last_sync_at" class="sync-time">
                    {{ formatTime(row.last_sync_at) }}
                  </div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="状态" width="80">
              <template #default="{ row }">
                <el-switch
                  v-model="row.is_active"
                  @change="toggleSiteStatus(row)"
                />
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <div class="action-buttons compact">
                  <el-button
                    size="small"
                    type="primary"
                    @click="syncSite(row)"
                    :loading="row.syncing"
                    :icon="Refresh"
                  />
                  
                  <el-button
                    size="small"
                    @click="editSite(row)"
                    :icon="Edit"
                  />
                  
                  <el-popconfirm
                    title="确定要删除这个站点吗？"
                    @confirm="deleteSite(row)"
                  >
                    <template #reference>
                      <el-button
                        size="small"
                        type="danger"
                        :icon="Delete"
                      />
                    </template>
                  </el-popconfirm>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.size"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="loadSites"
              @current-change="loadSites"
            />
          </div>
        </el-card>
      </div>
    </div>

    <!-- 添加站点对话框 -->
    <el-dialog
      v-model="addDialogVisible"
      title="添加站点"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="addFormRef"
        :model="siteForm"
        :rules="siteRules"
        label-width="120px"
      >
        <el-form-item label="站点名称" prop="name">
          <el-input v-model="siteForm.name" placeholder="请输入站点名称" />
        </el-form-item>

        <el-form-item label="站点URL" prop="url">
          <el-input
            v-model="siteForm.url"
            placeholder="https://example.com"
          />
        </el-form-item>

        <el-form-item label="站点分类" prop="category">
          <el-autocomplete
            v-model="siteForm.category"
            placeholder="选择现有分类或输入新分类名称"
            :fetch-suggestions="queryCategories"
            :trigger-on-focus="true"
            clearable
            style="width: 100%;"
            @select="handleCategorySelect"
            value-key="value"
          >
            <template #default="{ item }">
              <div class="category-suggestion-item">
                <span class="category-name">{{ item.value }}</span>
                <span class="category-count" v-if="item.count">({{ item.count }}个站点)</span>
              </div>
            </template>
          </el-autocomplete>
          <div class="form-tip">
            选择现有分类或输入新分类名称，如：企业官网、电商网站、博客站点等
          </div>
        </el-form-item>

        <el-form-item label="WordPress用户名" prop="wp_username">
          <el-input v-model="siteForm.wp_username" placeholder="WordPress管理员用户名" />
          <div class="form-tip">
            上传图片创建分类需要edit角色权限
          </div>
        </el-form-item>

        <el-form-item label="应用程序密码" prop="wp_app_password">
          <el-input
            v-model="siteForm.wp_app_password"
            type="password"
            placeholder="WordPress应用程序密码"
            show-password
          />
          <div class="form-tip">
            在WordPress后台 用户 -> 个人资料 -> 应用程序密码 中生成
          </div>
        </el-form-item>

        <el-form-item label="站点描述" prop="description">
          <el-input
            v-model="siteForm.description"
            type="textarea"
            :rows="3"
            placeholder="站点描述信息（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitSite" :loading="submitting">
            <el-icon><Plus /></el-icon>
            添加站点
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑站点对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑站点"
      width="600px"
      @close="resetEditForm"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="siteRules"
        label-width="120px"
      >
        <el-form-item label="站点名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入站点名称" />
        </el-form-item>
        
        <el-form-item label="站点URL" prop="url">
          <el-input
            v-model="editForm.url"
            placeholder="https://example.com"
            disabled
          />
        </el-form-item>
        
        <el-form-item label="站点分类" prop="category">
          <el-autocomplete
            v-model="editForm.category"
            placeholder="选择现有分类或输入新分类名称"
            :fetch-suggestions="queryCategories"
            :trigger-on-focus="true"
            clearable
            style="width: 100%;"
            @select="handleCategorySelect"
            value-key="value"
          >
            <template #default="{ item }">
              <div class="category-suggestion-item">
                <span class="category-name">{{ item.value }}</span>
                <span class="category-count" v-if="item.count">({{ item.count }}个站点)</span>
              </div>
            </template>
          </el-autocomplete>
          <div class="form-tip">
            选择现有分类或输入新分类名称，如：企业官网、电商网站、博客站点等
          </div>
        </el-form-item>
        
        <el-form-item label="WordPress用户名" prop="wp_username">
          <el-input v-model="editForm.wp_username" placeholder="WordPress管理员用户名" />
          <div class="form-tip">
            上传图片创建分类需要edit角色权限
          </div>
        </el-form-item>
        
        <el-form-item label="应用程序密码" prop="wp_app_password">
          <el-input
            v-model="editForm.wp_app_password"
            type="password"
            placeholder="WordPress应用程序密码"
            show-password
          />
          <div class="form-tip">
            在WordPress后台 用户 -> 个人资料 -> 应用程序密码 中生成
          </div>
        </el-form-item>
        
        <el-form-item label="站点描述" prop="description">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
            placeholder="站点描述信息（可选）"
          />
        </el-form-item>
        
        <!-- 显示博客分类（只读） -->
        <el-form-item v-if="editForm.blog_categories && editForm.blog_categories.length > 0" label="博客分类">
          <div class="form-categories">
            <el-tag
              v-for="category in editForm.blog_categories"
              :key="category.id"
              size="small"
              class="category-tag-form"
              :title="`ID: ${category.id}, 文章数: ${category.count}, 描述: ${category.description || '无'}`"
            >
              {{ category.name }}
            </el-tag>
          </div>
          <div class="form-tip">
            博客分类来自WordPress站点，添加站点后自动同步
          </div>
        </el-form-item>
        
        <!-- 显示博客标签（只读） -->
        <el-form-item v-if="editForm.blog_tags && editForm.blog_tags.length > 0" label="博客标签">
          <div class="form-tags">
            <el-tag
              v-for="tag in getDisplayTags(editForm.blog_tags)"
              :key="tag.id"
              size="small"
              type="success"
              class="tag-form"
              :title="`ID: ${tag.id}, 文章数: ${tag.count}, 描述: ${tag.description || '无'}`"
            >
              {{ tag.name }}
            </el-tag>
            <el-tag
              v-if="editForm.blog_tags.length > getMaxDisplayTags()"
              size="small"
              type="info"
              class="tag-more"
            >
              等{{ editForm.blog_tags.length - getMaxDisplayTags() }}个标签
            </el-tag>
          </div>
          <div class="form-tip">
            博客标签来自WordPress站点，添加站点后自动同步
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <div class="action-buttons">
            <el-button @click="editDialogVisible = false" class="action-button secondary-button">
              取消
            </el-button>
            <el-button
              type="primary"
              @click="submitEditSite"
              :loading="submitting"
              class="action-button primary-button"
            >
              更新站点
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 右键菜单 -->
    <div 
      v-if="contextMenu.visible" 
      class="context-menu"
      :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
      @click.stop
    >
      <div class="context-menu-item" @click="showEditCategoryDialog">
        <el-icon><Edit /></el-icon>
        修改
      </div>
      <div class="context-menu-item" @click="showAddCategoryDialog">
        <el-icon><Plus /></el-icon>
        添加
      </div>
      <div class="context-menu-item danger" @click="confirmDeleteCategory">
        <el-icon><Delete /></el-icon>
        删除
      </div>
    </div>

    <!-- 添加分类对话框 -->
    <el-dialog
      v-model="addCategoryDialogVisible"
      title="添加分类"
      width="400px"
      @close="resetCategoryForm"
    >
      <el-form
        ref="addCategoryFormRef"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="80px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input
            v-model="categoryForm.name"
            placeholder="请输入分类名称"
            clearable
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addCategoryDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAddCategory" :loading="categorySubmitting">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改分类对话框 -->
    <el-dialog
      v-model="editCategoryDialogVisible"
      title="修改分类"
      width="400px"
      @close="resetCategoryForm"
    >
      <el-form
        ref="editCategoryFormRef"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="80px"
      >
        <el-form-item label="原分类">
          <el-input v-model="categoryForm.oldName" disabled />
        </el-form-item>
        <el-form-item label="新分类" prop="name">
          <el-input
            v-model="categoryForm.name"
            placeholder="请输入新的分类名称"
            clearable
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editCategoryDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleEditCategory" :loading="categorySubmitting">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 博客分类详情对话框 -->
    <el-dialog
      v-model="categoryDetailDialogVisible"
      title="博客分类详情"
      width="600px"
      @close="closeCategoryDetail"
    >
      <div class="category-detail-content">
        <div class="detail-header">
          <h4>{{ selectedSite?.name }} - 博客分类列表</h4>
          <p class="site-url">{{ selectedSite?.url }}</p>
        </div>
        
        <el-table
          :data="selectedSite?.blog_categories || []"
          style="width: 100%"
          max-height="400px"
        >
          <el-table-column prop="id" label="分类ID" width="80" />
          <el-table-column prop="name" label="分类名称" min-width="120" />
          <el-table-column prop="slug" label="Slug" min-width="100" />
          <el-table-column prop="count" label="文章数" width="80">
            <template #default="{ row }">
              <el-tag size="small" type="info">{{ row.count }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="150">
            <template #default="{ row }">
              <span v-if="row.description" :title="row.description">
                {{ row.description.length > 20 ? row.description.substring(0, 20) + '...' : row.description }}
              </span>
              <span v-else class="no-description">无描述</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="categoryDetailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 博客标签详情对话框 -->
    <el-dialog
      v-model="tagDetailDialogVisible"
      title="博客标签详情"
      width="600px"
      @close="closeTagDetail"
    >
      <div class="tag-detail-content">
        <div class="detail-header">
          <h4>{{ selectedSite?.name }} - 博客标签列表</h4>
          <p class="site-url">{{ selectedSite?.url }}</p>
        </div>
        
        <el-table
          :data="selectedSite?.blog_tags || []"
          style="width: 100%"
          max-height="400px"
        >
          <el-table-column prop="id" label="标签ID" width="80" />
          <el-table-column prop="name" label="标签名称" min-width="120" />
          <el-table-column prop="slug" label="Slug" min-width="100" />
          <el-table-column prop="count" label="文章数" width="80">
            <template #default="{ row }">
              <el-tag size="small" type="success">{{ row.count }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="150">
            <template #default="{ row }">
              <span v-if="row.description" :title="row.description">
                {{ row.description.length > 20 ? row.description.substring(0, 20) + '...' : row.description }}
              </span>
              <span v-else class="no-description">无描述</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="tagDetailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Compass,
  Plus,
  Search,
  Refresh,
  Edit,
  Delete,
  Fold,
  Collection,
  Sort,
  Grid,
  Folder,
  FolderOpened,
} from '@element-plus/icons-vue'
import apiClient from '@/utils/request'
import { formatDateTime } from '@/utils/timezone'

const store = useStore()
const isCollapse = computed(() => store.state.isCollapse)

// 数据状态
const loading = ref(false)
const refreshing = ref(false)
const submitting = ref(false)
const batchSyncing = ref(false)
const sites = ref([])
const categories = ref([])
const summary = ref({})

// 同步状态显示
const syncStatus = reactive({
  show: false,
  type: 'info',
  text: ''
})

// 添加同步中状态标志
const isSyncing = ref(false)

// 布局控制
const isFormCollapsed = ref(true)

// 搜索表单
const searchForm = reactive({
  name: '',
  category: '',
  is_active: null
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 添加表单相关
const siteFormRef = ref()
const siteForm = reactive({
  name: '',
  url: '',
  category: '',
  wp_username: '',
  wp_app_password: '',
  description: ''
})

// 添加表单相关
const addDialogVisible = ref(false)
const addFormRef = ref()

// 编辑表单相关
const editDialogVisible = ref(false)
const editFormRef = ref()
const editForm = reactive({
  id: null,
  name: '',
  url: '',
  category: '',
  wp_username: '',
  wp_app_password: '',
  description: '',
  blog_categories: [],
  blog_tags: []
})

// 分类筛选相关
const categorySearchKeyword = ref('')
const categoryCounts = ref({})
const categorySortOrder = ref('name-asc') // 默认按名称升序
const categoryPageSize = 10 // 每页10条
const categoryPagination = reactive({
  page: 1
})

// 分类管理相关
const addCategoryDialogVisible = ref(false)
const editCategoryDialogVisible = ref(false)
const categorySubmitting = ref(false)
const addCategoryFormRef = ref()
const editCategoryFormRef = ref()
const contextMenu = reactive({
  visible: false,
  x: 0,
  y: 0,
  category: ''
})

// 博客分类和标签详情对话框相关
const categoryDetailDialogVisible = ref(false)
const tagDetailDialogVisible = ref(false)
const selectedSite = ref(null)

const categoryForm = reactive({
  name: '',
  oldName: ''
})

const categoryRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 1, max: 50, message: '分类名称长度在 1 到 50 个字符', trigger: 'blur' }
  ]
}

// 表单验证规则
const siteRules = {
  name: [
    { required: true, message: '请输入站点名称', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入站点URL', trigger: 'blur' },
    { pattern: /^https?:\/\//, message: 'URL必须以http://或https://开头', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择或输入站点分类', trigger: 'blur' }
  ],
  wp_username: [
    { required: true, message: '请输入WordPress用户名', trigger: 'blur' }
  ],
  wp_app_password: [
    { required: true, message: '请输入应用程序密码', trigger: 'blur' }
  ]
}

// 计算属性
const filteredCategories = computed(() => {
  // 先过滤无效数据
  let result = categories.value.filter(category => 
    category && 
    typeof category === 'string' && 
    category.trim().length > 0
  )
  
  // 再进行搜索过滤
  if (categorySearchKeyword.value) {
    result = result.filter(category => 
    category.toLowerCase().includes(categorySearchKeyword.value.toLowerCase())
  )
  }
  
  // 最后排序
  const sorted = [...result]
  switch (categorySortOrder.value) {
    case 'name-asc':
      sorted.sort((a, b) => a.localeCompare(b, 'zh-CN'))
      break
    case 'name-desc':
      sorted.sort((a, b) => b.localeCompare(a, 'zh-CN'))
      break
    case 'count-desc':
      sorted.sort((a, b) => (categoryCounts.value[b] || 0) - (categoryCounts.value[a] || 0))
      break
    case 'count-asc':
      sorted.sort((a, b) => (categoryCounts.value[a] || 0) - (categoryCounts.value[b] || 0))
      break
  }
  
  return sorted
})

const paginatedCategories = computed(() => {
  const start = (categoryPagination.page - 1) * categoryPageSize
  const end = start + categoryPageSize
  return filteredCategories.value.slice(start, end)
})

// 格式化数字
const formatNumber = (num) => {
  if (!num) return '0'
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

// 格式化时间 - 使用统一的时区处理
const formatTime = (time) => {
  if (!time) return '--'
  try {
    // 使用统一的时区工具函数
    const result = formatDateTime(time)
    return result || '--'
  } catch (error) {
    console.error('时间格式化错误:', error, time)
    // 降级处理：使用简单的本地时间格式化
    try {
      const date = new Date(time)
      if (isNaN(date.getTime())) return '--'

      // 手动转换为北京时间（UTC+8）
      const utcTime = date.getTime()
      const beijingTime = new Date(utcTime + (8 * 60 * 60 * 1000))

      return beijingTime.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      })
    } catch (fallbackError) {
      console.error('降级时间格式化也失败:', fallbackError)
      return '--'
    }
  }
}

// 获取同步状态类型
const getSyncStatusType = (status) => {
  const statusMap = {
    success: 'success',
    failed: 'danger',
    pending: 'warning'
  }
  return statusMap[status] || 'info'
}

// 获取同步状态文本
const getSyncStatusText = (status) => {
  const statusMap = {
    success: '成功',
    failed: '失败',
    pending: '待同步'
  }
  return statusMap[status] || '未知'
}

// 布局控制方法
const toggleFormCollapse = () => {
  isFormCollapsed.value = !isFormCollapsed.value
}

// 分类筛选方法
const handleCategorySearch = () => {
  // 搜索时重置分页到第一页
  categoryPagination.page = 1
}

const handleCategorySortChange = (command) => {
  categorySortOrder.value = command
}

// 处理分类过滤
const handleCategoryFilter = async (category) => {
  searchForm.category = category
  pagination.page = 1
  await loadSites()
  // 重新加载分类计数
  await updateAllCategoryCounts()
}

// 获取所有站点的分类计数
const updateAllCategoryCounts = async () => {
  try {
    let page = 1
    const size = 100 // 使用最大允许的页面大小
    let allSites = []
    let hasMore = true

    // 分页获取所有站点
    while (hasMore) {
      const response = await apiClient.get('/v1/wordpress-site/', {
        params: {
          page,
          size,
          // 不传category参数，这样可以获取所有分类的站点
        }
      })
      
      if (response.items && response.items.length > 0) {
        allSites = allSites.concat(response.items)
      }
      
      // 检查是否还有更多数据
      hasMore = response.items && response.items.length === size && 
                response.total > page * size
      page++
    }

    // 计算分类计数
    const counts = {}
    allSites.forEach(site => {
      const siteCategory = site.category || 'uncategorized'
      counts[siteCategory] = (counts[siteCategory] || 0) + 1
    })
    categoryCounts.value = counts
  } catch (error) {
    console.error('更新分类计数失败:', error)
  }
}

const handleCategoryPageChange = (page) => {
  categoryPagination.page = page
}

// 加载站点列表
const loadSites = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null) {
        delete params[key]
      }
    })
    
    const response = await apiClient.get('/v1/wordpress-site/', { params })
    
    sites.value = response.items || []
    pagination.total = response.total || 0
    
    // 更新分类计数
    updateCategoryCounts()
  } catch (error) {
    ElMessage.error('加载站点列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 加载分类列表
const loadCategories = async () => {
  try {
    const response = await apiClient.get('/v1/wordpress-site/categories/list')
    // 过滤掉无效的分类数据
    categories.value = (response.categories || []).filter(category => 
      category && 
      typeof category === 'string' && 
      category.trim().length > 0
    )
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

// 加载统计摘要
const loadSummary = async () => {
  try {
    const response = await apiClient.get('/v1/wordpress-site/summary')
    summary.value = {
      total_sites: response.total_sites || 0,
      active_sites: response.active_sites || 0,
      total_daily_uv: response.total_daily_uv || 0,
      total_daily_pv: response.total_daily_pv || 0
    }
  } catch (error) {
    console.error('加载统计失败:', error)
    // 如果统计加载失败，提供默认值
    summary.value = {
      total_sites: 0,
      active_sites: 0,
      total_daily_uv: 0,
      total_daily_pv: 0
    }
  }
}

// 更新分类计数
const updateCategoryCounts = () => {
  // 获取所有站点的分类统计
  const counts = {}
  sites.value.forEach(site => {
    const category = site.category || 'uncategorized'
    counts[category] = (counts[category] || 0) + 1
  })
  categoryCounts.value = counts
}

// 搜索处理
let searchTimer = null
const handleSearch = () => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    pagination.page = 1
    loadSites()
  }, 500)
}

// 快速添加站点处理
const handleQuickAddSite = () => {
  // 重置表单并打开对话框
  resetForm()
  addDialogVisible.value = true
}

// 编辑站点
const editSite = (site) => {
  Object.assign(editForm, {
    id: site.id,
    name: site.name,
    url: site.url,
    category: site.category || '',
    wp_username: site.wp_username,
    wp_app_password: site.wp_app_password,
    description: site.description || '',
    blog_categories: site.blog_categories || [],
    blog_tags: site.blog_tags || []
  })
  editDialogVisible.value = true
}

// 重置添加表单
const resetForm = () => {
  Object.assign(siteForm, {
    name: '',
    url: '',
    category: '',
    wp_username: '',
    wp_app_password: '',
    description: ''
  })
  if (siteFormRef.value) {
    siteFormRef.value.clearValidate()
  }
}

// 重置编辑表单
const resetEditForm = () => {
  Object.assign(editForm, {
    id: null,
    name: '',
    url: '',
    category: '',
    wp_username: '',
    wp_app_password: '',
    description: '',
    blog_categories: [],
    blog_tags: []
  })
  if (editFormRef.value) {
    editFormRef.value.clearValidate()
  }
}

// 提交添加站点
const submitSite = async () => {
  const formRef = addFormRef.value || siteFormRef.value
  if (!formRef) return

  try {
    await formRef.validate()
    submitting.value = true

    const data = { ...siteForm }
    await apiClient.post('/v1/wordpress-site/', data)

    ElMessage.success('站点添加成功')
    resetForm()
    addDialogVisible.value = false
    loadSites()
    loadCategories()
    loadSummary()
  } catch (error) {
    // 错误信息已经在request.js的拦截器中显示了，这里不需要再显示
    console.error('添加站点失败:', error)
  } finally {
    submitting.value = false
  }
}

// 提交编辑站点
const submitEditSite = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()
    submitting.value = true

    const data = { ...editForm }
    delete data.id
    delete data.blog_categories

    await apiClient.put(`/v1/wordpress-site/${editForm.id}`, data)

    ElMessage.success('站点更新成功')
    editDialogVisible.value = false
    resetEditForm()
    loadSites()
    loadCategories()
    loadSummary()
  } catch (error) {
    // 错误信息已经在request.js的拦截器中显示了，这里不需要再显示
    console.error('更新站点失败:', error)
  } finally {
    submitting.value = false
  }
}

// 删除站点
const deleteSite = async (site) => {
  try {
    await apiClient.delete(`/v1/wordpress-site/${site.id}`)
    ElMessage.success('站点删除成功')
    loadSites()
    loadCategories()
    loadSummary()
  } catch (error) {
    // 错误信息已经在request.js的拦截器中显示了，这里不需要再显示
    console.error('删除站点失败:', error)
  }
}

// 切换站点状态
const toggleSiteStatus = async (site) => {
  try {
    await apiClient.put(`/v1/wordpress-site/${site.id}`, {
      is_active: site.is_active
    })
    ElMessage.success(`站点已${site.is_active ? '启用' : '禁用'}`)
    loadSummary()
  } catch (error) {
    // 错误信息已经在request.js的拦截器中显示了，这里不需要再显示
    console.error('更新站点状态失败:', error)
    // 恢复原状态
    site.is_active = !site.is_active
  }
}

// 同步站点
const syncSite = async (site) => {
  try {
    await apiClient.post(`/v1/wordpress-site/${site.id}/sync`)
    ElMessage.success('站点所有信息同步成功')
    loadSites()
  } catch (error) {
    // 错误信息已经在request.js的拦截器中显示了，这里不需要再显示
    console.error('同步站点失败:', error)
    loadSites() // 即使失败也要刷新状态
  }
}

// 刷新所有数据
const refreshAll = async () => {
  refreshing.value = true
  try {
    await Promise.all([
      loadSites(),
      loadCategories(),
      loadSummary()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    // 错误信息已经在request.js的拦截器中显示了，这里不需要再显示
    console.error('刷新数据失败:', error)
  } finally {
    refreshing.value = false
  }
}

// 一键同步所有站点
const syncAllSites = async () => {
  try {
    batchSyncing.value = true
    isSyncing.value = true

    // 不再显示同步状态标签，而是通过按钮状态显示
    const response = await apiClient.post('/v1/wordpress-site/sync-all')

    if (response.sync_started) {
      ElMessage.info(`开始同步全部站点信息，共${response.total_sites}个站点，请稍候...`)

      // 开始轮询检查同步状态
      checkSyncProgress()
    } else {
      ElMessage.info(response.message)
      isSyncing.value = false
    }
  } catch (error) {
    // 错误信息已经在request.js的拦截器中显示了，这里不需要再显示
    console.error('启动批量同步失败:', error)
    isSyncing.value = false
  } finally {
    batchSyncing.value = false
  }
}

// 检查同步进度
const checkSyncProgress = () => {
  let checkCount = 0
  const maxCheckCount = 150 // 最多检查5分钟（150次 × 2秒）
  
  const checkInterval = setInterval(async () => {
    try {
      checkCount++
      
      // 重新加载站点数据，检查同步状态
      await loadSites()
      await loadSummary()
      
      // 检查是否还有正在同步的站点
      const syncingSites = sites.value.filter(site => site.syncing)
      
      if (syncingSites.length === 0) {
        // 同步完成
        clearInterval(checkInterval)
        isSyncing.value = false
        
        ElMessage.success('所有站点信息同步完成')
      } else if (checkCount >= maxCheckCount) {
        // 超时停止检查
        clearInterval(checkInterval)
        isSyncing.value = false
        
        ElMessage.warning('同步检查超时，请手动刷新查看结果')
      }
      // 如果还有站点在同步，继续等待，不显示任何消息
    } catch (error) {
      console.error('检查同步进度失败:', error)
      clearInterval(checkInterval)
      isSyncing.value = false
      
      ElMessage.error('同步状态检查失败')
    }
  }, 2000) // 每2秒检查一次
}

// 分类自动完成查询
const queryCategories = (queryString, callback) => {
  const results = categories.value
    .filter(category => category.toLowerCase().includes(queryString.toLowerCase()))
    .map(category => ({
      value: category,
      count: categoryCounts.value[category] || 0
    }))
  
  // 如果输入的不在现有分类中，添加为新选项
  if (queryString && !categories.value.includes(queryString)) {
    results.unshift({ value: queryString, count: 0 })
  }
  
  callback(results)
}

// 分类选择处理
const handleCategorySelect = (item) => {
  // 处理分类选择
}

// 分类管理方法
const showCategoryContextMenu = (event, category) => {
  contextMenu.visible = true
  contextMenu.x = event.clientX
  contextMenu.y = event.clientY
  contextMenu.category = category
  searchForm.category = category

  // 点击其他地方隐藏菜单
  const hideMenu = () => {
    contextMenu.visible = false
    document.removeEventListener('click', hideMenu)
  }
  
  setTimeout(() => {
    document.addEventListener('click', hideMenu)
  }, 100)
}

const showAddCategoryDialog = () => {
  contextMenu.visible = false
  resetCategoryForm()
  addCategoryDialogVisible.value = true
}

const showEditCategoryDialog = () => {
  contextMenu.visible = false
  if (!searchForm.category || searchForm.category === '') {
    ElMessage.warning('请先选择要修改的分类')
    return
  }
  
  categoryForm.oldName = searchForm.category
  categoryForm.name = searchForm.category
  editCategoryDialogVisible.value = true
}

const confirmDeleteCategory = () => {
  contextMenu.visible = false
  if (!searchForm.category || searchForm.category === '') {
    ElMessage.warning('请先选择要删除的分类')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除分类"${searchForm.category}"吗？删除后该分类下的所有站点将变为"未分类"状态。`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    handleDeleteCategory()
  }).catch(() => {
    // 用户取消删除
  })
}

const handleAddCategory = async () => {
  if (!addCategoryFormRef.value) return

  try {
    await addCategoryFormRef.value.validate()
    categorySubmitting.value = true

    // 检查分类是否已存在
    if (categories.value.includes(categoryForm.name)) {
      ElMessage.warning('该分类名称已存在')
      return
    }

    // 调用API添加分类
    const response = await apiClient.post('/v1/wordpress-site/categories', {
      name: categoryForm.name
    })

    ElMessage.success('分类添加成功')
    addCategoryDialogVisible.value = false
    resetCategoryForm()

    // 重新加载数据
    await loadCategories()
    await loadSites()
  } catch (error) {
    // 错误信息已经在request.js的拦截器中显示了，这里不需要再显示
    console.error('添加分类失败:', error)
  } finally {
    categorySubmitting.value = false
  }
}

const handleEditCategory = async () => {
  if (!editCategoryFormRef.value) return

  try {
    await editCategoryFormRef.value.validate()
    categorySubmitting.value = true

    // 检查新分类名是否已存在
    if (categoryForm.name !== categoryForm.oldName && categories.value.includes(categoryForm.name)) {
      ElMessage.warning('该分类名称已存在')
      return
    }

    // 调用API修改分类
    const response = await apiClient.put('/v1/wordpress-site/categories', {
      oldName: categoryForm.oldName,
      newName: categoryForm.name
    })

    ElMessage.success('分类修改成功')
    editCategoryDialogVisible.value = false
    resetCategoryForm()

    // 重新加载数据
    await loadCategories()
    await loadSites()

    // 更新当前选中的分类
    searchForm.category = categoryForm.name
  } catch (error) {
    // 错误信息已经在request.js的拦截器中显示了，这里不需要再显示
    console.error('修改分类失败:', error)
  } finally {
    categorySubmitting.value = false
  }
}

const handleDeleteCategory = async () => {
  try {
    categorySubmitting.value = true

    // 调用API删除分类
    const response = await apiClient.delete('/v1/wordpress-site/categories', {
      data: { name: searchForm.category }
    })

    ElMessage.success('分类删除成功')

    // 重新加载数据
    await loadCategories()
    await loadSites()

    // 清除当前选中的分类
    searchForm.category = ''
  } catch (error) {
    // 错误信息已经在request.js的拦截器中显示了，这里不需要再显示
    console.error('删除分类失败:', error)
  } finally {
    categorySubmitting.value = false
  }
}

const resetCategoryForm = () => {
  categoryForm.name = ''
  categoryForm.oldName = ''
  if (addCategoryFormRef.value) {
    addCategoryFormRef.value.clearValidate()
  }
  if (editCategoryFormRef.value) {
    editCategoryFormRef.value.clearValidate()
  }
}

// 显示博客分类详情
const showCategoryDetails = (site) => {
  selectedSite.value = site
  categoryDetailDialogVisible.value = true
}

// 显示博客标签详情
const showTagDetails = (site) => {
  selectedSite.value = site
  tagDetailDialogVisible.value = true
}

// 计算最大显示标签数量（基于两行显示）
const getMaxDisplayTags = () => {
  // 考虑到标签长度不一，两行大约可以显示10-12个标签
  // 这里设置为10个，确保不会超过两行
  return 10
}

// 获取要显示的标签列表
const getDisplayTags = (tags) => {
  if (!tags || tags.length === 0) return []
  const maxTags = getMaxDisplayTags()
  return tags.slice(0, maxTags)
}

// 关闭分类详情对话框
const closeCategoryDetail = () => {
  categoryDetailDialogVisible.value = false
  selectedSite.value = null
}

// 关闭标签详情对话框
const closeTagDetail = () => {
  tagDetailDialogVisible.value = false
  selectedSite.value = null
}

// 页面初始化
onMounted(() => {
  loadSites()
  loadCategories()
  loadSummary()
})
</script>

<style scoped>
.my-sites {
  padding: 16px; /* 恢复页面内边距 */
  margin: 0;
  width: calc(100vw - 236px); /* 减去侧边栏宽度 + 左边距 */
  height: calc(100vh - 72px); /* 减去header高度 + 上边距 */
  overflow: hidden;
  background: #f5f5f5;
  position: fixed;
  top: 72px; /* header高度 + 上边距8px */
  left: 236px; /* 侧边栏宽度 + 左边距16px */
  z-index: 1;
  box-sizing: border-box; /* 确保padding包含在宽高计算中 */
}

/* 侧边栏收起状态适配 */
.my-sites.sidebar-collapsed {
  width: calc(100vw - 80px); /* 减去收起侧边栏宽度 + 左边距 */
  left: 80px; /* 收起侧边栏宽度 + 左边距16px */
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
}

.header-icon {
  margin-right: 8px;
  font-size: 20px;
}

/* 内嵌在标题右侧的统计栏样式 */
.content-header .header-stats {
  display: flex;
  gap: 24px;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.content-header .stat-item {
  text-align: center;
}

.content-header .stat-number {
  font-size: 20px;
  font-weight: 700;
  color: white;
  line-height: 1;
  margin-bottom: 4px;
}

.content-header .stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1;
}

.content-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.content-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #909399 0%, #606266 100%); /* 修改为灰色渐变 */
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 20px 24px 16px; /* 统一表头高度 */
}

.content-card :deep(.el-card__body) {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  min-height: 0; /* 允许内容区域收缩 */
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0; /* 移除底部边距 */
  flex-shrink: 0; /* 头部不收缩 */
  min-height: 28px; /* 进一步减小最小高度 */
}

.list-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
  flex-shrink: 0;
}

.search-filters {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.site-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.site-category {
  width: fit-content;
}

.stats-cell {
  font-size: 12px;
  line-height: 1.4;
}

.site-info {
  font-size: 12px;
  line-height: 1.4;
}

.theme-info {
  color: #666;
  margin-top: 2px;
}

.sync-status {
  text-align: center;
}

.sync-time {
  font-size: 11px;
  color: #999;
  margin-top: 2px;
}

.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 表格样式优化 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table :deep(.el-table__header-wrapper) {
  background: #f8f9fa;
}

.el-table :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 按钮样式优化 */
.el-button {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.el-button:hover {
  transform: translateY(-1px);
}

/* 输入框样式优化 */
.el-input :deep(.el-input__wrapper) {
  border-radius: 6px;
}

.el-select :deep(.el-input__wrapper) {
  border-radius: 6px;
}

/* 博客分类样式 */
.blog-categories {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.categories-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.category-tag {
  font-weight: 500;
}



/* 站点分类列样式 */
.site-category-tag {
  font-weight: 500;
  border-radius: 4px;
}

.no-category {
  color: #999;
  font-style: italic;
  font-size: 12px;
}

/* 分类建议项样式 */
.category-suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.category-name {
  font-weight: 500;
  color: #303133;
}

/* 主要内容区域 - 左右布局 */
.main-content {
  display: flex;
  gap: 16px;
  height: calc(100vh - 112px); /* 减少高度：80px顶部 + 16px上边距 + 16px下边距 */
  box-sizing: border-box;
  overflow: hidden; /* 防止内容溢出 */
  min-height: 0; /* 允许flex项目收缩 */
}

/* 当表单区域隐藏时，调整布局 */
.main-content:has(.form-section.hidden) {
  gap: 20px;
}

.main-content:has(.form-section.hidden) .category-filter-section {
  flex: 0 0 280px;
  min-width: 280px;
}

.main-content:has(.form-section.hidden) .list-section {
  flex: 1;
  min-width: 0;
}

/* 左侧表单区域 */
.form-section {
  flex: 0 0 320px;
  min-width: 320px;
  transition: all 0.3s ease;
}

.form-section.collapsed {
  flex: 0 0 60px;
  min-width: 60px;
}

.form-section.hidden {
  display: none;
}

.form-collapsed {
  width: 60px;
  height: 100%;
  background: linear-gradient(135deg, rgba(144, 147, 153, 0.85) 0%, rgba(96, 98, 102, 0.9) 100%);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-collapsed:hover {
  transform: translateX(2px);
  background: linear-gradient(135deg, rgba(144, 147, 153, 0.9) 0%, rgba(96, 98, 102, 0.95) 100%);
  box-shadow: 0 6px 20px rgba(144, 147, 153, 0.25);
  border-color: rgba(255, 255, 255, 0.2);
}

.collapsed-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.collapsed-text {
  font-weight: 600;
  font-size: 22px;
  letter-spacing: 3px;
  line-height: 1.3;
  writing-mode: vertical-lr;
  text-orientation: mixed;
  opacity: 0.95;
}

.collapsed-icon {
  font-size: 42px;
  color: white;
  opacity: 0.9;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.form-card {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.form-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #909399 0%, #606266 100%); /* 修改为灰色渐变 */
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 20px 24px 16px; /* 统一表头高度 */
}

.form-card :deep(.el-card__body) {
  flex: 1;
  padding: 16px;
  min-height: 0; /* 允许内容区域收缩 */
  display: flex;
  flex-direction: column;
  height: calc(100% - 76px);
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
}

.collapse-button {
  color: white !important;
  border: none !important;
  background: transparent !important;
}

.collapse-button:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.add-form {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.form-fields {
  flex: 1;
  overflow-y: auto;
  min-height: 0; /* 允许表单字段区域收缩 */
}

/* 按钮区域样式 */
.button-form-item {
  margin-bottom: 0 !important;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
  background: white;
  position: sticky;
  bottom: 0;
  z-index: 10;
  flex-shrink: 0; /* 按钮区域不收缩 */
}

.action-buttons {
  display: flex;
  gap: 12px;
  width: 100%;
}

.action-button {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
}

.primary-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  color: white;
}

.primary-button:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.5);
}

.primary-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.secondary-button {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #6c757d;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.secondary-button:hover {
  background: #e9ecef;
  border-color: #adb5bd;
  color: #495057;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.secondary-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

/* 中间分类筛选区域 */
.category-filter-section {
  flex: 0 0 240px;
  min-width: 240px;
  max-width: 240px; /* 防止宽度超出 */
  overflow: hidden; /* 防止内容溢出 */
}

.category-filter-card {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.category-filter-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); /* 修改为紫色渐变 */
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 20px 24px 16px; /* 统一表头高度 */
}

.category-filter-card :deep(.el-card__body) {
  flex: 1;
  padding: 16px;
  min-height: 0; /* 允许内容区域收缩 */
  display: flex;
  flex-direction: column;
  height: calc(100% - 76px);
  overflow: hidden; /* 防止内容溢出 */
  width: 100%; /* 确保宽度不超出 */
  box-sizing: border-box; /* 包含边框和内边距 */
}

.category-filter-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
}

.category-search {
  margin-bottom: 16px;
}

.search-with-sort {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sort-button {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #6c757d;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
}

.sort-button:hover {
  background: #e9ecef;
  border-color: #adb5bd;
  color: #495057;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
}

.category-list {
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden; /* 防止横向滚动条 */
  display: flex;
  flex-direction: column;
  width: 100%; /* 确保宽度不超出容器 */
  box-sizing: border-box; /* 包含边框和内边距 */
}

.all-category {
  order: -1; /* 确保全部分类显示在最上方 */
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.uncategorized-item {
  order: 999; /* 确保未分类显示在最下方 */
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e5e7eb;
}

.category-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  margin-bottom: 2px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: transparent;
  border: none;
  width: 100%; /* 确保宽度不超出容器 */
  box-sizing: border-box; /* 包含边框和内边距 */
  min-width: 0; /* 允许内容收缩 */
}

.category-item:hover {
  background: #f3f4f6;
}

.category-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.category-content {
  display: flex;
  align-items: center;
  flex: 1;
  margin-right: 8px;
  max-width: calc(100% - 60px); /* 为计数留出空间 */
  overflow: hidden;
}

.category-icon {
  font-size: 16px;
  margin-right: 8px;
  color: #6b7280;
  flex-shrink: 0;
}

.category-text {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.category-item:hover .category-icon {
  color: #4b5563;
}

.category-item:hover .category-text {
  color: #4b5563;
}

.category-item.active .category-icon {
  color: white;
}

.category-item.active .category-text {
  color: white;
}

.category-count {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 400;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

.category-item:hover .category-count {
  background: #e5e7eb;
  color: #6b7280;
}

.category-item.active .category-count {
  background: rgba(255, 255, 255, 0.25);
  color: rgba(255, 255, 255, 0.95);
}

/* 右侧列表区域 */
.list-section {
  flex: 1;
  min-width: 0;
}

.list-card {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.list-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%); /* 保持蓝色渐变 */
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 20px 24px 16px; /* 统一表头高度 */
}

.list-card :deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
  padding: 20px;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许内容区域收缩 */
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0; /* 移除底部边距 */
  flex-shrink: 0; /* 头部不收缩 */
  min-height: 28px; /* 进一步减小最小高度 */
}

.list-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
  flex-shrink: 0;
}

.search-filters {
  display: flex;
  align-items: center;
}

/* 表格容器 */
.list-card :deep(.el-table) {
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
  min-height: 0; /* 允许表格收缩 */
}

.list-card :deep(.el-table__header-wrapper) {
  background: #f8f9fa;
}

.list-card :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

.site-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.site-category-tag {
  font-weight: 500;
  border-radius: 4px;
}

.no-category {
  color: #999;
  font-style: italic;
}

.stats-cell {
  font-size: 12px;
  line-height: 1.4;
}

.site-info {
  font-size: 12px;
  line-height: 1.4;
}

.theme-info {
  color: #666;
  margin-top: 2px;
}

.blog-categories {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.categories-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.category-tag {
  margin: 2px;
  border-radius: 12px;
  background: linear-gradient(45deg, #e3f2fd, #bbdefb);
  border: 1px solid #2196f3;
  color: #1976d2;
  cursor: help;
}

.category-tag:hover {
  background: linear-gradient(45deg, #bbdefb, #90caf9);
  transform: scale(1.05);
  transition: all 0.3s ease;
}

.no-categories {
  text-align: center;
}

.sync-status {
  text-align: center;
}

.sync-time {
  font-size: 11px;
  color: #999;
  margin-top: 2px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  flex-shrink: 0; /* 分页不收缩 */
}

/* 表单样式 */
.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.category-suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-name {
  font-weight: 500;
}

.category-count {
  font-size: 12px;
  color: #999;
}

.form-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.category-tag-form {
  margin: 0;
  border-radius: 12px;
}

.form-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 60px; /* 限制最大高度，大约两行标签的高度 */
  overflow: hidden;
  align-items: flex-start;
}

.tag-form {
  margin: 0;
  border-radius: 12px;
}

.tag-more {
  margin: 0;
  border-radius: 12px;
  background: linear-gradient(135deg, #909399 0%, #606266 100%);
  border: none;
  color: white;
  font-style: italic;
}

/* 博客标签样式 */
.blog-tags {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.blog-tag {
  margin: 1px !important;
  font-size: 11px !important;
  padding: 2px 6px !important;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 紧凑操作按钮样式 */
.action-buttons.compact {
  display: flex;
  gap: 2px;
  flex-wrap: nowrap;
}

.action-buttons.compact .el-button {
  min-width: 28px;
  padding: 5px 8px;
}

/* 分类导航标签样式 */

/* 分类分页样式 */
.category-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

/* 分类管理按钮样式 */

/* 博客分类标签样式 */
.blog-category-tag {
  margin: 1px !important;
  font-size: 11px !important;
  padding: 2px 6px !important;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 博客标签样式 */
.blog-tag {
  margin: 1px !important;
  font-size: 11px !important;
  padding: 2px 6px !important;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: linear-gradient(45deg, #f0f9ff, #dbeafe);
  border: 1px solid #22c55e;
  color: #16a34a;
  cursor: help;
}

.blog-tag:hover {
  background: linear-gradient(45deg, #dbeafe, #bfdbfe);
  transform: scale(1.05);
  transition: all 0.3s ease;
}

.no-tags {
  text-align: center;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: center;
  padding: 20px 0 0 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    height: auto;
    min-height: calc(100vh - 112px); /* 保持与桌面端一致 */
    overflow-y: auto;
  }
  
  .form-section {
    flex: none;
    width: 100%;
    order: 1;
  }
  
  .form-section.collapsed {
    flex: none;
    width: 100%;
    height: 60px;
  }
  
  .form-collapsed {
    width: 100%;
    height: 60px;
    flex-direction: row;
  }
  
  .collapsed-text {
    writing-mode: initial;
    text-orientation: initial;
    margin-bottom: 0;
    margin-right: 8px;
  }
  
  .category-filter-section {
    flex: none;
    width: 100%;
    order: 2;
  }
  
  .list-section {
    flex: none;
    width: 100%;
    order: 3;
  }
  
  .form-card {
    min-height: 500px;
  }
  
  /* 中屏幕下的统计栏调整 */
  .content-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .content-header .header-stats {
    justify-content: space-around;
    margin: 0;
  }
}

@media (max-width: 768px) {
  .my-sites {
    width: calc(100vw - 16px); /* 移动端保留小边距 */
    left: 8px; /* 移动端小左边距 */
    top: 72px; /* 移动端小上边距 */
    height: calc(100vh - 80px);
    padding: 8px; /* 移动端减少内边距 */
  }
  
  .main-content {
    flex-direction: column;
    gap: 12px;
    height: auto;
    min-height: calc(100vh - 104px); /* 移动端调整 */
  }
  
  .form-section {
    flex: none;
    min-width: auto;
  }
  
  .form-section.collapsed {
    width: 100%;
    height: 50px;
  }
  
  .form-collapsed {
    height: 50px;
  }
  
  .category-filter-section {
    width: 100%;
  }
  
  .list-section {
    flex: none;
    height: 60vh;
  }
  
  .search-filters {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .search-filters .el-input,
  .search-filters .el-select {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 8px;
  }
  
  .content-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  /* 移动端统计栏调整 */
  .content-header .header-stats {
    flex-direction: column;
    gap: 8px;
    margin: 0;
  }
  
  .content-header .stat-item {
    padding: 6px 8px;
    min-width: auto;
  }
  
  .content-header .stat-number {
    font-size: 16px;
  }
  
  .content-header .stat-label {
    font-size: 10px;
  }
}

/* 处理浏览器缩放和各种屏幕尺寸 */
@media (min-width: 769px) and (max-width: 1024px) {
  .form-section {
    flex: 0 0 320px;
    min-width: 320px;
  }
}

@media (min-width: 1025px) and (max-width: 1440px) {
  .form-section {
    flex: 0 0 340px;
    min-width: 340px;
  }
}

@media (min-width: 1441px) and (max-width: 1920px) {
  .form-section {
    flex: 0 0 360px;
    min-width: 360px;
  }
}

/* 超宽屏适配 */
@media (min-width: 1921px) {
  .form-section {
    flex: 0 0 380px;
    min-width: 380px;
  }
}

/* 处理高DPI屏幕和浏览器缩放 */
@media (-webkit-min-device-pixel-ratio: 1.5), (min-resolution: 144dpi) {
  .my-sites {
    /* 高DPI屏幕下保持布局稳定 */
    transform: translateZ(0); /* 启用硬件加速 */
  }
}

/* 确保在任何缩放比例下都不会出现滚动条 */
.main-content {
  min-height: 0; /* 允许flex项目收缩 */
}

.form-card :deep(.el-card__body),
.list-card :deep(.el-card__body),
.category-filter-card :deep(.el-card__body) {
  min-height: 0; /* 允许内容区域收缩 */
}

.keyword-library {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.category-list {
  max-height: 500px;
  overflow-y: auto;
}



/* 分类管理按钮样式 */
.category-actions {
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.category-actions .el-button {
  flex: 1;
  min-width: 60px;
  font-size: 12px;
}

/* 删除按钮特殊样式 - 宽度小一点避免误点 */
.category-actions .delete-btn {
  flex: 0 0 auto;
  min-width: 60px;
  max-width: 60px;
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  z-index: 9999;
  min-width: 120px;
  padding: 4px 0;
  animation: contextMenuShow 0.2s ease;
}

@keyframes contextMenuShow {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #606266;
}

.context-menu-item:hover {
  background-color: #f5f7fa;
}

.context-menu-item.danger {
  color: #f56565;
}

.context-menu-item.danger:hover {
  background-color: #fef5f5;
}

.context-menu-item .el-icon {
  font-size: 16px;
}



/* 分类分页样式 */
.category-pagination {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

/* 分类管理按钮样式 */

/* 博客分类和标签列表样式 */
.blog-categories,
.blog-tags {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
}

.categories-summary,
.tags-summary {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  width: 100%;
}

.count-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  text-align: center;
}

.view-button {
  font-size: 12px !important;
  padding: 4px 8px !important;
  height: auto !important;
  min-height: auto !important;
  color: #409eff !important;
  border-radius: 4px;
}

.view-button:hover {
  color: #66b1ff !important;
  background: rgba(64, 158, 255, 0.1) !important;
}

.no-categories,
.no-tags {
  text-align: center;
  width: 100%;
}

/* 详情对话框样式 */
.category-detail-content,
.tag-detail-content {
  padding: 8px 0;
}

.detail-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.detail-header h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.detail-header .site-url {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.no-description {
  color: #c0c4cc;
  font-style: italic;
  font-size: 12px;
}

/* 博客分类样式 */
.blog-categories {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.categories-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 一键同步按钮样式 */
.sync-all-button {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

.sync-all-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #5cb230 0%, #73b352 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
}

.sync-all-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(103, 194, 58, 0.3);
}

.sync-all-button.is-disabled,
.sync-all-button:disabled {
  background: #a0cfff !important;
  border-color: #a0cfff !important;
  color: #c0c4cc !important;
  cursor: not-allowed !important;
  opacity: 0.6 !important;
  transform: none !important;
  box-shadow: none !important;
}

.sync-all-button.syncing .el-icon {
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 移除同步状态显示样式 */
</style> 