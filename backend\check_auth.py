#!/usr/bin/env python3
"""
查询数据库中的阿里巴巴授权记录

使用方法:
python check_auth.py
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from app.core.database import SessionLocal
    from app.models.alibaba_auth import AlibabaAuth
    from datetime import datetime
    
    db = SessionLocal()
    
    try:
        print("查询数据库中的阿里巴巴授权记录...")
        
        # 查询所有授权记录
        auths = db.query(AlibabaAuth).all()
        
        if not auths:
            print("❌ 数据库中没有找到阿里巴巴授权记录")
        else:
            print(f"✅ 找到 {len(auths)} 个授权记录:")
            
            for i, auth in enumerate(auths, 1):
                print(f"\n记录 {i}:")
                print(f"  用户ID: {auth.user_id}")
                print(f"  租户ID: {auth.tenant_id}")
                print(f"  阿里账户: {auth.alibaba_account}")
                print(f"  国家: {auth.country}")
                print(f"  平台: {auth.account_platform}")
                print(f"  是否激活: {auth.is_active}")
                print(f"  Access Token: {auth.access_token[:50]}..." if auth.access_token else "None")
                print(f"  Refresh Token: {auth.refresh_token[:50]}..." if auth.refresh_token else "None")
                print(f"  过期时间: {auth.expires_in}秒")
                print(f"  创建时间: {auth.token_created_at}")
                
                # 检查token是否过期
                if auth.token_created_at and auth.expires_in:
                    from datetime import timedelta
                    expire_time = auth.token_created_at + timedelta(seconds=auth.expires_in)
                    is_expired = datetime.utcnow() > expire_time
                    print(f"  Token状态: {'已过期' if is_expired else '有效'}")
                    print(f"  过期时间: {expire_time}")
        
        # 查询激活的授权记录
        active_auths = db.query(AlibabaAuth).filter(AlibabaAuth.is_active == True).all()
        
        if active_auths:
            print(f"\n✅ 活跃授权记录 ({len(active_auths)} 个):")
            for auth in active_auths:
                print(f"  用户 {auth.user_id}: {auth.alibaba_account} - Token: {auth.access_token}")
                
    finally:
        db.close()
        
except Exception as e:
    print(f"❌ 查询失败: {e}")
    import traceback
    traceback.print_exc() 