{"timestamp": "2025-06-05T15:09:20.206971", "system_info": {"platform": "Windows-10-10.0.19045-SP0", "system": "Windows", "release": "10", "version": "10.0.19045", "machine": "AMD64", "processor": "Intel64 Family 6 Model 165 Stepping 3, GenuineIntel", "python_version": "3.13.3", "python_implementation": "CPython"}, "python_env": {"version": [3, 13, 3, "final", 0], "executable": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe", "path": ["D:\\AI\\AICBEC\\CBEC\\scripts", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages"], "pip_available": true, "venv_active": false, "venv_path": "D:\\AI\\AICBEC\\venv", "venv_python": "D:\\AI\\AICBEC\\venv\\Scripts\\python.exe", "packages": {}}, "node_env": {"node_available": true, "npm_available": true, "node_version": "v22.14.0", "npm_version": "10.9.2", "npm_path": "C:\\Program Files\\nodejs\\npm.cmd"}, "dependencies": {"frontend": {"installed": {"@achrinza/node-ipc": "10.1.11", "@element-plus/icons-vue": "2.3.1", "axios": "0.24.0", "core-js": "3.42.0", "element-plus": "2.9.9", "path-browserify": "1.0.1", "vue": "3.5.13", "vue-router": "4.5.1", "vuex": "4.1.0", "@vue/cli-plugin-babel": "5.0.8", "@vue/cli-plugin-eslint": "5.0.8", "@vue/cli-plugin-router": "5.0.8", "@vue/cli-plugin-vuex": "5.0.8", "@vue/cli-service": "5.0.8", "@vue/compiler-sfc": "3.5.13", "@vue/eslint-config-standard": "6.1.0", "babel-eslint": "10.1.0", "eslint": "7.32.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-vue": "8.7.1", "sass": "1.87.0", "sass-loader": "12.6.0"}, "missing": [], "total_required": 24}}, "issues": [], "recommendations": ["建议激活虚拟环境: D:\\AI\\AICBEC\\venv"], "backend_deps": {"python_executable": "D:\\AI\\AICBEC\\venv\\Scripts\\python.exe", "requirements_file": "D:\\AI\\AICBEC\\CBEC\\backend\\requirements.txt", "total_packages": 28, "installed_packages": 28, "missing_packages": [], "installed_list": ["<PERSON><PERSON><PERSON>", "u<PERSON><PERSON>", "sqlalchemy", "pydantic", "python-jose", "passlib", "bcrypt", "alembic", "python-multipart", "pydantic-settings", "pymysql", "requests", "httpx", "beautifulsoup4", "pandas", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seaborn", "google-ads", "google-api-python-client", "google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "python-dateutil", "openpyxl", "xlsxwriter", "pytrends", "trendspy", "pytz"]}}