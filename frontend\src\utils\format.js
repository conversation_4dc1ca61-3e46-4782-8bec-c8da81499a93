/**
 * 格式化数字，添加千分位分隔符
 * @param {number} num 
 * @returns {string}
 */
// 导入全局时区配置
import {
  formatDateTime as formatDateTimeWithTimezone,
  formatDate as formatDateWithTimezone,
  formatTime as formatTimeWithTimezone,
  formatRelativeTime
} from './timezone.js'

export function formatNumber(num) {
  if (num === null || num === undefined) {
    return '--'
  }
  
  if (typeof num !== 'number') {
    return num.toString()
  }

  return num.toLocaleString('zh-CN')
}

/**
 * 格式化日期时间
 *
 * 采用 UTC + 前端本地化 方案：
 * - 后端传来的是UTC时间（ISO格式，如 "2024-01-01T10:00:00Z"）
 * - 前端根据环境变量配置自动转换为指定时区显示
 *
 * @param {string|Date} datetime
 * @returns {string}
 */
export function formatDateTime(datetime) {
  return formatDateTimeWithTimezone(datetime)
}

/**
 * 格式化日期
 *
 * 使用全局时区配置
 *
 * @param {string|Date} date
 * @returns {string}
 */
export function formatDate(date) {
  return formatDateWithTimezone(date)
}

/**
 * 格式化时间
 *
 * 使用全局时区配置
 *
 * @param {string|Date} time
 * @returns {string}
 */
export function formatTime(time) {
  return formatTimeWithTimezone(time)
}

/**
 * 格式化文件大小
 * @param {number} bytes 
 * @returns {string}
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化百分比
 * @param {number} value 
 * @param {number} precision 精度，默认1
 * @returns {string}
 */
export function formatPercentage(value, precision = 1) {
  if (value === null || value === undefined) {
    return '--'
  }
  
  return (value * 100).toFixed(precision) + '%'
}

/**
 * 格式化货币
 * @param {number} amount 
 * @param {string} currency 货币符号，默认¥
 * @returns {string}
 */
export function formatCurrency(amount, currency = '¥') {
  if (amount === null || amount === undefined) {
    return '--'
  }
  
  return currency + formatNumber(amount)
}

/**
 * 截断文本
 * @param {string} text 
 * @param {number} maxLength 最大长度
 * @param {string} suffix 后缀，默认...
 * @returns {string}
 */
export function truncateText(text, maxLength, suffix = '...') {
  if (!text) return ''
  
  if (text.length <= maxLength) {
    return text
  }
  
  return text.substring(0, maxLength - suffix.length) + suffix
} 