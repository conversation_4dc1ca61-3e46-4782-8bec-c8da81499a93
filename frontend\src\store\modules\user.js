import axios from 'axios'

const state = {
  currentUser: null,
  users: [],
  status: '',
  error: null
}

const getters = {
  currentUser: state => state.currentUser,
  users: state => state.users,
  userStatus: state => state.status,
  userError: state => state.error
}

const actions = {
  // 获取当前用户信息
  fetchCurrentUser ({ commit }) {
    return new Promise((resolve, reject) => {
      commit('user_request')
      axios.get('/api/v1/users/me')
        .then(resp => {
          const user = resp.data
          commit('set_current_user', user)
          resolve(user)
        })
        .catch(err => {
          commit('user_error', err.response ? err.response.data : err.message)
          reject(err)
        })
    })
  },

  // 获取所有用户
  fetchUsers ({ commit }) {
    return new Promise((resolve, reject) => {
      commit('user_request')
      axios.get('/api/v1/users/')
        .then(resp => {
          const users = resp.data
          commit('set_users', users)
          resolve(users)
        })
        .catch(err => {
          commit('user_error', err.response ? err.response.data : err.message)
          reject(err)
        })
    })
  },

  // 更新当前用户资料
  updateProfile ({ commit }, userData) {
    return new Promise((resolve, reject) => {
      commit('user_request')
      axios.put('/api/v1/users/me', userData)
        .then(resp => {
          const user = resp.data
          commit('set_current_user', user)
          resolve(user)
        })
        .catch(err => {
          commit('user_error', err.response ? err.response.data : err.message)
          reject(err)
        })
    })
  },

  // 创建新用户
  createUser ({ commit }, userData) {
    return new Promise((resolve, reject) => {
      commit('user_request')
      axios.post('/api/v1/users/', userData)
        .then(resp => {
          commit('add_user', resp.data)
          resolve(resp.data)
        })
        .catch(err => {
          commit('user_error', err.response ? err.response.data : err.message)
          reject(err)
        })
    })
  },

  // 删除用户
  deleteUser ({ commit }, userId) {
    return new Promise((resolve, reject) => {
      commit('user_request')
      axios.delete(`/api/v1/users/${userId}`)
        .then(resp => {
          commit('remove_user', userId)
          resolve(resp.data)
        })
        .catch(err => {
          commit('user_error', err.response ? err.response.data : err.message)
          reject(err)
        })
    })
  }
}

const mutations = {
  user_request (state) {
    state.status = 'loading'
    state.error = null
  },
  set_current_user (state, user) {
    state.status = 'success'
    state.currentUser = user
    state.error = null
  },
  set_users (state, users) {
    state.status = 'success'
    state.users = users
    state.error = null
  },
  add_user (state, user) {
    state.status = 'success'
    state.users.push(user)
    state.error = null
  },
  remove_user (state, userId) {
    state.status = 'success'
    state.users = state.users.filter(user => user.id !== userId)
    state.error = null
  },
  user_error (state, error) {
    state.status = 'error'
    state.error = error
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
