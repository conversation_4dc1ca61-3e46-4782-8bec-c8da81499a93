const { defineConfig } = require('@vue/cli-service')
const path = require('path')

// 从环境变量读取配置
const FRONTEND_PORT = process.env.FRONTEND_PORT || 8080
const API_BASE_URL = process.env.VUE_APP_API_BASE_URL || 'http://localhost:5000'

// 获取项目根目录（D:\LP01\AI\AICBEC）
const projectRoot = path.resolve(__dirname, '../..')

module.exports = defineConfig({
  transpileDependencies: true,
  devServer: {
    port: FRONTEND_PORT,
    host: '0.0.0.0',
    allowedHosts: 'all',
    
    // 禁用WebSocket连接，避免HTTPS混合内容问题
    client: {
      webSocketTransport: 'sockjs',
      overlay: {
        errors: true,
        warnings: false
      }
    },
    
    // 禁用热重载，使用轮询代替
    hot: false,
    liveReload: false,
    
    proxy: {
      '/api': {
        target: API_BASE_URL,
        changeOrigin: true,
        secure: false
      }
    }
  },
  
  configureWebpack: {
    resolve: {
      modules: [
        path.join(projectRoot, 'node_modules'),
        'node_modules'
      ],
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    },
    resolveLoader: {
      modules: [
        path.join(projectRoot, 'node_modules'),
        'node_modules'
      ]
    }
  }
}) 