from pydantic_settings import BaseSettings
from pydantic import ConfigDict, field_validator
from typing import List, Optional, Dict, Any, Union
import os
from pathlib import Path

class Settings(BaseSettings):
    # 基本设置
    PROJECT_NAME: str = "AI Foreign Trade Operation System"
    API_V1_STR: str = "/api/v1"
    
    # 安全配置
    SECRET_KEY: str = "c2fc5f09e96dfc9b4d756eca0d7b3008a3544aefaa9ab1b1e02204a56c6baa3f"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 10080  # 7天
    
    # CORS设置 - 支持字符串或列表格式，新增新域名
    CORS_ORIGINS: Union[str, List[str]] = [
        "http://localhost:8080", 
        "http://localhost:3000",
        "http://127.0.0.1:8080",
        "http://127.0.0.1:3000", 
        "http://localhost:8000",
        "http://127.0.0.1:8000",
        "https://1088442hg3xl8.vicp.fun",
        "http://1088442hg3xl8.vicp.fun",
        "http://wf00659340.gnway.cc:8000",
        "https://wf00659340.gnway.cc:8000",
        # 添加可能的外网访问域名
        "http://wf00659340.gnway.cc",
        "https://wf00659340.gnway.cc"
        # 移除通配符"*"，提高安全性
    ]
    
    # 数据库设置
    DATABASE_URL: str = "mysql+pymysql://root:S3rSTRBm3dBw8EB7@**************:13306/CBEC"
    
    # 多租户设置
    MULTI_TENANT_ENABLED: bool = True
    DEFAULT_TENANT_ID: str = "default"
    
    # 文件存储配置
    UPLOAD_DIR: Union[str, Path] = "uploads"
    
    # 后端服务配置 - 使用环境变量或默认为5000端口
    BACKEND_HOST: str = "0.0.0.0"
    BACKEND_PORT: int = 5000
    BACKEND_DEBUG: bool = True
    BACKEND_RELOAD: bool = True
    
    # 前端服务配置
    FRONTEND_HOST: str = "0.0.0.0"
    FRONTEND_PORT: int = 8080
    
    # 外网前端地址配置 - 更新为新域名
    FRONTEND_URL: str = "https://1088442hg3xl8.vicp.fun"
    
    # 生产环境配置
    PRODUCTION: bool = False
    
    # 日志级别
    LOG_LEVEL: str = "INFO"
    
    # 阿里国际站API配置 - 更新回调地址为新域名
    ALIBABA_APP_KEY: str = "502750"
    ALIBABA_APP_SECRET: str = "a7cd4a24d3081fd89ae6233f127ba461"
    ALIBABA_OAUTH_URL: str = "https://open-api.alibaba.com/oauth/authorize"
    ALIBABA_TOKEN_URL: str = "https://open-api.alibaba.com/rest/auth/token/create"
    ALIBABA_REFRESH_TOKEN_URL: str = "https://open-api.alibaba.com/rest/auth/token/refresh"
    ALIBABA_REDIRECT_URI: str = "https://1088442hg3xl8.vicp.fun/api/v1/alibaba/callback"
    ALIBABA_GATEWAY_URL: str = "https://open-api.alibaba.com/rest"
    
    # 阿里巴巴API模式配置
    ALIBABA_USE_REAL_API: bool = True  # True=使用真实API，False=使用模拟数据
    ALIBABA_API_TIMEOUT: int = 30  # API超时时间（秒）
    ALIBABA_API_RETRY_COUNT: int = 3  # API重试次数
    
    @field_validator('CORS_ORIGINS')
    @classmethod
    def validate_cors_origins(cls, v):
        """验证和转换CORS_ORIGINS格式"""
        if isinstance(v, str):
            # 如果是字符串，按逗号分割
            return [origin.strip() for origin in v.split(',') if origin.strip()]
        elif isinstance(v, list):
            # 如果已经是列表，直接返回
            return v
        else:
            # 其他情况，返回默认值
            return ["*"]
    
    @field_validator('UPLOAD_DIR')
    @classmethod
    def validate_upload_dir(cls, v):
        """验证和转换UPLOAD_DIR为Path对象"""
        if isinstance(v, str):
            return Path(v)
        return v
    
    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding='utf-8',
        case_sensitive=True,
        extra='ignore'
    )
        
    def __init__(self, **kwargs):
        # 查找.env文件的路径
        env_paths = [
            Path(__file__).parent.parent.parent.parent / ".env",  # CBEC/.env
            Path(__file__).parent.parent.parent / ".env",        # backend/.env
            Path(".env"),                                         # 当前目录/.env
        ]
        
        for env_path in env_paths:
            if env_path.exists():
                kwargs.setdefault('_env_file', str(env_path))
                break
        
        super().__init__(**kwargs)

settings = Settings()

# 确保上传目录存在
os.makedirs(settings.UPLOAD_DIR, exist_ok=True) 