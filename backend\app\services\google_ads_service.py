import json
import logging
from typing import List, Dict, Any, Optional

# 可选导入 Google Ads SDK
try:
    from google.ads.googleads.client import GoogleAdsClient
    from google.ads.googleads.errors import GoogleAdsException
    from google.api_core import protobuf_helpers
    GOOGLE_ADS_AVAILABLE = True
except ImportError:
    GOOGLE_ADS_AVAILABLE = False
    GoogleAdsClient = None
    GoogleAdsException = Exception
    protobuf_helpers = None

import tempfile
import os
import requests

logger = logging.getLogger(__name__)

class GoogleAdsService:
    def __init__(self, config: Dict[str, str], proxy_config: Dict[str, Any] = None):
        """
        初始化 Google Ads 服务
        
        Args:
            config: 包含 Google Ads API 配置的字典
                - developer_token: 开发者令牌
                - client_id: 客户端ID
                - client_secret: 客户端密钥
                - refresh_token: 刷新令牌
                - customer_id: 客户ID
                - login_customer_id: 登录客户ID (可选)
            proxy_config: 代理服务器配置字典，包含：
                - use_proxy: 是否使用代理
                - proxy_host: 代理主机
                - proxy_port: 代理端口
                - proxy_username: 代理用户名（可选）
                - proxy_password: 代理密码（可选）
                - proxy_type: 代理类型（http, https, socks5）
        """
        if not GOOGLE_ADS_AVAILABLE:
            raise ImportError(
                "Google Ads SDK 未安装。请安装: pip install google-ads"
            )
        
        self.config = config
        self.proxy_config = proxy_config or {}
        self.client = self._create_client()
    
    def _create_client(self) -> GoogleAdsClient:
        """创建 Google Ads 客户端"""
        try:
            # 清理和验证customer_id格式（去掉连字符）
            customer_id = self.config["customer_id"].replace("-", "").strip()
            self.config["customer_id"] = customer_id
            
            # 添加调试信息
            logger.info(f"创建Google Ads客户端，配置信息:")
            logger.info(f"  - customer_id: {customer_id}")
            logger.info(f"  - login_customer_id: {self.config.get('login_customer_id')}")
            logger.info(f"  - developer_token存在: {'developer_token' in self.config and bool(self.config.get('developer_token'))}")
            logger.info(f"  - client_id存在: {'client_id' in self.config and bool(self.config.get('client_id'))}")
            logger.info(f"  - client_secret存在: {'client_secret' in self.config and bool(self.config.get('client_secret'))}")
            logger.info(f"  - refresh_token存在: {'refresh_token' in self.config and bool(self.config.get('refresh_token'))}")
            
            # 验证必需的配置字段
            required_fields = ['developer_token', 'client_id', 'client_secret', 'refresh_token']
            for field in required_fields:
                if not self.config.get(field):
                    raise ValueError(f"缺少必需的配置字段: {field}")
            
            # 打印开发者令牌的前几位和后几位（用于调试）
            dev_token = self.config["developer_token"]
            if len(dev_token) > 10:
                logger.info(f"  - developer_token: {dev_token[:5]}...{dev_token[-5:]}")
            else:
                logger.info(f"  - developer_token长度: {len(dev_token)}")
            
            # 首先获取有效的access_token
            logger.info("获取access_token...")
            access_token = self._get_access_token()
            logger.info(f"access_token获取成功: {access_token[:20]}..." if access_token else "access_token获取失败")
            
            # 创建临时配置文件
            config_dict = {
                "developer_token": self.config["developer_token"],
                "client_id": self.config["client_id"],
                "client_secret": self.config["client_secret"],
                "refresh_token": self.config["refresh_token"],
                "use_proto_plus": True
            }
            
            # 如果提供了API Key，添加到配置中
            api_key = self.config.get("api_key")
            if api_key:
                config_dict["api_key"] = api_key
                logger.info("已添加API Key到配置")
            
            # 如果成功获取access_token，添加到配置中
            if access_token:
                config_dict["access_token"] = access_token
                logger.info("已添加access_token到配置")
            
            # 设置login_customer_id（如果有的话）
            login_customer_id = self.config.get("login_customer_id")
            if login_customer_id:
                # 也去掉login_customer_id的连字符
                login_customer_id = login_customer_id.replace("-", "").strip()
                config_dict["login_customer_id"] = login_customer_id
                logger.info(f"  - 已设置login_customer_id: {login_customer_id}")
            
            # 配置代理设置（使用Google Ads SDK官方支持的http_proxy字段）
            if self.proxy_config.get('use_proxy') and self.proxy_config.get('proxy_host'):
                proxy_url = self._build_proxy_url()
                if proxy_url:
                    # 使用http_proxy配置字段，这是Google Ads SDK官方支持的方式
                    config_dict["http_proxy"] = proxy_url
                    logger.info(f"已配置Google Ads API代理: {proxy_url}")
            
            # 打印最终的配置字典（隐藏敏感信息）
            safe_config = {k: (v[:5] + "..." + v[-5:] if len(str(v)) > 10 else str(v)) for k, v in config_dict.items() if k not in ['client_secret', 'refresh_token', 'access_token']}
            safe_config['client_secret'] = "***" if config_dict.get('client_secret') else None
            safe_config['refresh_token'] = "***" if config_dict.get('refresh_token') else None
            safe_config['access_token'] = "***" if config_dict.get('access_token') else None
            logger.info(f"最终配置字典: {safe_config}")
            
            # 使用字典配置创建客户端
            logger.info("正在创建Google Ads客户端...")
            client = GoogleAdsClient.load_from_dict(config_dict)
            
            # 验证客户端创建成功
            logger.info(f"Google Ads客户端创建成功")
            
            # 尝试获取客户端的配置信息来验证
            try:
                # 检查客户端是否正确配置了customer_id
                logger.info(f"客户端配置验证完成")
            except Exception as config_error:
                logger.warning(f"客户端配置验证失败: {config_error}")
            
            return client
            
        except Exception as e:
            logger.error(f"创建 Google Ads 客户端失败: {e}")
            import traceback
            logger.error(f"创建客户端异常堆栈: {traceback.format_exc()}")
            raise
    
    def _build_proxy_url(self) -> Optional[str]:
        """构建代理URL"""
        try:
            proxy_host = self.proxy_config.get('proxy_host')
            proxy_port = self.proxy_config.get('proxy_port')
            proxy_username = self.proxy_config.get('proxy_username')
            proxy_password = self.proxy_config.get('proxy_password')
            proxy_type = self.proxy_config.get('proxy_type', 'http').lower()
            
            if not proxy_host or not proxy_port:
                logger.warning("代理配置不完整，跳过代理设置")
                return None
            
            # 构建代理URL
            if proxy_username and proxy_password:
                proxy_url = f"{proxy_type}://{proxy_username}:{proxy_password}@{proxy_host}:{proxy_port}"
            else:
                proxy_url = f"{proxy_type}://{proxy_host}:{proxy_port}"
            
            return proxy_url
            
        except Exception as e:
            logger.error(f"构建代理URL失败: {e}")
            return None
    
    def _get_access_token(self) -> Optional[str]:
        """使用refresh_token获取access_token"""
        try:
            # 使用代理配置
            proxies = {}
            if self.proxy_config.get('use_proxy') and self.proxy_config.get('proxy_host'):
                proxy_url = self._build_proxy_url()
                if proxy_url:
                    proxies = {
                        'http': proxy_url,
                        'https': proxy_url
                    }
            
            # OAuth2令牌刷新请求
            token_url = "https://oauth2.googleapis.com/token"
            data = {
                'grant_type': 'refresh_token',
                'client_id': self.config['client_id'],
                'client_secret': self.config['client_secret'],
                'refresh_token': self.config['refresh_token']
            }
            
            logger.info("向Google OAuth2服务器请求access_token...")
            response = requests.post(token_url, data=data, proxies=proxies, timeout=30)
            
            if response.status_code == 200:
                token_data = response.json()
                access_token = token_data.get('access_token')
                logger.info("access_token获取成功")
                return access_token
            else:
                logger.error(f"获取access_token失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"获取access_token异常: {e}")
            return None
    
    def generate_keyword_ideas(
        self,
        seed_keywords: Optional[List[str]] = None,
        url: Optional[str] = None,
        language_code: str = "zh",
        location_ids: List[int] = None,
        include_adult_keywords: bool = False,
        page_size: int = 100
    ) -> List[Dict[str, Any]]:
        """
        生成关键词提示
        
        Args:
            seed_keywords: 种子关键词列表
            url: 网站URL
            language_code: 语言代码，默认为中文
            location_ids: 地理位置ID列表，默认为中国
            include_adult_keywords: 是否包含成人关键词 (注意：在Google Ads API v19中此参数已被移除，保留仅为向后兼容)
            page_size: 每页结果数
            
        Returns:
            关键词提示列表
        """
        if location_ids is None:
            location_ids = [2156]  # 中国
            
        try:
            keyword_plan_idea_service = self.client.get_service("KeywordPlanIdeaService")
            
            # 构建请求
            request = self.client.get_type("GenerateKeywordIdeasRequest")
            request.customer_id = self.config["customer_id"]
            
            # 设置种子关键词或URL
            if seed_keywords:
                request.keyword_seed.keywords.extend(seed_keywords)
            
            if url:
                request.url_seed.url = url
            
            # 设置地理位置定位
            for location_id in location_ids:
                request.geo_target_constants.append(
                    self.client.get_service("GeoTargetConstantService").geo_target_constant_path(location_id)
                )
            
            # 设置语言定位
            language_constant_id = self._get_language_id(language_code)
            language_constant = f"languageConstants/{language_constant_id}"
            request.language = language_constant
            
            # 设置网络
            request.keyword_plan_network = self.client.enums.KeywordPlanNetworkEnum.GOOGLE_SEARCH_AND_PARTNERS
            
            # 设置历史指标选项
            request.historical_metrics_options.year_month_range.start.year = 2023
            request.historical_metrics_options.year_month_range.start.month = 1
            request.historical_metrics_options.year_month_range.end.year = 2024
            request.historical_metrics_options.year_month_range.end.month = 12
            # 注意：在Google Ads API v19中，include_adult_keywords字段已被移除
            
            # 设置分页
            request.page_size = min(page_size, 10000)  # Google Ads API 限制
            
            # 执行请求
            response = keyword_plan_idea_service.generate_keyword_ideas(request=request)
            
            # 解析结果
            keywords = []
            for idea in response.results:
                keyword_data = self._parse_keyword_idea(idea)
                keywords.append(keyword_data)
            
            return keywords
            
        except GoogleAdsException as ex:
            logger.error(f"Google Ads API 请求失败: {ex}")
            self._log_google_ads_failure(ex)
            raise
        except Exception as e:
            logger.error(f"生成关键词提示失败: {e}")
            raise
    
    def generate_historical_metrics(
        self,
        keywords: List[str],
        language_code: str = "zh",
        location_ids: List[int] = None
    ) -> List[Dict[str, Any]]:
        """
        获取关键词历史指标
        
        Args:
            keywords: 关键词列表
            language_code: 语言代码
            location_ids: 地理位置ID列表
            
        Returns:
            关键词历史指标列表
        """
        if location_ids is None:
            location_ids = [2156]  # 中国
            
        try:
            keyword_plan_idea_service = self.client.get_service("KeywordPlanIdeaService")
            
            # 构建请求
            request = self.client.get_type("GenerateHistoricalMetricsRequest")
            request.customer_id = self.config["customer_id"]
            
            # 设置关键词
            request.keywords.extend(keywords)
            
            # 设置地理位置定位
            for location_id in location_ids:
                request.geo_target_constants.append(
                    self.client.get_service("GeoTargetConstantService").geo_target_constant_path(location_id)
                )
            
            # 设置语言定位
            language_constant_id = self._get_language_id(language_code)
            language_constant = f"languageConstants/{language_constant_id}"
            request.language = language_constant
            
            # 设置网络
            request.keyword_plan_network = self.client.enums.KeywordPlanNetworkEnum.GOOGLE_SEARCH_AND_PARTNERS
            
            # 设置历史指标选项
            request.historical_metrics_options.year_month_range.start.year = 2023
            request.historical_metrics_options.year_month_range.start.month = 1
            request.historical_metrics_options.year_month_range.end.year = 2024
            request.historical_metrics_options.year_month_range.end.month = 12
            # 注意：在Google Ads API v19中，include_adult_keywords字段已被移除
            
            # 执行请求
            response = keyword_plan_idea_service.generate_historical_metrics(request=request)
            
            # 解析结果
            metrics = []
            for metric in response.results:
                metric_data = self._parse_historical_metric(metric)
                metrics.append(metric_data)
            
            return metrics
            
        except GoogleAdsException as ex:
            logger.error(f"Google Ads API 历史指标请求失败: {ex}")
            self._log_google_ads_failure(ex)
            raise
        except Exception as e:
            logger.error(f"获取历史指标失败: {e}")
            raise
    
    def _parse_keyword_idea(self, idea) -> Dict[str, Any]:
        """解析关键词提示数据"""
        keyword_data = {
            "keyword_name": idea.text,
            "avg_monthly_searches": None,
            "monthly_searches": None,
            "competition_level": "UNSPECIFIED",
            "competition_index": None,
            "low_bid_micros": None,
            "high_bid_micros": None,
            "currency_code": "CNY"
        }
        
        # 解析指标数据
        if hasattr(idea, 'keyword_idea_metrics') and idea.keyword_idea_metrics:
            metrics = idea.keyword_idea_metrics
            
            # 平均月搜索量
            if hasattr(metrics, 'avg_monthly_searches') and metrics.avg_monthly_searches:
                keyword_data["avg_monthly_searches"] = metrics.avg_monthly_searches
            
            # 竞争级别
            if hasattr(metrics, 'competition') and metrics.competition:
                keyword_data["competition_level"] = metrics.competition.name
            
            # 竞争指数
            if hasattr(metrics, 'competition_index') and metrics.competition_index:
                keyword_data["competition_index"] = float(metrics.competition_index)
            
            # 出价范围
            if hasattr(metrics, 'low_top_of_page_bid_micros') and metrics.low_top_of_page_bid_micros:
                keyword_data["low_bid_micros"] = metrics.low_top_of_page_bid_micros
            
            if hasattr(metrics, 'high_top_of_page_bid_micros') and metrics.high_top_of_page_bid_micros:
                keyword_data["high_bid_micros"] = metrics.high_top_of_page_bid_micros
            
            # 月度搜索量
            if hasattr(metrics, 'monthly_search_volumes') and metrics.monthly_search_volumes:
                monthly_data = []
                for volume in metrics.monthly_search_volumes:
                    monthly_data.append({
                        "year": volume.year,
                        "month": volume.month.name,
                        "searches": volume.monthly_searches if volume.monthly_searches else 0
                    })
                keyword_data["monthly_searches"] = json.dumps(monthly_data)
        
        return keyword_data
    
    def _parse_historical_metric(self, metric) -> Dict[str, Any]:
        """解析历史指标数据"""
        metric_data = {
            "keyword_name": metric.search_query,
            "avg_monthly_searches": None,
            "monthly_searches": None,
            "competition_level": "UNSPECIFIED",
            "competition_index": None,
            "low_bid_micros": None,
            "high_bid_micros": None,
            "currency_code": "CNY"
        }
        
        # 解析指标数据
        if hasattr(metric, 'keyword_metrics') and metric.keyword_metrics:
            metrics = metric.keyword_metrics
            
            # 平均月搜索量
            if hasattr(metrics, 'avg_monthly_searches') and metrics.avg_monthly_searches:
                metric_data["avg_monthly_searches"] = metrics.avg_monthly_searches
            
            # 竞争级别
            if hasattr(metrics, 'competition') and metrics.competition:
                metric_data["competition_level"] = metrics.competition.name
            
            # 竞争指数
            if hasattr(metrics, 'competition_index') and metrics.competition_index:
                metric_data["competition_index"] = float(metrics.competition_index)
            
            # 出价范围
            if hasattr(metrics, 'low_top_of_page_bid_micros') and metrics.low_top_of_page_bid_micros:
                metric_data["low_bid_micros"] = metrics.low_top_of_page_bid_micros
            
            if hasattr(metrics, 'high_top_of_page_bid_micros') and metrics.high_top_of_page_bid_micros:
                metric_data["high_bid_micros"] = metrics.high_top_of_page_bid_micros
            
            # 月度搜索量
            if hasattr(metrics, 'monthly_search_volumes') and metrics.monthly_search_volumes:
                monthly_data = []
                for volume in metrics.monthly_search_volumes:
                    monthly_data.append({
                        "year": volume.year,
                        "month": volume.month.name,
                        "searches": volume.monthly_searches if volume.monthly_searches else 0
                    })
                metric_data["monthly_searches"] = json.dumps(monthly_data)
        
        return metric_data
    
    def _get_language_id(self, language_code: str) -> str:
        """获取语言ID"""
        language_map = {
            "zh": "1000",     # Chinese (Simplified)
            "zh-CN": "1000",  # Chinese (Simplified)  
            "zh-TW": "1002",  # Chinese (Traditional)
            "en": "1000",     # English (US) - 默认英语为美国英语
            "en-US": "1000",  # English (US)
            "en-GB": "1001",  # English (UK)
            "ja": "1005",     # Japanese
            "ko": "1012",     # Korean
        }
        return language_map.get(language_code, "1000")  # 默认中文简体
    
    def _log_google_ads_failure(self, ex: GoogleAdsException):
        """记录 Google Ads API 错误详情"""
        logger.error(f"Request ID: {ex.request_id}")
        for error in ex.failure.errors:
            logger.error(f"Error: {error.error_code}: {error.message}")
            if error.location:
                for field_path_element in error.location.field_path_elements:
                    logger.error(f"Field: {field_path_element.field_name}")
    
    def test_connection(self) -> bool:
        """测试 Google Ads API 连接"""
        try:
            # 确保customer_id格式正确（去掉连字符）
            customer_id = self.config["customer_id"].replace("-", "").strip()
            logger.info(f"测试连接，使用customer_id: {customer_id}")
            
            customer_service = self.client.get_service("CustomerService")
            
            # 使用 list_accessible_customers 来测试连接
            logger.info("调用 list_accessible_customers...")
            logger.info(f"客户端类型: {type(self.client)}")
            logger.info(f"服务类型: {type(customer_service)}")
            
            # 直接调用API，让SDK处理认证
            response = customer_service.list_accessible_customers()
            
            # 检查当前客户ID是否在可访问客户列表中
            customer_resource_name = f"customers/{customer_id}"
            accessible_customers = [name for name in response.resource_names]
            
            logger.info(f"API调用成功，返回的可访问客户列表: {accessible_customers}")
            logger.info(f"当前客户资源名称: {customer_resource_name}")
            
            if customer_resource_name in accessible_customers:
                logger.info(f"连接成功，客户ID {customer_id} 可访问")
                return True
            else:
                logger.warning(f"客户ID {customer_id} 不在可访问列表中")
                logger.info(f"可访问的客户: {accessible_customers}")
                # 即使客户ID不在列表中，如果能够调用API，说明连接是成功的
                logger.info("但API调用成功，说明认证配置正确")
                return True
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            logger.error(f"异常类型: {type(e).__name__}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return False 