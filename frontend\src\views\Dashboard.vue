<template>
  <div class="dashboard-container">
    <div class="welcome-section">
      <h1>欢迎使用AI外贸运营系统</h1>
      <p>{{ currentTime }}</p>
    </div>

    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="12" :lg="6" :xl="6">
        <el-card class="box-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>数据采集</span>
              <el-button class="button" type="text" @click="navigateTo('/data-crawl/tasks')">查看</el-button>
            </div>
          </template>
          <div class="card-content">
            <div class="statistic">
              <div class="number">{{ stats.dataCrawl.total }}</div>
              <div class="label">采集任务总数</div>
            </div>
            <div class="pie-chart">
              <div class="chart-item">
                <span class="dot success"></span>
                <span>{{ stats.dataCrawl.success }} 成功</span>
              </div>
              <div class="chart-item">
                <span class="dot processing"></span>
                <span>{{ stats.dataCrawl.processing }} 进行中</span>
              </div>
              <div class="chart-item">
                <span class="dot error"></span>
                <span>{{ stats.dataCrawl.error }} 失败</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="6" :xl="6">
        <el-card class="box-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>AI站群</span>
              <el-button class="button" type="text" @click="navigateTo('/ai-cluster/wordpress-hosts')">查看</el-button>
            </div>
          </template>
          <div class="card-content">
            <div class="statistic">
              <div class="number">{{ stats.aiCluster.hosts }}</div>
              <div class="label">WordPress站点</div>
            </div>
            <div class="statistic">
              <div class="number">{{ stats.aiCluster.posts }}</div>
              <div class="label">AI生成文章</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="6" :xl="6">
        <el-card class="box-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>AI运营</span>
              <el-button class="button" type="text" @click="navigateTo('/ai-operation/inquiries')">查看</el-button>
            </div>
          </template>
          <div class="card-content">
            <div class="statistic">
              <div class="number">{{ stats.aiOperation.products }}</div>
              <div class="label">商品数</div>
            </div>
            <div class="statistic">
              <div class="number">{{ stats.aiOperation.inquiries }}</div>
              <div class="label">今日询盘</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12" :lg="6" :xl="6">
        <el-card class="box-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>系统状态</span>
              <el-button class="button" type="text" @click="navigateTo('/system/users')">管理</el-button>
            </div>
          </template>
          <div class="card-content">
            <div class="statistic">
              <div class="number">{{ stats.system.users }}</div>
              <div class="label">系统用户</div>
            </div>
            <div class="system-info">
              <div class="chart-item">
                <span>当前租户: {{ tenantName }}</span>
              </div>
              <div class="chart-item">
                <span>订阅计划: {{ subscriptionPlan }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="chart-row">
      <el-col :xs="24" :sm="24" :md="12">
        <el-card class="box-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>近7天询盘统计</span>
            </div>
          </template>
          <div class="chart-placeholder">
            <!-- 图表占位 -->
            <div class="mock-chart">
              <div v-for="(item, index) in 7" :key="index" class="bar-item" :style="{ height: randomBarHeight() + '%' }"></div>
            </div>
            <div class="chart-footer">
              <span v-for="(day, index) in last7Days" :key="index">{{ day }}</span>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="12">
        <el-card class="box-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>询盘来源分布</span>
            </div>
          </template>
          <div class="chart-placeholder">
            <div class="doughnut-chart">
              <div class="doughnut-center">{{ stats.aiOperation.inquiries }}</div>
              <div class="doughnut-segments">
                <div class="segment segment-1"></div>
                <div class="segment segment-2"></div>
                <div class="segment segment-3"></div>
                <div class="segment segment-4"></div>
              </div>
            </div>
            <div class="chart-legend">
              <div class="legend-item"><span class="legend-dot segment-1"></span>Google (42%)</div>
              <div class="legend-item"><span class="legend-dot segment-2"></span>直接访问 (30%)</div>
              <div class="legend-item"><span class="legend-dot segment-3"></span>社交媒体 (18%)</div>
              <div class="legend-item"><span class="legend-dot segment-4"></span>其他 (10%)</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :xs="24">
        <el-card class="box-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
            </div>
          </template>
          <div class="timeline">
            <div class="timeline-item" v-for="(activity, index) in recentActivities" :key="index">
              <div class="time">{{ activity.time }}</div>
              <div class="content">
                <div class="title">{{ activity.title }}</div>
                <div class="description">{{ activity.description }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { defineComponent, ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { formatToSystemTimezone } from '@/utils/timezone'

export default defineComponent({
  name: 'Dashboard',
  setup () {
    const store = useStore()
    const router = useRouter()
    const currentTime = ref(formatToSystemTimezone(new Date(), {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    }))

    const tenantName = computed(() => {
      const tenant = store.getters['tenant/currentTenant']
      return tenant ? tenant.name : '加载中...'
    })

    const subscriptionPlan = ref('专业版')

    // 示例数据
    const stats = ref({
      dataCrawl: {
        total: 24,
        success: 18,
        processing: 4,
        error: 2
      },
      aiCluster: {
        hosts: 6,
        posts: 128
      },
      aiOperation: {
        products: 87,
        inquiries: 12
      },
      system: {
        users: 5
      }
    })

    const last7Days = computed(() => {
      const days = []
      const today = new Date()
      for (let i = 6; i >= 0; i--) {
        const day = new Date(today)
        day.setDate(today.getDate() - i)
        days.push(day.getDate() + '日')
      }
      return days
    })

    const recentActivities = ref([
      {
        time: '10分钟前',
        title: '新询盘',
        description: '收到来自美国客户的产品咨询。'
      },
      {
        time: '1小时前',
        title: 'AI文章发布',
        description: '系统自动生成并发布了3篇SEO优化文章。'
      },
      {
        time: '3小时前',
        title: '数据采集完成',
        description: '成功采集了竞品数据分析报告。'
      },
      {
        time: '昨天',
        title: '系统更新',
        description: 'AI模型更新完成，提升了文案生成质量。'
      }
    ])

    const randomBarHeight = () => {
      return Math.floor(Math.random() * 70) + 30
    }

    const navigateTo = (path) => {
      router.push(path)
    }

    onMounted(() => {
      // 获取租户信息
      store.dispatch('tenant/fetchCurrentTenant').catch(error => {
        console.error('获取租户信息失败', error)
      })
    })

    return {
      currentTime,
      tenantName,
      subscriptionPlan,
      stats,
      last7Days,
      recentActivities,
      randomBarHeight,
      navigateTo
    }
  }
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
}

.welcome-section {
  margin-bottom: 24px;
  h1 {
    font-size: 24px;
    margin: 0;
  }
  p {
    margin: 5px 0 0;
    color: #909399;
    font-size: 14px;
  }
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  padding: 10px 0;
}

.statistic {
  margin-bottom: 15px;

  .number {
    font-size: 28px;
    font-weight: bold;
    color: #303133;
  }

  .label {
    font-size: 14px;
    color: #909399;
  }
}

.pie-chart {
  margin-top: 15px;

  .chart-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;

    .dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 8px;

      &.success {
        background-color: #67C23A;
      }

      &.processing {
        background-color: #409EFF;
      }

      &.error {
        background-color: #F56C6C;
      }
    }
  }
}

.system-info {
  margin-top: 15px;

  .chart-item {
    margin-bottom: 8px;
    font-size: 14px;
  }
}

.chart-row {
  margin-bottom: 20px;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.mock-chart {
  height: 150px;
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  margin: 0 10px;

  .bar-item {
    width: 12%;
    background-color: #409EFF;
    border-radius: 3px 3px 0 0;
  }
}

.chart-footer {
  display: flex;
  justify-content: space-around;
  margin-top: 10px;
  color: #909399;
  font-size: 12px;
}

.doughnut-chart {
  position: relative;
  width: 150px;
  height: 150px;
  margin: 0 auto;

  .doughnut-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 32px;
    font-weight: bold;
    color: #303133;
  }

  .doughnut-segments {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    overflow: hidden;
  }

  .segment {
    position: absolute;
    width: 100%;
    height: 100%;

    &.segment-1 {
      background-color: #409EFF;
      clip-path: polygon(50% 0, 100% 0, 100% 100%, 50% 100%);
      transform: rotate(0deg);
    }

    &.segment-2 {
      background-color: #67C23A;
      clip-path: polygon(50% 0, 100% 0, 100% 40%, 50% 40%);
      transform: rotate(151.2deg);
    }

    &.segment-3 {
      background-color: #E6A23C;
      clip-path: polygon(50% 0, 100% 0, 100% 25%, 50% 25%);
      transform: rotate(259.2deg);
    }

    &.segment-4 {
      background-color: #F56C6C;
      clip-path: polygon(50% 0, 100% 0, 100% 15%, 50% 15%);
      transform: rotate(324deg);
    }
  }
}

.chart-legend {
  margin-top: 15px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;

  .legend-item {
    display: flex;
    align-items: center;
    margin-right: 15px;
    font-size: 12px;

    .legend-dot {
      width: 10px;
      height: 10px;
      margin-right: 5px;
      border-radius: 50%;

      &.segment-1 {
        background-color: #409EFF;
      }

      &.segment-2 {
        background-color: #67C23A;
      }

      &.segment-3 {
        background-color: #E6A23C;
      }

      &.segment-4 {
        background-color: #F56C6C;
      }
    }
  }
}

.timeline {
  .timeline-item {
    display: flex;
    margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
    }

    .time {
      width: 100px;
      flex-shrink: 0;
      color: #909399;
      font-size: 13px;
    }

    .content {
      .title {
        font-weight: bold;
        margin-bottom: 5px;
      }

      .description {
        color: #606266;
        font-size: 14px;
      }
    }
  }
}
</style>
