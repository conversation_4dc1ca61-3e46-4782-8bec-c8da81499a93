# 环境变量配置指南

## 概述
AI外贸运营系统使用环境变量进行统一配置管理，所有的数据库连接、服务端口、API地址等配置都通过 `.env` 文件进行管理。

## 配置文件结构

```
项目根目录/
├── .env                    # 主配置文件（包含敏感信息，不提交到版本控制）
├── .env.example           # 配置模板文件（可提交到版本控制）
└── frontend/
    └── .env.local         # 前端专用配置（不提交到版本控制）
```

## 配置项详解

### 基础配置
```bash
# 项目名称
PROJECT_NAME=AI外贸运营系统

# API版本前缀
API_V1_STR=/api/v1
```

### 安全配置
```bash
# JWT密钥 - 生产环境必须使用强随机密钥
SECRET_KEY=your-secret-key-here

# Token过期时间（分钟）
ACCESS_TOKEN_EXPIRE_MINUTES=10080  # 7天
```

### 数据库配置
```bash
# MySQL数据库连接URL
# 格式：mysql+pymysql://用户名:密码@主机:端口/数据库名
DATABASE_URL=mysql+pymysql://username:password@host:port/database_name
```

### 多租户配置
```bash
# 是否启用多租户
MULTI_TENANT_ENABLED=true

# 默认租户ID
DEFAULT_TENANT_ID=default
```

### 文件存储配置
```bash
# 文件上传目录
UPLOAD_DIR=uploads
```

### 后端服务配置
```bash
# 后端服务地址
BACKEND_HOST=localhost

# 后端服务端口
BACKEND_PORT=8000

# 调试模式
BACKEND_DEBUG=true

# 热重载
BACKEND_RELOAD=true
```

### 前端服务配置
```bash
# 前端服务地址
FRONTEND_HOST=localhost

# 前端服务端口
FRONTEND_PORT=8080
```

### CORS配置
```bash
# 允许的跨域源地址（用逗号分隔）
CORS_ORIGINS=http://localhost:8080,http://localhost:3000
```

### 前端API配置
```bash
# 前端访问的API基础URL
VUE_APP_API_BASE_URL=http://localhost:8000
```

### 环境配置
```bash
# 是否为生产环境
PRODUCTION=false

# 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO
```

## 配置管理操作

### 初始化配置
```bash
# 使用配置管理工具初始化
python scripts/config-manager.py init

# 或手动复制
cp .env.example .env
```

### 检查配置
```bash
python scripts/config-manager.py check
```

### 显示当前配置
```bash
python scripts/config-manager.py show
```

### 验证配置
```bash
python scripts/config-manager.py validate
```

## 环境特定配置

### 开发环境
```bash
PRODUCTION=false
BACKEND_DEBUG=true
BACKEND_RELOAD=true
LOG_LEVEL=DEBUG
```

### 测试环境
```bash
PRODUCTION=false
BACKEND_DEBUG=false
BACKEND_RELOAD=false
LOG_LEVEL=INFO
```

### 生产环境
```bash
PRODUCTION=true
BACKEND_DEBUG=false
BACKEND_RELOAD=false
LOG_LEVEL=WARNING

# 必须配置的生产环境项
SECRET_KEY=<强随机密钥>
DATABASE_URL=<生产数据库连接>
BACKEND_HOST=<实际域名>
FRONTEND_HOST=<实际域名>
```

## 安全注意事项

1. **敏感信息保护**
   - `.env` 文件包含敏感信息，不应提交到版本控制
   - 生产环境必须使用强随机密钥
   - 数据库密码应定期更换

2. **文件权限**
   - `.env` 文件应设置适当的文件权限
   - 只有必要的用户才能读取配置文件

3. **环境隔离**
   - 不同环境使用不同的配置文件
   - 避免在代码中硬编码敏感信息

## 配置优先级

环境变量的优先级从高到低：
1. 系统环境变量
2. `.env` 文件配置
3. 代码中的默认值

## 故障排除

### 常见问题

1. **配置文件不存在**
   ```bash
   # 检查并初始化配置
   python scripts/config-manager.py check
   python scripts/config-manager.py init
   ```

2. **数据库连接失败**
   - 检查 `DATABASE_URL` 格式是否正确
   - 确认数据库服务是否运行
   - 验证用户名密码是否正确

3. **端口冲突**
   - 修改 `BACKEND_PORT` 或 `FRONTEND_PORT`
   - 检查端口是否被其他服务占用

4. **CORS错误**
   - 检查 `CORS_ORIGINS` 配置
   - 确保前端URL在允许列表中

### 配置验证失败
```bash
# 运行验证并查看详细错误信息
python scripts/config-manager.py validate
```

## 最佳实践

1. **版本控制**
   - 只提交 `.env.example` 模板文件
   - 使用 `.gitignore` 排除敏感配置文件

2. **文档更新**
   - 添加新配置项时更新 `.env.example`
   - 在配置文档中说明新配置的用途

3. **配置管理**
   - 定期审查配置项
   - 移除不再使用的配置
   - 保持配置文件整洁

4. **部署自动化**
   - 使用配置管理工具验证配置
   - 在部署脚本中检查必需配置项 