#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试阿里巴巴产品API修复
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
from datetime import date, timedelta
from app.core.database import get_db
from app.services.alibaba_product_service_real import AlibabaProductServiceReal
from app.services.alibaba_service import AlibabaService
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_alibaba_product_api():
    """测试阿里巴巴产品API"""
    
    # 模拟测试参数
    test_access_token = "test_token"  # 请替换为真实的access_token
    test_user_id = 1
    test_tenant_id = "test_tenant"
    
    # 获取数据库连接
    db = next(get_db())
    
    try:
        # 初始化服务
        product_service = AlibabaProductServiceReal()
        alibaba_service = AlibabaService(db)
        
        # 测试产品列表API调用
        logger.info("开始测试产品列表API...")
        
        try:
            # 直接测试API调用
            products_response = alibaba_service._call_alibaba_api(
                test_access_token,
                "alibaba.icbu.product.list",
                {
                    "language": "ENGLISH",
                    "page_size": 10,
                    "current_page": 1
                }
            )
            
            logger.info(f"API调用成功: {products_response}")
            
            # 检查响应结构
            if "alibaba_icbu_product_list_response" in products_response:
                logger.info("✅ API响应格式正确")
                
                response_data = products_response["alibaba_icbu_product_list_response"]
                logger.info(f"响应数据: {response_data}")
                
                # 检查产品数据
                if "products" in response_data:
                    products = response_data["products"]
                    logger.info(f"产品数据: {products}")
                else:
                    logger.info("响应中没有产品数据")
                    
            elif "error_response" in products_response:
                error = products_response["error_response"]
                logger.error(f"❌ API返回错误: {error}")
                
                # 分析错误类型
                error_code = error.get("code")
                error_msg = error.get("msg")
                sub_code = error.get("sub_code")
                sub_msg = error.get("sub_msg")
                
                logger.error(f"错误码: {error_code}")
                logger.error(f"错误信息: {error_msg}")
                logger.error(f"子错误码: {sub_code}")
                logger.error(f"子错误信息: {sub_msg}")
                
                if error_code == 50 and "InvalidApiPath" in str(error):
                    logger.error("🔍 这是API路径无效错误，说明API名称可能仍然不正确")
                elif "insufficient_scope" in str(error):
                    logger.error("🔍 这是权限不足错误，需要检查API权限申请")
                elif "invalid-sessionkey" in str(error):
                    logger.error("🔍 这是session无效错误，需要检查access_token")
            else:
                logger.warning(f"⚠️ 未知响应格式: {products_response}")
                
        except Exception as api_error:
            logger.error(f"❌ API调用异常: {api_error}")
            
        # 测试完整的同步流程
        logger.info("\n开始测试完整同步流程...")
        
        try:
            result = await product_service.sync_data_from_alibaba(
                db=db,
                user_id=test_user_id,
                tenant_id=test_tenant_id,
                statistics_type="day",
                start_date=date.today() - timedelta(days=7),
                end_date=date.today(),
                access_token=test_access_token
            )
            
            logger.info(f"✅ 同步流程完成: {result}")
            
        except Exception as sync_error:
            logger.error(f"❌ 同步流程失败: {sync_error}")
            
    finally:
        db.close()

def main():
    """主函数"""
    logger.info("=== 阿里巴巴产品API修复测试 ===")
    
    # 提示用户
    logger.info("📝 注意: 请确保已配置正确的阿里巴巴API密钥")
    logger.info("📝 注意: 请将test_access_token替换为真实的授权令牌")
    
    # 运行测试
    asyncio.run(test_alibaba_product_api())

if __name__ == "__main__":
    main() 