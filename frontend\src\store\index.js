import { createStore } from 'vuex'
import auth from './modules/auth'
import user from './modules/user'
import tenant from './modules/tenant'

export default createStore({
  state: {
    appName: 'AI外贸运营系统',
    isCollapse: false,
    isMobile: false,
    breadcrumbs: [],
    theme: 'light'
  },
  mutations: {
    toggleSideBar (state) {
      state.isCollapse = !state.isCollapse
      // 在移动端保存状态到localStorage
      if (state.isMobile) {
        localStorage.setItem('sidebarCollapse', state.isCollapse)
      }
    },
    closeSideBar (state) {
      state.isCollapse = true
      if (state.isMobile) {
        localStorage.setItem('sidebarCollapse', true)
      }
    },
    openSideBar (state) {
      state.isCollapse = false
      if (state.isMobile) {
        localStorage.setItem('sidebarCollapse', false)
      }
    },
    setMobile (state, isMobile) {
      state.isMobile = isMobile
    },
    updateBreadcrumbs (state, breadcrumbs) {
      state.breadcrumbs = breadcrumbs
    },
    setTheme (state, theme) {
      state.theme = theme
      localStorage.setItem('theme', theme)
    },
    initSidebarState (state) {
      // 从localStorage恢复侧边栏状态
      const savedCollapse = localStorage.getItem('sidebarCollapse')
      if (savedCollapse !== null) {
        state.isCollapse = JSON.parse(savedCollapse)
      }
    }
  },
  actions: {
    toggleSideBar ({ commit }) {
      commit('toggleSideBar')
    },
    closeSideBar ({ commit }) {
      commit('closeSideBar')
    },
    openSideBar ({ commit }) {
      commit('openSideBar')
    },
    setMobile ({ commit }, isMobile) {
      commit('setMobile', isMobile)
    },
    updateBreadcrumbs ({ commit }, breadcrumbs) {
      commit('updateBreadcrumbs', breadcrumbs)
    },
    setTheme ({ commit }, theme) {
      commit('setTheme', theme)
    },
    initApp ({ commit }) {
      // 初始化应用状态
      commit('initSidebarState')

      // 初始化主题
      const savedTheme = localStorage.getItem('theme') || 'light'
      commit('setTheme', savedTheme)
    }
  },
  getters: {
    appName: state => state.appName,
    isCollapse: state => state.isCollapse,
    isMobile: state => state.isMobile,
    breadcrumbs: state => state.breadcrumbs,
    theme: state => state.theme,
    isDarkTheme: state => state.theme === 'dark',
    token: (state, getters) => getters['auth/token']
  },
  modules: {
    auth,
    user,
    tenant
  }
})
