# 我的效果-获取公司询盘流量行业表现

我的效果-获取公司询盘流量行业表现

GET/POST

alibaba.mydata.overview.indicator.basic.get

描述：获取公司询盘流量行业表现

## 参数

|  名称  |  类型  |  是否必须  |  描述  |
| --- | --- | --- | --- |
|  date\_range  |  Object  |  是  |  要查询的数据周期  |
|  end\_date  |  String  |  是  |  数据周期结束时间（含）  |
|  start\_date  |  String  |  是  |  数据周期开始时间（含）  |
|  industry  |  Object  |  是  |  要查询的行业信息  |
|  industry\_desc  |  String  |  否  |  行业描述  |
|  industry\_id  |  Number  |  是  |  行业ID  |
|  main\_category  |  Boolean  |  是  |  是否主营行业  |

## 响应参数

|  名称  |  类型  |  描述  |
| --- | --- | --- |
|  result  |  Object  |  公司询盘流量指标  |
|  clk  |  Number  |  点击  |
|  clk\_rate  |  String  |  点击率  |
|  fb  |  Number  |  反馈（询盘）  |
|  imps  |  Number  |  曝光  |
|  reply  |  String  |  最近30天询盘一次回复率  |
|  visitor  |  Number  |  访客  |

## 错误码

|  错误码  |  错误信息  |  解决方案  |
| --- | --- | --- |
|  没有数据  |  |  |

示例：

GET/POSTalibaba.mydata.overview.indicator.basic.get

*   PYTHON
    

```PYTHON
client = iop.IopClient(url, appkey ,appSecret)
request = iop.IopRequest('alibaba.mydata.overview.indicator.basic.get')
request.add_api_param('date_range', '{\"end_date\":\"2015-01-24\",\"start_date\":\"2015-01-18\"}')
request.add_api_param('industry', '{\"industry_id\":\"111\",\"main_category\":\"true\",\"industry_desc\":\"All\"}')
response = client.execute(request, access_token)
print(response.type)
print(response.body)

```

*   非精简返回
    

```json
{
  "code": "0",
  "alibaba_mydata_overview_indicator_basic_get_response": {
    "result": {
      "clk": "1",
      "clk_rate": "1",
      "imps": "1",
      "fb": "1",
      "reply": "1",
      "visitor": "1"
    }
  },
  "request_id": "0ba2887315178178017221014"
```