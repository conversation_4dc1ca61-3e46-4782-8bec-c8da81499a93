<template>
  <div class="auth-error-container">
    <div class="error-card">
      <div class="error-icon">
        <i class="el-icon-error" style="font-size: 64px; color: #F56C6C;"></i>
      </div>
      
      <h2>阿里国际站授权失败</h2>
      
      <div class="error-message" v-if="message">
        <p>{{ message }}</p>
      </div>
      
      <div class="error-details" v-if="errorDetails">
        <h4>错误详情：</h4>
        <p>{{ errorDetails }}</p>
      </div>
      
      <div class="suggestions">
        <h4>解决建议：</h4>
        <ul>
          <li>检查阿里巴巴账号和密码是否正确</li>
          <li>确认您有权限授权该应用</li>
          <li>检查网络连接是否正常</li>
          <li>重新尝试授权流程</li>
        </ul>
      </div>
      
      <div class="action-buttons">
        <button class="btn btn-primary" @click="retryAuth">重新授权</button>
        <button class="btn btn-secondary" @click="goToDashboard">返回首页</button>
        <button class="btn btn-info" @click="contactSupport">联系技术支持</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AlibabaAuthError',
  data() {
    return {
      message: '',
      errorDetails: ''
    }
  },
  
  mounted() {
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search)
    this.message = urlParams.get('message') || '授权过程中发生错误'
    this.errorDetails = urlParams.get('error') || ''
  },
  
  methods: {
    retryAuth() {
      // 使用完整的路径确保在外网环境下正确跳转
      window.location.href = '/ai-operation/alibaba-auth'
    },
    
    goToDashboard() {
      // 使用完整的路径确保在外网环境下正确跳转
      window.location.href = '/'
    },
    
    contactSupport() {
      // 这里可以添加联系技术支持的逻辑
      alert('请联系技术支持团队获取帮助')
    }
  }
}
</script>

<style scoped>
.auth-error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
}

.error-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 40px;
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.error-icon {
  margin-bottom: 24px;
}

h2 {
  color: #333;
  margin-bottom: 24px;
  font-size: 24px;
}

.error-message {
  margin-bottom: 24px;
  padding: 16px;
  background: #fef0f0;
  border-radius: 8px;
  border-left: 4px solid #F56C6C;
}

.error-message p {
  margin: 0;
  color: #606266;
}

.error-details {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: left;
}

.error-details h4 {
  margin: 0 0 12px 0;
  color: #495057;
}

.error-details p {
  margin: 0;
  color: #6c757d;
  font-family: monospace;
  font-size: 14px;
  word-break: break-all;
}

.suggestions {
  margin-bottom: 32px;
  padding: 16px;
  background: #e3f2fd;
  border-radius: 8px;
  text-align: left;
}

.suggestions h4 {
  margin: 0 0 12px 0;
  color: #1976d2;
}

.suggestions ul {
  margin: 0;
  padding-left: 20px;
  color: #424242;
}

.suggestions li {
  margin-bottom: 8px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s;
  min-width: 120px;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn-info {
  background-color: #17a2b8;
  color: white;
}

.btn-info:hover {
  background-color: #138496;
}
</style> 