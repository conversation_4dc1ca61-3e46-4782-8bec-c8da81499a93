# 我的效果-获取TOP行业列表

我的效果-获取Top行业列表

GET/POST

alibaba.mydata.overview.industry.get

描述：获取数据管家我的效果API可以使用的行业

## 参数

|  名称  |  类型  |  是否必须  |  描述  |
| --- | --- | --- | --- |
|  date\_range  |  Object  |  否  |  系统自动生成  |
|  end\_date  |  String  |  是  |  数据周期结束时间（含）  |
|  start\_date  |  String  |  是  |  数据周期开始时间（含）  |

## 响应参数

|  名称  |  类型  |  描述  |
| --- | --- | --- |
|  result\_list  |  Object\[\]  |  供应商Top行业列表  |
|  industry\_desc  |  String  |  行业描述  |
|  industry\_id  |  Number  |  行业ID  |
|  main\_category  |  Boolean  |  是否主营行业  |

## 错误码

|  错误码  |  错误信息  |  解决方案  |
| --- | --- | --- |
|  没有数据  |  |  |

GET/POSTalibaba.mydata.overview.industry.get

*   JAVA
    

```JAVA
IopClient client = new IopClient(url, appkey, appSecret);
IopRequest request = new IopRequest();
request.setApiName("alibaba.mydata.overview.industry.get");
request.addApiParameter("date_range", "{\"end_date\":\"2015-01-24\",\"start_date\":\"2015-01-18\"}");
IopResponse response = client.execute(request, accessToken, Protocol.TOP);
System.out.println(response.getBody());
Thread.sleep(10);

```

*   非精简返回
    

```json
{
  "code": "0",
  "alibaba_mydata_overview_industry_get_response": {
    "result_list": [
      {
        "industry_id": "111",
        "main_category": "true",
        "industry_desc": "All"
      }
    ]
  },
  "request_id": "0ba2887315178178017221014"
```