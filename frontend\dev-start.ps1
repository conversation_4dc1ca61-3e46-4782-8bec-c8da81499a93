# AICBEC Frontend 开发启动脚本
# 自动修复ESLint错误并启动开发服务器

Write-Host "🚀 AICBEC Frontend 开发环境启动..." -ForegroundColor Green
Write-Host ""

# 检查Node.js和npm
Write-Host "📋 检查环境..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
    Write-Host "✅ npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 请先安装Node.js和npm" -ForegroundColor Red
    exit 1
}

Write-Host ""

# 检查依赖
Write-Host "📦 检查依赖..." -ForegroundColor Yellow
if (!(Test-Path "node_modules")) {
    Write-Host "⚠️  未发现node_modules，正在安装依赖..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 依赖安装失败" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ 依赖安装完成" -ForegroundColor Green
} else {
    Write-Host "✅ 依赖已存在" -ForegroundColor Green
}

Write-Host ""

# 自动修复ESLint错误
Write-Host "🔧 自动修复ESLint错误..." -ForegroundColor Yellow
npm run lint:fix
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ ESLint修复完成" -ForegroundColor Green
} else {
    Write-Host "⚠️  ESLint修复遇到问题，但继续启动..." -ForegroundColor Yellow
}

Write-Host ""

# 格式化代码
Write-Host "💎 格式化代码..." -ForegroundColor Yellow
npm run format
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 代码格式化完成" -ForegroundColor Green
} else {
    Write-Host "⚠️  代码格式化遇到问题，但继续启动..." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🌟 启动开发服务器..." -ForegroundColor Green
Write-Host "📂 项目地址: http://localhost:8080" -ForegroundColor Cyan
Write-Host "🛑 按 Ctrl+C 停止服务器" -ForegroundColor Cyan
Write-Host ""

# 启动开发服务器
npm run serve:pure 