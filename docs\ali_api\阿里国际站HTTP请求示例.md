# 阿里国际站HTTP请求示例

# 请求示例

##### 一、原平台迁移API

以[alibaba.icbu.product.schema.get](https://openapi.alibaba.com/doc/api.htm?spm=a2o9m.11193531.0.0.20b2f453ZE5Uwx#/api?cid=1&path=/alibaba/icbu/product/schema/get&methodType=GET/POST)（获取商品发布字段和规则）为例，具体步骤如下：

1.设置参数值

公共参数：

1.  app\_key = “12345678”
    
2.  access\_token = “test”
    
3.  timestamp = “1517820392000”
    
4.  sign\_method = “sha256”
    
5.  simplify = “true”
    

业务参数：

1.  cat\_id = "333"
    
2.  language = "en\_US"
    
3.  method = "alibaba.icbu.product.schema.get"
    

2.按ASCII顺序排序

1.  access\_token = "test"
    
2.  app\_key = "12345678"
    
3.  cat\_id = "333"
    
4.  language = "en\_US"
    
5.  method = "alibaba.icbu.product.schema.get"
    
6.  sign\_method = "sha256"
    
7.  simplify = "true"
    
8.  timestamp = "1517820392000"
    

3.拼接参数名与参数值

PlainBashC++C#CSSDiffHTML/XMLJavaJavascriptMarkdownPHPPythonRubySQL

access\_tokentestapp\_key12345678cat\_id333languageen\_USmethodalibaba.icbu.product.schema.getsign\_methodsha256simplifytruetimestamp1517820392000

4.生成签名

假设app的secret为helloworld，则签名结果为：

PlainBashC++C#CSSDiffHTML/XMLJavaJavascriptMarkdownPHPPythonRubySQL

hex(sha256(access\_tokentestapp\_key12345678cat\_id333languageen\_USmethodalibaba.icbu.product.schema.getsign\_methodsha256simplifytruetimestamp1517820392000))=D72D433988FC4727F39E123E3396F557C3E53DEC392C1992C77812579DAA7CD2

5.组装HTTP请求

将所有参数名和参数值采用utf-8进行URL编码（参数顺序可随意，但必须要包括签名参数），然后通过GET或POST（含byte\[\]类型参数）发起请求，拼接方式为：https://open-api.alibaba.com/sync?method={api\_path}&{query}。如：

PlainBashC++C#CSSDiffHTML/XMLJavaJavascriptMarkdownPHPPythonRubySQL

http://open-api.alibaba.com/sync?method=alibaba.icbu.product.schema.get&access\_token=test&app\_key=12345678&timestamp=1517820392000&sign\_method=sha256&simplify=true&cat\_id=LP00038357949881&language=en\_US&sign=D72D433988FC4727F39E123E3396F557C3E53DEC392C1992C77812579DAA7CD2

##### 二、新平台注册API

###### 以[/auth/token/create](https://open.aliexpress.com/doc/api.htm?spm=a2o9m.11193531.0.0.26a24bd6anhbXd#/api?cid=3&path=/auth/token/create&methodType=GET/POST)(生成授权token)调用为例，具体步骤如下：

1.设置参数值

公共参数（该示例API不需要传入access\_token）：

1.  app\_key = “12345678”
    
2.  timestamp = “1517820392000”
    
3.  sign\_method = “sha256”
    
4.  simplify = “true”
    

业务参数：

1.  code = “3\_500102\_JxZ05Ux3cnnSSUm6dCxYg6Q26”
    

2.按ASCII顺序排序

1.  app\_key = “12345678”
    
2.  code = “3\_500102\_JxZ05Ux3cnnSSUm6dCxYg6Q26”
    
3.  sign\_method = “sha256”
    
4.  simplify = “true”
    
5.  timestamp = “1517820392000”
    

3.拼接参数名与参数值

PlainBashC++C#CSSDiffHTML/XMLJavaJavascriptMarkdownPHPPythonRubySQL

app\_key12345678code3\_500102\_JxZ05Ux3cnnSSUm6dCxYg6Q26sign\_methodsha256simplifytruetimestamp1517820392000

4.添加API名称至拼接字符串开头

PlainBashC++C#CSSDiffHTML/XMLJavaJavascriptMarkdownPHPPythonRubySQL

/auth/token/createapp\_key12345678code3\_500102\_JxZ05Ux3cnnSSUm6dCxYg6Q26sign\_methodsha256simplifytruetimestamp1517820392000

5.生成签名

假设app的secret为helloworld，则签名结果为：

PlainBashC++C#CSSDiffHTML/XMLJavaJavascriptMarkdownPHPPythonRubySQL

hex(sha256(/auth/token/createapp\_key12345678code3\_500102\_JxZ05Ux3cnnSSUm6dCxYg6Q26sign\_methodsha256simplifytruetimestamp1517820392000))=0B409A354C6FB222EE2C8095002ACDB2D30566151D1A2361AEE7D2627B9BCBEA

6.组装HTTP请求

将所有参数名和参数值采用utf-8进行URL编码（参数顺序可随意，但必须要包括签名参数），然后通过GET或POST（含byte\[\]类型参数）发起请求，拼接方式为：https://open-api.alibaba.com/rest{api\_path}?{query}。如：

PlainBashC++C#CSSDiffHTML/XMLJavaJavascriptMarkdownPHPPythonRubySQL

http://open-api.alibaba.com/rest/auth/token/create?app\_key=12345678&code=3\_500102\_JxZ05Ux3cnnSSUm6dCxYg6Q26&timestamp=1517820392000&sign\_method=sha256&simplify=true&sign=0B409A354C6FB222EE2C8095002ACDB2D30566151D1A2361AEE7D2627B9BCBEA

注意事项

1.  所有的请求和响应数据编码皆为utf-8格式，URL里的所有参数名和参数值请做URL编码。如果请求的Content-Type是application/x-www-form-urlencoded，则HTTP Body体里的所有参数值也做URL编码；如果是multipart/form-data格式，每个表单字段的参数值无需编码,但每个表单字段的charset部分需要指定为utf-8。
    
2.  参数名与参数值拼装起来的URL长度小于1024个字符时，可以用GET发起请求；参数类型含byte\[\]类型或拼装好的请求URL过长时，必须用POST发起请求。所有API都可以用POST发起请求。
    
3.  生成签名（sign）仅对未使用国际站官方SDK进行API调用时需要操作，如使用了国际站官方SDK，该步骤SDK会自动完成。