<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试刷新状态功能</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>测试刷新状态功能</h1>
    
    <div>
        <h2>登录</h2>
        <button onclick="login()">登录</button>
        <div id="loginResult"></div>
    </div>
    
    <div>
        <h2>测试刷新状态</h2>
        <input type="number" id="articleId" placeholder="文章ID" value="22">
        <button onclick="refreshStatus()">刷新状态</button>
        <div id="refreshResult"></div>
    </div>
    
    <script>
        let token = '';
        
        // 配置axios
        axios.defaults.baseURL = 'http://localhost:5000/api';
        
        // 请求拦截器
        axios.interceptors.request.use(config => {
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
            }
            return config;
        });
        
        // 响应拦截器（模拟前端的处理逻辑）
        axios.interceptors.response.use(
            response => response,
            error => {
                console.error('响应错误:', error);
                
                let message = '网络错误';
                
                if (error.response) {
                    const { status, data } = error.response;
                    
                    switch (status) {
                        case 400:
                            message = data.detail || data.message || '请求参数错误';
                            break;
                        case 401:
                            message = '未授权，请重新登录';
                            break;
                        case 403:
                            message = '权限不足';
                            break;
                        case 404:
                            message = data.detail || data.message || '接口不存在';
                            break;
                        case 500:
                            message = data.detail || data.message || '服务器内部错误';
                            break;
                        default:
                            message = data.detail || data.message || `连接错误${status}`;
                    }
                }
                
                alert(`错误: ${message}`);
                return Promise.reject(error);
            }
        );
        
        async function login() {
            try {
                const formData = new FormData();
                formData.append('username', '<EMAIL>');
                formData.append('password', 'admin123');
                
                const response = await axios.post('/v1/auth/login/access-token', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                });
                
                token = response.data.access_token;
                document.getElementById('loginResult').innerHTML = `<p style="color: green;">登录成功！Token: ${token.substring(0, 20)}...</p>`;
            } catch (error) {
                document.getElementById('loginResult').innerHTML = `<p style="color: red;">登录失败: ${error.message}</p>`;
            }
        }
        
        async function refreshStatus() {
            if (!token) {
                alert('请先登录');
                return;
            }
            
            const articleId = document.getElementById('articleId').value;
            if (!articleId) {
                alert('请输入文章ID');
                return;
            }
            
            try {
                const response = await axios.post(`/v1/ai-article/${articleId}/refresh-status`, {});
                document.getElementById('refreshResult').innerHTML = `<p style="color: green;">刷新成功: ${JSON.stringify(response.data, null, 2)}</p>`;
            } catch (error) {
                document.getElementById('refreshResult').innerHTML = `<p style="color: red;">刷新失败: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html> 