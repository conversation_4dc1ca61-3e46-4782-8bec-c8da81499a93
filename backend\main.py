import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import asyncio
from app.api.api_v1.api import api_router
from app.core.config import settings
from app.db.session import engine, Base, SessionLocal
from app.db.init_db import init_db

# 设置日志级别
logging.basicConfig(level=getattr(logging, settings.LOG_LEVEL))
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# 添加CORS中间件 - 使用配置中的允许源
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,  # 使用配置中的允许源
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 全局队列工作者任务
queue_worker_task = None
scheduler_task = None
site_sync_scheduler_task = None

@app.on_event("startup")
async def startup_event():
    global queue_worker_task, scheduler_task, site_sync_scheduler_task
    logger.info("应用启动中...")
    # 创建数据库表
    Base.metadata.create_all(bind=engine)
    # 初始化数据库数据
    db = SessionLocal()
    try:
        init_db(db)
    finally:
        db.close()
    
    # 启动队列工作者（使用一个新的数据库连接）
    async def start_worker():
        db_worker = SessionLocal()
        try:
            from app.services.scheduled_publish_service import ScheduledPublishService
            service = ScheduledPublishService(db_worker)
            await service.start_queue_worker()
        finally:
            db_worker.close()
    
    queue_worker_task = asyncio.create_task(start_worker())
    logger.info("队列工作者已启动")
    
    # 启动定时任务调度器
    async def start_scheduler():
        db_scheduler = SessionLocal()
        try:
            from app.services.scheduled_publish_service import ScheduledPublishService
            service = ScheduledPublishService(db_scheduler)
            await service.start_scheduler()
        finally:
            db_scheduler.close()
    
    scheduler_task = asyncio.create_task(start_scheduler())
    logger.info("定时任务调度器已启动")
    
    # 启动站点同步调度器
    from app.services.site_sync_scheduler import start_site_sync_scheduler
    await start_site_sync_scheduler()
    logger.info("站点同步调度器已启动")
    
    logger.info("应用启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    global queue_worker_task, scheduler_task, site_sync_scheduler_task
    logger.info("应用关闭中...")
    
    # 停止队列工作者
    if queue_worker_task:
        queue_worker_task.cancel()
        try:
            await queue_worker_task
        except asyncio.CancelledError:
            logger.info("队列工作者已停止")
    
    # 停止定时任务调度器
    if scheduler_task:
        scheduler_task.cancel()
        try:
            await scheduler_task
        except asyncio.CancelledError:
            logger.info("定时任务调度器已停止")
    
    # 停止站点同步调度器
    from app.services.site_sync_scheduler import stop_site_sync_scheduler
    stop_site_sync_scheduler()
    logger.info("站点同步调度器已停止")
    
    logger.info("应用关闭完成")

@app.get("/")
def root():
    return {"message": f"欢迎使用{settings.PROJECT_NAME} API"}

@app.get("/health")
def health_check():
    return {"status": "healthy", "message": "服务运行正常"}

if __name__ == "__main__":
    logger.info(f"启动服务器...地址: {settings.BACKEND_HOST}:{settings.BACKEND_PORT}")
    uvicorn.run(
        "main:app", 
        host="0.0.0.0",  # 允许外部访问
        port=settings.BACKEND_PORT,
        reload=settings.BACKEND_RELOAD
    ) 