-- 为定时发布计划表添加选词策略相关字段
-- 添加日期：2024年

-- 添加关键词来源字段
ALTER TABLE `scheduled_publish_plans` 
ADD COLUMN `keyword_source` VARCHAR(20) NOT NULL DEFAULT 'custom' COMMENT '关键词来源：custom/library/strategy' AFTER `keyword_categories`;

-- 添加选词策略配置字段
ALTER TABLE `scheduled_publish_plans` 
ADD COLUMN `use_keyword_strategy` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否使用选词策略' AFTER `keyword_source`;

ALTER TABLE `scheduled_publish_plans` 
ADD COLUMN `strategy_intent` VARCHAR(100) NULL COMMENT '策略-关键词意图筛选' AFTER `use_keyword_strategy`;

ALTER TABLE `scheduled_publish_plans` 
ADD COLUMN `strategy_volume_min` BIGINT NULL COMMENT '策略-最小搜索量' AFTER `strategy_intent`;

ALTER TABLE `scheduled_publish_plans` 
ADD COLUMN `strategy_volume_max` BIGINT NULL COMMENT '策略-最大搜索量' AFTER `strategy_volume_min`;

ALTER TABLE `scheduled_publish_plans` 
ADD COLUMN `strategy_difficulty_min` INT NULL COMMENT '策略-最小难度(0-100)' AFTER `strategy_volume_max`;

ALTER TABLE `scheduled_publish_plans` 
ADD COLUMN `strategy_difficulty_max` INT NULL COMMENT '策略-最大难度(0-100)' AFTER `strategy_difficulty_min`;

ALTER TABLE `scheduled_publish_plans` 
ADD COLUMN `strategy_cpc_min` DECIMAL(10,2) NULL COMMENT '策略-最小CPC(USD)' AFTER `strategy_difficulty_max`;

ALTER TABLE `scheduled_publish_plans` 
ADD COLUMN `strategy_cpc_max` DECIMAL(10,2) NULL COMMENT '策略-最大CPC(USD)' AFTER `strategy_cpc_min`;

ALTER TABLE `scheduled_publish_plans` 
ADD COLUMN `strategy_competitive_density_min` DECIMAL(3,2) NULL COMMENT '策略-最小竞争密度(0-1)' AFTER `strategy_cpc_max`;

ALTER TABLE `scheduled_publish_plans` 
ADD COLUMN `strategy_competitive_density_max` DECIMAL(3,2) NULL COMMENT '策略-最大竞争密度(0-1)' AFTER `strategy_competitive_density_min`;

ALTER TABLE `scheduled_publish_plans` 
ADD COLUMN `strategy_countries` JSON NULL COMMENT '策略-国家列表' AFTER `strategy_competitive_density_max`;

ALTER TABLE `scheduled_publish_plans` 
ADD COLUMN `strategy_categories` JSON NULL COMMENT '策略-分类列表' AFTER `strategy_countries`;

-- 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'scheduled_publish_plans' 
    AND COLUMN_NAME LIKE 'strategy_%' OR COLUMN_NAME IN ('keyword_source', 'use_keyword_strategy')
ORDER BY ORDINAL_POSITION;

-- 检查表结构
DESCRIBE scheduled_publish_plans;
