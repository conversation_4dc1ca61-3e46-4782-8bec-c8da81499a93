from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, func
from datetime import date, datetime, timedelta
from decimal import Decimal
import logging

from app.models.alibaba_keyword import AlibabaKeywordPerformance, AlibabaKeywordSummary, AlibabaKeywordDatePeriod
from app.services.alibaba_service import AlibabaService
from app.utils.datetime_utils import utc_now, to_iso_string

logger = logging.getLogger(__name__)

class AlibabaKeywordServiceReal:
    """阿里巴巴关键词表现服务 - 真实API版本"""
    
    def __init__(self):
        pass

    def get_keyword_performance(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str,
        start_date: date,
        end_date: date,
        keyword: Optional[str] = None
    ) -> List[AlibabaKeywordPerformance]:
        """获取关键词表现数据"""
        try:
            query = db.query(AlibabaKeywordPerformance).filter(
                and_(
                    AlibabaKeywordPerformance.user_id == user_id,
                    AlibabaKeywordPerformance.tenant_id == tenant_id,
                    AlibabaKeywordPerformance.statistics_type == statistics_type,
                    AlibabaKeywordPerformance.start_date >= start_date,
                    AlibabaKeywordPerformance.end_date <= end_date
                )
            )
            
            if keyword:
                query = query.filter(AlibabaKeywordPerformance.keyword.like(f"%{keyword}%"))
                
            return query.order_by(desc(AlibabaKeywordPerformance.sum_show_cnt)).all()
        except Exception as e:
            logger.error(f"获取关键词表现数据失败: {e}")
            raise

    def get_date_periods(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str
    ) -> Dict[str, Any]:
        """获取可用数据周期"""
        try:
            periods = db.query(AlibabaKeywordDatePeriod).filter(
                and_(
                    AlibabaKeywordDatePeriod.user_id == user_id,
                    AlibabaKeywordDatePeriod.tenant_id == tenant_id,
                    AlibabaKeywordDatePeriod.period_type == statistics_type,
                    AlibabaKeywordDatePeriod.is_available == True
                )
            ).order_by(desc(AlibabaKeywordDatePeriod.start_date)).all()
            
            result_list = []
            for period in periods:
                result_list.append({
                    "start_date": period.start_date.isoformat(),
                    "end_date": period.end_date.isoformat(),
                    "is_current": period.is_current
                })
            
            return {"resultList": result_list}
        except Exception as e:
            logger.error(f"获取数据周期失败: {e}")
            raise

    def get_weekly_data(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        start_date: date,
        end_date: date,
        keyword: Optional[str] = None
    ) -> List[AlibabaKeywordPerformance]:
        """获取关键词周统计数据"""
        return self.get_keyword_performance(
            db, user_id, tenant_id, "week", start_date, end_date, keyword
        )

    def get_monthly_data(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        start_date: date,
        end_date: date,
        keyword: Optional[str] = None
    ) -> List[AlibabaKeywordPerformance]:
        """获取关键词月统计数据"""
        return self.get_keyword_performance(
            db, user_id, tenant_id, "month", start_date, end_date, keyword
        )

    def save_keyword_performance(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        performance_data: Dict[str, Any],
        keyword: str,
        statistics_type: str,
        start_date: date,
        end_date: date
    ) -> AlibabaKeywordPerformance:
        """保存关键词表现数据"""
        # 检查是否已存在
        existing = db.query(AlibabaKeywordPerformance).filter(
            and_(
                AlibabaKeywordPerformance.user_id == user_id,
                AlibabaKeywordPerformance.tenant_id == tenant_id,
                AlibabaKeywordPerformance.keyword == keyword,
                AlibabaKeywordPerformance.statistics_type == statistics_type,
                AlibabaKeywordPerformance.start_date == start_date,
                AlibabaKeywordPerformance.end_date == end_date
            )
        ).first()
        
        if existing:
            # 更新现有记录
            existing.sum_show_cnt = performance_data.get("sum_show_cnt", 0)
            existing.sum_click_cnt = performance_data.get("sum_click_cnt", 0)
            existing.ctr = Decimal(str(performance_data.get("ctr", 0)))
            existing.search_pv_index = performance_data.get("search_pv_index", 0)
            existing.gs_tp_member_set_cnt = performance_data.get("gs_tp_member_set_cnt", 0)
            existing.avg_sum_show_cnt = performance_data.get("avg_sum_show_cnt", 0)
            existing.avg_sum_click_cnt = performance_data.get("avg_sum_click_cnt", 0)
            existing.sum_p4p_show_cnt = performance_data.get("sum_p4p_show_cnt", 0)
            existing.sum_p4p_click_cnt = performance_data.get("sum_p4p_click_cnt", 0)
            existing.is_p4p_kw = performance_data.get("is_p4p_kw", False)
            existing.keywords_in_use = performance_data.get("keywords_in_use", False)
            existing.keywords_viewed = performance_data.get("keywords_viewed", True)
            existing.sync_time = utc_now()
            existing.data_source = "alibaba_api"  # 标记为真实API数据
            db_performance = existing
        else:
            # 创建新记录
            db_performance = AlibabaKeywordPerformance(
                user_id=user_id,
                tenant_id=tenant_id,
                keyword=keyword,
                statistics_type=statistics_type,
                start_date=start_date,
                end_date=end_date,
                sum_show_cnt=performance_data.get("sum_show_cnt", 0),
                sum_click_cnt=performance_data.get("sum_click_cnt", 0),
                ctr=Decimal(str(performance_data.get("ctr", 0))),
                search_pv_index=performance_data.get("search_pv_index", 0),
                gs_tp_member_set_cnt=performance_data.get("gs_tp_member_set_cnt", 0),
                avg_sum_show_cnt=performance_data.get("avg_sum_show_cnt", 0),
                avg_sum_click_cnt=performance_data.get("avg_sum_click_cnt", 0),
                sum_p4p_show_cnt=performance_data.get("sum_p4p_show_cnt", 0),
                sum_p4p_click_cnt=performance_data.get("sum_p4p_click_cnt", 0),
                is_p4p_kw=performance_data.get("is_p4p_kw", False),
                keywords_in_use=performance_data.get("keywords_in_use", False),
                keywords_viewed=performance_data.get("keywords_viewed", True),
                sync_time=utc_now(),
                data_source="alibaba_api"  # 标记为真实API数据
            )
            db.add(db_performance)
        
        db.commit()
        db.refresh(db_performance)
        return db_performance

    def save_date_period(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        period_type: str,
        start_date: date,
        end_date: date,
        is_current: bool = False
    ) -> AlibabaKeywordDatePeriod:
        """保存数据周期"""
        # 检查是否已存在
        existing = db.query(AlibabaKeywordDatePeriod).filter(
            and_(
                AlibabaKeywordDatePeriod.user_id == user_id,
                AlibabaKeywordDatePeriod.tenant_id == tenant_id,
                AlibabaKeywordDatePeriod.period_type == period_type,
                AlibabaKeywordDatePeriod.start_date == start_date,
                AlibabaKeywordDatePeriod.end_date == end_date
            )
        ).first()
        
        if existing:
            existing.is_current = is_current
            existing.last_updated = utc_now()
            db_period = existing
        else:
            db_period = AlibabaKeywordDatePeriod(
                user_id=user_id,
                tenant_id=tenant_id,
                period_type=period_type,
                start_date=start_date,
                end_date=end_date,
                is_available=True,
                is_current=is_current,
                last_updated=utc_now()
            )
            db.add(db_period)
        
        db.commit()
        db.refresh(db_period)
        return db_period

    async def sync_data_from_alibaba(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str,
        start_date: date,
        end_date: date,
        access_token: str = None
    ) -> Dict[str, int]:
        """从阿里国际站同步关键词表现数据 - 真实API版本"""
        try:
            if not access_token:
                raise Exception("访问令牌不能为空")
            
            # 初始化阿里巴巴服务
            alibaba_service = AlibabaService(db)
            
            # 1. 首先同步可用的数据周期
            logger.info("同步关键词数据周期...")
            try:
                periods_response = alibaba_service.get_keyword_date_periods(access_token)
                
                if periods_response.get("error_response"):
                    error_msg = periods_response["error_response"].get("msg", "获取数据周期失败")
                    logger.error(f"获取数据周期API错误: {error_msg}")
                else:
                    # 解析数据周期响应
                    if "alibaba_mydata_self_keyword_date_get_response" in periods_response:
                        result = periods_response["alibaba_mydata_self_keyword_date_get_response"].get("result", {})
                        periods_list = result.get("date_periods", [])
                        
                        for period in periods_list:
                            period_start = datetime.strptime(period.get("start_date"), "%Y-%m-%d").date()
                            period_end = datetime.strptime(period.get("end_date"), "%Y-%m-%d").date()
                            
                            self.save_date_period(
                                db, user_id, tenant_id, statistics_type,
                                period_start, period_end, period.get("is_current", False)
                            )
                        
                        logger.info(f"同步了 {len(periods_list)} 个数据周期")
                        
            except Exception as e:
                logger.error(f"同步数据周期失败: {e}")
                # 继续执行关键词数据同步
            
            # 2. 同步关键词表现数据
            saved_performance = []
            
            logger.info(f"开始同步关键词表现数据: 统计类型 {statistics_type}，时间范围 {start_date} 到 {end_date}")
            
            try:
                if statistics_type == "week":
                    # 调用周统计API
                    date_range = {
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat()
                    }
                    properties = {
                        "limit": 100,  # 每次获取100个关键词
                        "offset": 0,
                        "order_by_field": "sumShowCnt",
                        "order_by_mode": "desc"
                    }
                    
                    performance_response = alibaba_service.get_keyword_weekly_data(
                        access_token=access_token,
                        date_range=date_range,
                        properties=properties
                    )
                    
                elif statistics_type == "month":
                    # 调用月统计API
                    properties = {
                        "limit": 100,
                        "offset": 0,
                        "order_by_field": "sumShowCnt",
                        "order_by_mode": "desc",
                        "start_date": start_date.isoformat(),
                        "end_date": end_date.isoformat()
                    }
                    
                    performance_response = alibaba_service.get_keyword_monthly_data(
                        access_token=access_token,
                        properties=properties
                    )
                else:
                    raise Exception(f"不支持的统计类型: {statistics_type}")
                
                if performance_response.get("error_response"):
                    error_msg = performance_response["error_response"].get("msg", "获取关键词表现数据失败")
                    logger.error(f"获取关键词表现数据API错误: {error_msg}")
                    raise Exception(f"获取关键词表现数据失败: {error_msg}")
                
                # 解析关键词表现响应
                api_method = f"alibaba_mydata_self_keyword_effect_{statistics_type}_get_response"
                if api_method in performance_response:
                    result = performance_response[api_method].get("result", {})
                    effects_list = result.get("effects", [])
                    
                    for effect in effects_list:
                        keyword = effect.get("keyword", "")
                        if not keyword:
                            continue
                        
                        # 处理关键词表现数据
                        performance_data = {
                            "sum_show_cnt": effect.get("sum_show_cnt", 0),
                            "sum_click_cnt": effect.get("sum_click_cnt", 0),
                            "ctr": float(effect.get("ctr", 0)),
                            "search_pv_index": effect.get("search_pv_index", 0),
                            "gs_tp_member_set_cnt": effect.get("gs_tp_member_set_cnt", 0),
                            "avg_sum_show_cnt": effect.get("avg_sum_show_cnt", 0),
                            "avg_sum_click_cnt": effect.get("avg_sum_click_cnt", 0),
                            "sum_p4p_show_cnt": effect.get("sum_p4p_show_cnt", 0),
                            "sum_p4p_click_cnt": effect.get("sum_p4p_click_cnt", 0),
                            "is_p4p_kw": effect.get("is_p4p_kw", False),
                            "keywords_in_use": effect.get("keywords_in_use", False),
                            "keywords_viewed": effect.get("keywords_viewed", True)
                        }
                        
                        # 保存关键词表现数据
                        saved_perf = self.save_keyword_performance(
                            db, user_id, tenant_id, performance_data,
                            keyword, statistics_type, start_date, end_date
                        )
                        saved_performance.append(saved_perf)
                    
                    logger.info(f"同步了 {len(effects_list)} 个关键词的表现数据")
                
            except Exception as e:
                logger.error(f"同步关键词表现数据失败: {e}")
                raise
            
            logger.info(f"关键词表现数据同步完成: {len(saved_performance)} 条记录")
            
            return {
                "keywords": len(set([p.keyword for p in saved_performance])),
                "performance_records": len(saved_performance)
            }
            
        except Exception as e:
            logger.error(f"同步关键词表现数据失败: {e}")
            raise

    def calculate_summary(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str,
        start_date: date,
        end_date: date
    ) -> Dict[str, Any]:
        """计算关键词表现汇总数据"""
        performance_data = self.get_keyword_performance(
            db, user_id, tenant_id, statistics_type, start_date, end_date
        )
        
        if not performance_data:
            return {
                "total_keywords": 0,
                "active_keywords": 0,
                "p4p_keywords": 0,
                "total_show": 0,
                "total_click": 0,
                "avg_ctr": 0.0,
                "total_search_pv_index": 0,
                "total_p4p_show": 0,
                "total_p4p_click": 0,
                "p4p_ctr": 0.0
            }
        
        total_keywords = len(performance_data)
        active_keywords = len([p for p in performance_data if p.keywords_viewed])
        p4p_keywords = len([p for p in performance_data if p.is_p4p_kw])
        total_show = sum(p.sum_show_cnt for p in performance_data)
        total_click = sum(p.sum_click_cnt for p in performance_data)
        total_search_pv_index = sum(p.search_pv_index for p in performance_data)
        total_p4p_show = sum(p.sum_p4p_show_cnt for p in performance_data)
        total_p4p_click = sum(p.sum_p4p_click_cnt for p in performance_data)
        
        avg_ctr = (total_click / total_show * 100) if total_show > 0 else 0.0
        p4p_ctr = (total_p4p_click / total_p4p_show * 100) if total_p4p_show > 0 else 0.0
        
        return {
            "total_keywords": total_keywords,
            "active_keywords": active_keywords,
            "p4p_keywords": p4p_keywords,
            "total_show": total_show,
            "total_click": total_click,
            "avg_ctr": round(avg_ctr, 4),
            "total_search_pv_index": total_search_pv_index,
            "total_p4p_show": total_p4p_show,
            "total_p4p_click": total_p4p_click,
            "p4p_ctr": round(p4p_ctr, 4)
        } 