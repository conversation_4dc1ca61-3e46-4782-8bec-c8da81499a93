# 关键词库新字段更新说明

## 概述

根据您提供的关键词结构要求，已完成关键词库的字段更新，新增了以下字段以支持更完整的关键词信息管理：

## 新增字段

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `intent` | VARCHAR(100) | 关键词意图 | "Informational, Commercial" |
| `volume` | BIGINT | 搜索量 | 40500 |
| `trend` | TEXT | 趋势数据（12个月） | "[0.54,0.54,0.44,0.66,0.81,0.81,0.66,1.00,0.66,0.81,0.66,0.81]" |
| `keyword_difficulty` | INT | 关键词难度（0-100） | 49 |
| `cpc_usd` | DECIMAL(10,2) | 每次点击费用（美元） | 0.78 |
| `competitive_density` | INT | 竞争密度（0-1） | 1 |
| `serp_features` | TEXT | SERP特征（JSON数组） | '["Sitelinks", "Reviews", "Image", "Video"]' |
| `number_of_results` | BIGINT | 搜索结果数量 | 74200000 |

## 更新内容

### 1. 数据库模型更新
- **文件**: `backend/app/models/keyword_library.py`
- **更新**: 在KeywordLibrary模型中添加了新字段定义
- **兼容性**: 保留了原有字段以确保向后兼容

### 2. API Schema更新
- **文件**: `backend/app/schemas/keyword_library.py`
- **更新**: 
  - 在KeywordLibraryBase中添加新字段
  - 在KeywordLibraryUpdate中添加新字段
  - 在KeywordLibraryResponse中添加解析属性
  - 新增验证器用于解析JSON字段

### 3. 前端表单更新
- **文件**: `frontend/src/components/keyword/KeywordDialog.vue`
- **更新**:
  - 添加意图选择器（信息型、商业型、导航型、交易型）
  - 添加搜索量输入框
  - 添加趋势数据输入框
  - 添加关键词难度输入框
  - 添加CPC（美元）输入框
  - 添加竞争密度输入框
  - 添加SERP特征输入框
  - 添加搜索结果数输入框
  - 将原有字段移至"高级设置"折叠面板

### 4. 前端列表更新
- **文件**: `frontend/src/views/KeywordLibrary.vue`
- **更新**:
  - 添加意图列显示
  - 更新搜索量列（优先显示新的volume字段）
  - 添加趋势列显示
  - 添加关键词难度列
  - 添加CPC（美元）列
  - 添加竞争密度列
  - 新增辅助函数处理新字段显示

### 5. 前端详情页更新
- **文件**: `frontend/src/components/keyword/KeywordDetailDialog.vue`
- **更新**:
  - 重新组织信息展示结构
  - 添加关键词信息部分（意图、搜索量、难度、结果数）
  - 添加趋势信息部分（趋势数据可视化）
  - 添加SERP特征信息部分
  - 更新出价信息部分（添加CPC美元显示）
  - 保留原有兼容数据显示

### 6. 后端服务层更新
- **文件**: `backend/app/services/keyword_library_service.py`
- **更新**:
  - 更新统计信息生成，添加意图分布、难度分布、平均CPC统计
  - 更新文件导入功能，支持新字段的CSV/Excel导入
  - 更新Google Ads导入功能，将数据映射到新字段结构
  - 更新搜索功能，支持新字段的过滤和排序
  - 优化搜索量显示，优先使用新的volume字段

### 7. API接口更新
- **文件**: `backend/app/api/keyword_library.py`
- **更新**:
  - 更新文件导入接口文档，说明新字段格式
  - 保持所有现有API接口的向后兼容性

### 8. 搜索功能增强
- **新增搜索过滤条件**:
  - 意图过滤 (intent)
  - 搜索量范围 (min_volume, max_volume)
  - 关键词难度范围 (min_difficulty, max_difficulty)
  - CPC范围 (min_cpc, max_cpc)
  - 竞争密度过滤 (competitive_density)
- **新增排序字段**:
  - 按搜索量排序 (volume)
  - 按关键词难度排序 (keyword_difficulty)
  - 按CPC排序 (cpc_usd)
  - 按竞争密度排序 (competitive_density)
  - 按搜索结果数排序 (number_of_results)

## 数据库更新

### 执行SQL脚本
```sql
-- 执行字段添加脚本
source sql/keyword_library_update_new_fields.sql;

-- 可选：插入示例数据
source sql/keyword_library_sample_data.sql;
```

### 字段说明

#### Intent（意图）
- **Informational**: 信息型查询
- **Commercial**: 商业型查询  
- **Navigational**: 导航型查询
- **Transactional**: 交易型查询

#### Trend（趋势）
- 存储12个月的趋势值数组
- 格式：JSON数组，如 `[0.54,0.54,0.44,0.66,0.81,0.81,0.66,1.00,0.66,0.81,0.66,0.81]`
- 值范围：0-1，表示相对趋势强度

#### SERP Features（SERP特征）
- 存储搜索结果页面特征
- 格式：JSON数组，如 `["Sitelinks", "Reviews", "Image", "Video", "People also ask"]`
- 常见特征：Sitelinks, Reviews, Image, Video, People also ask, Related searches, Popular products, Things to know

## 前端界面优化

### 表单界面
- 新字段按重要性排序在主要区域显示
- 原有兼容字段移至"高级设置"折叠面板
- 添加字段说明和输入提示
- 优化表单布局和样式

### 列表界面
- 新增意图、趋势、难度、CPC等关键列
- 优化列宽和显示效果
- 添加趋势变化指示器
- 保持响应式设计

### 详情界面
- 重新组织信息展示结构
- 分组显示相关信息
- 添加趋势数据可视化
- 优化标签和特征显示

## 兼容性说明

- 保留所有原有字段，确保现有数据不受影响
- 新字段均为可选字段，不影响现有功能
- 搜索量显示优先使用新的`volume`字段，如果为空则回退到`avg_monthly_searches`
- API接口保持向后兼容

## 使用建议

1. **数据迁移**: 可以将现有的`avg_monthly_searches`数据复制到新的`volume`字段
2. **数据录入**: 建议使用新字段录入更完整的关键词信息
3. **趋势数据**: 趋势数据建议使用12个月的相对值（0-1范围）
4. **SERP特征**: 建议使用标准的SERP特征名称以保持一致性

## 示例数据

参考 `sql/keyword_library_sample_data.sql` 文件中的示例数据，了解如何正确填写各个字段。

## 导入模板

提供了CSV导入模板文件：
- `templates/keyword_import_template.csv` - 中文字段名模板
- `templates/keyword_import_template_en.csv` - 英文字段名模板

模板包含所有新字段和兼容字段，可以直接下载使用。

## 注意事项

- 执行数据库更新前请备份现有数据
- 新字段的索引已添加以优化查询性能
- 前端界面已适配新字段，无需额外配置
- 建议在测试环境先验证更新效果
