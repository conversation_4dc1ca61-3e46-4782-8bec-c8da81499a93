from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import date
from decimal import Decimal

# 数据周期相关
class AlibabaDataPeriodBase(BaseModel):
    start_date: date = Field(..., description="开始日期")
    end_date: date = Field(..., description="结束日期")

class AlibabaDataPeriodCreate(AlibabaDataPeriodBase):
    pass

class AlibabaDataPeriod(AlibabaDataPeriodBase):
    id: int
    tenant_id: str
    user_id: int
    is_available: bool

    class Config:
        from_attributes = True

# TOP行业列表相关
class AlibabaTopIndustryBase(BaseModel):
    industry_id: str = Field(..., description="行业ID")
    industry_desc: str = Field(..., description="行业描述")
    main_category: bool = Field(False, description="是否主营行业")

class AlibabaTopIndustryCreate(AlibabaTopIndustryBase):
    data_start_date: date = Field(..., description="数据开始日期")
    data_end_date: date = Field(..., description="数据结束日期")

class AlibabaTopIndustry(AlibabaTopIndustryBase):
    id: int
    tenant_id: str
    user_id: int
    data_start_date: date
    data_end_date: date

    class Config:
        from_attributes = True

# 询盘流量行业表现相关
class AlibabaInquiryPerformanceBase(BaseModel):
    industry_id: str = Field(..., description="行业ID")
    industry_desc: Optional[str] = Field(None, description="行业描述")
    main_category: bool = Field(False, description="是否主营行业")
    clk: int = Field(0, description="点击数")
    clk_rate: Optional[str] = Field(None, description="点击率")
    fb: int = Field(0, description="反馈（询盘）")
    imps: int = Field(0, description="曝光数")
    reply: Optional[str] = Field(None, description="最近30天询盘一次回复率")
    visitor: int = Field(0, description="访客数")

class AlibabaInquiryPerformanceCreate(AlibabaInquiryPerformanceBase):
    data_start_date: date = Field(..., description="数据开始日期")
    data_end_date: date = Field(..., description="数据结束日期")

class AlibabaInquiryPerformance(AlibabaInquiryPerformanceBase):
    id: int
    tenant_id: str
    user_id: int
    data_start_date: date
    data_end_date: date

    class Config:
        from_attributes = True

# 汇总统计相关
class AlibabaInquirySummaryBase(BaseModel):
    summary_date: date = Field(..., description="汇总日期")
    summary_type: str = Field(..., description="汇总类型")
    total_industries: int = Field(0, description="总行业数")
    main_industries: int = Field(0, description="主营行业数")
    total_clk: int = Field(0, description="总点击数")
    total_fb: int = Field(0, description="总询盘数")
    total_imps: int = Field(0, description="总曝光数")
    total_visitor: int = Field(0, description="总访客数")
    avg_clk_rate: Decimal = Field(Decimal("0.00"), description="平均点击率")
    avg_reply_rate: Decimal = Field(Decimal("0.00"), description="平均回复率")

class AlibabaInquirySummaryCreate(AlibabaInquirySummaryBase):
    pass

class AlibabaInquirySummary(AlibabaInquirySummaryBase):
    id: int
    tenant_id: str
    user_id: int

    class Config:
        from_attributes = True

# API请求/响应schemas
class InquiryStatisticsRequest(BaseModel):
    start_date: date = Field(..., description="查询开始日期")
    end_date: date = Field(..., description="查询结束日期")
    industry_id: Optional[str] = Field(None, description="特定行业ID")

class InquiryStatisticsResponse(BaseModel):
    data_periods: List[AlibabaDataPeriod] = Field([], description="可用数据周期")
    top_industries: List[AlibabaTopIndustry] = Field([], description="TOP行业列表")
    performance_data: List[AlibabaInquiryPerformance] = Field([], description="行业表现数据")
    summary: Optional[AlibabaInquirySummary] = Field(None, description="汇总统计")

class IndustryPerformanceDetail(BaseModel):
    """行业表现详情"""
    industry_info: AlibabaTopIndustry
    performance: AlibabaInquiryPerformance
    conversion_rate: float = Field(0.0, description="转化率 (询盘/点击)")
    engagement_rate: float = Field(0.0, description="参与率 (访客/曝光)")

class DashboardData(BaseModel):
    """仪表板数据"""
    summary: AlibabaInquirySummary
    top_performing_industries: List[IndustryPerformanceDetail] = Field([], description="表现最佳的行业")
    trend_data: List[AlibabaInquirySummary] = Field([], description="趋势数据")
    industry_distribution: dict = Field({}, description="行业分布")

# 批量操作相关
class BatchUpdateRequest(BaseModel):
    date_range: AlibabaDataPeriodBase
    force_update: bool = Field(False, description="是否强制更新") 