-- 检查和修复数据库中的枚举数据
USE cbec;

-- 检查 keyword_library 表中的 update_method 字段
SELECT DISTINCT update_method, COUNT(*) as count
FROM keyword_library 
GROUP BY update_method;

-- 检查 keyword_update_history 表中的 update_method 字段
SELECT DISTINCT update_method, COUNT(*) as count
FROM keyword_update_history 
GROUP BY update_method;

-- 检查 keyword_import_tasks 表中的 status 字段
SELECT DISTINCT status, COUNT(*) as count
FROM keyword_import_tasks 
GROUP BY status;

-- 如果有不匹配的数据，可以删除或更新
-- 删除所有记录以避免枚举冲突（根据需要选择使用）
-- DELETE FROM keyword_update_history;
-- DELETE FROM keyword_library;
-- DELETE FROM keyword_import_tasks;

-- 或者修复不匹配的枚举值（如果有的话）
-- UPDATE keyword_library SET update_method = 'manual' WHERE update_method = 'MANUAL';
-- UPDATE keyword_update_history SET update_method = 'manual' WHERE update_method = 'MANUAL'; 