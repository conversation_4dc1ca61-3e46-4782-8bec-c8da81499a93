{"timestamp": "2025-06-03T18:48:11.213645", "system_info": {"platform": "Windows-10-10.0.19045-SP0", "system": "Windows", "release": "10", "version": "10.0.19045", "machine": "AMD64", "processor": "Intel64 Family 6 Model 165 Stepping 3, GenuineIntel", "python_version": "3.13.3", "python_implementation": "CPython"}, "python_env": {"version": [3, 13, 3, "final", 0], "executable": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe", "path": ["D:\\AI\\AICBEC\\CBEC\\scripts", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages"], "pip_available": true, "venv_active": false, "venv_path": "D:\\AI\\AICBEC\\venv", "venv_python": "D:\\AI\\AICBEC\\venv\\Scripts\\python.exe", "packages": {}}, "node_env": {"node_available": true, "npm_available": true, "node_version": "v22.14.0", "npm_version": "10.9.2", "npm_path": "C:\\Program Files\\nodejs\\npm.cmd"}, "dependencies": {}, "issues": ["缺失 27 个后端依赖包", "前端依赖未安装"], "recommendations": ["建议激活虚拟环境: D:\\AI\\AICBEC\\venv"], "backend_deps": {"python_executable": "D:\\AI\\AICBEC\\venv\\Scripts\\python.exe", "requirements_file": "D:\\AI\\AICBEC\\CBEC\\backend\\requirements.txt", "total_packages": 27, "installed_packages": 0, "missing_packages": ["fastapi==0.103.1", "uvicorn==0.23.2", "sqlalchemy>=1.4.20,<2.0.0", "pydantic>=2.11.0", "python-jose==3.3.0", "passlib==1.7.4", "bcrypt==4.1.2", "alembic>=1.8.0,<2.0.0", "python-multipart==0.0.6", "pydantic-settings>=2.9.0", "pymysql==1.1.0", "requests==2.31.0", "httpx==0.25.0", "beautifulsoup4==4.12.2", "pandas>=2.1.4", "matplotlib>=3.8.2", "seaborn>=0.13.0", "google-ads>=24.0.0", "google-api-python-client>=2.100.0", "google-auth>=2.20.0", "google-auth-oauthlib>=1.0.0", "google-auth-httplib2>=0.1.0", "python-dateutil>=2.8.0", "openpyxl>=3.1.0", "xlsxwriter>=3.1.0", "pytrends>=4.9.2", "trendspy>=0.1.6"], "installed_list": []}}