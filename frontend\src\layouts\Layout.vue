<template>
  <div class="app-wrapper">
    <!-- 侧边栏遮罩 -->
    <div
      v-show="!isCollapse && isMobile"
      class="sidebar-mask"
      @click="closeSidebar"
    />

    <!-- 侧边栏 -->
    <aside
      :class="sidebarClasses"
      class="sidebar-container"
    >
      <!-- Logo区域 -->
      <div class="logo-container">
        <transition name="logo-fade">
          <div v-if="!isCollapse" class="logo-expanded">
            <img src="../assets/logo.svg" alt="Logo" class="logo-img">
            <h2 class="logo-text">CBEC系统</h2>
          </div>
          <div v-else class="logo-collapsed">
            <img src="../assets/logo.svg" alt="Logo" class="logo-img">
          </div>
        </transition>
      </div>

      <!-- 菜单区域 -->
      <div class="menu-container">
        <el-scrollbar class="menu-scrollbar">
          <div class="sidebar-menu" :class="{ 'is-collapse': isCollapse }">
            <SidebarItem
              v-for="route in routes"
              :key="route.path"
              :item="route"
              :base-path="route.path"
            />
          </div>
        </el-scrollbar>
      </div>

      <!-- 折叠按钮 -->
      <div class="sidebar-footer">
        <el-tooltip
          :content="isCollapse ? '展开菜单' : '收起菜单'"
          placement="right"
        >
          <button class="collapse-toggle" @click="toggleSideBar">
            <el-icon>
              <component :is="isCollapse ? 'Expand' : 'Fold'" />
            </el-icon>
          </button>
        </el-tooltip>
      </div>
    </aside>

    <!-- 主体内容区域 -->
    <div class="main-container" :class="{ 'is-collapse': isCollapse }">
      <!-- 顶部导航栏 -->
      <header class="header-container">
        <div class="header-left">
          <!-- 移动端菜单按钮 -->
          <button v-if="isMobile" class="mobile-menu-btn" @click="toggleSideBar">
            <el-icon>
              <Menu />
            </el-icon>
          </button>

          <!-- 面包屑导航 -->
          <Breadcrumb class="breadcrumb" />
        </div>

        <div class="header-right">
          <!-- 全屏按钮 -->
          <el-tooltip content="全屏" placement="bottom">
            <button class="header-btn" @click="toggleFullscreen">
              <el-icon>
                <FullScreen />
              </el-icon>
            </button>
          </el-tooltip>

          <!-- 主题切换 -->
          <el-tooltip content="主题切换" placement="bottom">
            <button class="header-btn" @click="toggleTheme">
              <el-icon>
                <component :is="isDark ? 'Sunny' : 'Moon'" />
              </el-icon>
            </button>
          </el-tooltip>

          <!-- 用户头像下拉菜单 -->
          <el-dropdown trigger="click" class="user-dropdown">
            <div class="user-info">
              <img :src="avatarUrl" class="user-avatar">
              <span v-if="!isMobile" class="username">{{ username }}</span>
              <el-icon class="dropdown-icon">
                <ArrowDown />
              </el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="goToProfile">
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item @click="goToSettings">
                  <el-icon><Setting /></el-icon>
                  系统设置
                </el-dropdown-item>
                <el-dropdown-item divided @click="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </header>

      <!-- 主要内容区域 -->
      <main class="content-container">
        <div class="content-wrapper">
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import { computed, onMounted, ref, onUnmounted } from 'vue'
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router'
import SidebarItem from '../components/SidebarItem.vue'
import Breadcrumb from '../components/Breadcrumb.vue'
import {
  Menu, Expand, Fold, FullScreen, Sunny, Moon,
  User, Setting, SwitchButton, ArrowDown
} from '@element-plus/icons-vue'

export default {
  name: 'Layout',
  components: {
    SidebarItem,
    Breadcrumb,
    Menu,
    Expand,
    Fold,
    FullScreen,
    Sunny,
    Moon,
    User,
    Setting,
    SwitchButton,
    ArrowDown
  },
  setup () {
    const store = useStore()
    const route = useRoute()
    const router = useRouter()

    // 响应式数据
    const isMobile = ref(false)
    const isDark = ref(false)
    const isFullscreen = ref(false)

    // 计算属性
    const isCollapse = computed(() => store.state.isCollapse)
    const username = computed(() => {
      const user = store.getters['user/currentUser']
      return user ? user.full_name || user.email : '未登录'
    })

    const sidebarClasses = computed(() => ({
      'is-collapse': isCollapse.value,
      'is-mobile': isMobile.value
    }))

    const routes = computed(() => {
      const mainRoute = router.options.routes.find(route => route.path === '/')
      const children = mainRoute ? mainRoute.children : []
      console.log('Menu routes:', children)
      return children
    })

    const activeMenu = computed(() => {
      const { path } = route
      return path
    })

    const avatarUrl = ref('https://cube.elemecdn.com/3/7c/********************************.png')

    // 方法
    const toggleSideBar = () => {
      store.dispatch('toggleSideBar')
    }

    const closeSidebar = () => {
      store.dispatch('closeSideBar')
    }

    const toggleFullscreen = () => {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
        isFullscreen.value = true
      } else {
        document.exitFullscreen()
        isFullscreen.value = false
      }
    }

    const toggleTheme = () => {
      isDark.value = !isDark.value
      document.documentElement.classList.toggle('dark', isDark.value)
      localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
    }

    const goToProfile = () => {
      router.push('/system/profile')
    }

    const goToSettings = () => {
      router.push('/system/settings')
    }

    const logout = async () => {
      try {
        await store.dispatch('auth/logout')
        router.push('/login')
      } catch (error) {
        console.error('退出登录失败', error)
      }
    }

    // 检测屏幕尺寸
    const checkMobile = () => {
      isMobile.value = window.innerWidth < 768
      if (isMobile.value && !isCollapse.value) {
        store.dispatch('closeSideBar')
      }
    }

    // 监听窗口大小变化
    const handleResize = () => {
      checkMobile()
    }

    // 初始化主题
    const initTheme = () => {
      const savedTheme = localStorage.getItem('theme')
      isDark.value = savedTheme === 'dark'
      document.documentElement.classList.toggle('dark', isDark.value)
    }

    onMounted(() => {
      checkMobile()
      initTheme()
      window.addEventListener('resize', handleResize)

      // 获取当前用户信息
      store.dispatch('user/fetchCurrentUser').catch(error => {
        console.error('获取用户信息失败', error)
      })

      // 获取当前租户信息
      store.dispatch('tenant/fetchCurrentTenant').catch(error => {
        console.error('获取租户信息失败', error)
      })
    })

    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
    })

    return {
      isCollapse,
      isMobile,
      isDark,
      username,
      sidebarClasses,
      toggleSideBar,
      closeSidebar,
      toggleFullscreen,
      toggleTheme,
      avatarUrl,
      routes,
      activeMenu,
      goToProfile,
      goToSettings,
      logout
    }
  }
}
</script>

<style scoped>
.app-wrapper {
  height: 100vh;
  width: 100%;
  display: flex;
  background: var(--el-bg-color-page);
}

/* 侧边栏样式 */
.sidebar-container {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 220px;
  background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.sidebar-container.is-collapse {
  width: 64px;
}

.sidebar-container.is-mobile {
  transform: translateX(-100%);
}

.sidebar-container.is-mobile:not(.is-collapse) {
  transform: translateX(0);
}

/* 侧边栏遮罩 */
.sidebar-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

/* Logo区域 */
.logo-container {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.logo-expanded {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-img {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.logo-text {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
}

/* Logo动画 */
.logo-fade-enter-active,
.logo-fade-leave-active {
  transition: all 0.3s ease;
}

.logo-fade-enter-from,
.logo-fade-leave-to {
  opacity: 0;
  transform: scale(0.8);
}

/* 菜单容器 */
.menu-container {
  flex: 1;
  overflow: hidden;
}

.menu-scrollbar {
  height: 100%;
}

/* 侧边栏菜单 */
.sidebar-menu {
  padding: 6px 0;
  background: transparent;
  width: 100%;
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.collapse-toggle {
  width: 100%;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapse-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

/* 主体容器 */
.main-container {
  flex: 1;
  margin-left: 220px;
  display: flex;
  flex-direction: column;
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--el-bg-color-page);
}

.main-container.is-collapse {
  margin-left: 64px;
}

/* 顶部导航栏 */
.header-container {
  height: 64px;
  background: #ffffff;
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mobile-menu-btn,
.header-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: transparent;
  border-radius: 8px;
  color: var(--el-text-color-regular);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-menu-btn:hover,
.header-btn:hover {
  background: var(--el-fill-color-light);
  color: var(--el-color-primary);
}

.breadcrumb {
  font-size: 14px;
}

/* 用户信息 */
.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.user-info:hover {
  background: var(--el-fill-color-light);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--el-border-color-lighter);
}

.username {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.dropdown-icon {
  color: var(--el-text-color-regular);
  transition: transform 0.3s ease;
}

/* 内容容器 */
.content-container {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: var(--el-bg-color-page);
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-container {
    margin-left: 0;
  }

  .main-container.is-collapse {
    margin-left: 0;
  }

  .header-container {
    padding: 0 16px;
  }

  .content-container {
    padding: 16px;
  }

  .username {
    display: none;
  }
}

/* 暗黑主题 */
:root.dark {
  --el-bg-color-page: #141414;
}

:root.dark .header-container {
  background: #1f1f1f;
  border-bottom-color: #333;
}

:root.dark .content-wrapper {
  background: #1f1f1f;
}
</style>
