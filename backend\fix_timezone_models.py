#!/usr/bin/env python3
"""
修复所有数据库模型的时区统一问题

将所有TIMESTAMP字段替换为DateTime(timezone=True)
将所有func.now()替换为utc_now()
"""

import os
import re

def fix_model_file(file_path):
    """修复单个模型文件"""
    print(f"修复文件: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 备份原文件
    backup_path = file_path + '.backup'
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    # 修复导入
    if 'from sqlalchemy.sql import func' in content:
        content = content.replace('from sqlalchemy.sql import func', '')
    
    if 'TIMESTAMP' in content:
        # 添加DateTime导入
        if 'from sqlalchemy.types import' in content:
            content = re.sub(
                r'from sqlalchemy\.types import ([^,\n]+)',
                r'from sqlalchemy.types import \1, DateTime',
                content
            )
        else:
            # 在第一个from sqlalchemy导入后添加
            content = re.sub(
                r'(from sqlalchemy import [^\n]+)',
                r'\1\nfrom sqlalchemy.types import DateTime',
                content,
                count=1
            )
    
    # 添加utc_now导入
    if 'from app.utils.datetime_utils import utc_now' not in content:
        # 在最后一个导入后添加
        lines = content.split('\n')
        import_end = 0
        for i, line in enumerate(lines):
            if line.startswith('from ') or line.startswith('import '):
                import_end = i
        
        lines.insert(import_end + 1, 'from app.utils.datetime_utils import utc_now')
        content = '\n'.join(lines)
    
    # 替换TIMESTAMP为DateTime(timezone=True)
    content = re.sub(r'Column\(TIMESTAMP,', 'Column(DateTime(timezone=True),', content)
    content = re.sub(r'Column\(TIMESTAMP\)', 'Column(DateTime(timezone=True))', content)
    
    # 替换func.now()为utc_now
    content = re.sub(r'server_default=func\.now\(\)', 'default=utc_now', content)
    content = re.sub(r'onupdate=func\.now\(\)', 'onupdate=utc_now', content)
    content = re.sub(r'default=func\.now\(\)', 'default=utc_now', content)
    
    # 确保DateTime字段都有timezone=True
    content = re.sub(r'Column\(DateTime,', 'Column(DateTime(timezone=True),', content)
    content = re.sub(r'Column\(DateTime\)', 'Column(DateTime(timezone=True))', content)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ 修复完成: {file_path}")

def main():
    """主函数"""
    models_dir = 'app/models'
    
    # 需要修复的模型文件
    model_files = [
        'keyword_library.py',
        'alibaba_keyword.py',
        'alibaba_auth.py',
        'alibaba_inquiry.py',
        'alibaba_product.py',
        'ai_article.py',
        'ai_config.py',
        'tenant.py',
        'role.py',
        'subscription.py'
    ]
    
    for model_file in model_files:
        file_path = os.path.join(models_dir, model_file)
        if os.path.exists(file_path):
            try:
                fix_model_file(file_path)
            except Exception as e:
                print(f"❌ 修复失败 {file_path}: {e}")
        else:
            print(f"⚠️  文件不存在: {file_path}")
    
    print("\n🎉 所有模型文件修复完成！")

if __name__ == "__main__":
    main()
