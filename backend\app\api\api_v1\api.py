from fastapi import APIRouter
from app.api.api_v1.endpoints import auth, users, tenants, alibaba, alibaba_inquiry, product_performance, keyword_performance, ai_config, ai_article, wordpress_site, scheduled_publish  # , roles, subscriptions
from app.api import keyword_library
# from app.api.api_v1.endpoints import data_crawl, ai_cluster, ai_operation, ai_report

api_router = APIRouter()

# 认证相关路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])

# 第三方平台集成路由
api_router.include_router(alibaba.router, prefix="/alibaba", tags=["阿里国际站"])
api_router.include_router(alibaba_inquiry.router, prefix="/alibaba-inquiry", tags=["阿里询盘统计"])

# 阿里国际站报表相关路由
api_router.include_router(product_performance.router, prefix="/alibaba-product", tags=["产品表现"])
api_router.include_router(keyword_performance.router, prefix="/alibaba-keyword", tags=["关键词表现"])

# 关键词库管理路由
api_router.include_router(keyword_library.router, tags=["关键词库"])

# 系统管理路由
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(tenants.router, prefix="/tenants", tags=["租户管理"])
# api_router.include_router(roles.router, prefix="/roles", tags=["角色管理"])
# api_router.include_router(subscriptions.router, prefix="/subscriptions", tags=["订阅管理"])

# AI管理路由
api_router.include_router(ai_config.router, prefix="/ai-config", tags=["AI配置管理"])
api_router.include_router(ai_article.router, prefix="/ai-article", tags=["AI文章管理"])
api_router.include_router(wordpress_site.router, prefix="/wordpress-site", tags=["WordPress站点管理"])

# 定时发布管理路由
api_router.include_router(scheduled_publish.router, prefix="/scheduled-publish", tags=["定时发布管理"])

# 业务功能路由
# api_router.include_router(data_crawl.router, prefix="/data-crawl", tags=["数据采集"])
# api_router.include_router(ai_cluster.router, prefix="/ai-cluster", tags=["AI站群"])
# api_router.include_router(ai_operation.router, prefix="/ai-operation", tags=["AI运营"])
# api_router.include_router(ai_report.router, prefix="/ai-report", tags=["AI报表"]) 