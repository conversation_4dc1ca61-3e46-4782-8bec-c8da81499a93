from sqlalchemy import Column, Integer, String, Boolean, Text, DateTime, Float

from .base import Base
from app.utils.datetime_utils import utc_now


class AIConfig(Base):
    """AI配置模型"""
    __tablename__ = "ai_configs"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="配置名称")
    service_type = Column(String(50), nullable=False, comment="服务类型，如DIFY")
    api_key = Column(String(255), nullable=False, comment="API密钥")
    base_url = Column(String(255), nullable=True, comment="API基础URL")
    model_name = Column(String(100), nullable=True, comment="模型名称")
    max_tokens = Column(Integer, default=4000, comment="最大token数")
    temperature = Column(Float, default=0.7, comment="创造性参数")
    is_active = Column(Boolean, default=True, comment="是否启用")
    description = Column(Text, nullable=True, comment="配置描述")
    created_at = Column(DateTime(timezone=True), default=utc_now, comment="创建时间")
    updated_at = Column(DateTime(timezone=True), default=utc_now, onupdate=utc_now, comment="更新时间") 