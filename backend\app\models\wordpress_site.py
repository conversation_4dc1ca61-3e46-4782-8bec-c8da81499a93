from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, BigInteger, JSON
from .base import Base
from app.utils.datetime_utils import utc_now


class WordPressSite(Base):
    """WordPress站点模型"""
    __tablename__ = "wordpress_sites"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, comment="站点名称")
    url = Column(String(255), nullable=False, comment="站点URL")
    category = Column(String(100), nullable=True, comment="站点分类")
    
    # WordPress REST API认证信息
    wp_username = Column(String(255), nullable=False, comment="WordPress用户名")
    wp_app_password = Column(String(255), nullable=False, comment="WordPress应用程序密码")
    
    # WordPress博客分类和标签信息
    blog_categories = Column(JSON, nullable=True, comment="WordPress博客分类信息(JSON格式)")
    blog_tags = Column(JSON, nullable=True, comment="WordPress博客标签信息(JSON格式)")
    
    # 站点统计数据
    daily_uv = Column(BigInteger, default=0, comment="当日UV(独立访客)")
    daily_pv = Column(BigInteger, default=0, comment="当日PV(页面浏览量)")
    monthly_uv = Column(BigInteger, default=0, comment="当月UV")
    monthly_pv = Column(BigInteger, default=0, comment="当月PV")
    total_posts = Column(Integer, default=0, comment="总文章数")
    total_pages = Column(Integer, default=0, comment="总页面数")
    
    # 站点状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    syncing = Column(Boolean, default=False, comment="是否正在同步")
    last_sync_at = Column(DateTime(timezone=True), nullable=True, comment="最后同步时间")
    sync_status = Column(String(50), default="pending", comment="同步状态")
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 站点信息
    description = Column(Text, nullable=True, comment="站点描述")
    wordpress_version = Column(String(50), nullable=True, comment="WordPress版本")
    theme_name = Column(String(255), nullable=True, comment="主题名称")
    language = Column(String(10), nullable=True, comment="站点语言")
    timezone = Column(String(50), nullable=True, comment="时区")
    
    # 时间戳 - 使用UTC时间统一方案
    created_at = Column(DateTime(timezone=True), nullable=False, default=utc_now, comment="创建时间")
    updated_at = Column(DateTime(timezone=True), nullable=True, onupdate=utc_now, comment="更新时间")