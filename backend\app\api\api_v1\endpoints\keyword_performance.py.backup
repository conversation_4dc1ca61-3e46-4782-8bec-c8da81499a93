from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import date, datetime, timedelta
import logging

from app.api import deps
from app.models.user import User
from app.models.alibaba_auth import <PERSON><PERSON>ba<PERSON><PERSON>
from app.core.config import settings

# 根据配置选择使用真实API还是模拟数据
if settings.ALIBABA_USE_REAL_API:
    from app.services.alibaba_keyword_service_real import AlibabaKeywordServiceReal
    keyword_service = AlibabaKeywordServiceReal()
    logger = logging.getLogger(__name__)
    logger.info("关键词表现API使用真实阿里巴巴API服务")
else:
    from app.services.alibaba_keyword_service import AlibabaKeywordService
    keyword_service = AlibabaKeywordService()
    logger = logging.getLogger(__name__)
    logger.info("关键词表现API使用模拟数据服务")

router = APIRouter()

@router.get("/date-periods")
def get_keyword_date_periods(
    statistics_type: str = Query(..., description="统计周期类型：week/month"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取关键词数据的可用时间周期
    """
    try:
        date_periods = keyword_service.get_date_periods(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            statistics_type=statistics_type
        )
        
        return {
            "code": 200,
            "data": date_periods,
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取关键词时间周期失败: {e}")
        raise HTTPException(status_code=500, detail="获取关键词时间周期失败")

@router.get("/weekly")
def get_keyword_weekly_data(
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    keyword: Optional[str] = Query(None, description="关键词搜索"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取关键词周统计数据
    """
    try:
        weekly_data = keyword_service.get_weekly_data(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            start_date=start_date,
            end_date=end_date,
            keyword=keyword
        )
        
        return {
            "code": 200,
            "data": weekly_data,
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取关键词周统计数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取关键词周统计数据失败")

@router.get("/monthly")
def get_keyword_monthly_data(
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    keyword: Optional[str] = Query(None, description="关键词搜索"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取关键词月统计数据
    """
    try:
        monthly_data = keyword_service.get_monthly_data(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            start_date=start_date,
            end_date=end_date,
            keyword=keyword
        )
        
        return {
            "code": 200,
            "data": monthly_data,
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取关键词月统计数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取关键词月统计数据失败")

@router.get("/summary")
def get_keyword_summary(
    statistics_type: str = Query(..., description="统计周期类型：week/month"),
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取关键词表现汇总数据
    """
    try:
        summary_data = keyword_service.calculate_summary(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            statistics_type=statistics_type,
            start_date=start_date,
            end_date=end_date
        )
        
        return {
            "code": 200,
            "data": summary_data,
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取关键词汇总数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取关键词汇总数据失败")

@router.post("/sync")
async def sync_keyword_data(
    statistics_type: str = Query(..., description="统计周期类型：week/month"),
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    force_update: bool = Query(False, description="是否强制更新"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    从阿里国际站同步关键词表现数据
    """
    try:
        # 检查用户是否有阿里国际站授权
        auth_record = db.query(AlibabaAuth).filter(
            AlibabaAuth.user_id == current_user.id,
            AlibabaAuth.tenant_id == current_user.tenant_id,
            AlibabaAuth.is_active == True
        ).first()
        
        if not auth_record:
            raise HTTPException(
                status_code=400, 
                detail="请先完成阿里国际站授权"
            )
        
        # 检查token是否过期
        token_expires_at = auth_record.token_created_at + timedelta(seconds=auth_record.expires_in)
        if datetime.now() > token_expires_at:
            raise HTTPException(
                status_code=400,
                detail="阿里国际站访问令牌已过期，请重新授权"
            )
        
        # 同步数据
        result = await keyword_service.sync_data_from_alibaba(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            statistics_type=statistics_type,
            start_date=start_date,
            end_date=end_date,
            access_token=auth_record.access_token
        )
        
        logger.info(f"用户 {current_user.id} 同步关键词表现数据成功: {result}")
        
        return {
            "code": 200,
            "data": result,
            "message": "数据同步成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步关键词表现数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"同步数据失败: {str(e)}") 