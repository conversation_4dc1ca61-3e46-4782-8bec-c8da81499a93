import logging
import secrets
import urllib.parse
from typing import Dict, Optional, Tuple
from google_auth_oauthlib.flow import Flow
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
import tempfile
import os
import json

logger = logging.getLogger(__name__)

class GoogleOAuthService:
    """Google OAuth2 授权服务"""
    
    # Google Ads API 所需的权限范围
    SCOPES = ['https://www.googleapis.com/auth/adwords']
    
    def __init__(self, client_id: str, client_secret: str, redirect_uri: str = None):
        """
        初始化OAuth服务
        
        Args:
            client_id: Google OAuth2客户端ID
            client_secret: Google OAuth2客户端密钥
            redirect_uri: 重定向URI，默认为本地回调
        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri or "http://localhost:8080/oauth/callback"
        
    def generate_authorization_url(self, state: str = None) -> <PERSON><PERSON>[str, str]:
        """
        生成授权URL
        
        Args:
            state: 状态参数，用于防止CSRF攻击
            
        Returns:
            Tuple[授权URL, state参数]
        """
        try:
            # 如果没有提供state，生成一个随机state
            if not state:
                state = secrets.token_urlsafe(32)
            
            # 创建临时配置文件
            client_config = {
                "web": {
                    "client_id": self.client_id,
                    "client_secret": self.client_secret,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": [self.redirect_uri]
                }
            }
            
            # 创建Flow对象
            flow = Flow.from_client_config(
                client_config=client_config,
                scopes=self.SCOPES
            )
            flow.redirect_uri = self.redirect_uri
            
            # 生成授权URL
            authorization_url, _ = flow.authorization_url(
                access_type='offline',  # 获取刷新令牌
                include_granted_scopes='true',
                state=state,
                prompt='consent'  # 强制显示同意界面以获取刷新令牌
            )
            
            logger.info(f"生成授权URL成功: {authorization_url}")
            return authorization_url, state
            
        except Exception as e:
            logger.error(f"生成授权URL失败: {e}")
            raise
    
    def exchange_code_for_tokens(self, authorization_code: str, state: str = None) -> Dict[str, str]:
        """
        使用授权码换取访问令牌和刷新令牌
        
        Args:
            authorization_code: 授权码
            state: 状态参数（可选）
            
        Returns:
            包含access_token和refresh_token的字典
        """
        try:
            # 创建临时配置文件
            client_config = {
                "web": {
                    "client_id": self.client_id,
                    "client_secret": self.client_secret,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": [self.redirect_uri]
                }
            }
            
            # 创建Flow对象
            flow = Flow.from_client_config(
                client_config=client_config,
                scopes=self.SCOPES
            )
            flow.redirect_uri = self.redirect_uri
            
            # 使用授权码获取令牌
            flow.fetch_token(code=authorization_code)
            
            # 获取凭证
            credentials = flow.credentials
            
            # 验证刷新令牌是否存在
            if not credentials.refresh_token:
                logger.warning("未获取到刷新令牌，可能需要重新授权")
                # 尝试强制重新授权
                raise ValueError("未获取到刷新令牌，请重新授权并同意所有权限")
            
            tokens = {
                "access_token": credentials.token,
                "refresh_token": credentials.refresh_token,
                "token_uri": credentials.token_uri,
                "client_id": credentials.client_id,
                "client_secret": credentials.client_secret,
                "scopes": credentials.scopes,
                "expiry": credentials.expiry.isoformat() if credentials.expiry else None
            }
            
            logger.info("令牌交换成功")
            return tokens
            
        except Exception as e:
            logger.error(f"令牌交换失败: {e}")
            raise
    
    def refresh_access_token(self, refresh_token: str) -> Dict[str, str]:
        """
        使用刷新令牌获取新的访问令牌
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            新的令牌信息
        """
        try:
            # 创建凭证对象
            credentials = Credentials(
                token=None,  # 访问令牌为空，将会自动刷新
                refresh_token=refresh_token,
                token_uri="https://oauth2.googleapis.com/token",
                client_id=self.client_id,
                client_secret=self.client_secret,
                scopes=self.SCOPES
            )
            
            # 刷新令牌
            request = Request()
            credentials.refresh(request)
            
            tokens = {
                "access_token": credentials.token,
                "refresh_token": credentials.refresh_token,  # 刷新令牌通常不会改变
                "token_uri": credentials.token_uri,
                "client_id": credentials.client_id,
                "client_secret": credentials.client_secret,
                "scopes": credentials.scopes,
                "expiry": credentials.expiry.isoformat() if credentials.expiry else None
            }
            
            logger.info("访问令牌刷新成功")
            return tokens
            
        except Exception as e:
            logger.error(f"访问令牌刷新失败: {e}")
            raise
    
    def validate_tokens(self, access_token: str, refresh_token: str) -> bool:
        """
        验证令牌是否有效
        
        Args:
            access_token: 访问令牌
            refresh_token: 刷新令牌
            
        Returns:
            是否有效
        """
        try:
            # 创建凭证对象
            credentials = Credentials(
                token=access_token,
                refresh_token=refresh_token,
                token_uri="https://oauth2.googleapis.com/token",
                client_id=self.client_id,
                client_secret=self.client_secret,
                scopes=self.SCOPES
            )
            
            # 检查令牌是否过期，如果过期则尝试刷新
            if credentials.expired:
                request = Request()
                credentials.refresh(request)
            
            # 如果能成功执行到这里，说明令牌有效
            logger.info("令牌验证成功")
            return True
            
        except Exception as e:
            logger.error(f"令牌验证失败: {e}")
            return False 