from typing import Optional
from pydantic import BaseModel, ConfigDict

# 共享属性
class UserBase(BaseModel):
    email: Optional[str] = None  # 临时改为 str
    full_name: Optional[str] = None
    is_active: Optional[bool] = True
    is_superuser: Optional[bool] = False

class UserCreate(UserBase):
    """创建用户时的数据模型"""
    email: str  # 临时改为 str
    password: str
    tenant_id: str
    role_id: Optional[int] = None

class UserUpdate(UserBase):
    """更新用户时的数据模型"""
    password: Optional[str] = None
    role_id: Optional[int] = None

class UserInDBBase(UserBase):
    """数据库中存储的用户数据模型"""
    id: Optional[int] = None
    tenant_id: str
    role_id: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)

# API中返回的用户模型
class User(UserInDBBase):
    pass

# 数据库中存储的用户模型，包含敏感字段
class UserInDB(UserInDBBase):
    hashed_password: str 