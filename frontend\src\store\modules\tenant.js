import axios from 'axios'

const state = {
  tenants: [],
  currentTenant: null,
  status: '',
  error: null
}

const getters = {
  tenants: state => state.tenants,
  currentTenant: state => state.currentTenant,
  tenantStatus: state => state.status,
  tenantError: state => state.error
}

const actions = {
  // 获取当前租户信息
  fetchCurrentTenant ({ commit, rootGetters }) {
    return new Promise((resolve, reject) => {
      const currentUser = rootGetters['user/currentUser']
      if (!currentUser) {
        const error = '无法获取当前租户：用户未登录'
        commit('tenant_error', error)
        reject(new Error(error))
        return
      }

      commit('tenant_request')
      axios.get(`/api/v1/tenants/${currentUser.tenant_id}`)
        .then(resp => {
          const tenant = resp.data
          commit('set_current_tenant', tenant)
          resolve(tenant)
        })
        .catch(err => {
          commit('tenant_error', err.response ? err.response.data : err.message)
          reject(err)
        })
    })
  },

  // 获取所有租户
  fetchTenants ({ commit }) {
    return new Promise((resolve, reject) => {
      commit('tenant_request')
      axios.get('/api/v1/tenants/')
        .then(resp => {
          const tenants = resp.data
          commit('set_tenants', tenants)
          resolve(tenants)
        })
        .catch(err => {
          commit('tenant_error', err.response ? err.response.data : err.message)
          reject(err)
        })
    })
  },

  // 创建新租户
  createTenant ({ commit }, tenantData) {
    return new Promise((resolve, reject) => {
      commit('tenant_request')
      axios.post('/api/v1/tenants/', tenantData)
        .then(resp => {
          commit('add_tenant', resp.data)
          resolve(resp.data)
        })
        .catch(err => {
          commit('tenant_error', err.response ? err.response.data : err.message)
          reject(err)
        })
    })
  },

  // 更新租户
  updateTenant ({ commit }, { id, data }) {
    return new Promise((resolve, reject) => {
      commit('tenant_request')
      axios.put(`/api/v1/tenants/${id}`, data)
        .then(resp => {
          commit('update_tenant', resp.data)
          resolve(resp.data)
        })
        .catch(err => {
          commit('tenant_error', err.response ? err.response.data : err.message)
          reject(err)
        })
    })
  },

  // 删除租户
  deleteTenant ({ commit }, id) {
    return new Promise((resolve, reject) => {
      commit('tenant_request')
      axios.delete(`/api/v1/tenants/${id}`)
        .then(resp => {
          commit('remove_tenant', id)
          resolve(resp.data)
        })
        .catch(err => {
          commit('tenant_error', err.response ? err.response.data : err.message)
          reject(err)
        })
    })
  }
}

const mutations = {
  tenant_request (state) {
    state.status = 'loading'
    state.error = null
  },
  set_tenants (state, tenants) {
    state.status = 'success'
    state.tenants = tenants
    state.error = null
  },
  set_current_tenant (state, tenant) {
    state.status = 'success'
    state.currentTenant = tenant
    state.error = null
  },
  add_tenant (state, tenant) {
    state.status = 'success'
    state.tenants.push(tenant)
    state.error = null
  },
  update_tenant (state, tenant) {
    state.status = 'success'
    const index = state.tenants.findIndex(t => t.id === tenant.id)
    if (index !== -1) {
      state.tenants.splice(index, 1, tenant)
    }
    // 如果更新的是当前租户，也更新currentTenant
    if (state.currentTenant && state.currentTenant.id === tenant.id) {
      state.currentTenant = tenant
    }
    state.error = null
  },
  remove_tenant (state, id) {
    state.status = 'success'
    state.tenants = state.tenants.filter(tenant => tenant.id !== id)
    state.error = null
  },
  tenant_error (state, error) {
    state.status = 'error'
    state.error = error
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
