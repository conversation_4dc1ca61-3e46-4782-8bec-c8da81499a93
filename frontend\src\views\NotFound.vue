<template>
  <div class="not-found-container">
    <div class="content">
      <h1>404</h1>
      <h2>页面不存在</h2>
      <p>抱歉，您访问的页面不存在或已被移除。</p>
      <el-button type="primary" @click="goBack">返回首页</el-button>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue'
import { useRouter } from 'vue-router'

export default defineComponent({
  name: 'NotFound',
  setup () {
    const router = useRouter()

    const goBack = () => {
      router.push('/')
    }

    return {
      goBack
    }
  }
})
</script>

<style lang="scss" scoped>
.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-color: #f0f2f5;

  .content {
    text-align: center;

    h1 {
      font-size: 120px;
      margin: 0;
      color: #409EFF;
    }

    h2 {
      font-size: 30px;
      margin: 0;
      margin-bottom: 20px;
      color: #303133;
    }

    p {
      font-size: 16px;
      color: #606266;
      margin-bottom: 30px;
    }
  }
}
</style>
