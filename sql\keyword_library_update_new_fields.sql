-- 关键词库表新字段更新脚本
-- 添加新的关键词数据字段以支持更完整的关键词信息

-- 添加意图字段
ALTER TABLE `keyword_library` 
ADD COLUMN `intent` VARCHAR(100) NULL COMMENT '关键词意图（Informational, Commercial等）' AFTER `keyword_name`;

-- 添加搜索量字段（新版本）
ALTER TABLE `keyword_library` 
ADD COLUMN `volume` BIGINT NULL COMMENT '搜索量' AFTER `intent`;

-- 添加趋势数据字段
ALTER TABLE `keyword_library` 
ADD COLUMN `trend` TEXT NULL COMMENT '趋势数据（12个月的趋势值数组，JSON格式）' AFTER `volume`;

-- 添加关键词难度字段
ALTER TABLE `keyword_library` 
ADD COLUMN `keyword_difficulty` INT NULL COMMENT '关键词难度（0-100）' AFTER `trend`;

-- 添加CPC美元字段
ALTER TABLE `keyword_library` 
ADD COLUMN `cpc_usd` DECIMAL(10,2) NULL COMMENT '每次点击费用（美元）' AFTER `keyword_difficulty`;

-- 添加竞争密度字段
ALTER TABLE `keyword_library` 
ADD COLUMN `competitive_density` INT NULL COMMENT '竞争密度（0-1）' AFTER `cpc_usd`;

-- 添加SERP特征字段
ALTER TABLE `keyword_library` 
ADD COLUMN `serp_features` TEXT NULL COMMENT 'SERP特征（JSON数组格式）' AFTER `competitive_density`;

-- 添加搜索结果数量字段
ALTER TABLE `keyword_library` 
ADD COLUMN `number_of_results` BIGINT NULL COMMENT '搜索结果数量' AFTER `serp_features`;

-- 添加索引以提高查询性能
ALTER TABLE `keyword_library` 
ADD INDEX `idx_volume` (`volume`),
ADD INDEX `idx_keyword_difficulty` (`keyword_difficulty`),
ADD INDEX `idx_cpc_usd` (`cpc_usd`),
ADD INDEX `idx_intent` (`intent`);

-- 更新现有数据：将avg_monthly_searches的值复制到volume字段（如果需要的话）
-- UPDATE `keyword_library` SET `volume` = `avg_monthly_searches` WHERE `volume` IS NULL AND `avg_monthly_searches` IS NOT NULL;

-- 验证更新结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'keyword_library' 
    AND COLUMN_NAME IN ('intent', 'volume', 'trend', 'keyword_difficulty', 'cpc_usd', 'competitive_density', 'serp_features', 'number_of_results')
ORDER BY ORDINAL_POSITION;
