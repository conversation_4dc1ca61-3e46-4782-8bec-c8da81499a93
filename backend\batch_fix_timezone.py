#!/usr/bin/env python3
"""
批量修复时区统一问题

自动修复所有发现的时区相关问题
"""

import os
import re
import glob

def fix_backend_file(file_path):
    """修复后端文件的时区问题"""
    print(f"修复: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 1. 替换已废弃的datetime.utcnow()
        if 'datetime.utcnow()' in content:
            # 添加导入
            if 'from app.utils.datetime_utils import' not in content:
                # 找到最后一个import行
                lines = content.split('\n')
                import_end = 0
                for i, line in enumerate(lines):
                    if line.startswith('from ') or line.startswith('import '):
                        import_end = i
                lines.insert(import_end + 1, 'from app.utils.datetime_utils import utc_now, to_iso_string')
                content = '\n'.join(lines)
            
            # 替换调用
            content = content.replace('datetime.utcnow()', 'utc_now()')
        
        # 2. 替换datetime.now()
        if 'datetime.now()' in content and 'utc_now' not in content:
            # 添加导入
            if 'from app.utils.datetime_utils import' not in content:
                lines = content.split('\n')
                import_end = 0
                for i, line in enumerate(lines):
                    if line.startswith('from ') or line.startswith('import '):
                        import_end = i
                lines.insert(import_end + 1, 'from app.utils.datetime_utils import utc_now, to_iso_string')
                content = '\n'.join(lines)
            
            # 替换调用
            content = content.replace('datetime.now()', 'utc_now()')
        
        # 3. 修复DateTime字段
        if 'Column(DateTime,' in content:
            content = content.replace('Column(DateTime,', 'Column(DateTime(timezone=True),')
        
        # 4. 替换func.now()
        if 'func.now()' in content:
            # 添加导入
            if 'from app.utils.datetime_utils import' not in content:
                lines = content.split('\n')
                import_end = 0
                for i, line in enumerate(lines):
                    if line.startswith('from ') or line.startswith('import '):
                        import_end = i
                lines.insert(import_end + 1, 'from app.utils.datetime_utils import utc_now')
                content = '\n'.join(lines)
            
            # 替换调用
            content = re.sub(r'server_default=func\.now\(\)', 'default=utc_now', content)
            content = re.sub(r'onupdate=func\.now\(\)', 'onupdate=utc_now', content)
            content = re.sub(r'default=func\.now\(\)', 'default=utc_now', content)
        
        # 5. 清理重复的导入
        lines = content.split('\n')
        seen_imports = set()
        cleaned_lines = []
        
        for line in lines:
            if line.startswith('from app.utils.datetime_utils import'):
                if line not in seen_imports:
                    seen_imports.add(line)
                    cleaned_lines.append(line)
            else:
                cleaned_lines.append(line)
        
        content = '\n'.join(cleaned_lines)
        
        # 只有内容发生变化时才写入
        if content != original_content:
            # 备份原文件
            backup_path = file_path + '.backup'
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            
            # 写入修复后的内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"  ✅ 已修复")
            return True
        else:
            print(f"  ℹ️  无需修复")
            return False
            
    except Exception as e:
        print(f"  ❌ 修复失败: {e}")
        return False

def fix_frontend_file(file_path):
    """修复前端文件的时区问题"""
    print(f"检查: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        needs_fix = False
        
        # 检查是否需要添加时区工具函数导入
        if ('toLocaleString(' in content or 'new Date(' in content) and 'timezone.js' not in file_path:
            if 'import' in content and 'formatDateTime' not in content:
                # 这个文件可能需要手动检查
                print(f"  ⚠️  可能需要手动检查时间格式化")
                return False
        
        return False
        
    except Exception as e:
        print(f"  ❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 批量修复时区统一问题")
    print("=" * 50)
    
    # 切换到backend目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # 修复后端文件
    print("\n🔧 修复后端文件...")
    
    backend_files = []
    backend_files.extend(glob.glob('app/models/*.py'))
    backend_files.extend(glob.glob('app/api/**/*.py', recursive=True))
    backend_files.extend(glob.glob('app/services/*.py'))
    
    fixed_count = 0
    
    for file_path in backend_files:
        if '__pycache__' in file_path or '.backup' in file_path or '__init__.py' in file_path:
            continue
        
        if fix_backend_file(file_path):
            fixed_count += 1
    
    print(f"\n✅ 后端修复完成，共修复 {fixed_count} 个文件")
    
    # 前端文件提示
    print("\n📝 前端文件修复建议:")
    print("前端文件需要手动修复，建议：")
    print("1. 导入时区工具函数：import { formatDateTime } from '@/utils/timezone.js'")
    print("2. 替换时间格式化：使用 formatDateTime(datetime) 替代 toLocaleString()")
    print("3. 替换表格格式化：使用 formatTableDateTime 作为表格列的 formatter")
    
    print(f"\n🎉 批量修复完成！")
    print("建议运行 python check_timezone_compliance.py 重新检查")

if __name__ == "__main__":
    main()
