import requests
import json

# 基础配置
BASE_URL = "http://localhost:8000/api"

def test_keyword_categories():
    """测试关键词分类API"""
    try:
        url = f"{BASE_URL}/v1/scheduled-publish/keyword-categories"
        response = requests.get(url)
        print(f"关键词分类API - 状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"关键词分类数据: {data}")
        else:
            print(f"响应错误: {response.text}")
    except Exception as e:
        print(f"关键词分类API异常: {e}")

def test_site_categories():
    """测试站点分类API"""
    try:
        url = f"{BASE_URL}/v1/wordpress-site/categories/list"
        response = requests.get(url)
        print(f"站点分类API - 状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"站点分类数据: {data}")
        else:
            print(f"响应错误: {response.text}")
    except Exception as e:
        print(f"站点分类API异常: {e}")

def test_wordpress_sites():
    """测试WordPress站点API"""
    try:
        url = f"{BASE_URL}/v1/wordpress-site/"
        params = {"size": 5, "is_active": True}
        response = requests.get(url, params=params)
        print(f"WordPress站点API - 状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"WordPress站点数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"响应错误: {response.text}")
    except Exception as e:
        print(f"WordPress站点API异常: {e}")

if __name__ == "__main__":
    print("=== 测试后端API响应 ===")
    print()
    
    print("1. 测试关键词分类")
    test_keyword_categories()
    print()
    
    print("2. 测试站点分类")
    test_site_categories()
    print()
    
    print("3. 测试WordPress站点")
    test_wordpress_sites()
    print()
    
    print("=== 测试完成 ===") 