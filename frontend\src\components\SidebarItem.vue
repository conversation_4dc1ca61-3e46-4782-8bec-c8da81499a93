<template>
  <div v-if="!item.hidden" class="sidebar-item">
    <!-- 单个菜单项（没有子菜单或不需要显示为子菜单） -->
    <template v-if="hasOneShowingChild(item.children, item) && (!onlyOneChild.children || onlyOneChild.noShowingChildren) && !item.alwaysShow">
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
        <div 
          class="menu-item single-item"
          :class="{ 'is-active': isActive(resolvePath(onlyOneChild.path)) }"
        >
          <div class="menu-item-content">
            <el-icon v-if="onlyOneChild.meta.icon" class="menu-icon">
              <component :is="getIconComponent(onlyOneChild.meta.icon)" />
            </el-icon>
            <span class="menu-title">{{ onlyOneChild.meta.title }}</span>
          </div>
        </div>
      </app-link>
    </template>

    <!-- 有子菜单的菜单项 -->
    <div v-else class="submenu-container">
      <div 
        class="menu-item submenu-title"
        :class="{ 'is-expanded': isExpanded }"
        @click="toggleExpand"
      >
        <div class="menu-item-content">
          <el-icon v-if="item.meta && item.meta.icon" class="menu-icon">
            <component :is="getIconComponent(item.meta.icon)" />
          </el-icon>
          <span class="menu-title">{{ item.meta.title }}</span>
          <i class="expand-icon" :class="{ 'is-expanded': isExpanded }">
            <svg viewBox="0 0 1024 1024" width="12" height="12">
              <path d="M384 349.866667l256 256c17.066667 17.066667 17.066667 42.666667 0 59.733333s-42.666667 17.066667-59.733333 0L341.333333 426.666667c-17.066667-17.066667-17.066667-42.666667 0-59.733334L580.266667 128c17.066667-17.066667 42.666667-17.066667 59.733333 0s17.066667 42.666667 0 59.733333L384 349.866667z" fill="currentColor"></path>
            </svg>
          </i>
        </div>
      </div>
      
      <transition name="submenu-collapse">
        <div v-show="isExpanded" class="submenu-children">
          <sidebar-item
            v-for="child in item.children"
            :key="child.path"
            :is-nest="true"
            :item="child"
            :base-path="resolvePath(child.path)"
            class="nest-menu"
          />
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElIcon } from 'element-plus'
import {
  Document,
  Folder,
  User,
  Setting,
  DataAnalysis,
  Management,
  Monitor,
  Files,
  List,
  Edit,
  Plus,
  Delete,
  Search,
  Star,
  Bell,
  ShoppingCart,
  Goods,
  Money,
  TrendCharts,
  UserFilled,
  Lock,
  Key,
  Tools,
  Help,
  Promotion,
  Connection
} from '@element-plus/icons-vue'
import AppLink from './AppLink.vue'

export default defineComponent({
  name: 'SidebarItem',
  components: {
    ElIcon,
    AppLink,
    Document,
    Folder,
    User,
    Setting,
    DataAnalysis,
    Management,
    Monitor,
    Files,
    List,
    Edit,
    Plus,
    Delete,
    Search,
    Star,
    Bell,
    ShoppingCart,
    Goods,
    Money,
    TrendCharts,
    UserFilled,
    Lock,
    Key,
    Tools,
    Help,
    Promotion,
    Connection
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  setup (props) {
    const route = useRoute()
    const onlyOneChild = ref(null)
    const isExpanded = ref(true) // 默认展开

    const iconMap = {
      // 系统图标
      document: 'Document',
      folder: 'Folder',
      user: 'User',
      setting: 'Setting',
      'data-analysis': 'DataAnalysis',
      management: 'Management',
      monitor: 'Monitor',
      files: 'Files',
      list: 'List',
      edit: 'Edit',
      plus: 'Plus',
      delete: 'Delete',
      search: 'Search',
      star: 'Star',
      bell: 'Bell',
      // 电商相关图标
      'shopping-cart': 'ShoppingCart',
      goods: 'Goods',
      money: 'Money',
      'trend-charts': 'TrendCharts',
      // 用户管理图标
      'user-filled': 'UserFilled',
      lock: 'Lock',
      key: 'Key',
      tools: 'Tools',
      help: 'Help',
      // 业务图标
      rocket: 'Promotion',
      connection: 'Connection',
      // 默认图标
      default: 'Document'
    }

    const getIconComponent = (iconName) => {
      if (!iconName) return iconMap.default
      return iconMap[iconName] || iconMap.default
    }

    const hasOneShowingChild = (children = [], parent) => {
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          onlyOneChild.value = item
          return true
        }
      })

      if (showingChildren.length === 1) {
        return true
      }

      if (showingChildren.length === 0) {
        onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    }

    const resolvePath = (routePath) => {
      if (/^(https?:|mailto:|tel:)/.test(routePath)) {
        return routePath
      }

      if (routePath.startsWith('/')) {
        return routePath
      }

      const basePath = props.basePath || ''

      if (!basePath) {
        return routePath.startsWith('/') ? routePath : '/' + routePath
      }

      const base = basePath.endsWith('/') ? basePath.slice(0, -1) : basePath
      const routeStr = routePath.startsWith('/') ? routePath : '/' + routePath

      return base + routeStr
    }

    const isActive = (routePath) => {
      return route.path === routePath
    }

    const toggleExpand = () => {
      isExpanded.value = !isExpanded.value
    }

    return {
      onlyOneChild,
      isExpanded,
      hasOneShowingChild,
      resolvePath,
      getIconComponent,
      isActive,
      toggleExpand
    }
  }
})
</script>

<style scoped>
.sidebar-item {
  margin: 2px 0;
}

.menu-item {
  position: relative;
  cursor: pointer;
  border-radius: 8px;
  margin: 2px 8px;
  transition: all 0.3s ease;
}

.menu-item-content {
  display: flex;
  align-items: center;
  padding: 10px 14px;
  height: 44px;
  box-sizing: border-box;
}

.menu-icon {
  margin-right: 10px;
  font-size: 18px;
  width: 18px;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.menu-title {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-decoration: none;
}

.expand-icon {
  margin-left: 8px;
  transition: transform 0.3s ease;
  color: rgba(255, 255, 255, 0.6);
}

.expand-icon.is-expanded {
  transform: rotate(90deg);
}

.expand-icon svg {
  vertical-align: middle;
}

/* 菜单项悬停效果 */
.menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.menu-item:hover .menu-icon,
.menu-item:hover .menu-title {
  color: #ffffff;
}

.menu-item:hover .menu-icon {
  transform: scale(1.1);
}

/* 激活状态 */
.menu-item.is-active {
  background: linear-gradient(90deg, #409eff 0%, #66b1ff 100%);
}

.menu-item.is-active .menu-icon,
.menu-item.is-active .menu-title {
  color: #ffffff;
}

/* 子菜单容器 */
.submenu-container {
  margin: 2px 0;
}

.submenu-title {
  background: rgba(255, 255, 255, 0.05);
}

.submenu-title:hover {
  background: rgba(255, 255, 255, 0.15);
}

.submenu-children {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin: 0 8px 4px 8px;
  overflow: hidden;
}

.nest-menu {
  margin-left: 20px;
}

.nest-menu .menu-item {
  margin: 1px 6px;
  border-radius: 6px;
}

.nest-menu .menu-item-content {
  padding: 6px 14px;
  height: 32px;
}

.nest-menu .menu-title {
  font-size: 13px;
  text-decoration: none;
}

.nest-menu .menu-icon {
  font-size: 14px;
  margin-right: 8px;
}

/* 子菜单展开动画 */
.submenu-collapse-enter-active,
.submenu-collapse-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.submenu-collapse-enter-from,
.submenu-collapse-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.submenu-collapse-enter-to,
.submenu-collapse-leave-from {
  max-height: 500px;
  opacity: 1;
  transform: translateY(0);
}

/* 折叠状态样式 */
.sidebar-container.is-collapse .menu-title {
  opacity: 0;
  transform: translateX(-10px);
}

.sidebar-container.is-collapse .expand-icon {
  opacity: 0;
}

.sidebar-container.is-collapse .menu-item-content {
  justify-content: center;
  padding: 12px 8px;
}

.sidebar-container.is-collapse .menu-icon {
  margin-right: 0;
}

.sidebar-container.is-collapse .submenu-children {
  display: none;
}
</style> 