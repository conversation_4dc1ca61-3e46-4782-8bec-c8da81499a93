from sqlalchemy import Column, String, Integer, Float, ForeignKey, DateTime, JSON, Text, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.session import Base

class Subscription(Base):
    """订阅计划模型"""
    __tablename__ = "subscription"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    price = Column(Float, nullable=False, default=0.0)
    description = Column(Text, nullable=True)
    features = Column(JSON, nullable=False, default=list)
    
    # 租户关联
    tenant_id = Column(String(50), ForeignKey("tenant.id"), nullable=False)
    tenant = relationship("Tenant")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class TenantSubscription(Base):
    """租户订阅关系模型"""
    __tablename__ = "tenant_subscription"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 租户关联
    tenant_id = Column(String(50), ForeignKey("tenant.id"), nullable=False)
    tenant = relationship("Tenant")
    
    # 订阅计划关联
    subscription_id = Column(Integer, ForeignKey("subscription.id"), nullable=False)
    subscription = relationship("Subscription")
    
    # 订阅状态
    is_active = Column(Boolean, default=True)
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=True)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now()) 