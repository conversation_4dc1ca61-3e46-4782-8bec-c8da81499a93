-- AI配置表
CREATE TABLE `ai_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '配置名称',
  `service_type` varchar(50) NOT NULL COMMENT '服务类型，如DIFY',
  `api_key` varchar(255) NOT NULL COMMENT 'API密钥',
  `base_url` varchar(255) DEFAULT NULL COMMENT 'API基础URL',
  `model_name` varchar(100) DEFAULT NULL COMMENT '模型名称',
  `max_tokens` int(11) DEFAULT 4000 COMMENT '最大token数',
  `temperature` decimal(3,2) DEFAULT 0.70 COMMENT '创造性参数',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用，1=启用，0=禁用',
  `description` text DEFAULT NULL COMMENT '配置描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_service_type` (`service_type`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI配置表';

-- AI文章表
CREATE TABLE `ai_articles` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `keywords` varchar(500) NOT NULL COMMENT '关键词',
  `wordpress_url` varchar(255) NOT NULL COMMENT 'WordPress站点URL',
  `type` varchar(50) NOT NULL COMMENT '类型，如Floor',
  `model` varchar(50) DEFAULT NULL COMMENT 'AI模型，WanX或Jimeng',
  `image_url` varchar(500) DEFAULT NULL COMMENT '上传的图片URL',
  
  -- 生成结果
  `title` varchar(255) DEFAULT NULL COMMENT '生成的文章标题',
  `article_url` varchar(500) DEFAULT NULL COMMENT '发布后的文章链接',
  `article_id` varchar(100) DEFAULT NULL COMMENT 'WordPress文章ID',
  `featured_image_id` varchar(100) DEFAULT NULL COMMENT '特色图片ID',
  
  -- 状态和错误信息
  `status` enum('pending','generating','success','failed','published') DEFAULT 'pending' COMMENT '文章状态',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `result_text` text DEFAULT NULL COMMENT '完整返回结果',
  
  -- DIFY相关
  `workflow_run_id` varchar(100) DEFAULT NULL COMMENT 'DIFY工作流运行ID',
  `task_id` varchar(100) DEFAULT NULL COMMENT 'DIFY任务ID',
  
  -- AI配置关联
  `ai_config_id` int(11) DEFAULT NULL COMMENT 'AI配置ID',
  
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_keywords` (`keywords`(191)),
  KEY `idx_wordpress_url` (`wordpress_url`),
  KEY `idx_ai_config_id` (`ai_config_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_workflow_run_id` (`workflow_run_id`),
  
  CONSTRAINT `fk_ai_articles_ai_config` FOREIGN KEY (`ai_config_id`) REFERENCES `ai_configs` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI文章表';

-- 插入默认DIFY配置
INSERT INTO `ai_configs` (
  `name`, 
  `service_type`, 
  `api_key`, 
  `base_url`, 
  `description`, 
  `is_active`
) VALUES (
  'DIFY默认配置',
  'DIFY',
  'app-QRfxgIcYH6i874hF7bUB38q5',
  'http://ai.yufeiind.cn:81/v1',
  '系统默认的DIFY配置，用于AI文章生成',
  1
);

-- 创建索引以优化查询性能
-- AI配置表索引
CREATE INDEX `idx_ai_configs_name` ON `ai_configs` (`name`);
CREATE INDEX `idx_ai_configs_service_active` ON `ai_configs` (`service_type`, `is_active`);

-- AI文章表索引
CREATE INDEX `idx_ai_articles_status_created` ON `ai_articles` (`status`, `created_at`);
CREATE INDEX `idx_ai_articles_wordpress_status` ON `ai_articles` (`wordpress_url`, `status`);
CREATE FULLTEXT INDEX `idx_ai_articles_keywords_fulltext` ON `ai_articles` (`keywords`);

-- 视图：活跃的AI配置
CREATE VIEW `v_active_ai_configs` AS
SELECT 
  `id`,
  `name`,
  `service_type`,
  `api_key`,
  `base_url`,
  `model_name`,
  `max_tokens`,
  `temperature`,
  `description`,
  `created_at`,
  `updated_at`
FROM `ai_configs`
WHERE `is_active` = 1
ORDER BY `created_at` DESC;

-- 视图：文章生成统计
CREATE VIEW `v_ai_article_stats` AS
SELECT 
  `status`,
  COUNT(*) as `count`,
  DATE(`created_at`) as `date`
FROM `ai_articles`
GROUP BY `status`, DATE(`created_at`)
ORDER BY `date` DESC, `status`;

-- 视图：AI配置使用统计
CREATE VIEW `v_ai_config_usage` AS
SELECT 
  ac.`id`,
  ac.`name`,
  ac.`service_type`,
  COUNT(aa.`id`) as `usage_count`,
  COUNT(CASE WHEN aa.`status` = 'published' THEN 1 END) as `success_count`,
  COUNT(CASE WHEN aa.`status` = 'failed' THEN 1 END) as `failed_count`
FROM `ai_configs` ac
LEFT JOIN `ai_articles` aa ON ac.`id` = aa.`ai_config_id`
WHERE ac.`is_active` = 1
GROUP BY ac.`id`, ac.`name`, ac.`service_type`
ORDER BY `usage_count` DESC; 