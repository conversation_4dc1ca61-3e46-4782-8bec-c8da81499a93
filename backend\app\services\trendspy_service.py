"""
TrendsPy趋势服务
直接替代PyTrends，解决'list index out of range'错误
提供完整的Google Trends数据获取功能
"""
import json
import time
import logging
import os
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import pandas as pd
import requests

# 导入TrendsPy
try:
    from trendspy import Trends
    TRENDSPY_AVAILABLE = True
except ImportError:
    Trends = None
    TRENDSPY_AVAILABLE = False

# 初始化日志记录器
logger = logging.getLogger(__name__)

class TrendsPyService:
    """
    TrendsPy趋势服务
    直接替代PyTrends，提供完整的Google Trends功能
    """
    
    def __init__(self, config: Dict[str, Any] = None, proxy_config: Dict[str, Any] = None):
        """
        初始化TrendsPy服务
        
        Args:
            config: 服务配置字典
            proxy_config: 代理配置字典
        """
        if not TRENDSPY_AVAILABLE:
            raise ImportError("trendspy库未安装，请运行: pip install trendspy")
        
        self.config = config or {}
        self.proxy_config = proxy_config or {}
        
        # 服务配置
        self.language = self.config.get('language', 'en-GB')
        self.timezone = self.config.get('timezone', 0)
        self.geo_location = self.config.get('geo_location', 'GB')
        self.default_timeframe = self.config.get('default_timeframe', 'today 1-m')
        self.max_keywords_per_batch = self.config.get('max_keywords_per_batch', 5)
        self.request_delay = self.config.get('request_delay', 8)
        self.retry_attempts = self.config.get('retry_attempts', 3)
        
        # 设置代理环境变量
        self._setup_proxy_env()
        
        # 初始化TrendsPy客户端
        self._init_client()
        
        # referer策略（用于绕过quota限制）
        self.referer_strategies = [
            {'referer': 'https://www.google.com/'},
            {'referer': 'https://trends.google.com/'},
            {'referer': 'https://www.google.com.hk/'},
            {'referer': 'https://www.google.co.uk/'},
            {'referer': 'https://www.google.de/'},
        ]
        
        logger.info(f"TrendsPy服务初始化完成")
        logger.info(f"语言: {self.language}, 时区: {self.timezone}, 地区: {self.geo_location}")
        logger.info(f"默认时间范围: {self.default_timeframe}")
    
    def _setup_proxy_env(self):
        """设置代理环境变量"""
        if self.proxy_config.get('use_proxy') and self.proxy_config.get('proxy_host'):
            proxy_url = self._build_proxy_url()
            os.environ['HTTP_PROXY'] = proxy_url
            os.environ['HTTPS_PROXY'] = proxy_url
            logger.info(f"设置TrendsPy代理环境变量: {proxy_url}")
    
    def _build_proxy_url(self) -> str:
        """构建代理URL"""
        proxy_type = self.proxy_config.get('proxy_type', 'http')
        host = self.proxy_config.get('proxy_host')
        port = self.proxy_config.get('proxy_port')
        username = self.proxy_config.get('proxy_username')
        password = self.proxy_config.get('proxy_password')
        
        if username and password:
            return f"{proxy_type}://{username}:{password}@{host}:{port}"
        else:
            return f"{proxy_type}://{host}:{port}"
    
    def _init_client(self):
        """初始化TrendsPy客户端"""
        try:
            # 如果配置了代理，使用代理初始化
            if self.proxy_config.get('use_proxy') and self.proxy_config.get('proxy_host'):
                proxy_dict = {
                    'http': self._build_proxy_url(),
                    'https': self._build_proxy_url()
                }
                # 设置更长的请求延迟以避免429错误
                self.tr = Trends(proxy=proxy_dict, request_delay=max(self.request_delay, 6.0))
                logger.info(f"TrendsPy客户端初始化成功（使用代理），请求延迟: {max(self.request_delay, 6.0)}秒")
            else:
                # 设置更长的请求延迟以避免429错误
                self.tr = Trends(request_delay=max(self.request_delay, 6.0))
                logger.info(f"TrendsPy客户端初始化成功（不使用代理），请求延迟: {max(self.request_delay, 6.0)}秒")
                
        except Exception as e:
            logger.error(f"TrendsPy客户端初始化失败: {e}")
            raise
    
    def _is_valid_data(self, data) -> bool:
        """检查数据是否有效"""
        if data is None:
            return False
        
        if isinstance(data, dict):
            # 检查字典中是否有有效的DataFrame
            for key, value in data.items():
                if isinstance(value, pd.DataFrame) and not value.empty:
                    return True
            return False
        elif isinstance(data, pd.DataFrame):
            return not data.empty
        elif isinstance(data, list):
            return len(data) > 0
        else:
            return bool(data)
    
    def _safe_get_with_retries(self, func, *args, headers=None, **kwargs):
        """
        带重试机制的安全数据获取
        
        Args:
            func: TrendsPy方法
            *args: 位置参数
            headers: 请求头（包含referer策略）
            **kwargs: 关键字参数
            
        Returns:
            获取的数据，失败时返回None
        """
        for attempt in range(self.retry_attempts):
            # 尝试不同的referer策略
            strategies_to_try = [headers] if headers else self.referer_strategies
            
            for strategy in strategies_to_try:
                try:
                    if strategy:
                        result = func(*args, headers=strategy, **kwargs)
                    else:
                        result = func(*args, **kwargs)
                    
                    if self._is_valid_data(result):
                        return result
                    
                except Exception as e:
                    error_msg = str(e).lower()
                    if "quota exceeded" in error_msg:
                        logger.debug(f"遇到quota限制，尝试下一个策略...")
                        time.sleep(1)
                        continue
                    else:
                        logger.debug(f"请求失败: {e}")
                        continue
            
            # 如果所有策略都失败，等待后重试
            if attempt < self.retry_attempts - 1:
                wait_time = self.request_delay * (2 ** attempt)
                logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
        
        return None
    
    def get_interest_over_time(
        self,
        keywords: List[str],
        timeframe: str = None,
        geo: str = None,
        category: int = 0,
        gprop: str = ''
    ) -> Dict[str, Any]:
        """
        获取关键词的时间趋势数据
        
        Args:
            keywords: 关键词列表
            timeframe: 时间范围
            geo: 地理位置代码
            category: 分类ID（TrendsPy不直接支持，保留兼容性）
            gprop: Google产品类型（TrendsPy不直接支持，保留兼容性）
            
        Returns:
            包含趋势数据的字典
        """
        if len(keywords) > 5:
            raise ValueError("一次最多支持5个关键词")
        
        timeframe = timeframe or self.default_timeframe
        geo = geo or self.geo_location
        
        logger.info(f"获取关键词趋势数据: {keywords}")
        logger.info(f"时间范围: {timeframe}, 地区: {geo}")
        
        result = {
            'keywords': keywords,
            'interest_over_time': {},
            'metadata': {
                'timeframe': timeframe,
                'geo': geo,
                'source': 'trendspy',
                'timestamp': datetime.now().isoformat()
            }
        }
        
        success_count = 0
        
        for keyword in keywords:
            try:
                logger.info(f"获取关键词 '{keyword}' 的兴趣度数据...")
                
                # 使用TrendsPy获取兴趣度数据
                interest_data = self.tr.interest_over_time(keyword)
                
                if interest_data is not None and not interest_data.empty:
                    # 转换为兼容格式
                    records = []
                    for index, row in interest_data.iterrows():
                        record = {
                            'date': index.strftime('%Y-%m-%d') if hasattr(index, 'strftime') else str(index),
                            'value': int(row[keyword]) if keyword in row else 0,
                            'isPartial': bool(row.get('isPartial', False)) if 'isPartial' in row else False
                        }
                        records.append(record)
                    
                    result['interest_over_time'][keyword] = records
                    success_count += 1
                    
                    # 计算统计信息
                    values = [r['value'] for r in records]
                    avg_value = sum(values) / len(values) if values else 0
                    max_value = max(values) if values else 0
                    
                    logger.info(f"关键词 '{keyword}' 兴趣度数据获取成功")
                    logger.info(f"数据点数: {len(records)}, 平均值: {avg_value:.2f}, 最大值: {max_value}")
                else:
                    logger.warning(f"关键词 '{keyword}' 没有兴趣度数据")
                    result['interest_over_time'][keyword] = []
                
                # 关键词间延迟 - 增加延迟时间避免429错误
                if keyword != keywords[-1]:
                    time.sleep(12)  # 进一步增加关键词间延迟到12秒
                    
            except Exception as e:
                logger.error(f"获取关键词 '{keyword}' 兴趣度失败: {e}")
                result['interest_over_time'][keyword] = []
                continue
        
        logger.info(f"兴趣度数据获取完成，成功: {success_count}/{len(keywords)}")
        return result
    
    def get_related_topics_and_queries(
        self,
        keywords: List[str],
        timeframe: str = None,
        geo: str = None,
        category: int = 0
    ) -> Dict[str, Any]:
        """
        获取相关主题和查询（TrendsPy的核心优势功能）
        
        Args:
            keywords: 关键词列表
            timeframe: 时间范围
            geo: 地理位置代码
            category: 分类ID
            
        Returns:
            包含相关主题和查询的字典
        """
        if len(keywords) > 5:
            raise ValueError("一次最多支持5个关键词")
        
        timeframe = timeframe or self.default_timeframe
        geo = geo or self.geo_location
        
        logger.info(f"获取相关主题和查询: {keywords}")
        logger.info(f"时间范围: {timeframe}, 地区: {geo}")
        
        result = {
            'keywords': keywords,
            'related_topics': {},
            'related_queries': {},
            'rising_topics': {},
            'rising_queries': {},
            'metadata': {
                'timeframe': timeframe,
                'geo': geo,
                'source': 'trendspy',
                'timestamp': datetime.now().isoformat()
            }
        }
        
        success_count = 0
        
        for keyword in keywords:
            try:
                logger.info(f"获取关键词 '{keyword}' 的相关数据...")
                
                # 获取相关查询
                queries_data = self._get_related_queries_safe(keyword)
                if queries_data:
                    if 'top' in queries_data:
                        result['related_queries'][keyword] = queries_data['top']
                    if 'rising' in queries_data:
                        result['rising_queries'][keyword] = queries_data['rising']
                    
                    logger.info(f"关键词 '{keyword}' 相关查询获取成功")
                
                # 添加延迟避免频率限制
                time.sleep(3)
                
                # 获取相关主题
                topics_data = self._get_related_topics_safe(keyword)
                if topics_data:
                    if 'top' in topics_data:
                        result['related_topics'][keyword] = topics_data['top']
                    if 'rising' in topics_data:
                        result['rising_topics'][keyword] = topics_data['rising']
                    
                    logger.info(f"关键词 '{keyword}' 相关主题获取成功")
                
                # 如果至少获取到一种数据，算作成功
                if queries_data or topics_data:
                    success_count += 1
                
                # 关键词间延迟 - 增加延迟时间避免429错误
                if keyword != keywords[-1]:
                    time.sleep(12)  # 进一步增加关键词间延迟到12秒
                    
            except Exception as e:
                logger.error(f"获取关键词 '{keyword}' 相关数据失败: {e}")
                continue
        
        logger.info(f"相关数据获取完成，成功: {success_count}/{len(keywords)}")
        return result
    
    def _get_related_queries_safe(self, keyword: str) -> Optional[Dict[str, Any]]:
        """安全获取相关查询"""
        logger.debug(f"获取关键词 '{keyword}' 的相关查询")
        
        for attempt in range(self.retry_attempts):
            for headers in self.referer_strategies:
                try:
                    related = self.tr.related_queries(keyword, headers=headers)
                    
                    if self._is_valid_data(related):
                        # 转换数据格式
                        converted_data = self._convert_trendspy_data(related, keyword, 'queries')
                        
                        if converted_data:
                            total_count = sum(len(records) for records in converted_data.values())
                            logger.debug(f"关键词 '{keyword}' 获取 {total_count} 条相关查询")
                            return converted_data
                        
                except Exception as e:
                    if "quota exceeded" in str(e).lower() or "429" in str(e):
                        time.sleep(8)  # 遇到429错误增加更长延迟
                        continue
                    else:
                        continue
            
            if attempt < self.retry_attempts - 1:
                wait_time = self.request_delay * (2 ** attempt)
                logger.debug(f"等待 {wait_time} 秒后重试相关查询...")
                time.sleep(wait_time)
        
        logger.warning(f"无法获取关键词 '{keyword}' 的相关查询")
        return None
    
    def _get_related_topics_safe(self, keyword: str) -> Optional[Dict[str, Any]]:
        """安全获取相关主题"""
        logger.debug(f"获取关键词 '{keyword}' 的相关主题")
        
        for attempt in range(self.retry_attempts):
            for headers in self.referer_strategies:
                try:
                    related = self.tr.related_topics(keyword, headers=headers)
                    
                    if self._is_valid_data(related):
                        # 转换数据格式
                        converted_data = self._convert_trendspy_data(related, keyword, 'topics')
                        
                        if converted_data:
                            total_count = sum(len(records) for records in converted_data.values())
                            logger.debug(f"关键词 '{keyword}' 获取 {total_count} 条相关主题")
                            return converted_data
                        
                except Exception as e:
                    if "quota exceeded" in str(e).lower() or "429" in str(e):
                        time.sleep(8)  # 遇到429错误增加更长延迟
                        continue
                    else:
                        continue
            
            if attempt < self.retry_attempts - 1:
                wait_time = self.request_delay * (2 ** attempt)
                logger.debug(f"等待 {wait_time} 秒后重试相关主题...")
                time.sleep(wait_time)
        
        logger.warning(f"无法获取关键词 '{keyword}' 的相关主题")
        return None
    
    def _convert_trendspy_data(self, data: Dict[str, pd.DataFrame], keyword: str, data_type: str) -> Dict[str, Any]:
        """
        将TrendsPy数据格式转换为标准格式
        
        Args:
            data: TrendsPy返回的数据字典
            keyword: 关键词
            data_type: 数据类型 ('queries' 或 'topics')
            
        Returns:
            转换后的数据字典
        """
        result = {}
        
        if not data or not isinstance(data, dict):
            return result
        
        # 处理top和rising数据
        for category in ['top', 'rising']:
            if category in data and isinstance(data[category], pd.DataFrame) and not data[category].empty:
                df = data[category]
                
                # 转换为标准格式
                records = []
                for _, row in df.iterrows():
                    record = {}
                    for col in df.columns:
                        value = row[col]
                        # 处理特殊值
                        if pd.isna(value):
                            record[col] = None
                        elif isinstance(value, (int, float)):
                            record[col] = value
                        else:
                            record[col] = str(value)
                    records.append(record)
                
                result[category] = records
                logger.debug(f"转换 {keyword} 的 {category} {data_type}: {len(records)} 条记录")
        
        return result
    
    def generate_keyword_ideas(
        self,
        seed_keywords: List[str],
        timeframe: str = None,
        geo_location: str = None,
        include_related: bool = True,
        include_rising: bool = True
    ) -> List[Dict[str, Any]]:
        """
        基于种子关键词生成关键词创意
        """
        timeframe = timeframe or self.default_timeframe
        geo_location = geo_location or self.geo_location
        
        all_keywords = []
        all_keyword_data = []
        
        try:
            logger.info(f"基于种子关键词生成创意: {seed_keywords}")
            
            # 分批处理种子关键词
            for i in range(0, len(seed_keywords), self.max_keywords_per_batch):
                batch_keywords = seed_keywords[i:i + self.max_keywords_per_batch]
                
                logger.info(f"处理关键词批次 {i//self.max_keywords_per_batch + 1}: {batch_keywords}")
                
                # 获取相关数据
                related_data = self.get_related_topics_and_queries(
                    keywords=batch_keywords,
                    timeframe=timeframe,
                    geo=geo_location
                )
                
                # 提取关键词创意
                batch_ideas = self._extract_keyword_ideas(
                    related_data,
                    include_related=include_related,
                    include_rising=include_rising
                )
                
                all_keyword_data.extend(batch_ideas)
                
                # 添加批次间延迟
                if i + self.max_keywords_per_batch < len(seed_keywords):
                    time.sleep(self.request_delay * 2)
            
            # 去重并排序
            unique_keywords = {}
            for keyword_data in all_keyword_data:
                keyword = keyword_data['keyword']
                if keyword not in unique_keywords:
                    unique_keywords[keyword] = keyword_data
                else:
                    # 合并数据，保留更高的分数
                    existing = unique_keywords[keyword]
                    if keyword_data.get('relevance_score', 0) > existing.get('relevance_score', 0):
                        unique_keywords[keyword] = keyword_data
            
            # 转换为列表并排序
            result = list(unique_keywords.values())
            result.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
            
            logger.info(f"生成关键词创意完成，共 {len(result)} 个创意")
            return result
            
        except Exception as e:
            logger.error(f"生成关键词创意失败: {e}")
            raise
    
    def _extract_keyword_ideas(
        self,
        related_data: Dict[str, Any],
        include_related: bool = True,
        include_rising: bool = True
    ) -> List[Dict[str, Any]]:
        """从相关数据中提取关键词创意"""
        ideas = []
        
        for keyword in related_data['keywords']:
            # 处理相关查询
            if include_related and keyword in related_data.get('related_queries', {}):
                for query_data in related_data['related_queries'][keyword]:
                    idea = {
                        'keyword': query_data.get('query', ''),
                        'source_keyword': keyword,
                        'type': 'related_query',
                        'relevance_score': query_data.get('value', 0),
                        'category': 'related'
                    }
                    if idea['keyword']:
                        ideas.append(idea)
            
            # 处理上升查询
            if include_rising and keyword in related_data.get('rising_queries', {}):
                for query_data in related_data['rising_queries'][keyword]:
                    idea = {
                        'keyword': query_data.get('query', ''),
                        'source_keyword': keyword,
                        'type': 'rising_query',
                        'relevance_score': query_data.get('value', 0),
                        'category': 'rising'
                    }
                    if idea['keyword']:
                        ideas.append(idea)
            
            # 处理相关主题
            if include_related and keyword in related_data.get('related_topics', {}):
                for topic_data in related_data['related_topics'][keyword]:
                    idea = {
                        'keyword': topic_data.get('title', ''),
                        'source_keyword': keyword,
                        'type': 'related_topic',
                        'relevance_score': topic_data.get('value', 0),
                        'category': 'related',
                        'topic_type': topic_data.get('type', ''),
                        'topic_mid': topic_data.get('mid', '')
                    }
                    if idea['keyword']:
                        ideas.append(idea)
            
            # 处理上升主题
            if include_rising and keyword in related_data.get('rising_topics', {}):
                for topic_data in related_data['rising_topics'][keyword]:
                    idea = {
                        'keyword': topic_data.get('title', ''),
                        'source_keyword': keyword,
                        'type': 'rising_topic',
                        'relevance_score': topic_data.get('value', 0),
                        'category': 'rising',
                        'topic_type': topic_data.get('type', ''),
                        'topic_mid': topic_data.get('mid', '')
                    }
                    if idea['keyword']:
                        ideas.append(idea)
        
        return ideas
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试TrendsPy服务连接状态
        
        Returns:
            连接状态字典
        """
        result = {
            'timestamp': datetime.now().isoformat(),
            'service': 'trendspy',
            'available': False,
            'status': 'unknown'
        }
        
        try:
            # 简单测试连接
            test_data = self.tr.interest_over_time('test')
            
            result.update({
                'available': True,
                'status': 'connected',
                'message': 'TrendsPy服务连接正常',
                'config': {
                    'language': self.language,
                    'timezone': self.timezone,
                    'geo_location': self.geo_location,
                    'default_timeframe': self.default_timeframe,
                    'proxy_enabled': bool(self.proxy_config.get('use_proxy'))
                }
            })
            
        except Exception as e:
            result.update({
                'available': False,
                'status': 'error',
                'error': str(e),
                'message': 'TrendsPy服务连接失败'
            })
        
        return result 