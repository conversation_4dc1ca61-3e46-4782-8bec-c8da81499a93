from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, func
from datetime import date, datetime, timedelta
from decimal import Decimal
import logging

from app.models.alibaba_product import AlibabaProductPerformance, AlibabaProduct
from app.services.alibaba_service import AlibabaService
from app.core.config import settings
from app.utils.datetime_utils import utc_now, to_iso_string

logger = logging.getLogger(__name__)

class AlibabaProductServiceReal:
    """阿里巴巴产品表现服务 - 真实API版本"""
    
    def __init__(self):
        pass

    def get_product_performance(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str,
        start_date: date,
        end_date: date,
        product_ids: Optional[List[str]] = None
    ) -> List[AlibabaProductPerformance]:
        """获取产品表现数据"""
        query = db.query(AlibabaProductPerformance).filter(
            and_(
                AlibabaProductPerformance.user_id == user_id,
                AlibabaProductPerformance.tenant_id == tenant_id,
                AlibabaProductPerformance.statistics_type == statistics_type,
                AlibabaProductPerformance.stat_date >= start_date,
                AlibabaProductPerformance.stat_date <= end_date
            )
        )
        
        if product_ids:
            query = query.filter(AlibabaProductPerformance.product_id.in_(product_ids))
            
        return query.order_by(desc(AlibabaProductPerformance.impression)).all()

    def get_available_products(
        self,
        db: Session,
        user_id: int,
        tenant_id: str
    ) -> List[AlibabaProduct]:
        """获取可用的产品列表"""
        # 先查询总数（包括非活跃的）
        total_products = db.query(AlibabaProduct).filter(
            and_(
                AlibabaProduct.user_id == user_id,
                AlibabaProduct.tenant_id == tenant_id
            )
        ).count()
        
        # 查询活跃产品数量
        active_products = db.query(AlibabaProduct).filter(
            and_(
                AlibabaProduct.user_id == user_id,
                AlibabaProduct.tenant_id == tenant_id,
                AlibabaProduct.is_active == True
            )
        ).all()
        
        logger.info(f"产品统计 - 总数: {total_products}, 活跃: {len(active_products)}, 非活跃: {total_products - len(active_products)}")
        
        if total_products > len(active_products):
            logger.warning(f"发现 {total_products - len(active_products)} 个非活跃产品，可能需要重新同步产品列表")
        
        return active_products

    def get_date_range(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str
    ) -> Dict[str, Any]:
        """获取可用的时间范围"""
        result = db.query(
            func.min(AlibabaProductPerformance.stat_date).label('start_date'),
            func.max(AlibabaProductPerformance.stat_date).label('end_date')
        ).filter(
            and_(
                AlibabaProductPerformance.user_id == user_id,
                AlibabaProductPerformance.tenant_id == tenant_id,
                AlibabaProductPerformance.statistics_type == statistics_type
            )
        ).first()
        
        if result.start_date and result.end_date:
            return {
                "start_date": result.start_date.isoformat(),
                "end_date": result.end_date.isoformat()
            }
        else:
            # 如果没有数据，返回最近30天
            end_date = date.today()
            start_date = end_date - timedelta(days=30)
            return {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }

    def save_product_performance(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        performance_data: Dict[str, Any],
        product_id: str,
        product_name: str,
        statistics_type: str,
        stat_date: date
    ) -> AlibabaProductPerformance:
        """保存产品表现数据"""
        # 检查是否已存在
        existing = db.query(AlibabaProductPerformance).filter(
            and_(
                AlibabaProductPerformance.user_id == user_id,
                AlibabaProductPerformance.tenant_id == tenant_id,
                AlibabaProductPerformance.product_id == product_id,
                AlibabaProductPerformance.statistics_type == statistics_type,
                AlibabaProductPerformance.stat_date == stat_date
            )
        ).first()
        
        if existing:
            # 更新现有记录
            existing.product_name = product_name
            existing.impression = performance_data.get("impression", performance_data.get("imps", 0))
            existing.click = performance_data.get("click", performance_data.get("clk", 0))
            existing.visitor = performance_data.get("visitor", performance_data.get("uv", 0))
            existing.order_count = performance_data.get("order_count", performance_data.get("order", 0))
            existing.bookmark = performance_data.get("bookmark", performance_data.get("collect", 0))
            existing.compare = performance_data.get("compare", 0)
            existing.share = performance_data.get("share", 0)
            existing.feedback = performance_data.get("feedback", performance_data.get("fb", 0))
            existing.ctr = Decimal(str(performance_data.get("ctr", 0)))
            existing.conversion_rate = Decimal(str(performance_data.get("conversion_rate", 0)))
            existing.sync_time = utc_now()
            existing.updated_at = utc_now()  # 添加更新时间
            existing.data_source = "alibaba_api"  # 标记为真实API数据
            db_performance = existing
        else:
            # 创建新记录
            db_performance = AlibabaProductPerformance(
                user_id=user_id,
                tenant_id=tenant_id,
                product_id=product_id,
                product_name=product_name,
                statistics_type=statistics_type,
                stat_date=stat_date,
                impression=performance_data.get("impression", performance_data.get("imps", 0)),
                click=performance_data.get("click", performance_data.get("clk", 0)),
                visitor=performance_data.get("visitor", performance_data.get("uv", 0)),
                order_count=performance_data.get("order_count", performance_data.get("order", 0)),
                bookmark=performance_data.get("bookmark", performance_data.get("collect", 0)),
                compare=performance_data.get("compare", 0),
                share=performance_data.get("share", 0),
                feedback=performance_data.get("feedback", performance_data.get("fb", 0)),
                ctr=Decimal(str(performance_data.get("ctr", 0))),
                conversion_rate=Decimal(str(performance_data.get("conversion_rate", 0))),
                sync_time=utc_now(),
                created_at=utc_now(),  # 添加创建时间
                updated_at=utc_now(),  # 添加更新时间
                data_source="alibaba_api"  # 标记为真实API数据
            )
            db.add(db_performance)
        
        db.commit()
        db.refresh(db_performance)
        return db_performance

    def save_products(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        products_data: List[Dict[str, Any]]
    ) -> List[AlibabaProduct]:
        """保存产品列表"""
        saved_products = []
        
        for product_data in products_data:
            # 检查是否已存在
            existing = db.query(AlibabaProduct).filter(
                and_(
                    AlibabaProduct.user_id == user_id,
                    AlibabaProduct.tenant_id == tenant_id,
                    AlibabaProduct.product_id == str(product_data.get("product_id", product_data.get("id", "")))
                )
            ).first()
            
            if existing:
                # 更新现有记录
                existing.product_name = product_data.get("product_name", product_data.get("name", ""))
                existing.product_title = product_data.get("product_title", product_data.get("title", ""))
                existing.product_category = product_data.get("product_category", product_data.get("category", ""))
                existing.product_status = product_data.get("product_status", product_data.get("status", "active"))
                existing.product_url = product_data.get("product_url", product_data.get("url", ""))
                existing.main_image_url = product_data.get("main_image_url", product_data.get("image", ""))
                existing.description = product_data.get("description", "")
                existing.sync_time = utc_now()
                saved_products.append(existing)
            else:
                # 创建新记录
                db_product = AlibabaProduct(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    product_id=str(product_data.get("product_id", product_data.get("id", ""))),
                    product_name=product_data.get("product_name", product_data.get("name", "")),
                    product_title=product_data.get("product_title", product_data.get("title", "")),
                    product_category=product_data.get("product_category", product_data.get("category", "")),
                    product_status=product_data.get("product_status", product_data.get("status", "active")),
                    product_url=product_data.get("product_url", product_data.get("url", "")),
                    main_image_url=product_data.get("main_image_url", product_data.get("image", "")),
                    description=product_data.get("description", ""),
                    sync_time=utc_now(),
                    is_active=True
                )
                db.add(db_product)
                saved_products.append(db_product)
        
        db.commit()
        return saved_products

    async def sync_data_from_alibaba(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str,
        start_date: date,
        end_date: date,
        product_ids: Optional[List[str]] = None,
        access_token: str = None
    ) -> Dict[str, int]:
        """从阿里国际站同步产品表现数据 - 真实API版本"""
        try:
            if not access_token:
                raise Exception("访问令牌不能为空")
            
            # 初始化阿里巴巴服务
            alibaba_service = AlibabaService(db)
            
            # 1. 获取产品ID列表
            if not product_ids:
                logger.info("检查现有产品列表...")
                existing_products = self.get_available_products(db, user_id, tenant_id)
                if existing_products:
                    product_ids = [p.product_id for p in existing_products]
                    logger.info(f"使用现有产品列表: {len(product_ids)} 个产品")
                else:
                    logger.warning("未找到产品数据。")
                    return {
                        "products": 0, 
                        "performance_records": 0,
                        "message": "需要配置产品ID。"
                    }
            
            # 2. 获取产品表现数据
            saved_performance = []
            
            # 对于阿里巴巴API，只需要调用一次，传递单个日期
            # API会根据statistics_type自动返回对应维度的数据
            stat_date = start_date  # 使用前端传递的选择日期
            
            logger.info(f"开始同步产品表现数据: {len(product_ids)} 个产品，统计类型: {statistics_type}，日期: {stat_date}")
            
            # 分批处理产品ID
            batch_size = 20  # 改回一个批次同步20条产品数据
            product_batches = [product_ids[i:i + batch_size] for i in range(0, len(product_ids), batch_size)]
            
            for batch_index, batch_product_ids in enumerate(product_batches):
                logger.info(f"处理第 {batch_index + 1}/{len(product_batches)} 批产品 ({len(batch_product_ids)} 个)")
                
                # 调用阿里巴巴API获取产品表现数据
                performance_response = alibaba_service.get_product_performance_data(
                    access_token=access_token,
                    statistics_type=statistics_type,
                    stat_date=stat_date.isoformat(),  # 直接使用单个日期
                    product_ids=batch_product_ids
                )
                
                if performance_response.get("error_response"):
                    error_info = performance_response["error_response"]
                    error_code = error_info.get("code", "UNKNOWN")
                    error_msg = error_info.get("msg", "获取产品表现数据失败")
                    logger.error(f"获取产品表现数据API错误 - 批次: {batch_index + 1}, 错误码: {error_code}, 错误信息: {error_msg}")
                    continue
                
                # 解析响应数据
                result_data = None
                if "alibaba_mydata_self_product_get_response" in performance_response:
                    result_data = performance_response["alibaba_mydata_self_product_get_response"].get("result", {})
                elif "result" in performance_response:
                    result_data = performance_response.get("result", {})
                
                if result_data is not None:
                    effects_list = result_data.get("effects", [])
                    logger.info(f"批次 {batch_index + 1} 获取到 {len(effects_list)} 条效果数据")
                    
                    for effect in effects_list:
                        effect_product_id = str(effect.get("product_id", ""))
                        
                        # 处理表现数据
                        performance_data = {
                            "impression": int(effect.get("impression", 0)),
                            "click": int(effect.get("click", 0)),
                            "visitor": int(effect.get("visitor", 0)),
                            "order_count": int(effect.get("order", 0)),
                            "bookmark": int(effect.get("bookmark", 0)),
                            "compare": int(effect.get("compare", 0)),
                            "share": int(effect.get("share", 0)),
                            "feedback": int(effect.get("fb", 0)),
                            "ctr": 0,
                            "conversion_rate": 0
                        }
                        
                        # 计算CTR和转化率
                        if performance_data["impression"] > 0:
                            performance_data["ctr"] = float(performance_data["click"]) / float(performance_data["impression"]) * 100
                        if performance_data["visitor"] > 0:
                            performance_data["conversion_rate"] = float(performance_data["order_count"]) / float(performance_data["visitor"]) * 100
                        
                        # 获取产品名称
                        product_name = effect_product_id
                        existing_product = db.query(AlibabaProduct).filter(
                            AlibabaProduct.user_id == user_id,
                            AlibabaProduct.tenant_id == tenant_id,
                            AlibabaProduct.product_id == effect_product_id
                        ).first()
                        
                        if existing_product:
                            product_name = existing_product.product_name
                        
                        # 保存表现数据
                        saved_perf = self.save_product_performance(
                            db, user_id, tenant_id, performance_data,
                            effect_product_id, product_name, statistics_type, stat_date
                        )
                        saved_performance.append(saved_perf)
            
            logger.info(f"产品表现数据同步完成: {len(saved_performance)} 条记录")
            
            return {
                "products": len(product_ids) if product_ids else 0,
                "performance_records": len(saved_performance)
            }
            
        except Exception as e:
            logger.error(f"同步产品表现数据失败: {e}")
            raise

    def _process_single_performance_response(
        self,
        performance_response: Dict[str, Any],
        product_id: str,
        current_date: date,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str,
        saved_performance: List[Any]
    ) -> None:
        """
        处理单个产品的表现数据响应
        
        Args:
            performance_response: API响应数据
            product_id: 产品ID
            current_date: 当前日期
            db: 数据库会话
            user_id: 用户ID
            tenant_id: 租户ID
            statistics_type: 统计类型
            saved_performance: 保存的表现数据列表
        """
        # 解析产品表现响应
        # 支持两种响应格式：完整格式和简化格式（simplify=true）
        result_data = None
        if "alibaba_mydata_self_product_get_response" in performance_response:
            # 完整格式响应
            result_data = performance_response["alibaba_mydata_self_product_get_response"].get("result", {})
        elif "result" in performance_response:
            # 简化格式响应（simplify=true）
            result_data = performance_response.get("result", {})
        
        if result_data is not None:
            effects_list = result_data.get("effects", [])
            
            logger.info(f"产品 {product_id} 日期 {current_date} 获取到 {len(effects_list)} 条效果数据")
            
            # 如果没有效果数据，记录详细信息用于调试
            if len(effects_list) == 0:
                logger.info(f"产品 {product_id} 日期 {current_date} 无效果数据，result_data内容: {result_data}")
            
            for effect in effects_list:
                effect_product_id = str(effect.get("product_id", ""))
                
                # 处理表现数据 - 使用API文档中的字段名
                performance_data = {
                    "impression": int(effect.get("impression", 0)),
                    "click": int(effect.get("click", 0)),
                    "visitor": int(effect.get("visitor", 0)),
                    "order_count": int(effect.get("order", 0)),
                    "bookmark": int(effect.get("bookmark", 0)),
                    "compare": int(effect.get("compare", 0)),
                    "share": int(effect.get("share", 0)),
                    "feedback": int(effect.get("fb", 0)),
                    "ctr": 0,  # API响应中没有直接提供CTR
                    "conversion_rate": 0  # API响应中没有直接提供转化率
                }
                
                # 计算CTR和转化率
                if performance_data["impression"] > 0:
                    performance_data["ctr"] = float(performance_data["click"]) / float(performance_data["impression"]) * 100
                
                if performance_data["visitor"] > 0:
                    performance_data["conversion_rate"] = float(performance_data["order_count"]) / float(performance_data["visitor"]) * 100
                
                # 获取产品名称（如果现有产品中有的话）
                product_name = effect_product_id  # 默认使用产品ID
                existing_product = db.query(AlibabaProduct).filter(
                    AlibabaProduct.user_id == user_id,
                    AlibabaProduct.tenant_id == tenant_id,
                    AlibabaProduct.product_id == effect_product_id
                ).first()
                
                if existing_product:
                    product_name = existing_product.product_name
                
                # 保存表现数据
                saved_perf = self.save_product_performance(
                    db, user_id, tenant_id, performance_data,
                    effect_product_id, product_name, statistics_type, current_date
                )
                saved_performance.append(saved_perf)
            
            logger.info(f"同步产品 {product_id} 日期 {current_date}: {len(effects_list)} 条表现记录")
        else:
            logger.warning(f"产品 {product_id} 日期 {current_date} 的API响应格式异常: {performance_response}")

    def _get_next_date(self, current_date: date, statistics_type: str) -> date:
        """根据统计类型获取下一个日期"""
        if statistics_type == "day":
            return current_date + timedelta(days=1)
        elif statistics_type == "week":
            return current_date + timedelta(days=7)
        elif statistics_type == "month":
            # 简化处理，每月递增30天
            return current_date + timedelta(days=30)
        else:
            return current_date + timedelta(days=1)

    async def sync_date_range_from_alibaba(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str,
        access_token: str
    ) -> Dict[str, Any]:
        """从阿里国际站同步可用时间范围"""
        try:
            alibaba_service = AlibabaService(db)
            
            # 调用阿里巴巴API获取时间范围
            range_response = alibaba_service.get_product_performance_date_range(
                access_token=access_token,
                statistics_type=statistics_type
            )
            
            if range_response.get("error_response"):
                error_msg = range_response["error_response"].get("msg", "获取时间范围失败")
                logger.error(f"获取时间范围API错误: {error_msg}")
                raise Exception(f"获取时间范围失败: {error_msg}")
            
            # 解析时间范围响应
            # 支持两种响应格式：完整格式和简化格式（simplify=true）
            result_data = None
            if "alibaba_mydata_self_product_date_get_response" in range_response:
                # 完整格式响应
                result_data = range_response["alibaba_mydata_self_product_date_get_response"].get("result", {})
            elif "result" in range_response:
                # 简化格式响应（simplify=true）
                result_data = range_response.get("result", {})
            
            if result_data:
                return {
                    "start_date": result_data.get("start_date", ""),
                    "end_date": result_data.get("end_date", "")
                }
            
            return {}
            
        except Exception as e:
            logger.error(f"同步时间范围失败: {e}")
            raise 

    def _extract_main_image(self, main_image_data: Dict[str, Any]) -> str:
        """从主图数据中提取第一张图片URL"""
        if not main_image_data:
            return ""
        
        images = main_image_data.get("images", [])
        if isinstance(images, dict) and "string" in images:
            # 处理响应中的特殊格式
            string_list = images["string"]
            if isinstance(string_list, list) and len(string_list) > 0:
                # 解析可能包含JSON格式的字符串
                first_image = string_list[0]
                if first_image.startswith('["') and first_image.endswith('"'):
                    # 移除JSON字符串格式的引号
                    return first_image.replace('["', '').replace('"', '')
                return first_image
        elif isinstance(images, list) and len(images) > 0:
            return images[0]
        
        return "" 

    async def get_alibaba_product_list(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        access_token: str,
        page_size: int = 30,
        current_page: int = 1,
        status: str = None,
        display: str = None,
        category_id: str = None,
        subject: str = None,
        sync_to_db: bool = True
    ) -> Dict[str, Any]:
        """
        获取阿里巴巴商品列表
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            tenant_id: 租户ID
            access_token: 访问令牌
            page_size: 每页大小
            current_page: 当前页码
            status: 商品状态
            display: 上下架状态
            category_id: 类目ID
            subject: 商品名称
            sync_to_db: 是否同步到数据库
            
        Returns:
            商品列表数据
        """
        try:
            alibaba_service = AlibabaService(db)
            
            # 调用阿里巴巴API
            response = alibaba_service.get_icbu_product_list(
                access_token=access_token,
                page_size=page_size,
                current_page=current_page,
                status=status,
                display=display,
                category_id=category_id,
                subject=subject
            )
            
            if response.get("error_response"):
                error_msg = response["error_response"].get("msg", "获取商品列表失败")
                logger.error(f"获取商品列表API错误: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "products": [],
                    "pagination": {}
                }
            
            # 解析响应
            products_data = []
            pagination = {}
            
            # API直接返回结果，不需要alibaba_icbu_product_list_response包装层
            if "total_item" in response:
                # 解析分页信息
                pagination = {
                    "current_page": int(response.get("current_page", current_page)),
                    "page_size": int(response.get("page_size", page_size)),
                    "total_item": int(response.get("total_item", 0))
                }
                
                # 解析商品列表
                products_list = response.get("products", [])
                if not isinstance(products_list, list):
                    products_list = [products_list] if products_list else []
                
                for product in products_list:
                    product_data = self._parse_icbu_product_data(product)
                    products_data.append(product_data)
                
                # 如果需要同步到数据库
                if sync_to_db and products_data:
                    self.save_products(db, user_id, tenant_id, products_data)
                    logger.info(f"同步 {len(products_data)} 个商品到数据库")
                    
                logger.info(f"获取商品列表成功: 第{pagination['current_page']}页，本页{len(products_data)}个商品，总计{pagination['total_item']}个")
            else:
                # 如果有错误响应的处理
                logger.warning(f"未识别的响应格式: {list(response.keys())}")
            
            return {
                "success": True,
                "products": products_data,
                "pagination": pagination,
                "total_count": len(products_data)
            }
            
        except Exception as e:
            logger.error(f"获取商品列表失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "products": [],
                "pagination": {}
            }
    
    def _parse_icbu_product_data(self, product: Dict[str, Any]) -> Dict[str, Any]:
        """解析ICBU商品数据"""
        # 解析主图
        main_image_url = ""
        main_image = product.get("main_image", {})
        if main_image and "images" in main_image:
            images = main_image["images"]
            if isinstance(images, list) and len(images) > 0:
                main_image_url = images[0]
        
        # 解析关键词
        keywords = product.get("keywords", [])
        keywords_str = ", ".join(keywords) if isinstance(keywords, list) else str(keywords)
        
        return {
            "product_id": str(product.get("id", "")),
            "product_name": product.get("subject", ""),
            "product_title": product.get("subject", ""),
            "product_category": str(product.get("category_id", "")),
            "product_status": product.get("status", ""),
            "product_url": product.get("pc_detail_url", ""),
            "main_image_url": main_image_url,
            "description": product.get("subject", ""),
            "display": product.get("display", ""),
            "product_type": product.get("product_type", ""),
            "language": product.get("language", ""),
            "keywords": keywords_str,
            "group_id": str(product.get("group_id", "")),
            "group_name": product.get("group_name", ""),
            "owner_member": product.get("owner_member_display_name", ""),
            "gmt_create": product.get("gmt_create", ""),
            "gmt_modified": product.get("gmt_modified", ""),
            "red_model": product.get("red_model", ""),
            "is_specific": product.get("is_specific", False),
            "is_rts": product.get("is_rts", False),
            "smart_edit": product.get("smart_edit", False),
            "sub_market_type": product.get("sub_market_type", "")
        } 