<template>
  <el-dialog
    v-model="dialogVisible"
    title="PyTrends 配置管理"
    width="900px"
    :close-on-click-modal="false"
  >
    <div class="config-management">
      <!-- 配置列表 -->
      <div class="config-list-section">
        <div class="section-header">
          <div class="header-left">
            <el-icon class="header-icon"><Setting /></el-icon>
            <span>配置列表</span>
          </div>
          <el-button type="primary" @click="showCreateForm">
            <el-icon><Plus /></el-icon>
            新增配置
          </el-button>
        </div>

        <el-table :data="configList" v-loading="loading" stripe>
          <el-table-column prop="config_name" label="配置名称" min-width="120" />
          <el-table-column prop="language" label="语言" width="80" />
          <el-table-column prop="geo_location" label="地理位置" width="100" />
          <el-table-column prop="default_timeframe" label="默认时间范围" width="120" />
          <el-table-column prop="max_keywords_per_batch" label="批次大小" width="100">
            <template #default="{ row }">
              {{ row.max_keywords_per_batch }} 个/批
            </template>
          </el-table-column>
          <el-table-column prop="is_active" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.is_active ? 'success' : 'danger'" size="small">
                {{ row.is_active ? '激活' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link size="small" @click="testConfig(row)">
                测试
              </el-button>
              <el-button type="primary" link size="small" @click="editConfig(row)">
                编辑
              </el-button>
              <el-button 
                :type="row.is_active ? 'warning' : 'success'" 
                link 
                size="small" 
                @click="toggleConfig(row)"
              >
                {{ row.is_active ? '禁用' : '激活' }}
              </el-button>
              <el-button type="danger" link size="small" @click="deleteConfig(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 配置表单 -->
      <div class="config-form-section" v-if="showForm">
        <div class="section-header">
          <div class="header-left">
            <el-icon class="header-icon"><EditPen /></el-icon>
            <span>{{ isEdit ? '编辑配置' : '新增配置' }}</span>
          </div>
          <el-button @click="cancelForm">取消</el-button>
        </div>

        <el-form 
          :model="configForm" 
          :rules="rules" 
          ref="formRef" 
          label-width="120px"
          @submit.prevent="submitForm"
        >
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="配置名称" prop="config_name">
                <el-input v-model="configForm.config_name" placeholder="请输入配置名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="语言设置" prop="language">
                <el-select v-model="configForm.language" placeholder="选择语言">
                  <el-option label="中文 (zh-CN)" value="zh-CN" />
                  <el-option label="英文 (en-US)" value="en-US" />
                  <el-option label="日文 (ja-JP)" value="ja-JP" />
                  <el-option label="韩文 (ko-KR)" value="ko-KR" />
                  <el-option label="法文 (fr-FR)" value="fr-FR" />
                  <el-option label="德文 (de-DE)" value="de-DE" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="时区偏移" prop="timezone">
                <el-input-number 
                  v-model="configForm.timezone" 
                  :min="-720" 
                  :max="720"
                  placeholder="分钟"
                  style="width: 100%;"
                />
                <div class="help-text">
                  <small>时区偏移（分钟），如中国时区为480</small>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="地理位置" prop="geo_location">
                <el-select v-model="configForm.geo_location" placeholder="选择地理位置">
                  <el-option label="全球" value="" />
                  <el-option label="中国 (CN)" value="CN" />
                  <el-option label="美国 (US)" value="US" />
                  <el-option label="英国 (GB)" value="GB" />
                  <el-option label="德国 (DE)" value="DE" />
                  <el-option label="法国 (FR)" value="FR" />
                  <el-option label="日本 (JP)" value="JP" />
                  <el-option label="韩国 (KR)" value="KR" />
                  <el-option label="澳大利亚 (AU)" value="AU" />
                  <el-option label="加拿大 (CA)" value="CA" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="默认时间范围" prop="default_timeframe">
                <el-select v-model="configForm.default_timeframe" placeholder="选择时间范围">
                  <el-option label="过去12个月" value="today 12-m" />
                  <el-option label="过去5年" value="today 5-y" />
                  <el-option label="过去3个月" value="today 3-m" />
                  <el-option label="过去30天" value="today 1-m" />
                  <el-option label="过去7天" value="now 7-d" />
                  <el-option label="过去1天" value="now 1-d" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="批次大小" prop="max_keywords_per_batch">
                <el-input-number 
                  v-model="configForm.max_keywords_per_batch" 
                  :min="1" 
                  :max="5"
                  style="width: 100%;"
                />
                <div class="help-text">
                  <small>每批最大关键词数（1-5）</small>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="请求延迟" prop="request_delay">
                <el-input-number 
                  v-model="configForm.request_delay" 
                  :min="1" 
                  :max="10"
                  style="width: 100%;"
                />
                <div class="help-text">
                  <small>请求间隔延迟（秒）</small>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="重试次数" prop="retry_attempts">
                <el-input-number 
                  v-model="configForm.retry_attempts" 
                  :min="1" 
                  :max="5"
                  style="width: 100%;"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 代理设置 -->
          <el-divider content-position="left">代理设置</el-divider>
          
          <el-form-item label="使用代理">
            <el-switch v-model="configForm.use_proxy" />
          </el-form-item>

          <template v-if="configForm.use_proxy">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="代理主机" prop="proxy_host">
                  <el-input v-model="configForm.proxy_host" placeholder="代理服务器地址" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="代理端口" prop="proxy_port">
                  <el-input-number 
                    v-model="configForm.proxy_port" 
                    :min="1" 
                    :max="65535"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="8">
                <el-form-item label="代理类型" prop="proxy_type">
                  <el-select v-model="configForm.proxy_type" style="width: 100%;">
                    <el-option label="HTTP" value="http" />
                    <el-option label="HTTPS" value="https" />
                    <el-option label="SOCKS5" value="socks5" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="代理用户名" prop="proxy_username">
                  <el-input v-model="configForm.proxy_username" placeholder="可选" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="代理密码" prop="proxy_password">
                  <el-input 
                    v-model="configForm.proxy_password" 
                    type="password" 
                    placeholder="可选"
                    show-password 
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <el-form-item>
            <el-button type="primary" @click="submitForm" :loading="submitting">
              {{ isEdit ? '更新配置' : '创建配置' }}
            </el-button>
            <el-button @click="cancelForm">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, Plus, EditPen } from '@element-plus/icons-vue'
import { pyTrendsConfigService } from '@/services/keyword'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const formRef = ref()
const loading = ref(false)
const submitting = ref(false)
const showForm = ref(false)
const isEdit = ref(false)
const configList = ref([])
const currentConfig = ref(null)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 配置表单
const configForm = reactive({
  config_name: '',
  language: 'zh-CN',
  timezone: 480,
  geo_location: 'CN',
  default_timeframe: 'today 12-m',
  max_keywords_per_batch: 5,
  request_delay: 2,
  retry_attempts: 3,
  use_proxy: false,
  proxy_host: '',
  proxy_port: null,
  proxy_username: '',
  proxy_password: '',
  proxy_type: 'http',
  is_active: true
})

// 表单验证规则
const rules = {
  config_name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' },
    { min: 2, max: 50, message: '配置名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  language: [
    { required: true, message: '请选择语言', trigger: 'change' }
  ],
  timezone: [
    { required: true, message: '请输入时区偏移', trigger: 'blur' }
  ],
  geo_location: [
    { required: true, message: '请选择地理位置', trigger: 'change' }
  ],
  default_timeframe: [
    { required: true, message: '请选择默认时间范围', trigger: 'change' }
  ],
  max_keywords_per_batch: [
    { required: true, message: '请输入批次大小', trigger: 'blur' }
  ],
  request_delay: [
    { required: true, message: '请输入请求延迟', trigger: 'blur' }
  ],
  retry_attempts: [
    { required: true, message: '请输入重试次数', trigger: 'blur' }
  ],
  proxy_host: [
    {
 required: true,
message: '请输入代理主机',
trigger: 'blur', 
      validator: (rule, value, callback) => {
        if (configForm.use_proxy && !value) {
          callback(new Error('使用代理时请输入代理主机'))
        } else {
          callback()
        }
      }
    }
  ],
  proxy_port: [
    {
 required: true,
message: '请输入代理端口',
trigger: 'blur',
      validator: (rule, value, callback) => {
        if (configForm.use_proxy && !value) {
          callback(new Error('使用代理时请输入代理端口'))
        } else {
          callback()
        }
      }
    }
  ]
}

// 生命周期
onMounted(() => {
  loadConfigs()
})

// 方法
const loadConfigs = async () => {
  try {
    loading.value = true
    configList.value = await pyTrendsConfigService.getConfigs(false) // 获取所有配置
  } catch (error) {
    console.error('加载配置失败:', error)
    ElMessage.error('加载配置失败')
  } finally {
    loading.value = false
  }
}

const showCreateForm = () => {
  resetForm()
  isEdit.value = false
  showForm.value = true
}

const editConfig = (config) => {
  currentConfig.value = config
  
  // 填充表单数据
  Object.keys(configForm).forEach(key => {
    if (config[key] !== undefined) {
      configForm[key] = config[key]
    }
  })
  
  isEdit.value = true
  showForm.value = true
}

const testConfig = async (config) => {
  try {
    ElMessage.info('正在测试连接...')
    const result = await pyTrendsConfigService.testConnection(config.id)
    
    if (result.success) {
      ElMessage.success('连接测试成功！')
    } else {
      ElMessage.error(`连接测试失败: ${result.message}`)
    }
  } catch (error) {
    console.error('测试连接失败:', error)
    ElMessage.error('测试连接失败')
  }
}

const toggleConfig = async (config) => {
  try {
    await pyTrendsConfigService.toggleActiveStatus(config.id)
    config.is_active = !config.is_active
    ElMessage.success(`配置已${config.is_active ? '激活' : '禁用'}`)
  } catch (error) {
    console.error('切换状态失败:', error)
    ElMessage.error('切换状态失败')
  }
}

const deleteConfig = async (config) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除配置 "${config.config_name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await pyTrendsConfigService.deleteConfig(config.id)
    ElMessage.success('配置删除成功')
    loadConfigs()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除配置失败:', error)
      ElMessage.error('删除配置失败')
    }
  }
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    if (isEdit.value) {
      await pyTrendsConfigService.updateConfig(currentConfig.value.id, configForm)
      ElMessage.success('配置更新成功')
    } else {
      await pyTrendsConfigService.createConfig(configForm)
      ElMessage.success('配置创建成功')
    }
    
    showForm.value = false
    loadConfigs()
    emit('success')
  } catch (error) {
    console.error('保存配置失败:', error)
    if (error.response?.data?.detail) {
      ElMessage.error(`保存失败: ${error.response.data.detail}`)
    } else {
      ElMessage.error('保存配置失败')
    }
  } finally {
    submitting.value = false
  }
}

const cancelForm = () => {
  showForm.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(configForm, {
    config_name: '',
    language: 'zh-CN',
    timezone: 480,
    geo_location: 'CN',
    default_timeframe: 'today 12-m',
    max_keywords_per_batch: 5,
    request_delay: 2,
    retry_attempts: 3,
    use_proxy: false,
    proxy_host: '',
    proxy_port: null,
    proxy_username: '',
    proxy_password: '',
    proxy_type: 'http',
    is_active: true
  })
  
  currentConfig.value = null
  
  // 清除表单验证
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}
</script>

<style scoped>
.config-management {
  max-height: 600px;
  overflow-y: auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-icon {
  margin-right: 8px;
  color: #409eff;
}

.config-list-section,
.config-form-section {
  margin-bottom: 24px;
}

.help-text {
  margin-top: 4px;
}

.help-text small {
  color: #909399;
  font-size: 12px;
}

:deep(.el-divider__text) {
  background-color: var(--el-bg-color);
  color: var(--el-text-color-primary);
  font-weight: 500;
}
</style> 