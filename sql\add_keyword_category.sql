-- ============================================================
-- 添加关键词分类字段的数据库更新脚本
-- 创建时间: 2025-01-27
-- 说明: 为关键词库表添加分类字段，支持关键词组功能
-- ============================================================

-- 1. 为 keyword_library 表添加 category 字段
ALTER TABLE `keyword_library` 
ADD COLUMN `category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关键词分类/组名称' 
AFTER `tags`;

-- 2. 为 category 字段添加索引，提高查询性能
ALTER TABLE `keyword_library` 
ADD INDEX `idx_category` (`category` ASC);

-- 3. 更新全文索引，包含新的 category 字段
ALTER TABLE `keyword_library` 
DROP INDEX `idx_keyword_tags_fulltext`;

ALTER TABLE `keyword_library` 
ADD FULLTEXT INDEX `idx_keyword_tags_category_fulltext` (`keyword_name`, `tags`, `category`);

-- 4. 验证表结构
SHOW CREATE TABLE `keyword_library`;

-- 5. 显示所有字段
DESCRIBE `keyword_library`;

-- 6. 检查索引
SHOW INDEX FROM `keyword_library`;

-- 7. 添加一些示例分类数据（可选）
-- UPDATE `keyword_library` SET `category` = '电商核心词' WHERE `keyword_name` LIKE '%电商%' OR `keyword_name` LIKE '%购物%';
-- UPDATE `keyword_library` SET `category` = 'B2B相关词' WHERE `keyword_name` LIKE '%批发%' OR `keyword_name` LIKE '%供应商%';
-- UPDATE `keyword_library` SET `category` = '产品描述词' WHERE `keyword_name` LIKE '%质量%' OR `keyword_name` LIKE '%材料%';

-- 8. 查看分类统计（可选）
SELECT 
    `category`,
    COUNT(*) as keyword_count,
    AVG(`avg_monthly_searches`) as avg_searches
FROM `keyword_library` 
WHERE `category` IS NOT NULL
GROUP BY `category`
ORDER BY keyword_count DESC;

-- 9. 执行完成提示
SELECT '关键词分类字段添加完成！' as result; 