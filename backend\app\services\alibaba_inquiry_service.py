from typing import List, Optional, Dict
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, func
from datetime import date, datetime, timedelta
from decimal import Decimal
import logging

from app.models.alibaba_inquiry import (
    AlibabaTopIndustry,
    AlibabaInquiryPerformance,
    AlibabaDataPeriod,
    AlibabaInquirySummary
)
from app.schemas.alibaba_inquiry import (
    AlibabaTopIndustryCreate,
    AlibabaInquiryPerformanceCreate,
    AlibabaDataPeriodCreate,
    AlibabaInquirySummaryCreate,
    IndustryPerformanceDetail,
    DashboardData
)
from app.services.alibaba_service import AlibabaAPIService

logger = logging.getLogger(__name__)

class AlibabaInquiryService:
    """阿里询盘统计服务"""
    
    def __init__(self):
        self.alibaba_api = AlibabaAPIService()

    def get_data_periods(
        self, 
        db: Session, 
        user_id: int, 
        tenant_id: str
    ) -> List[AlibabaDataPeriod]:
        """获取可用数据周期"""
        return db.query(AlibabaDataPeriod).filter(
            and_(
                AlibabaDataPeriod.user_id == user_id,
                AlibabaDataPeriod.tenant_id == tenant_id,
                AlibabaDataPeriod.is_available == True
            )
        ).order_by(desc(AlibabaDataPeriod.end_date)).all()

    def get_top_industries(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        start_date: date,
        end_date: date
    ) -> List[AlibabaTopIndustry]:
        """获取TOP行业列表"""
        return db.query(AlibabaTopIndustry).filter(
            and_(
                AlibabaTopIndustry.user_id == user_id,
                AlibabaTopIndustry.tenant_id == tenant_id,
                AlibabaTopIndustry.data_start_date >= start_date,
                AlibabaTopIndustry.data_end_date <= end_date
            )
        ).order_by(AlibabaTopIndustry.main_category.desc()).all()

    def get_inquiry_performance(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        start_date: date,
        end_date: date,
        industry_id: Optional[str] = None
    ) -> List[AlibabaInquiryPerformance]:
        """获取询盘流量行业表现数据"""
        query = db.query(AlibabaInquiryPerformance).filter(
            and_(
                AlibabaInquiryPerformance.user_id == user_id,
                AlibabaInquiryPerformance.tenant_id == tenant_id,
                AlibabaInquiryPerformance.data_start_date >= start_date,
                AlibabaInquiryPerformance.data_end_date <= end_date
            )
        )
        
        if industry_id:
            query = query.filter(AlibabaInquiryPerformance.industry_id == industry_id)
            
        return query.order_by(desc(AlibabaInquiryPerformance.fb)).all()

    def create_data_period(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        period_data: AlibabaDataPeriodCreate
    ) -> AlibabaDataPeriod:
        """创建数据周期记录"""
        # 检查是否已存在
        existing = db.query(AlibabaDataPeriod).filter(
            and_(
                AlibabaDataPeriod.user_id == user_id,
                AlibabaDataPeriod.tenant_id == tenant_id,
                AlibabaDataPeriod.start_date == period_data.start_date,
                AlibabaDataPeriod.end_date == period_data.end_date
            )
        ).first()
        
        if existing:
            return existing
            
        db_period = AlibabaDataPeriod(
            user_id=user_id,
            tenant_id=tenant_id,
            start_date=period_data.start_date,
            end_date=period_data.end_date,
            is_available=True
        )
        db.add(db_period)
        db.commit()
        db.refresh(db_period)
        return db_period

    def save_top_industries(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        industries_data: List[Dict],
        start_date: date,
        end_date: date
    ) -> List[AlibabaTopIndustry]:
        """保存TOP行业列表数据"""
        saved_industries = []
        
        for industry_data in industries_data:
            # 检查是否已存在
            existing = db.query(AlibabaTopIndustry).filter(
                and_(
                    AlibabaTopIndustry.user_id == user_id,
                    AlibabaTopIndustry.tenant_id == tenant_id,
                    AlibabaTopIndustry.industry_id == industry_data["industry_id"],
                    AlibabaTopIndustry.data_start_date == start_date,
                    AlibabaTopIndustry.data_end_date == end_date
                )
            ).first()
            
            if existing:
                # 更新现有记录
                existing.industry_desc = industry_data.get("industry_desc", "")
                existing.main_category = industry_data.get("main_category", False)
                saved_industries.append(existing)
            else:
                # 创建新记录
                db_industry = AlibabaTopIndustry(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    industry_id=industry_data["industry_id"],
                    industry_desc=industry_data.get("industry_desc", ""),
                    main_category=industry_data.get("main_category", False),
                    data_start_date=start_date,
                    data_end_date=end_date
                )
                db.add(db_industry)
                saved_industries.append(db_industry)
        
        db.commit()
        return saved_industries

    def save_inquiry_performance(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        performance_data: Dict,
        industry_id: str,
        start_date: date,
        end_date: date
    ) -> AlibabaInquiryPerformance:
        """保存询盘流量行业表现数据"""
        # 检查是否已存在
        existing = db.query(AlibabaInquiryPerformance).filter(
            and_(
                AlibabaInquiryPerformance.user_id == user_id,
                AlibabaInquiryPerformance.tenant_id == tenant_id,
                AlibabaInquiryPerformance.industry_id == industry_id,
                AlibabaInquiryPerformance.data_start_date == start_date,
                AlibabaInquiryPerformance.data_end_date == end_date
            )
        ).first()
        
        if existing:
            # 更新现有记录
            existing.clk = int(performance_data.get("clk", 0))
            existing.clk_rate = performance_data.get("clk_rate", "0")
            existing.fb = int(performance_data.get("fb", 0))
            existing.imps = int(performance_data.get("imps", 0))
            existing.reply = performance_data.get("reply", "0")
            existing.visitor = int(performance_data.get("visitor", 0))
            db_performance = existing
        else:
            # 创建新记录
            db_performance = AlibabaInquiryPerformance(
                user_id=user_id,
                tenant_id=tenant_id,
                industry_id=industry_id,
                industry_desc=performance_data.get("industry_desc", ""),
                main_category=performance_data.get("main_category", False),
                data_start_date=start_date,
                data_end_date=end_date,
                clk=int(performance_data.get("clk", 0)),
                clk_rate=performance_data.get("clk_rate", "0"),
                fb=int(performance_data.get("fb", 0)),
                imps=int(performance_data.get("imps", 0)),
                reply=performance_data.get("reply", "0"),
                visitor=int(performance_data.get("visitor", 0))
            )
            db.add(db_performance)
        
        db.commit()
        db.refresh(db_performance)
        return db_performance

    def calculate_summary(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        start_date: date,
        end_date: date,
        summary_type: str = "custom"
    ) -> AlibabaInquirySummary:
        """计算汇总统计数据"""
        # 获取该时间段内的所有行业表现数据
        performance_data = self.get_inquiry_performance(
            db, user_id, tenant_id, start_date, end_date
        )
        
        if not performance_data:
            # 如果没有数据，返回空的汇总
            empty_summary = AlibabaInquirySummary(
                user_id=user_id,
                tenant_id=tenant_id,
                summary_date=end_date,
                summary_type=summary_type,
                total_industries=0,
                main_industries=0,
                total_clk=0,
                total_fb=0,
                total_imps=0,
                total_visitor=0,
                avg_clk_rate=Decimal("0.00"),
                avg_reply_rate=Decimal("0.00")
            )
            db.add(empty_summary)
            db.commit()
            db.refresh(empty_summary)
            return empty_summary
        
        # 计算汇总指标
        total_industries = len(performance_data)
        main_industries = len([p for p in performance_data if p.main_category])
        total_clk = sum(p.clk for p in performance_data)
        total_fb = sum(p.fb for p in performance_data)
        total_imps = sum(p.imps for p in performance_data)
        total_visitor = sum(p.visitor for p in performance_data)
        
        # 计算平均点击率
        clk_rates = []
        reply_rates = []
        for p in performance_data:
            if p.clk_rate and p.clk_rate != "0":
                try:
                    clk_rates.append(float(p.clk_rate))
                except:
                    pass
            if p.reply and p.reply != "0":
                try:
                    reply_rates.append(float(p.reply))
                except:
                    pass
        
        avg_clk_rate = Decimal(str(sum(clk_rates) / len(clk_rates))) if clk_rates else Decimal("0.00")
        avg_reply_rate = Decimal(str(sum(reply_rates) / len(reply_rates))) if reply_rates else Decimal("0.00")
        
        # 检查是否已存在相同的汇总记录
        existing_summary = db.query(AlibabaInquirySummary).filter(
            and_(
                AlibabaInquirySummary.user_id == user_id,
                AlibabaInquirySummary.tenant_id == tenant_id,
                AlibabaInquirySummary.summary_date == end_date,
                AlibabaInquirySummary.summary_type == summary_type
            )
        ).first()
        
        if existing_summary:
            # 更新现有记录
            existing_summary.total_industries = total_industries
            existing_summary.main_industries = main_industries
            existing_summary.total_clk = total_clk
            existing_summary.total_fb = total_fb
            existing_summary.total_imps = total_imps
            existing_summary.total_visitor = total_visitor
            existing_summary.avg_clk_rate = avg_clk_rate
            existing_summary.avg_reply_rate = avg_reply_rate
            db.commit()
            db.refresh(existing_summary)
            return existing_summary
        else:
            # 创建新记录
            summary = AlibabaInquirySummary(
                user_id=user_id,
                tenant_id=tenant_id,
                summary_date=end_date,
                summary_type=summary_type,
                total_industries=total_industries,
                main_industries=main_industries,
                total_clk=total_clk,
                total_fb=total_fb,
                total_imps=total_imps,
                total_visitor=total_visitor,
                avg_clk_rate=avg_clk_rate,
                avg_reply_rate=avg_reply_rate
            )
            db.add(summary)
            db.commit()
            db.refresh(summary)
            return summary

    def get_dashboard_data(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        days: int = 30
    ) -> DashboardData:
        """获取仪表板数据"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        # 获取汇总数据
        summary = self.calculate_summary(
            db, user_id, tenant_id, start_date, end_date, "dashboard"
        )
        
        # 获取表现最佳的行业（按询盘数排序）
        top_performance = self.get_inquiry_performance(
            db, user_id, tenant_id, start_date, end_date
        )[:10]  # 取前10
        
        top_industries = self.get_top_industries(
            db, user_id, tenant_id, start_date, end_date
        )
        
        # 组合详细数据
        top_performing_industries = []
        for performance in top_performance:
            industry_info = next(
                (ind for ind in top_industries if ind.industry_id == performance.industry_id),
                None
            )
            if industry_info:
                conversion_rate = (performance.fb / performance.clk * 100) if performance.clk > 0 else 0.0
                engagement_rate = (performance.visitor / performance.imps * 100) if performance.imps > 0 else 0.0
                
                top_performing_industries.append(IndustryPerformanceDetail(
                    industry_info=industry_info,
                    performance=performance,
                    conversion_rate=conversion_rate,
                    engagement_rate=engagement_rate
                ))
        
        # 获取趋势数据（按周汇总）
        trend_data = []
        for i in range(4):  # 最近4周
            week_end = end_date - timedelta(days=i*7)
            week_start = week_end - timedelta(days=6)
            week_summary = self.calculate_summary(
                db, user_id, tenant_id, week_start, week_end, "weekly"
            )
            trend_data.append(week_summary)
        
        trend_data.reverse()  # 按时间顺序排列
        
        # 行业分布（主营 vs 非主营）
        industry_distribution = {
            "main_industries": summary.main_industries,
            "other_industries": summary.total_industries - summary.main_industries
        }
        
        return DashboardData(
            summary=summary,
            top_performing_industries=top_performing_industries,
            trend_data=trend_data,
            industry_distribution=industry_distribution
        )

    async def sync_data_from_alibaba(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        start_date: date,
        end_date: date,
        access_token: str
    ) -> Dict[str, int]:
        """从阿里国际站同步数据"""
        try:
            # 1. 同步数据周期
            period_data = AlibabaDataPeriodCreate(
                start_date=start_date,
                end_date=end_date
            )
            self.create_data_period(db, user_id, tenant_id, period_data)
            
            # 2. 获取并保存TOP行业列表
            # 这里需要调用实际的阿里巴巴API
            # industries_response = await self.alibaba_api.get_top_industries(
            #     access_token, start_date.isoformat(), end_date.isoformat()
            # )
            
            # 模拟数据用于演示
            mock_industries = [
                {"industry_id": "111", "industry_desc": "All", "main_category": True},
                {"industry_id": "200003034", "industry_desc": "Electronics", "main_category": True},
                {"industry_id": "200003539", "industry_desc": "Machinery", "main_category": False},
            ]
            
            saved_industries = self.save_top_industries(
                db, user_id, tenant_id, mock_industries, start_date, end_date
            )
            
            # 3. 为每个行业获取并保存表现数据
            saved_performance = []
            for industry in saved_industries:
                # performance_response = await self.alibaba_api.get_inquiry_performance(
                #     access_token, start_date.isoformat(), end_date.isoformat(), industry.industry_id
                # )
                
                # 模拟表现数据
                mock_performance = {
                    "clk": 100 + int(industry.industry_id) % 100,
                    "clk_rate": "2.5",
                    "fb": 15 + int(industry.industry_id) % 20,
                    "imps": 5000 + int(industry.industry_id) % 1000,
                    "reply": "80.5",
                    "visitor": 300 + int(industry.industry_id) % 150,
                    "industry_desc": industry.industry_desc,
                    "main_category": industry.main_category
                }
                
                performance = self.save_inquiry_performance(
                    db, user_id, tenant_id, mock_performance, 
                    industry.industry_id, start_date, end_date
                )
                saved_performance.append(performance)
            
            return {
                "industries": len(saved_industries),
                "performance_records": len(saved_performance)
            }
            
        except Exception as e:
            logger.error(f"同步阿里巴巴数据失败: {e}")
            raise 