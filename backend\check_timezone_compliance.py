#!/usr/bin/env python3
"""
检查时区统一方案的完整性

检查所有文件是否都正确应用了UTC + 前端本地化方案
"""

import os
import re
import glob

def check_file_compliance(file_path):
    """检查单个文件的时区合规性"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否使用了已废弃的函数
        if 'datetime.utcnow()' in content:
            issues.append("使用了已废弃的 datetime.utcnow()")
        
        if 'func.now()' in content:
            issues.append("使用了 func.now() 而不是 utc_now()")
        
        if 'TIMESTAMP' in content and 'Column(' in content:
            issues.append("使用了 TIMESTAMP 而不是 DateTime(timezone=True)")
        
        if 'DateTime,' in content and 'Column(' in content and 'timezone=True' not in content and 'BaseModel' not in content:
            issues.append("DateTime字段缺少 timezone=True")
        
        if 'datetime.now()' in content and 'utc_now' not in content:
            issues.append("使用了 datetime.now() 而不是 utc_now()")
        
        # 检查是否正确导入了时区工具函数
        if ('utc_now(' in content or 'to_iso_string(' in content) and 'from app.utils.datetime_utils import' not in content:
            issues.append("使用了时区函数但没有正确导入")
        
        return issues
        
    except Exception as e:
        return [f"文件读取错误: {e}"]

def check_backend_compliance():
    """检查后端文件合规性"""
    print("🔍 检查后端时区合规性...")
    
    # 检查模型文件
    model_files = glob.glob('app/models/*.py')
    api_files = glob.glob('app/api/**/*.py', recursive=True)
    service_files = glob.glob('app/services/*.py')
    
    all_files = model_files + api_files + service_files
    
    total_issues = 0
    
    for file_path in all_files:
        if '__pycache__' in file_path or '.backup' in file_path:
            continue
            
        issues = check_file_compliance(file_path)
        if issues:
            print(f"\n❌ {file_path}:")
            for issue in issues:
                print(f"   - {issue}")
            total_issues += len(issues)
        else:
            print(f"✅ {file_path}")
    
    return total_issues

def check_frontend_compliance():
    """检查前端文件合规性"""
    print("\n🔍 检查前端时区合规性...")
    
    # 检查Vue文件中的时间格式化
    vue_files = glob.glob('../frontend/src/**/*.vue', recursive=True)
    js_files = glob.glob('../frontend/src/**/*.js', recursive=True)
    
    all_files = vue_files + js_files
    
    total_issues = 0
    
    for file_path in all_files:
        if 'node_modules' in file_path or '.backup' in file_path:
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            issues = []
            
            # 检查是否使用了旧的时间格式化方法
            if 'toLocaleString(' in content and 'timezone.js' not in file_path:
                if 'import' not in content or 'timezone' not in content:
                    issues.append("可能使用了旧的时间格式化方法")
            
            if 'new Date(' in content and 'formatDateTime' not in content and 'timezone.js' not in file_path:
                if 'test-timezone.html' not in file_path:
                    issues.append("可能需要使用统一的时间格式化函数")
            
            if issues:
                print(f"\n⚠️  {file_path}:")
                for issue in issues:
                    print(f"   - {issue}")
                total_issues += len(issues)
            else:
                print(f"✅ {file_path}")
                
        except Exception as e:
            print(f"❌ {file_path}: 读取错误 - {e}")
            total_issues += 1
    
    return total_issues

def check_environment_config():
    """检查环境变量配置"""
    print("\n🔍 检查环境变量配置...")
    
    issues = []
    
    # 检查后端环境变量
    backend_env = '../.env'
    if os.path.exists(backend_env):
        with open(backend_env, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'SYSTEM_TIMEZONE_OFFSET' not in content:
            issues.append("后端 .env 缺少 SYSTEM_TIMEZONE_OFFSET 配置")
        
        if 'SYSTEM_TIMEZONE_NAME' not in content:
            issues.append("后端 .env 缺少 SYSTEM_TIMEZONE_NAME 配置")
    else:
        issues.append("后端 .env 文件不存在")
    
    # 检查前端环境变量
    frontend_env = '../frontend/.env'
    if os.path.exists(frontend_env):
        with open(frontend_env, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'VITE_SYSTEM_TIMEZONE_OFFSET' not in content:
            issues.append("前端 .env 缺少 VITE_SYSTEM_TIMEZONE_OFFSET 配置")
        
        if 'VITE_SYSTEM_TIMEZONE_NAME' not in content:
            issues.append("前端 .env 缺少 VITE_SYSTEM_TIMEZONE_NAME 配置")
    else:
        issues.append("前端 .env 文件不存在")
    
    if issues:
        print("❌ 环境变量配置问题:")
        for issue in issues:
            print(f"   - {issue}")
    else:
        print("✅ 环境变量配置正确")
    
    return len(issues)

def check_core_files():
    """检查核心文件是否存在"""
    print("\n🔍 检查核心文件...")
    
    core_files = [
        ('app/utils/datetime_utils.py', '后端时间工具函数'),
        ('../frontend/src/utils/timezone.js', '前端时区工具函数'),
        ('../frontend/src/utils/format.js', '前端格式化函数'),
    ]
    
    missing_files = []
    
    for file_path, description in core_files:
        if os.path.exists(file_path):
            print(f"✅ {description}: {file_path}")
        else:
            print(f"❌ {description}: {file_path} - 文件不存在")
            missing_files.append(file_path)
    
    return len(missing_files)

def main():
    """主函数"""
    print("🕐 UTC + 前端本地化时区统一方案合规性检查")
    print("=" * 60)
    
    # 切换到backend目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    total_issues = 0
    
    # 检查核心文件
    total_issues += check_core_files()
    
    # 检查环境变量配置
    total_issues += check_environment_config()
    
    # 检查后端合规性
    total_issues += check_backend_compliance()
    
    # 检查前端合规性
    total_issues += check_frontend_compliance()
    
    print("\n" + "=" * 60)
    
    if total_issues == 0:
        print("🎉 恭喜！所有文件都已正确应用时区统一方案！")
        print("\n✅ 检查结果:")
        print("- 后端统一使用UTC时间")
        print("- 数据库存储带时区信息")
        print("- API返回标准ISO格式")
        print("- 前端自动本地化显示")
        print("- 环境变量配置完整")
    else:
        print(f"⚠️  发现 {total_issues} 个问题需要修复")
        print("\n建议:")
        print("1. 修复上述问题")
        print("2. 重新运行此检查脚本")
        print("3. 运行测试验证功能")
    
    return total_issues

if __name__ == "__main__":
    exit_code = main()
    exit(0 if exit_code == 0 else 1)
