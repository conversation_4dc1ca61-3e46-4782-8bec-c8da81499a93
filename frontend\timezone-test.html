<!DOCTYPE html>
<html>
<head>
    <title>时区转换测试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>时区转换测试</h1>
    
    <div>
        <h2>当前时间信息</h2>
        <p>浏览器本地时间: <span id="browserLocal"></span></p>
        <p>浏览器UTC时间: <span id="browserUTC"></span></p>
        <p>浏览器时区偏移: <span id="browserOffset"></span></p>
    </div>
    
    <div>
        <h2>测试时间转换</h2>
        <input type="datetime-local" id="testInput" value="2025-06-09T01:00">
        <button onclick="testConversion()">测试转换</button>
        <div id="result"></div>
    </div>

    <script>
        // 模拟前端的时区配置
        function getSystemTimezoneConfig() {
            return {
                offset: 8,
                name: 'Asia/Shanghai',
                offsetString: 'UTC+8'
            }
        }

        // 模拟前端的toUTCString函数
        function toUTCString(localDate) {
            if (!localDate || isNaN(localDate.getTime())) {
                return null
            }

            try {
                const config = getSystemTimezoneConfig()

                // 获取表单中Date对象的各个组件（这些被视为系统时区的本地时间）
                const year = localDate.getFullYear()
                const month = localDate.getMonth()
                const date = localDate.getDate()
                const hours = localDate.getHours()
                const minutes = localDate.getMinutes()
                const seconds = localDate.getSeconds()

                // 创建一个表示系统时区时间的Date对象
                // 使用Date.UTC创建UTC时间，然后加上时区偏移来模拟本地时间
                const systemLocalTime = new Date(Date.UTC(year, month, date, hours, minutes, seconds))
                
                // 转换为真正的UTC时间：减去时区偏移
                const offsetMs = config.offset * 60 * 60 * 1000
                const utcTime = new Date(systemLocalTime.getTime() - offsetMs)

                console.log(`时间转换调试:`)
                console.log(`  用户选择的时间: ${year}-${String(month+1).padStart(2,'0')}-${String(date).padStart(2,'0')} ${String(hours).padStart(2,'0')}:${String(minutes).padStart(2,'0')}:${String(seconds).padStart(2,'0')} (${config.name})`)
                console.log(`  系统时区偏移: UTC${config.offset >= 0 ? '+' : ''}${config.offset}`)
                console.log(`  转换后的UTC时间: ${utcTime.toISOString()}`)

                return utcTime.toISOString()
            } catch (error) {
                console.warn('转换本地时间为UTC时间失败:', localDate, error)
                return null
            }
        }

        // 显示当前时间信息
        function showCurrentTime() {
            const now = new Date()
            document.getElementById('browserLocal').textContent = now.toString()
            document.getElementById('browserUTC').textContent = now.toISOString()
            document.getElementById('browserOffset').textContent = `${now.getTimezoneOffset()} 分钟`
        }

        // 测试转换
        function testConversion() {
            const input = document.getElementById('testInput')
            const testDate = new Date(input.value)
            
            const result = document.getElementById('result')
            result.innerHTML = `
                <h3>转换结果</h3>
                <p>输入值: ${input.value}</p>
                <p>JavaScript Date对象: ${testDate.toString()}</p>
                <p>Date对象的UTC表示: ${testDate.toISOString()}</p>
                <p>toUTCString转换结果: ${toUTCString(testDate)}</p>
                <h4>期望结果</h4>
                <p>如果输入是北京时间 2025-06-09 01:00:00</p>
                <p>对应的UTC时间应该是: 2025-06-08 17:00:00Z</p>
            `
        }

        // 页面加载时显示当前时间
        showCurrentTime()
        setInterval(showCurrentTime, 1000)
    </script>
</body>
</html>
