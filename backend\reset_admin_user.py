#!/usr/bin/env python3
"""
重置管理员用户脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app.models.user import User
from app.models.tenant import Tenant
from app.models.role import Role
from app.core.security import get_password_hash, verify_password

def reset_admin_user():
    """重置管理员用户"""
    db = SessionLocal()
    try:
        print("开始重置管理员用户...")
        
        # 删除现有的admin用户
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            print(f"删除现有用户: {existing_user.email}")
            db.delete(existing_user)
            db.commit()
        
        # 确保默认租户存在
        tenant = db.query(Tenant).filter(Tenant.id == "default").first()
        if not tenant:
            print("创建默认租户...")
            tenant = Tenant(
                id="default",
                name="默认租户",
                description="系统默认租户",
                is_active=True
            )
            db.add(tenant)
            db.commit()
            print("默认租户创建成功")
        
        # 确保管理员角色存在
        role = db.query(Role).filter(Role.name == "超级管理员", Role.tenant_id == "default").first()
        if not role:
            print("创建超级管理员角色...")
            role = Role(
                name="超级管理员",
                permissions=["*"],
                tenant_id="default"
            )
            db.add(role)
            db.commit()
            db.refresh(role)
            print("超级管理员角色创建成功")
        
        # 生成正确的密码哈希
        password = "admin123"
        password_hash = get_password_hash(password)
        print(f"生成密码哈希: {password_hash}")
        
        # 创建新的管理员用户
        new_user = User(
            email="<EMAIL>",
            hashed_password=password_hash,
            full_name="系统管理员",
            is_active=True,
            is_superuser=True,
            tenant_id="default",
            role_id=role.id
        )
        
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        print(f"创建新用户成功: {new_user.email}")
        print(f"用户ID: {new_user.id}")
        print(f"是否激活: {new_user.is_active}")
        print(f"是否超级用户: {new_user.is_superuser}")
        print(f"租户ID: {new_user.tenant_id}")
        print(f"角色ID: {new_user.role_id}")
        
        # 验证密码
        if verify_password(password, new_user.hashed_password):
            print("✅ 密码验证成功！")
            return True
        else:
            print("❌ 密码验证失败！")
            return False
            
    except Exception as e:
        print(f"重置过程中出错: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def test_login():
    """测试登录功能"""
    print("\n测试登录功能...")
    
    # 这里可以添加API调用测试
    import requests
    
    try:
        # 准备登录数据
        login_data = {
            'username': '<EMAIL>',
            'password': 'admin123'
        }
        
        # 发送登录请求
        response = requests.post(
            'http://localhost:8000/api/v1/auth/login/access-token',
            data=login_data
        )
        
        if response.status_code == 200:
            print("✅ API登录测试成功！")
            token_data = response.json()
            print(f"获得token: {token_data.get('access_token', '')[:20]}...")
            return True
        else:
            print(f"❌ API登录失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API测试出错: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("重置管理员用户脚本")
    print("=" * 50)
    
    success = reset_admin_user()
    
    if success:
        print("\n✅ 管理员用户重置成功！")
        
        # 测试API登录
        if test_login():
            print("\n🎉 所有测试通过！现在可以使用以下信息登录：")
            print("邮箱: <EMAIL>")
            print("密码: admin123")
        else:
            print("\n⚠️  用户创建成功，但API测试失败。请检查后端服务是否正常运行。")
    else:
        print("\n❌ 管理员用户重置失败！")
        sys.exit(1) 