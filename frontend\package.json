{"name": "aicbec-frontend", "version": "0.1.0", "private": true, "scripts": {"serve": "node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js lint --fix && node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js serve", "serve:no-client": "node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js lint --fix && cross-env WDS_SOCKET_HOST=0.0.0.0 WDS_SOCKET_PORT=0 node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js serve", "serve:https": "node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js lint --fix && cross-env HTTPS=true node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js serve", "serve:pure": "node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js serve", "build": "node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js lint --fix && node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js build", "lint": "node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js lint", "lint:fix": "node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js lint --fix", "format": "prettier --write \"src/**/*.{js,vue,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{js,vue,json,css,scss,md}\"", "dev": "node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js lint --fix && node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js serve", "start": "powershell -ExecutionPolicy Bypass -File start.ps1"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^0.24.0", "core-js": "^3.19.1", "echarts": "^5.6.0", "element-plus": "^2.2.30", "path-browserify": "^1.0.1", "vue": "^3.2.22", "vue-router": "^4.0.12", "vuex": "^4.0.2"}, "devDependencies": {"@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0-beta.7", "@vue/cli-plugin-eslint": "~5.0.0-beta.7", "@vue/cli-plugin-router": "~5.0.0-beta.7", "@vue/cli-plugin-vuex": "~5.0.0-beta.7", "@vue/cli-service": "~5.0.0-beta.7", "@vue/compiler-sfc": "^3.2.22", "@vue/eslint-config-standard": "^6.1.0", "eslint": "^7.32.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.1", "eslint-plugin-vue": "^7.0.0", "sass": "^1.43.4", "sass-loader": "^12.3.0"}}