<template>
  <div class="ali-monthly-report">
    <div class="page-header">
      <h2>阿里月报</h2>
      <p>查看阿里国际站每月运营数据报表及趋势分析</p>
    </div>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>本月概览</span>
              <el-date-picker
                v-model="selectedMonth"
                type="month"
                placeholder="选择月份"
                format="YYYY年MM月"
                value-format="YYYY-MM"
                @change="handleMonthChange"
              />
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="6">
              <div class="stat-card primary">
                <div class="stat-icon">📈</div>
                <div class="stat-content">
                  <div class="stat-number">{{ monthlyStats.inquiries }}</div>
                  <div class="stat-label">本月询盘</div>
                  <div class="stat-change positive">+{{ monthlyStats.inquiriesGrowth }}%</div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="stat-card success">
                <div class="stat-icon">👁️</div>
                <div class="stat-content">
                  <div class="stat-number">{{ monthlyStats.views }}</div>
                  <div class="stat-label">商品浏览量</div>
                  <div class="stat-change positive">+{{ monthlyStats.viewsGrowth }}%</div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="stat-card warning">
                <div class="stat-icon">👥</div>
                <div class="stat-content">
                  <div class="stat-number">{{ monthlyStats.customers }}</div>
                  <div class="stat-label">新增客户</div>
                  <div class="stat-change positive">+{{ monthlyStats.customersGrowth }}%</div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="stat-card danger">
                <div class="stat-icon">💰</div>
                <div class="stat-content">
                  <div class="stat-number">${{ monthlyStats.revenue }}</div>
                  <div class="stat-label">销售额(K)</div>
                  <div class="stat-change positive">+{{ monthlyStats.revenueGrowth }}%</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :xs="24" :lg="16">
        <el-card>
          <template #header>
            <span>月度趋势分析</span>
          </template>
          <div class="monthly-trend-chart">
            <div class="chart-header">
              <div class="metric-tabs">
                <el-button 
                  v-for="metric in trendMetrics" 
                  :key="metric.key"
                  :type="selectedMetric === metric.key ? 'primary' : 'default'"
                  size="small"
                  @click="selectedMetric = metric.key"
                >
                  {{ metric.label }}
                </el-button>
              </div>
            </div>
            <div class="trend-chart-container">
              <div class="trend-line-chart">
                <svg width="100%" height="200" viewBox="0 0 600 200">
                  <defs>
                    <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" style="stop-color:#409EFF;stop-opacity:0.3" />
                      <stop offset="100%" style="stop-color:#409EFF;stop-opacity:0" />
                    </linearGradient>
                  </defs>
                  <!-- 网格线 -->
                  <g stroke="#f0f2f5" stroke-width="1">
                    <line x1="0" y1="40" x2="600" y2="40"/>
                    <line x1="0" y1="80" x2="600" y2="80"/>
                    <line x1="0" y1="120" x2="600" y2="120"/>
                    <line x1="0" y1="160" x2="600" y2="160"/>
                  </g>
                  <!-- 趋势线 -->
                  <polyline
                    fill="none"
                    stroke="#409EFF"
                    stroke-width="3"
                    points="50,160 150,140 250,100 350,80 450,60 550,40"
                  />
                  <!-- 填充区域 -->
                  <polygon
                    fill="url(#gradient)"
                    points="50,160 150,140 250,100 350,80 450,60 550,40 550,180 50,180"
                  />
                  <!-- 数据点 -->
                  <circle cx="50" cy="160" r="4" fill="#409EFF"/>
                  <circle cx="150" cy="140" r="4" fill="#409EFF"/>
                  <circle cx="250" cy="100" r="4" fill="#409EFF"/>
                  <circle cx="350" cy="80" r="4" fill="#409EFF"/>
                  <circle cx="450" cy="60" r="4" fill="#409EFF"/>
                  <circle cx="550" cy="40" r="4" fill="#409EFF"/>
                </svg>
              </div>
              <div class="chart-x-axis">
                <span v-for="week in weekLabels" :key="week">{{ week }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="8">
        <el-card>
          <template #header>
            <span>月度目标完成情况</span>
          </template>
          <div class="goals-progress">
            <div v-for="goal in monthlyGoals" :key="goal.name" class="goal-item">
              <div class="goal-header">
                <span class="goal-name">{{ goal.name }}</span>
                <span class="goal-percentage">{{ goal.percentage }}%</span>
              </div>
              <el-progress 
                :percentage="goal.percentage" 
                :color="getProgressColor(goal.percentage)"
                :stroke-width="8"
              />
              <div class="goal-details">
                <span>{{ goal.current }} / {{ goal.target }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <span>商品类别表现</span>
          </template>
          <div class="category-performance">
            <div v-for="category in categoryData" :key="category.name" class="category-item">
              <div class="category-info">
                <div class="category-name">{{ category.name }}</div>
                <div class="category-stats">
                  <span class="sales">销售: ${{ category.sales }}K</span>
                  <span class="inquiries">询盘: {{ category.inquiries }}</span>
                </div>
              </div>
              <div class="category-chart">
                <div class="mini-bar" :style="{ width: category.performance + '%', backgroundColor: category.color }"></div>
                <span class="performance-text">{{ category.performance }}%</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <span>客户分析</span>
          </template>
          <div class="customer-analysis">
            <div class="analysis-section">
              <h4>新客户获取</h4>
              <div class="metric-row">
                <span class="metric-label">本月新增:</span>
                <span class="metric-value">{{ customerAnalysis.newCustomers }}</span>
                <el-tag type="success" size="small">+{{ customerAnalysis.newGrowth }}%</el-tag>
              </div>
            </div>
            <div class="analysis-section">
              <h4>客户活跃度</h4>
              <div class="metric-row">
                <span class="metric-label">活跃客户:</span>
                <span class="metric-value">{{ customerAnalysis.activeCustomers }}</span>
                <el-tag type="info" size="small">{{ customerAnalysis.activeRate }}%</el-tag>
              </div>
            </div>
            <div class="analysis-section">
              <h4>客户留存</h4>
              <div class="metric-row">
                <span class="metric-label">回购率:</span>
                <span class="metric-value">{{ customerAnalysis.retentionRate }}%</span>
                <el-tag type="warning" size="small">{{ customerAnalysis.retentionTrend }}</el-tag>
              </div>
            </div>
            <div class="analysis-section">
              <h4>平均客单价</h4>
              <div class="metric-row">
                <span class="metric-label">AVG:</span>
                <span class="metric-value">${{ customerAnalysis.avgOrderValue }}</span>
                <el-tag type="success" size="small">+{{ customerAnalysis.avgGrowth }}%</el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>月度重点事件回顾</span>
              <el-button type="primary" size="small" @click="exportReport">导出月报</el-button>
            </div>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="event in monthlyEvents"
              :key="event.id"
              :timestamp="event.date"
              :type="getTimelineType(event.type)"
            >
              <el-card>
                <h4>{{ event.title }}</h4>
                <p>{{ event.description }}</p>
                <div class="event-metrics" v-if="event.metrics">
                  <el-tag v-for="metric in event.metrics" :key="metric.name" type="info" size="small">
                    {{ metric.name }}: {{ metric.value }}
                  </el-tag>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

export default defineComponent({
  name: 'AliMonthlyReport',
  setup() {
    const selectedMonth = ref(new Date().toISOString().slice(0, 7))
    const selectedMetric = ref('inquiries')
    
    const monthlyStats = ref({
      inquiries: 678,
      inquiriesGrowth: 25.8,
      views: 34567,
      viewsGrowth: 18.5,
      customers: 156,
      customersGrowth: 32.1,
      revenue: 245.8,
      revenueGrowth: 28.9
    })

    const trendMetrics = ref([
      { key: 'inquiries', label: '询盘数' },
      { key: 'views', label: '浏览量' },
      { key: 'customers', label: '客户数' },
      { key: 'revenue', label: '销售额' }
    ])

    const weekLabels = ref(['第1周', '第2周', '第3周', '第4周', '第5周'])

    const monthlyGoals = ref([
      { name: '询盘目标', current: 678, target: 800, percentage: 85 },
      { name: '客户获取', current: 156, target: 180, percentage: 87 },
      { name: '销售目标', current: 245.8, target: 300, percentage: 82 },
      { name: '转化率', current: 4.2, target: 5.0, percentage: 84 }
    ])

    const categoryData = ref([
      { name: '电子产品', sales: 125.6, inquiries: 345, performance: 85, color: '#409EFF' },
      { name: '家居用品', sales: 78.2, inquiries: 234, performance: 72, color: '#67C23A' },
      { name: '服装配饰', sales: 42.0, inquiries: 156, performance: 58, color: '#E6A23C' },
      { name: '运动户外', sales: 35.8, inquiries: 123, performance: 65, color: '#F56C6C' },
      { name: '美妆个护', sales: 28.4, inquiries: 89, performance: 48, color: '#909399' }
    ])

    const customerAnalysis = ref({
      newCustomers: 156,
      newGrowth: 32.1,
      activeCustomers: 892,
      activeRate: 78.5,
      retentionRate: 65.2,
      retentionTrend: '稳定',
      avgOrderValue: 1578,
      avgGrowth: 15.8
    })

    const monthlyEvents = ref([
      {
        id: 1,
        date: '2024-01-05',
        title: '春节营销活动启动',
        description: '推出春节特别促销活动，涵盖5大商品类别，预期提升20%销售额',
        type: 'success',
        metrics: [
          { name: '参与商品', value: '158件' },
          { name: '优惠幅度', value: '15-30%' }
        ]
      },
      {
        id: 2,
        date: '2024-01-12',
        title: '新市场拓展',
        description: '成功进入东南亚3个新市场，签约当地分销商',
        type: 'primary',
        metrics: [
          { name: '新市场', value: '3个' },
          { name: '分销商', value: '5家' }
        ]
      },
      {
        id: 3,
        date: '2024-01-20',
        title: '产品质量认证',
        description: '主要产品线通过ISO9001质量管理体系认证',
        type: 'success',
        metrics: [
          { name: '认证产品', value: '45款' },
          { name: '合格率', value: '99.8%' }
        ]
      },
      {
        id: 4,
        date: '2024-01-28',
        title: '客户服务升级',
        description: '上线24小时客服系统，客户满意度提升至98%',
        type: 'info',
        metrics: [
          { name: '响应时间', value: '<2分钟' },
          { name: '满意度', value: '98%' }
        ]
      }
    ])

    const handleMonthChange = (month) => {
      console.log('选择月份:', month)
      // 这里可以根据月份重新加载数据
    }

    const getProgressColor = (percentage) => {
      if (percentage >= 90) return '#67C23A'
      if (percentage >= 80) return '#E6A23C' 
      if (percentage >= 70) return '#409EFF'
      return '#F56C6C'
    }

    const getTimelineType = (type) => {
      const typeMap = {
        success: 'success',
        primary: 'primary',
        info: 'info',
        warning: 'warning'
      }
      return typeMap[type] || 'primary'
    }

    const exportReport = () => {
      ElMessage.success('月报导出功能开发中...')
    }

    onMounted(() => {
      // 初始化加载数据
    })

    return {
      selectedMonth,
      selectedMetric,
      monthlyStats,
      trendMetrics,
      weekLabels,
      monthlyGoals,
      categoryData,
      customerAnalysis,
      monthlyEvents,
      handleMonthChange,
      getProgressColor,
      getTimelineType,
      exportReport
    }
  }
})
</script>

<style scoped>
.ali-monthly-report {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.stat-card.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-card.success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stat-card.warning {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.stat-card.danger {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.stat-icon {
  font-size: 32px;
  margin-right: 16px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: bold;
}

.stat-change.positive {
  color: rgba(255, 255, 255, 0.9);
}

.monthly-trend-chart {
  padding: 20px 0;
}

.chart-header {
  margin-bottom: 20px;
}

.metric-tabs {
  display: flex;
  gap: 8px;
}

.trend-chart-container {
  position: relative;
}

.trend-line-chart {
  width: 100%;
  height: 200px;
}

.chart-x-axis {
  display: flex;
  justify-content: space-between;
  padding: 0 50px;
  margin-top: 10px;
  font-size: 12px;
  color: #606266;
}

.goals-progress {
  padding: 10px 0;
}

.goal-item {
  margin-bottom: 20px;
}

.goal-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.goal-name {
  font-size: 14px;
  color: #303133;
}

.goal-percentage {
  font-size: 14px;
  font-weight: bold;
  color: #409EFF;
}

.goal-details {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
  text-align: right;
}

.category-performance {
  padding: 10px 0;
}

.category-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f2f5;
}

.category-item:last-child {
  border-bottom: none;
}

.category-info {
  flex: 1;
  margin-right: 20px;
}

.category-name {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.category-stats {
  font-size: 12px;
  color: #606266;
}

.category-stats .sales {
  margin-right: 15px;
}

.category-chart {
  width: 120px;
  position: relative;
}

.mini-bar {
  height: 8px;
  border-radius: 4px;
  margin-bottom: 4px;
}

.performance-text {
  font-size: 12px;
  color: #606266;
}

.customer-analysis {
  padding: 10px 0;
}

.analysis-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f2f5;
}

.analysis-section:last-child {
  border-bottom: none;
}

.analysis-section h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #303133;
}

.metric-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-label {
  font-size: 12px;
  color: #606266;
  min-width: 60px;
}

.metric-value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.event-metrics {
  margin-top: 10px;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}
</style> 