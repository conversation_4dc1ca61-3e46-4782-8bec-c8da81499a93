-- 修复关键词库表枚举值不一致问题
-- 执行前请确保备份重要数据

USE cbec;

-- 禁用外键检查和安全模式
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_SAFE_UPDATES = 0;

-- 1. 备份现有数据（如果有的话）
DROP TABLE IF EXISTS keyword_library_backup;
CREATE TABLE keyword_library_backup AS SELECT * FROM keyword_library;

DROP TABLE IF EXISTS keyword_update_history_backup;
CREATE TABLE keyword_update_history_backup AS SELECT * FROM keyword_update_history;

-- 2. 删除有外键依赖的表
DROP TABLE IF EXISTS keyword_update_history;
DROP TABLE IF EXISTS keyword_import_tasks;

-- 3. 删除主表
DROP TABLE IF EXISTS keyword_library;

-- 4. 重新创建 keyword_library 表（确保使用小写枚举值）
CREATE TABLE keyword_library (
  id int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  keyword_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关键词名称',
  avg_monthly_searches bigint DEFAULT NULL COMMENT '平均每月搜索量（过去12个月）',
  monthly_searches varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '大致每月搜索量（JSON格式存储每月数据）',
  competition_level enum('low','medium','high','unspecified') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'unspecified' COMMENT '竞争级别',
  competition_index decimal(5,2) DEFAULT NULL COMMENT '竞争指数（0-100）',
  low_bid_micros bigint DEFAULT NULL COMMENT '出价第20百分位（微货币单位）',
  high_bid_micros bigint DEFAULT NULL COMMENT '出价第80百分位（微货币单位）',
  currency_code varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'CNY' COMMENT '货币代码',
  language_code varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'zh-CN' COMMENT '语言代码',
  location_ids varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地理位置ID列表（逗号分隔）',
  update_method enum('google_ads_api','manual','batch_import') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'manual' COMMENT '更新方式',
  tags varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签（逗号分隔）',
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_keyword_name (keyword_name),
  KEY idx_keyword_name (keyword_name),
  KEY idx_competition_level (competition_level),
  KEY idx_avg_monthly_searches (avg_monthly_searches),
  KEY idx_update_method (update_method),
  KEY idx_created_at (created_at),
  KEY idx_updated_at (updated_at),
  FULLTEXT KEY idx_keyword_tags_fulltext (keyword_name,tags)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关键词库表';

-- 5. 重新创建 keyword_update_history 表
CREATE TABLE keyword_update_history (
  id int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  keyword_id int NOT NULL COMMENT '关键词ID',
  update_method enum('google_ads_api','manual','batch_import') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '更新方式',
  old_data json DEFAULT NULL COMMENT '更新前的数据（JSON格式）',
  new_data json DEFAULT NULL COMMENT '更新后的数据（JSON格式）',
  batch_id varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '批量操作ID',
  operator_id int DEFAULT NULL COMMENT '操作人ID',
  operator_name varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作人姓名',
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  KEY idx_keyword_id (keyword_id),
  KEY idx_update_method (update_method),
  KEY idx_batch_id (batch_id),
  KEY idx_created_at (created_at),
  CONSTRAINT keyword_update_history_ibfk_1 FOREIGN KEY (keyword_id) REFERENCES keyword_library (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关键词更新历史表';

-- 6. 重新创建 keyword_import_tasks 表
CREATE TABLE keyword_import_tasks (
  id int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  task_id varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务ID',
  file_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件名',
  file_path varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  total_count int DEFAULT 0 COMMENT '总记录数',
  success_count int DEFAULT 0 COMMENT '成功数',
  failed_count int DEFAULT 0 COMMENT '失败数',
  status enum('pending','processing','completed','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '任务状态',
  error_message text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  result_file_path varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '结果文件路径',
  operator_id int DEFAULT NULL COMMENT '操作人ID',
  operator_name varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作人姓名',
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  UNIQUE KEY uk_task_id (task_id),
  KEY idx_status (status),
  KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关键词批量导入任务表';

-- 7. 尝试恢复备份数据（转换枚举值为小写）
INSERT INTO keyword_library (
  keyword_name, avg_monthly_searches, monthly_searches, competition_level, competition_index,
  low_bid_micros, high_bid_micros, currency_code, language_code, location_ids,
  update_method, tags, created_at, updated_at
)
SELECT 
  keyword_name, avg_monthly_searches, monthly_searches, 
  CASE 
    WHEN LOWER(competition_level) IN ('low', 'medium', 'high', 'unspecified') THEN LOWER(competition_level)
    ELSE 'unspecified'
  END as competition_level,
  competition_index, low_bid_micros, high_bid_micros, currency_code, language_code, location_ids,
  CASE 
    WHEN LOWER(update_method) IN ('google_ads_api', 'manual', 'batch_import') THEN LOWER(update_method)
    ELSE 'manual'
  END as update_method,
  tags, created_at, updated_at
FROM keyword_library_backup
WHERE EXISTS (SELECT 1 FROM keyword_library_backup);

-- 8. 启用外键检查和安全模式
SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;

-- 9. 验证修复结果
SELECT 'keyword_library表修复完成' as result;
SHOW CREATE TABLE keyword_library;
SELECT COUNT(*) as keyword_count FROM keyword_library;
SELECT competition_level, COUNT(*) as count FROM keyword_library GROUP BY competition_level;
SELECT update_method, COUNT(*) as count FROM keyword_library GROUP BY update_method; 