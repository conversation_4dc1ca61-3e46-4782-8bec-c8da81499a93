#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI外贸运营系统配置管理工具
用于管理环境变量配置
"""

import os
import shutil
from pathlib import Path
from typing import Dict, Any
import argparse

class ConfigManager:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.env_file = self.project_root / ".env"
        self.env_example_file = self.project_root / ".env.example"
        self.frontend_env_file = self.project_root / "frontend" / ".env.local"
        
    def check_config(self) -> bool:
        """检查配置文件状态"""
        print("=== 配置文件检查 ===")
        
        env_exists = self.env_file.exists()
        example_exists = self.env_example_file.exists()
        frontend_env_exists = self.frontend_env_file.exists()
        
        print(f"主配置文件 (.env): {'✓' if env_exists else '✗'}")
        print(f"配置模板 (.env.example): {'✓' if example_exists else '✗'}")
        print(f"前端配置 (frontend/.env.local): {'✓' if frontend_env_exists else '✗'}")
        
        if not env_exists:
            print("\n[警告] 主配置文件不存在，请运行 'python config-manager.py init' 进行初始化")
            
        return env_exists and example_exists and frontend_env_exists
        
    def init_config(self) -> bool:
        """初始化配置文件"""
        print("=== 初始化配置文件 ===")
        
        if not self.env_example_file.exists():
            print("[错误] .env.example 模板文件不存在")
            return False
            
        if self.env_file.exists():
            response = input("配置文件已存在，是否覆盖？(y/N): ")
            if response.lower() != 'y':
                print("取消初始化")
                return False
                
        # 复制配置模板
        shutil.copy2(self.env_example_file, self.env_file)
        print(f"✓ 已创建配置文件: {self.env_file}")
        
        # 确保前端配置文件存在
        if not self.frontend_env_file.exists():
            frontend_content = """# 前端服务配置
FRONTEND_PORT=8080

# API基础URL
VUE_APP_API_BASE_URL=http://localhost:8000
"""
            self.frontend_env_file.write_text(frontend_content, encoding='utf-8')
            print(f"✓ 已创建前端配置文件: {self.frontend_env_file}")
            
        print("\n配置文件初始化完成！")
        print("请编辑 .env 文件设置您的具体配置")
        return True
        
    def load_env(self) -> Dict[str, str]:
        """加载环境变量配置"""
        if not self.env_file.exists():
            return {}
            
        env_vars = {}
        # 尝试不同的编码方式读取文件
        encodings = ['utf-8', 'gbk', 'ansi', 'gb2312']
        
        for encoding in encodings:
            try:
                with open(self.env_file, 'r', encoding=encoding) as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            if '=' in line:
                                key, value = line.split('=', 1)
                                env_vars[key.strip()] = value.strip()
                break
            except UnicodeDecodeError:
                continue
        
        return env_vars
        
    def show_config(self):
        """显示当前配置"""
        print("=== 当前配置 ===")
        env_vars = self.load_env()
        
        if not env_vars:
            print("未找到配置或配置为空")
            return
            
        # 按类别显示配置
        categories = {
            "基础配置": ["PROJECT_NAME", "API_V1_STR"],
            "安全配置": ["SECRET_KEY", "ACCESS_TOKEN_EXPIRE_MINUTES"],
            "数据库配置": ["DATABASE_URL"],
            "服务配置": ["BACKEND_HOST", "BACKEND_PORT", "FRONTEND_HOST", "FRONTEND_PORT"],
            "其他配置": []
        }
        
        displayed_keys = set()
        
        for category, keys in categories.items():
            if keys and any(key in env_vars for key in keys):
                print(f"\n{category}:")
                for key in keys:
                    if key in env_vars:
                        # 隐藏敏感信息
                        value = env_vars[key]
                        if key in ["SECRET_KEY", "DATABASE_URL"] and len(value) > 10:
                            value = value[:10] + "..."
                        print(f"  {key}={value}")
                        displayed_keys.add(key)
        
        # 显示其他配置
        other_vars = {k: v for k, v in env_vars.items() if k not in displayed_keys}
        if other_vars:
            print(f"\n其他配置:")
            for key, value in other_vars.items():
                print(f"  {key}={value}")
                
    def validate_config(self) -> bool:
        """验证配置文件"""
        print("=== 验证配置 ===")
        env_vars = self.load_env()
        
        required_vars = [
            "PROJECT_NAME",
            "SECRET_KEY", 
            "DATABASE_URL",
            "BACKEND_HOST",
            "BACKEND_PORT"
        ]
        
        missing_vars = [var for var in required_vars if var not in env_vars]
        
        if missing_vars:
            print(f"[错误] 缺少必需的配置项: {', '.join(missing_vars)}")
            return False
            
        # 检查敏感配置
        if env_vars.get("SECRET_KEY") == "your-secret-key-here":
            print("[警告] 请修改默认的SECRET_KEY")
            
        if "username:password" in env_vars.get("DATABASE_URL", ""):
            print("[警告] 请配置正确的数据库连接信息")
            
        print("✓ 配置验证通过")
        return True

def main():
    parser = argparse.ArgumentParser(description="AI外贸运营系统配置管理工具")
    parser.add_argument('action', choices=['check', 'init', 'show', 'validate'], 
                       help='执行的操作: check(检查), init(初始化), show(显示), validate(验证)')
    
    args = parser.parse_args()
    
    manager = ConfigManager()
    
    if args.action == 'check':
        manager.check_config()
    elif args.action == 'init':
        manager.init_config()
    elif args.action == 'show':
        manager.show_config()
    elif args.action == 'validate':
        manager.validate_config()

if __name__ == "__main__":
    main() 