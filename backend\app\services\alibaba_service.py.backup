import hashlib
import hmac
import time
import requests
import json
import urllib.parse
import base64
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from app.core.config import settings
from app.models.alibaba_auth import AlibabaAuth
import logging

logger = logging.getLogger(__name__)

class AlibabaAPIService:
    """阿里国际站API服务类"""
    
    def __init__(self):
        self.app_key = settings.ALIBABA_APP_KEY
        self.app_secret = settings.ALIBABA_APP_SECRET
        self.oauth_url = settings.ALIBABA_OAUTH_URL
        self.token_url = settings.ALIBABA_TOKEN_URL
        self.refresh_token_url = settings.ALIBABA_REFRESH_TOKEN_URL
        self.redirect_uri = settings.ALIBABA_REDIRECT_URI
    
    def generate_auth_url(self, state: Optional[str] = None) -> str:
        """
        生成授权URL
        
        Args:
            state: 可选的状态参数，用于防止CSRF攻击
            
        Returns:
            授权URL字符串
        """
        params = {
            "response_type": "code",
            "force_auth": "true",
            "redirect_uri": self.redirect_uri,
            "client_id": self.app_key
        }
        
        if state:
            params["state"] = state
            
        query_string = urllib.parse.urlencode(params)
        auth_url = f"{self.oauth_url}?{query_string}"
        
        logger.info(f"生成授权URL: {auth_url}")
        return auth_url
    
    def _generate_sign(self, params: Dict[str, Any], api_path: str) -> str:
        """
        生成API签名
        
        Args:
            params: 请求参数
            api_path: API路径
            
        Returns:
            签名字符串
        """
        # 添加必要的参数
        params.update({
            "app_key": self.app_key,
            "partner_id": "iop-sdk-python",
            "sign_method": "sha256",
            "timestamp": str(int(time.time() * 1000))
        })
        
        # 按键排序
        sorted_params = sorted(params.items())
        
        # 构建签名字符串
        sign_string = api_path
        for key, value in sorted_params:
            if value is not None and value != "":
                sign_string += f"{key}{value}"
        
        # 生成签名
        signature = hmac.new(
            self.app_secret.encode('utf-8'),
            sign_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest().upper()
        
        return signature
    
    def get_access_token(self, code: str) -> Dict[str, Any]:
        """
        使用授权码获取访问令牌
        
        Args:
            code: 授权码
            
        Returns:
            包含token信息的字典
        """
        api_path = "/auth/token/create"
        params = {
            "code": code
        }
        
        # 生成签名
        signature = self._generate_sign(params, api_path)
        params["sign"] = signature
        
        # 构建完整URL
        query_string = urllib.parse.urlencode(params)
        full_url = f"{self.token_url}?{query_string}"
        
        # 设置请求头
        headers = {
            "Accept-Encoding": "gzip",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        try:
            logger.info(f"请求访问令牌: {full_url}")
            response = requests.post(full_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"获取访问令牌响应: {result}")
            
            if result.get("code") == "0":
                return result
            else:
                raise Exception(f"获取访问令牌失败: {result}")
                
        except requests.RequestException as e:
            logger.error(f"请求访问令牌异常: {e}")
            raise Exception(f"请求访问令牌失败: {e}")
    
    def refresh_access_token(self, refresh_token: str) -> Dict[str, Any]:
        """
        刷新访问令牌
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            包含新token信息的字典
        """
        api_path = "/auth/token/refresh"
        params = {
            "refresh_token": refresh_token
        }
        
        # 生成签名
        signature = self._generate_sign(params, api_path)
        params["sign"] = signature
        
        # 构建完整URL
        query_string = urllib.parse.urlencode(params)
        full_url = f"{self.refresh_token_url}?{query_string}"
        
        # 设置请求头
        headers = {
            "Accept-Encoding": "gzip",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        try:
            logger.info(f"刷新访问令牌: {full_url}")
            response = requests.post(full_url, headers=headers, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"刷新访问令牌响应: {result}")
            
            if result.get("code") == "0":
                return result
            else:
                raise Exception(f"刷新访问令牌失败: {result}")
                
        except requests.RequestException as e:
            logger.error(f"刷新访问令牌异常: {e}")
            raise Exception(f"刷新访问令牌失败: {e}")
    
    def save_auth_info(self, db: Session, user_id: int, tenant_id: str, 
                      token_data: Dict[str, Any], code: str = None) -> AlibabaAuth:
        """
        保存授权信息到数据库
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            tenant_id: 租户ID
            token_data: token数据
            code: 授权码
            
        Returns:
            阿里巴巴授权记录
        """
        # 查找现有记录
        existing_auth = db.query(AlibabaAuth).filter(
            AlibabaAuth.user_id == user_id,
            AlibabaAuth.tenant_id == tenant_id
        ).first()
        
        if existing_auth:
            # 更新现有记录
            auth_record = existing_auth
        else:
            # 创建新记录
            auth_record = AlibabaAuth(
                user_id=user_id,
                tenant_id=tenant_id
            )
            db.add(auth_record)
        
        # 更新字段
        auth_record.access_token = token_data.get("access_token")
        auth_record.refresh_token = token_data.get("refresh_token")
        auth_record.expires_in = token_data.get("expires_in")
        auth_record.refresh_expires_in = token_data.get("refresh_expires_in")
        auth_record.alibaba_account = token_data.get("account")
        auth_record.country = token_data.get("country")
        auth_record.account_platform = token_data.get("account_platform")
        auth_record.havana_id = token_data.get("havana_id")
        auth_record.request_id = token_data.get("request_id")
        auth_record.trace_id = token_data.get("_trace_id_")
        auth_record.token_created_at = datetime.utcnow()
        auth_record.is_active = True
        
        if code:
            auth_record.code = code
        
        db.commit()
        db.refresh(auth_record)
        
        logger.info(f"保存授权信息成功: user_id={user_id}, account={auth_record.alibaba_account}")
        return auth_record
    
    def get_valid_token(self, db: Session, user_id: int, tenant_id: str) -> Optional[str]:
        """
        获取有效的访问令牌，如果过期则自动刷新
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            tenant_id: 租户ID
            
        Returns:
            有效的访问令牌，如果无法获取则返回None
        """
        auth_record = db.query(AlibabaAuth).filter(
            AlibabaAuth.user_id == user_id,
            AlibabaAuth.tenant_id == tenant_id,
            AlibabaAuth.is_active == True
        ).first()
        
        if not auth_record:
            logger.warning(f"未找到授权记录: user_id={user_id}, tenant_id={tenant_id}")
            return None
        
        # 检查token是否过期
        token_expire_time = auth_record.token_created_at + timedelta(seconds=auth_record.expires_in)
        
        if datetime.utcnow() < token_expire_time:
            # token未过期，直接返回
            return auth_record.access_token
        
        # token已过期，尝试刷新
        if not auth_record.refresh_token:
            logger.warning(f"没有刷新令牌，无法刷新: user_id={user_id}")
            return None
        
        # 检查refresh_token是否过期
        refresh_expire_time = auth_record.token_created_at + timedelta(seconds=auth_record.refresh_expires_in)
        
        if datetime.utcnow() >= refresh_expire_time:
            logger.warning(f"刷新令牌已过期: user_id={user_id}")
            auth_record.is_active = False
            db.commit()
            return None
        
        try:
            # 刷新token
            new_token_data = self.refresh_access_token(auth_record.refresh_token)
            
            # 更新数据库
            self.save_auth_info(db, user_id, tenant_id, new_token_data)
            auth_record.last_refresh_at = datetime.utcnow()
            db.commit()
            
            logger.info(f"成功刷新访问令牌: user_id={user_id}")
            return new_token_data.get("access_token")
            
        except Exception as e:
            logger.error(f"刷新访问令牌失败: user_id={user_id}, error={e}")
            auth_record.is_active = False
            db.commit()
            return None

class AlibabaService:
    """阿里巴巴业务服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.api_service = AlibabaAPIService()
        # 使用阿里国际站官方API网关地址
        self.gateway_url = "https://open-api.alibaba.com/sync"
    
    def get_user_auth_info(self, user_id: int) -> Optional[AlibabaAuth]:
        """获取用户授权信息"""
        return self.db.query(AlibabaAuth).filter(
            AlibabaAuth.user_id == user_id,
            AlibabaAuth.is_active == True
        ).first()
    
    def _call_alibaba_api(self, access_token: str, api_name: str, api_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用阿里巴巴API的通用方法 - 根据官方文档实现
        
        Args:
            access_token: 访问令牌
            api_name: API名称
            api_params: API参数
            
        Returns:
            API响应结果
        """
        try:
            # 构建请求参数 - 按照官方文档格式
            params = {
                "app_key": self.api_service.app_key,
                "session": access_token,  # 官方文档使用session而不是access_token
                "method": api_name,
                "sign_method": "sha256",  # 官方文档使用sha256
                "format": "json",
                "timestamp": str(int(time.time() * 1000)),  # 毫秒时间戳
                "simplify": "true"  # 官方文档建议使用简化格式
            }
            
            # 添加API特定参数
            for key, value in api_params.items():
                if key == "product_ids" and isinstance(value, list):
                    # 产品ID数组需要特殊处理 - 尝试批量查询
                    # API可能对产品ID数量有限制，分批处理
                    if len(value) > 50:  # 限制单次查询的产品数量
                        logger.warning(f"产品ID数量过多({len(value)})，仅使用前50个")
                        value = value[:50]
                    
                    # 尝试批量查询：使用逗号分隔的格式
                    # 根据阿里巴巴API常见模式，Number[]类型可能支持逗号分隔
                    params[key] = ",".join([str(pid) for pid in value])
                    logger.info(f"使用批量查询格式，产品ID数量: {len(value)}")
                elif isinstance(value, (dict, list)):
                    params[key] = json.dumps(value)
                else:
                    params[key] = str(value)
            
            # 生成签名 - 按照官方文档的签名算法
            signature = self._generate_signature(params, api_name)
            params["sign"] = signature
            
            logger.info(f"调用阿里巴巴API: {api_name}")
            logger.info(f"请求参数: {params}")
            
            # 计算URL长度，决定使用GET还是POST
            query_string = urllib.parse.urlencode(params)
            full_url_length = len(self.gateway_url) + len(query_string) + 1
            
            if full_url_length > 2000:  # 如果URL过长，使用POST
                logger.info(f"URL长度 {full_url_length} 超过限制，使用POST请求")
                response = requests.post(self.gateway_url, data=params, timeout=30)
            else:
                logger.info(f"URL长度 {full_url_length}，使用GET请求")
                response = requests.get(self.gateway_url, params=params, timeout=30)
            
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"API响应: {result}")
            
            return result
                
        except requests.RequestException as e:
            logger.error(f"调用阿里巴巴API异常: {e}")
            raise Exception(f"调用API失败: {e}")
    
    def _generate_signature(self, params: Dict[str, Any], api_name: str) -> str:
        """
        根据官方文档生成签名
        """
        # 1. 按ASCII顺序排序参数（排除sign参数）
        sorted_params = sorted(params.items())
        
        # 2. 拼接参数名和参数值 - 原平台迁移API格式
        query_string = ""
        for key, value in sorted_params:
            query_string += f"{key}{value}"
        
        # 3. 使用HMAC-SHA256算法生成签名
        import hmac
        import hashlib
        
        signature_bytes = hmac.new(
            self.api_service.app_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).digest()
        
        # 4. 转换为大写十六进制
        signature = signature_bytes.hex().upper()
        
        logger.info(f"🔧 签名调试信息:")
        logger.info(f"   拼接字符串: {query_string}")
        logger.info(f"   生成的签名: {signature}")
        
        return signature
    
    def get_product_performance_data(self, access_token: str, statistics_type: str, 
                                   stat_date: str, product_ids: List[str]) -> Dict[str, Any]:
        """
        获取产品表现数据
        
        Args:
            access_token: 访问令牌
            statistics_type: 统计周期
            stat_date: 统计日期
            product_ids: 产品ID列表
            
        Returns:
            产品表现数据
        """
        api_params = {
            "statistics_type": statistics_type,
            "stat_date": stat_date,
            "product_ids": product_ids
        }
        
        return self._call_alibaba_api(
            access_token, 
            "alibaba.mydata.self.product.get", 
            api_params
        )
    
    def get_product_performance_date_range(self, access_token: str, 
                                         statistics_type: str = "day") -> Dict[str, Any]:
        """
        获取产品表现数据的可用时间范围
        
        Args:
            access_token: 访问令牌
            statistics_type: 统计周期类型
            
        Returns:
            时间范围数据
        """
        api_params = {
            "statistics_type": statistics_type
        }
        
        return self._call_alibaba_api(
            access_token,
            "alibaba.mydata.self.product.date.get",
            api_params
        )
    
    def get_keyword_date_periods(self, access_token: str) -> Dict[str, Any]:
        """
        获取关键词数据的可用时间周期
        
        Args:
            access_token: 访问令牌
            
        Returns:
            时间周期数据
        """
        return self._call_alibaba_api(
            access_token,
            "alibaba.mydata.self.keyword.date.get",
            {}
        )
    
    def get_keyword_weekly_data(self, access_token: str, date_range: Dict[str, str], 
                               properties: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取关键词周统计数据
        
        Args:
            access_token: 访问令牌
            date_range: 时间范围
            properties: 查询属性
            
        Returns:
            关键词周统计数据
        """
        api_params = {
            "date_range": date_range,
            "properties": properties
        }
        
        return self._call_alibaba_api(
            access_token,
            "alibaba.mydata.self.keyword.effect.week.get",
            api_params
        )
    
    def get_keyword_monthly_data(self, access_token: str, 
                                properties: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取关键词月统计数据
        
        Args:
            access_token: 访问令牌
            properties: 查询属性
            
        Returns:
            关键词月统计数据
        """
        api_params = {
            "properties": properties
        }
        
        return self._call_alibaba_api(
            access_token,
            "alibaba.mydata.self.keyword.effect.month.get",
            api_params
        )
    
    def get_icbu_product_list(self, access_token: str, page_size: int = 30, 
                              current_page: int = 1, language: str = "ENGLISH",
                              status: str = None, display: str = None, 
                              category_id: str = None, subject: str = None,
                              id_list: List[str] = None, owner_member: str = None,
                              group_id1: int = None, group_id2: int = None, 
                              group_id3: int = None, id: int = None,
                              gmt_modified_from: str = None, gmt_modified_to: str = None) -> Dict[str, Any]:
        """
        获取ICBU商品列表
        
        Args:
            access_token: 访问令牌
            page_size: 每页大小（最大30）
            current_page: 当前页码
            language: 语言（ENGLISH）
            status: 商品状态（approved/auditing/tbd）
            display: 上下架状态（Y/N）
            category_id: 类目ID
            subject: 商品名称（支持模糊匹配）
            id_list: 批量查询的商品ID列表（最多20个）
            owner_member: 商品负责人loginId
            group_id1: 商品一级分组id
            group_id2: 商品二级分组id  
            group_id3: 商品三级分组id
            id: 商品明文id
            gmt_modified_from: 最早修改时间，格式yyyy-MM-dd HH:mm:ss
            gmt_modified_to: 最晚修改时间，格式yyyy-MM-dd HH:mm:ss
            
        Returns:
            商品列表数据
        """
        api_params = {
            "language": language,
            "page_size": min(page_size, 30),  # 最大30
            "current_page": current_page
        }
        
        # 添加可选参数
        if status:
            api_params["status"] = status
        if display:
            api_params["display"] = display
        if category_id:
            api_params["category_id"] = category_id
        if subject:
            api_params["subject"] = subject
        if id_list:
            api_params["id_list"] = id_list[:20]  # 最多20个
        if owner_member:
            api_params["owner_member"] = owner_member
        if group_id1 is not None:
            api_params["group_id1"] = group_id1
        if group_id2 is not None:
            api_params["group_id2"] = group_id2
        if group_id3 is not None:
            api_params["group_id3"] = group_id3
        if id is not None:
            api_params["id"] = id
        if gmt_modified_from:
            api_params["gmt_modified_from"] = gmt_modified_from
        if gmt_modified_to:
            api_params["gmt_modified_to"] = gmt_modified_to
        
        return self._call_alibaba_api(
            access_token,
            "alibaba.icbu.product.list",
            api_params
        ) 