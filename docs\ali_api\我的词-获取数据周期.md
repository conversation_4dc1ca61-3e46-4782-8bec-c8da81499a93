# 我的词-获取数据周期

我的词-获取数据周期

GET/POST

alibaba.mydata.self.keyword.date.get

描述：获取数据管家我的词API可以使用的数据周期

## 参数

|  名称  |  类型  |  是否必须  |  描述  |
| --- | --- | --- | --- |
|  没有数据  |  |  |  |

## 响应参数

|  名称  |  类型  |  描述  |
| --- | --- | --- |
|  result\_list  |  Object\[\]  |  我的词数据周期列表  |
|  end\_date  |  String  |  数据周期结束日期（含）  |
|  start\_date  |  String  |  数据周期开始日期（含）  |

## 错误码

|  错误码  |  错误信息  |  解决方案  |
| --- | --- | --- |
|  没有数据  |  |  |

GET/POSTalibaba.mydata.self.keyword.date.get

*   PYTHON
    

```PYTHON
client = iop.IopClient(url, appkey ,appSecret)
request = iop.IopRequest('alibaba.mydata.self.keyword.date.get')
response = client.execute(request)
print(response.type)
print(response.body)

```

*   非精简返回
    

---
```json
{
  "code": "0",
  "request_id": "0ba2887315178178017221014",
  "alibaba_mydata_self_keyword_date_get_response": {
    "result_list": [
      {
        "end_date": "2015-01-24",
        "start_date": "2015-01-18"
      }
    ]
  }
}
```