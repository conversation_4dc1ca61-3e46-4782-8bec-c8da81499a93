from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.core.config import settings

# 创建数据库引擎
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,  # 检查连接是否还活着
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基类
Base = declarative_base()

# 依赖函数，提供数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 获取租户特定的会话
def get_tenant_db(tenant_id: str = None):
    """
    如果启用了多租户，获取特定租户的数据库会话
    如果未启用多租户或未提供租户ID，则返回默认会话
    """
    db = SessionLocal()
    # 在这里可以添加设置租户信息的逻辑
    try:
        yield db
    finally:
        db.close() 