"""Add default_blog_category_id to scheduled_publish_plans

Revision ID: add_default_blog_category_id
Revises: 
Create Date: 2024-12-19 16:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_default_blog_category_id'
down_revision = None  # 请根据实际情况修改为上一个revision ID
branch_labels = None
depends_on = None


def upgrade():
    """Add default_blog_category_id column to scheduled_publish_plans table"""
    op.add_column('scheduled_publish_plans', 
                  sa.Column('default_blog_category_id', sa.Integer(), nullable=True, comment='默认文章分类ID'))


def downgrade():
    """Remove default_blog_category_id column from scheduled_publish_plans table"""
    op.drop_column('scheduled_publish_plans', 'default_blog_category_id')
