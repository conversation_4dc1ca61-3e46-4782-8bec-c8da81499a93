<template>
  <div class="plans-container">
    <div class="page-header">
      <h2>套餐管理</h2>
      <p>管理系统订阅套餐配置</p>
    </div>

    <div class="coming-soon">
      <el-empty
        :image-size="200"
        description="套餐管理功能即将上线"
      >
        <el-button type="primary">敬请期待</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'Plans',
  setup () {
    return {}
  }
})
</script>

<style scoped>
.plans-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.coming-soon {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}
</style>
