import { createRouter, createWebHistory } from 'vue-router'
import store from '../store'

// 路由懒加载
const Layout = () => import('../layouts/Layout.vue')
const Login = () => import('../views/Login.vue')
const Dashboard = () => import('../views/Dashboard.vue')
const NotFound = () => import('../views/NotFound.vue')
const RouteView = () => import('../components/RouteView.vue')

// AI站群相关页面
const WordpressHosts = () => import('../views/aiCluster/WordpressHosts.vue')
const MySites = () => import('../views/aiCluster/MySites.vue')
const SeoAiArticle = () => import('../views/aiCluster/SeoAiArticle.vue')
const ScheduledPublish = () => import('../views/aiCluster/ScheduledPublish.vue')
const KeywordLibrary = () => import('../views/KeywordLibrary.vue')

// AI运营相关页面
const Products = () => import('../views/aiOperation/Products.vue')
const Customers = () => import('../views/aiOperation/Customers.vue')
const Inquiries = () => import('../views/aiOperation/Inquiries.vue')
const Promotions = () => import('../views/aiOperation/Promotions.vue')

// 第三方平台集成页面
const AlibabaAuth = () => import('../views/AlibabAuth.vue')
const AlibabaAuthSuccess = () => import('../views/AlibabAuthSuccess.vue')
const AlibabaAuthError = () => import('../views/AlibabAuthError.vue')

// AI报表相关页面
const AliDailyReport = () => import('../views/aiReport/AliDailyReport.vue')
const AliWeeklyReport = () => import('../views/aiReport/AliWeeklyReport.vue')
const AliMonthlyReport = () => import('../views/aiReport/AliMonthlyReport.vue')
const AliInquiryStatistics = () => import('../views/aiReport/AliInquiryStatistics.vue')
const ProductPerformance = () => import('../views/aiReport/ProductPerformance.vue')
const KeywordPerformance = () => import('../views/aiReport/KeywordPerformance.vue')

// 系统管理相关页面
const Users = () => import('../views/system/Users.vue')
const AiManagement = () => import('../views/system/AiManagement.vue')
const Tenants = () => import('../views/system/Tenants.vue')
const Roles = () => import('../views/system/Roles.vue')
const Plans = () => import('../views/system/Plans.vue')
const Billings = () => import('../views/system/Billings.vue')

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  // 阿里国际站授权回调页面（无需布局）
  {
    path: '/alibaba-auth-success',
    name: 'AlibabaAuthSuccess',
    component: AlibabaAuthSuccess,
    meta: { requiresAuth: false }
  },
  {
    path: '/alibaba-auth-error',
    name: 'AlibabaAuthError',
    component: AlibabaAuthError,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: Dashboard,
        meta: { title: '首页', icon: 'monitor' }
      },
      // AI站群模块
      {
        path: '/ai-cluster',
        name: 'AiCluster',
        component: RouteView,
        meta: { title: 'AI站群', icon: 'management' },
        alwaysShow: true,
        children: [
          {
            path: 'wordpress-hosts',
            name: 'WordpressHosts',
            component: WordpressHosts,
            meta: { title: '主机管理', icon: 'folder' }
          },
          {
            path: 'my-sites',
            name: 'MySites',
            component: MySites,
            meta: { title: '站点管理', icon: 'platform-eleme' }
          },
          {
            path: 'keywords',
            name: 'Keywords',
            component: KeywordLibrary,
            meta: { title: '关键词库', icon: 'star' }
          },
          {
            path: 'seo-ai-article',
            name: 'SeoAiArticle',
            component: SeoAiArticle,
            meta: { title: 'AI发文', icon: 'magic-stick' }
          },
          {
            path: 'scheduled-publish',
            name: 'ScheduledPublish',
            component: ScheduledPublish,
            meta: { title: '定时任务', icon: 'timer' }
          }
        ]
      },
      // AI运营模块
      {
        path: '/ai-operation',
        name: 'AiOperation',
        component: RouteView,
        meta: { title: 'AI运营', icon: 'shopping-cart' },
        alwaysShow: true,
        children: [
          {
            path: 'products',
            name: 'Products',
            component: Products,
            meta: { title: '商品管理', icon: 'goods' }
          },
          {
            path: 'customers',
            name: 'Customers',
            component: Customers,
            meta: { title: '客户管理', icon: 'user-filled' }
          },
          {
            path: 'inquiries',
            name: 'Inquiries',
            component: Inquiries,
            meta: { title: '询盘管理', icon: 'bell' }
          },
          {
            path: 'promotions',
            name: 'Promotions',
            component: Promotions,
            meta: { title: '推广管理', icon: 'rocket' }
          },
          {
            path: 'alibaba-auth',
            name: 'AlibabaAuth',
            component: AlibabaAuth,
            meta: { title: '阿里国际站授权', icon: 'connection' }
          }
        ]
      },
      // AI报表模块
      {
        path: '/ai-report',
        name: 'AiReport',
        component: RouteView,
        meta: { title: 'AI报表', icon: 'trend-charts' },
        alwaysShow: true,
        children: [
          {
            path: 'ali-daily',
            name: 'AliDailyReport',
            component: AliDailyReport,
            meta: { title: '阿里日报', icon: 'document' }
          },
          {
            path: 'ali-weekly',
            name: 'AliWeeklyReport',
            component: AliWeeklyReport,
            meta: { title: '阿里周报', icon: 'data-analysis' }
          },
          {
            path: 'ali-monthly',
            name: 'AliMonthlyReport',
            component: AliMonthlyReport,
            meta: { title: '阿里月报', icon: 'trend-charts' }
          },
          {
            path: 'ali-inquiry-statistics',
            name: 'AliInquiryStatistics',
            component: AliInquiryStatistics,
            meta: { title: '阿里询盘统计', icon: 'data-analysis' }
          },
          {
            path: 'product-performance',
            name: 'ProductPerformance',
            component: ProductPerformance,
            meta: { title: '产品表现', icon: 'goods' }
          },
          {
            path: 'keyword-performance',
            name: 'KeywordPerformance',
            component: KeywordPerformance,
            meta: { title: '关键词表现', icon: 'star' }
          }
        ]
      },
      // 系统管理模块
      {
        path: '/system',
        name: 'System',
        component: RouteView,
        meta: { title: '系统管理', icon: 'setting' },
        alwaysShow: true,
        children: [
          {
            path: 'users',
            name: 'Users',
            component: Users,
            meta: { title: '用户管理', icon: 'user' }
          },
          {
            path: 'ai-management',
            name: 'AiManagement',
            component: AiManagement,
            meta: { title: 'AI管理', icon: 'cpu' }
          },
          {
            path: 'tenants',
            name: 'Tenants',
            component: Tenants,
            meta: { title: '租户管理', roles: ['超级管理员'], icon: 'tools' }
          },
          {
            path: 'roles',
            name: 'Roles',
            component: Roles,
            meta: { title: '角色管理', icon: 'key' }
          },
          {
            path: 'plans',
            name: 'Plans',
            component: Plans,
            meta: { title: '套餐管理', icon: 'document' }
          },
          {
            path: 'billings',
            name: 'Billings',
            component: Billings,
            meta: { title: '账单管理', icon: 'money' }
          }
        ]
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth !== false)
  const isAuthenticated = store.getters['auth/isAuthenticated']

  if (requiresAuth && !isAuthenticated) {
    next('/login')
  } else if (to.path === '/login' && isAuthenticated) {
    next('/')
  } else {
    next()
  }
})

export default router
