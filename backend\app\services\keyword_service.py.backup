"""
关键词服务模块
直接使用TrendsPy替代PyTrends，解决'list index out of range'错误
提供关键词趋势分析、相关词建议等功能
"""
import logging
import json
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import pandas as pd

# 导入TrendsPy服务
from .trendspy_service import TrendsPyService

# 初始化日志记录器
logger = logging.getLogger(__name__)

class KeywordService:
    """
    关键词服务类
    使用TrendsPy提供完整的关键词分析功能
    """
    
    def __init__(self, config: Dict[str, Any] = None, proxy_config: Dict[str, Any] = None):
        """
        初始化关键词服务
        
        Args:
            config: 服务配置字典
            proxy_config: 代理配置字典
        """
        self.config = config or {}
        self.proxy_config = proxy_config or {}
        
        # 服务配置
        self.language = self.config.get('language', 'en-GB')
        self.geo_location = self.config.get('geo_location', 'GB')
        self.default_timeframe = self.config.get('default_timeframe', 'today 1-m')
        self.max_keywords_per_request = self.config.get('max_keywords_per_request', 5)
        
        # 初始化TrendsPy服务
        try:
            self.trends_service = TrendsPyService(config=config, proxy_config=proxy_config)
            logger.info("关键词服务初始化完成，使用TrendsPy作为数据源")
        except ImportError as e:
            logger.error(f"TrendsPy初始化失败: {e}")
            raise
        except Exception as e:
            logger.error(f"关键词服务初始化失败: {e}")
            raise
    
    def get_keyword_trends(
        self,
        keywords: List[str],
        timeframe: str = None,
        geo_location: str = None,
        category: int = 0,
        gprop: str = ''
    ) -> Dict[str, Any]:
        """
        获取关键词趋势数据
        
        Args:
            keywords: 关键词列表
            timeframe: 时间范围
            geo_location: 地理位置代码
            category: 分类ID
            gprop: Google产品类型
            
        Returns:
            包含趋势数据的字典
        """
        if not keywords:
            raise ValueError("关键词列表不能为空")
        
        if len(keywords) > self.max_keywords_per_request:
            raise ValueError(f"一次最多支持{self.max_keywords_per_request}个关键词")
        
        timeframe = timeframe or self.default_timeframe
        geo_location = geo_location or self.geo_location
        
        logger.info(f"获取关键词趋势数据: {keywords}")
        logger.info(f"参数 - 时间范围: {timeframe}, 地区: {geo_location}")
        
        try:
            # 使用TrendsPy获取趋势数据
            trends_data = self.trends_service.get_interest_over_time(
                keywords=keywords,
                timeframe=timeframe,
                geo=geo_location,
                category=category,
                gprop=gprop
            )
            
            # 计算额外的统计信息
            for keyword in keywords:
                if keyword in trends_data['interest_over_time']:
                    trend_records = trends_data['interest_over_time'][keyword]
                    if trend_records:
                        values = [record['value'] for record in trend_records]
                        trends_data['interest_over_time'][keyword + '_stats'] = {
                            'total_points': len(values),
                            'average_interest': sum(values) / len(values) if values else 0,
                            'max_interest': max(values) if values else 0,
                            'min_interest': min(values) if values else 0,
                            'trend_direction': self._calculate_trend_direction(values)
                        }
            
            return trends_data
            
        except Exception as e:
            logger.error(f"获取关键词趋势数据失败: {e}")
            raise
    
    def get_related_keywords(
        self,
        keywords: List[str],
        timeframe: str = None,
        geo_location: str = None,
        include_rising: bool = True
    ) -> Dict[str, Any]:
        """
        获取相关关键词建议
        
        Args:
            keywords: 种子关键词列表
            timeframe: 时间范围
            geo_location: 地理位置代码
            include_rising: 是否包含上升趋势关键词
            
        Returns:
            包含相关关键词的字典
        """
        if not keywords:
            raise ValueError("关键词列表不能为空")
        
        timeframe = timeframe or self.default_timeframe
        geo_location = geo_location or self.geo_location
        
        logger.info(f"获取相关关键词: {keywords}")
        logger.info(f"参数 - 时间范围: {timeframe}, 地区: {geo_location}, 包含上升趋势: {include_rising}")
        
        try:
            # 使用TrendsPy获取相关主题和查询
            related_data = self.trends_service.get_related_topics_and_queries(
                keywords=keywords,
                timeframe=timeframe,
                geo=geo_location
            )
            
            # 格式化返回数据
            result = {
                'seed_keywords': keywords,
                'related_data': related_data,
                'summary': {
                    'total_related_queries': 0,
                    'total_related_topics': 0,
                    'total_rising_queries': 0,
                    'total_rising_topics': 0
                },
                'metadata': related_data.get('metadata', {})
            }
            
            # 计算统计信息
            for keyword in keywords:
                if keyword in related_data.get('related_queries', {}):
                    result['summary']['total_related_queries'] += len(related_data['related_queries'][keyword])
                
                if keyword in related_data.get('related_topics', {}):
                    result['summary']['total_related_topics'] += len(related_data['related_topics'][keyword])
                
                if include_rising:
                    if keyword in related_data.get('rising_queries', {}):
                        result['summary']['total_rising_queries'] += len(related_data['rising_queries'][keyword])
                    
                    if keyword in related_data.get('rising_topics', {}):
                        result['summary']['total_rising_topics'] += len(related_data['rising_topics'][keyword])
            
            logger.info(f"相关关键词获取完成，统计: {result['summary']}")
            return result
            
        except Exception as e:
            logger.error(f"获取相关关键词失败: {e}")
            raise
    
    def generate_keyword_suggestions(
        self,
        seed_keywords: List[str],
        timeframe: str = None,
        geo_location: str = None,
        max_suggestions: int = 50,
        include_related: bool = True,
        include_rising: bool = True
    ) -> List[Dict[str, Any]]:
        """
        生成关键词建议
        
        Args:
            seed_keywords: 种子关键词列表
            timeframe: 时间范围
            geo_location: 地理位置代码
            max_suggestions: 最大建议数量
            include_related: 是否包含相关关键词
            include_rising: 是否包含上升趋势关键词
            
        Returns:
            关键词建议列表
        """
        if not seed_keywords:
            raise ValueError("种子关键词列表不能为空")
        
        timeframe = timeframe or self.default_timeframe
        geo_location = geo_location or self.geo_location
        
        logger.info(f"生成关键词建议，种子关键词: {seed_keywords}")
        logger.info(f"参数 - 最大建议数: {max_suggestions}, 包含相关: {include_related}, 包含上升: {include_rising}")
        
        try:
            # 使用TrendsPy生成关键词创意
            suggestions = self.trends_service.generate_keyword_ideas(
                seed_keywords=seed_keywords,
                timeframe=timeframe,
                geo_location=geo_location,
                include_related=include_related,
                include_rising=include_rising
            )
            
            # 限制结果数量
            limited_suggestions = suggestions[:max_suggestions] if suggestions else []
            
            # 添加额外的元数据
            for suggestion in limited_suggestions:
                suggestion['generated_at'] = datetime.now().isoformat()
                suggestion['geo_location'] = geo_location
                suggestion['timeframe'] = timeframe
            
            logger.info(f"关键词建议生成完成，共 {len(limited_suggestions)} 个建议")
            return limited_suggestions
            
        except Exception as e:
            logger.error(f"生成关键词建议失败: {e}")
            raise
    
    def analyze_keyword_competition(
        self,
        keywords: List[str],
        timeframe: str = None,
        geo_location: str = None
    ) -> Dict[str, Any]:
        """
        分析关键词竞争情况
        
        Args:
            keywords: 关键词列表
            timeframe: 时间范围
            geo_location: 地理位置代码
            
        Returns:
            包含竞争分析的字典
        """
        if not keywords:
            raise ValueError("关键词列表不能为空")
        
        if len(keywords) > self.max_keywords_per_request:
            raise ValueError(f"一次最多支持{self.max_keywords_per_request}个关键词")
        
        timeframe = timeframe or self.default_timeframe
        geo_location = geo_location or self.geo_location
        
        logger.info(f"分析关键词竞争: {keywords}")
        
        try:
            # 获取趋势数据进行竞争分析
            trends_data = self.get_keyword_trends(
                keywords=keywords,
                timeframe=timeframe,
                geo_location=geo_location
            )
            
            competition_analysis = {
                'keywords': keywords,
                'competition_scores': {},
                'ranking': [],
                'analysis_summary': {},
                'metadata': {
                    'timeframe': timeframe,
                    'geo_location': geo_location,
                    'analysis_date': datetime.now().isoformat(),
                    'source': 'trendspy'
                }
            }
            
            # 计算竞争分数
            keyword_scores = []
            for keyword in keywords:
                if keyword in trends_data['interest_over_time']:
                    trend_records = trends_data['interest_over_time'][keyword]
                    
                    if trend_records:
                        values = [record['value'] for record in trend_records]
                        
                        # 计算竞争指标
                        avg_interest = sum(values) / len(values)
                        max_interest = max(values)
                        volatility = self._calculate_volatility(values)
                        trend_strength = self._calculate_trend_strength(values)
                        
                        # 综合竞争分数（加权计算）
                        competition_score = (
                            avg_interest * 0.4 +
                            max_interest * 0.3 +
                            trend_strength * 0.2 +
                            (100 - volatility) * 0.1  # 低波动性得分更高
                        )
                        
                        competition_data = {
                            'keyword': keyword,
                            'competition_score': round(competition_score, 2),
                            'average_interest': round(avg_interest, 2),
                            'max_interest': max_interest,
                            'volatility': round(volatility, 2),
                            'trend_strength': round(trend_strength, 2),
                            'recommendation': self._get_competition_recommendation(competition_score)
                        }
                        
                        competition_analysis['competition_scores'][keyword] = competition_data
                        keyword_scores.append(competition_data)
            
            # 排序关键词（按竞争分数降序）
            keyword_scores.sort(key=lambda x: x['competition_score'], reverse=True)
            competition_analysis['ranking'] = keyword_scores
            
            # 生成分析摘要
            if keyword_scores:
                scores = [kw['competition_score'] for kw in keyword_scores]
                competition_analysis['analysis_summary'] = {
                    'total_keywords_analyzed': len(keyword_scores),
                    'highest_competition_score': max(scores),
                    'lowest_competition_score': min(scores),
                    'average_competition_score': round(sum(scores) / len(scores), 2),
                    'top_keyword': keyword_scores[0]['keyword'],
                    'recommendations': self._generate_competition_recommendations(keyword_scores)
                }
            
            logger.info(f"关键词竞争分析完成，分析了 {len(keyword_scores)} 个关键词")
            return competition_analysis
            
        except Exception as e:
            logger.error(f"关键词竞争分析失败: {e}")
            raise
    
    def _calculate_trend_direction(self, values: List[int]) -> str:
        """计算趋势方向"""
        if len(values) < 2:
            return 'stable'
        
        first_half = values[:len(values)//2]
        second_half = values[len(values)//2:]
        
        first_avg = sum(first_half) / len(first_half)
        second_avg = sum(second_half) / len(second_half)
        
        diff_percent = ((second_avg - first_avg) / first_avg * 100) if first_avg > 0 else 0
        
        if diff_percent > 10:
            return 'rising'
        elif diff_percent < -10:
            return 'declining'
        else:
            return 'stable'
    
    def _calculate_volatility(self, values: List[int]) -> float:
        """计算波动性（标准差）"""
        if len(values) < 2:
            return 0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        volatility = (variance ** 0.5) / mean * 100 if mean > 0 else 0
        
        return min(volatility, 100)  # 限制在100以内
    
    def _calculate_trend_strength(self, values: List[int]) -> float:
        """计算趋势强度"""
        if len(values) < 3:
            return 0
        
        # 简单线性回归计算趋势强度
        n = len(values)
        x = list(range(n))
        x_mean = sum(x) / n
        y_mean = sum(values) / n
        
        numerator = sum((x[i] - x_mean) * (values[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return 0
        
        slope = numerator / denominator
        
        # 将斜率转换为0-100的强度分数
        strength = min(abs(slope) * 10, 100)
        return strength
    
    def _get_competition_recommendation(self, score: float) -> str:
        """根据竞争分数给出建议"""
        if score >= 80:
            return "高竞争关键词，需要强大的SEO策略"
        elif score >= 60:
            return "中等竞争关键词，有一定机会"
        elif score >= 40:
            return "低竞争关键词，容易排名"
        else:
            return "极低竞争关键词，可能搜索量较小"
    
    def _generate_competition_recommendations(self, keyword_scores: List[Dict[str, Any]]) -> List[str]:
        """生成竞争分析建议"""
        if not keyword_scores:
            return ["没有可分析的关键词数据"]
        
        recommendations = []
        
        # 根据分数分布给出建议
        high_competition = [kw for kw in keyword_scores if kw['competition_score'] >= 70]
        medium_competition = [kw for kw in keyword_scores if 40 <= kw['competition_score'] < 70]
        low_competition = [kw for kw in keyword_scores if kw['competition_score'] < 40]
        
        if high_competition:
            recommendations.append(f"发现 {len(high_competition)} 个高竞争关键词，建议制定长期SEO策略")
        
        if medium_competition:
            recommendations.append(f"发现 {len(medium_competition)} 个中等竞争关键词，是很好的目标关键词")
        
        if low_competition:
            recommendations.append(f"发现 {len(low_competition)} 个低竞争关键词，可以快速获得排名")
        
        # 趋势建议
        rising_keywords = [kw for kw in keyword_scores if kw.get('trend_strength', 0) > 60]
        if rising_keywords:
            recommendations.append(f"关键词 '{rising_keywords[0]['keyword']}' 显示强劲上升趋势，值得重点关注")
        
        return recommendations
    
    def test_service(self) -> Dict[str, Any]:
        """
        测试关键词服务状态
        
        Returns:
            服务状态字典
        """
        try:
            # 测试TrendsPy连接
            trends_status = self.trends_service.test_connection()
            
            service_status = {
                'timestamp': datetime.now().isoformat(),
                'service': 'keyword_service',
                'status': 'operational' if trends_status['available'] else 'error',
                'trends_service': trends_status,
                'configuration': {
                    'language': self.language,
                    'geo_location': self.geo_location,
                    'default_timeframe': self.default_timeframe,
                    'max_keywords_per_request': self.max_keywords_per_request
                }
            }
            
            if trends_status['available']:
                service_status['message'] = "关键词服务运行正常，TrendsPy连接成功"
            else:
                service_status['message'] = "关键词服务异常，TrendsPy连接失败"
            
            return service_status
            
        except Exception as e:
            return {
                'timestamp': datetime.now().isoformat(),
                'service': 'keyword_service',
                'status': 'error',
                'error': str(e),
                'message': '关键词服务测试失败'
            } 