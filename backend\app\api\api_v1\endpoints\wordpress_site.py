from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import asyncio
from ....db.session import get_db
from ....models.wordpress_site import WordPressSite
from ....schemas.wordpress_site import (
    WordPressSiteCreate, 
    WordPressSiteUpdate, 
    WordPressSiteResponse, 
    WordPressSiteList,
    WordPressSiteSync,
    CategoryCreate,
    CategoryResponse,
    CategoryUpdate,
    CategoryDelete
)
from ....api.deps import get_current_user
from ....models.user import User
from ....services.wordpress_service import WordPressService
from datetime import datetime
from sqlalchemy import or_
from fastapi import BackgroundTasks
from ....utils.datetime_utils import utc_now, to_iso_string

router = APIRouter()


@router.get("/", response_model=WordPressSiteList)
async def get_wordpress_sites(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    category: Optional[str] = Query(None, description="站点分类筛选"),
    name: Optional[str] = Query(None, description="站点名称搜索"),
    is_active: Optional[bool] = Query(None, description="是否启用筛选"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取WordPress站点列表"""
    # 基础查询，排除占位符站点
    query = db.query(WordPressSite).filter(
        WordPressSite.sync_status != "placeholder"
    )
    
    # 筛选条件
    if category:
        if category == "uncategorized":
            # 查询未分类的站点（category为null或空字符串）
            query = query.filter(
                or_(
                    WordPressSite.category.is_(None),
                    WordPressSite.category == ""
                )
            )
        else:
            query = query.filter(WordPressSite.category == category)
    
    if name:
        query = query.filter(WordPressSite.name.contains(name))
    
    if is_active is not None:
        query = query.filter(WordPressSite.is_active == is_active)
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * size
    items = query.order_by(WordPressSite.created_at.desc()).offset(offset).limit(size).all()
    
    return WordPressSiteList(
        items=items,
        total=total,
        page=page,
        size=size
    )


@router.post("/", response_model=WordPressSiteResponse)
async def create_wordpress_site(
    site_data: WordPressSiteCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建WordPress站点"""
    
    # 验证分类是否为空
    if not site_data.category or site_data.category.strip() == "":
        raise HTTPException(status_code=400, detail="站点分类不能为空")
    
    # 检查URL是否已存在
    existing_site = db.query(WordPressSite).filter(WordPressSite.url == site_data.url).first()
    if existing_site:
        raise HTTPException(status_code=400, detail="该站点URL已存在")
    
    # 测试WordPress连接
    wp_service = WordPressService(site_data.url, site_data.wp_username, site_data.wp_app_password)
    connection_test = await wp_service.test_connection()
    
    if not connection_test["success"]:
        raise HTTPException(status_code=400, detail=f"WordPress连接测试失败: {connection_test['error']}")
    
    # 创建站点记录
    site = WordPressSite(**site_data.model_dump())
    site.sync_status = "success"
    site.last_sync_at = utc_now()
    
    # 填充从WordPress获取的信息
    site_info = connection_test["site_info"]
    site.wordpress_version = site_info.get("wordpress_version")
    site.theme_name = site_info.get("theme_name")
    site.language = site_info.get("language")
    site.timezone = site_info.get("timezone")
    site.total_posts = site_info.get("total_posts", 0)
    site.total_pages = site_info.get("total_pages", 0)
    site.blog_categories = site_info.get("blog_categories", [])
    site.blog_tags = site_info.get("blog_tags", [])
    
    db.add(site)
    db.commit()
    db.refresh(site)
    
    return site


@router.get("/summary")
async def get_sites_summary(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取站点统计摘要"""
    from sqlalchemy import func
    
    # 获取统计数据，排除占位符站点
    total_sites = db.query(func.count(WordPressSite.id)).filter(
        WordPressSite.sync_status != "placeholder"
    ).scalar()
    active_sites = db.query(func.count(WordPressSite.id)).filter(
        WordPressSite.is_active == True,
        WordPressSite.sync_status != "placeholder"
    ).scalar()
    
    # 获取UV/PV统计，排除占位符站点
    uv_pv_stats = db.query(
        func.sum(WordPressSite.daily_uv).label('total_daily_uv'),
        func.sum(WordPressSite.daily_pv).label('total_daily_pv'),
        func.sum(WordPressSite.monthly_uv).label('total_monthly_uv'),
        func.sum(WordPressSite.monthly_pv).label('total_monthly_pv')
    ).filter(WordPressSite.sync_status != "placeholder").first()
    
    return {
        "total_sites": total_sites or 0,
        "active_sites": active_sites or 0,
        "total_daily_uv": uv_pv_stats.total_daily_uv or 0,
        "total_daily_pv": uv_pv_stats.total_daily_pv or 0,
        "total_monthly_uv": uv_pv_stats.total_monthly_uv or 0,
        "total_monthly_pv": uv_pv_stats.total_monthly_pv or 0
    }


@router.get("/categories/list")
async def get_site_categories(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有站点分类"""
    # 获取所有分类，包括占位符站点的分类，但要去重
    categories = db.query(WordPressSite.category).filter(
        WordPressSite.category.isnot(None),
        WordPressSite.category != ""
    ).distinct().all()
    
    # 过滤出有效的分类名称
    valid_categories = []
    for cat in categories:
        if cat[0] and not cat[0].startswith('_category_placeholder_'):
            valid_categories.append(cat[0])
    
    # 添加占位符站点的分类（去掉占位符前缀）
    placeholder_categories = db.query(WordPressSite.category).filter(
        WordPressSite.sync_status == "placeholder",
        WordPressSite.category.isnot(None)
    ).distinct().all()
    
    for cat in placeholder_categories:
        if cat[0] and cat[0] not in valid_categories:
            valid_categories.append(cat[0])
    
    return {
        "categories": sorted(list(set(valid_categories)))  # 去重并排序
    }


@router.get("/{site_id}", response_model=WordPressSiteResponse)
async def get_wordpress_site(
    site_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取单个WordPress站点"""
    site = db.query(WordPressSite).filter(WordPressSite.id == site_id).first()
    if not site:
        raise HTTPException(status_code=404, detail="站点不存在")
    return site


# 站点分类管理接口（必须在{site_id}路由之前定义）
@router.post("/categories", response_model=CategoryResponse)
async def create_site_category(
    category_data: CategoryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建站点分类"""
    name = category_data.name
    if not name or not name.strip():
        raise HTTPException(status_code=400, detail="分类名称不能为空")
    
    name = name.strip()
    
    # 检查分类是否已存在
    existing_categories = db.query(WordPressSite.category).distinct().filter(
        WordPressSite.category.isnot(None),
        WordPressSite.category == name
    ).first()
    
    if existing_categories:
        raise HTTPException(status_code=400, detail="分类名称已存在")
    
    # 创建一个占位符站点来确保分类存在（临时解决方案）
    # 注意：这个方案适用于当前的架构，如果需要更好的解决方案，应该创建独立的分类表
    placeholder_site = WordPressSite(
        name=f"_category_placeholder_{name}",
        url=f"https://placeholder.category.{name.lower().replace(' ', '-')}.example.com",
        category=name,
        wp_username="placeholder",
        wp_app_password="placeholder",
        description=f"分类 '{name}' 的占位符站点，用于分类管理",
        is_active=False,  # 设为不活跃，不会在正常站点列表中显示
        sync_status="placeholder"
    )
    
    db.add(placeholder_site)
    db.commit()
    
    return CategoryResponse(
        message=f"分类 '{name}' 创建成功",
        success=True
    )


@router.put("/categories", response_model=CategoryResponse)
async def update_site_category(
    category_data: CategoryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新站点分类名称"""
    old_name = category_data.oldName
    new_name = category_data.newName
    
    if not old_name or not new_name:
        raise HTTPException(status_code=400, detail="原分类名和新分类名都不能为空")
    
    # 检查原分类是否存在
    existing_site = db.query(WordPressSite).filter(
        WordPressSite.category == old_name
    ).first()
    
    if not existing_site:
        raise HTTPException(status_code=404, detail="原分类不存在")
    
    # 检查新分类名称是否已存在
    if old_name != new_name:
        existing_new_category = db.query(WordPressSite).filter(
            WordPressSite.category == new_name
        ).first()
        
        if existing_new_category:
            raise HTTPException(status_code=400, detail="新分类名称已存在")
    
    # 更新所有使用该分类的站点
    updated_count = db.query(WordPressSite).filter(
        WordPressSite.category == old_name
    ).update({"category": new_name})
    
    db.commit()
    
    return CategoryResponse(
        message=f"成功将分类 '{old_name}' 更新为 '{new_name}'",
        success=True,
        updated_count=updated_count
    )


@router.delete("/categories", response_model=CategoryResponse)
async def delete_site_category(
    category_data: CategoryDelete,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除站点分类"""
    name = category_data.name
    if not name:
        raise HTTPException(status_code=400, detail="分类名称不能为空")
    
    # 检查分类是否存在（包括占位符站点）
    existing_site = db.query(WordPressSite).filter(
        WordPressSite.category == name
    ).first()
    
    if not existing_site:
        raise HTTPException(status_code=404, detail="分类不存在")
    
    # 将使用该分类的真实站点设为 None（未分类）
    updated_count = db.query(WordPressSite).filter(
        WordPressSite.category == name,
        WordPressSite.sync_status != "placeholder"  # 排除占位符站点
    ).update({"category": None})
    
    # 删除占位符站点
    deleted_placeholder = db.query(WordPressSite).filter(
        WordPressSite.category == name,
        WordPressSite.sync_status == "placeholder"
    ).delete()
    
    db.commit()
    
    return CategoryResponse(
        message=f"成功删除分类 '{name}'，{updated_count} 个站点已设为未分类",
        success=True,
        updated_count=updated_count,
        deleted_placeholder=deleted_placeholder
    )


@router.delete("/{site_id}")
async def delete_wordpress_site(
    site_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除WordPress站点"""
    site = db.query(WordPressSite).filter(WordPressSite.id == site_id).first()
    if not site:
        raise HTTPException(status_code=404, detail="站点不存在")
    
    db.delete(site)
    db.commit()
    
    return {"message": "站点删除成功"}


@router.post("/{site_id}/sync", response_model=WordPressSiteSync)
async def sync_wordpress_site(
    site_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """同步WordPress站点信息"""
    site = db.query(WordPressSite).filter(WordPressSite.id == site_id).first()
    if not site:
        raise HTTPException(status_code=404, detail="站点不存在")
    
    try:
        # 设置同步状态
        site.syncing = True
        db.commit()
        
        wp_service = WordPressService(site.url, site.wp_username, site.wp_app_password)
        site_info_result = await wp_service.get_site_info()
        
        if site_info_result["success"]:
            site_info = site_info_result["data"]
            
            # 更新站点信息
            site.wordpress_version = site_info.get("wordpress_version")
            site.theme_name = site_info.get("theme_name")
            site.language = site_info.get("language")
            site.timezone = site_info.get("timezone")
            site.total_posts = site_info.get("total_posts", 0)
            site.total_pages = site_info.get("total_pages", 0)
            site.blog_categories = site_info.get("blog_categories", [])
            site.blog_tags = site_info.get("blog_tags", [])
            site.sync_status = "success"
            site.error_message = None
            site.last_sync_at = utc_now()
            site.syncing = False  # 同步完成

        else:
            site.sync_status = "failed"
            site.error_message = site_info_result["error"]
            site.last_sync_at = utc_now()
            site.syncing = False  # 同步完成
        
        db.commit()
        db.refresh(site)
        
        return WordPressSiteSync(
            id=site.id,
            sync_status=site.sync_status,
            wordpress_version=site.wordpress_version,
            theme_name=site.theme_name,
            language=site.language,
            timezone=site.timezone,
            total_posts=site.total_posts,
            total_pages=site.total_pages,
            blog_categories=site.blog_categories,
            blog_tags=site.blog_tags,
            error_message=site.error_message,
            last_sync_at=site.last_sync_at
        )
        
    except Exception as e:
        site.sync_status = "failed"
        site.error_message = f"同步异常: {str(e)}"
        site.last_sync_at = utc_now()
        site.syncing = False  # 同步完成
        db.commit()
        
        raise HTTPException(status_code=500, detail=f"同步失败: {str(e)}")


@router.post("/sync-all")
async def sync_all_wordpress_sites(
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """批量同步所有WordPress站点信息"""
    # 获取所有启用的站点（排除占位符站点）
    sites = db.query(WordPressSite).filter(
        WordPressSite.is_active == True,
        WordPressSite.sync_status != "placeholder"
    ).all()
    
    if not sites:
        return {
            "success": True,
            "message": "没有需要同步的站点",
            "total_sites": 0,
            "sync_started": False
        }
    
    # 将所有站点标记为正在同步
    for site in sites:
        site.syncing = True
    db.commit()
    
    # 添加后台任务进行批量同步
    background_tasks.add_task(batch_sync_sites, [site.id for site in sites], db)
    
    return {
        "success": True,
        "message": f"已开始同步 {len(sites)} 个站点，同步将在后台进行",
        "total_sites": len(sites),
        "sync_started": True
    }


async def batch_sync_sites(site_ids: List[int], db: Session):
    """批量同步站点的后台任务"""
    import asyncio
    from concurrent.futures import ThreadPoolExecutor
    import logging
    
    logger = logging.getLogger(__name__)
    logger.info(f"开始批量同步 {len(site_ids)} 个站点")
    
    success_count = 0
    failed_count = 0
    
    async def sync_single_site(site_id: int):
        nonlocal success_count, failed_count
        try:
            site = db.query(WordPressSite).filter(WordPressSite.id == site_id).first()
            if not site:
                logger.warning(f"站点 {site_id} 不存在，跳过同步")
                return
            
            logger.info(f"正在同步站点: {site.name} ({site.url})")
            
            wp_service = WordPressService(site.url, site.wp_username, site.wp_app_password)
            site_info_result = await wp_service.get_site_info()
            
            if site_info_result["success"]:
                site_info = site_info_result["data"]
                
                # 更新站点信息
                site.wordpress_version = site_info.get("wordpress_version")
                site.theme_name = site_info.get("theme_name")
                site.language = site_info.get("language")
                site.timezone = site_info.get("timezone")
                site.total_posts = site_info.get("total_posts", 0)
                site.total_pages = site_info.get("total_pages", 0)
                site.blog_categories = site_info.get("blog_categories", [])
                site.blog_tags = site_info.get("blog_tags", [])
                site.sync_status = "success"
                site.error_message = None
                site.last_sync_at = utc_now()
                site.syncing = False  # 同步完成，清除同步状态

                success_count += 1
                logger.info(f"站点同步成功: {site.name}")

            else:
                site.sync_status = "failed"
                site.error_message = site_info_result["error"]
                site.last_sync_at = utc_now()
                site.syncing = False  # 同步完成，清除同步状态
                failed_count += 1
                logger.error(f"站点同步失败: {site.name} - {site_info_result['error']}")
            
            db.commit()
            
        except Exception as e:
            failed_count += 1
            logger.error(f"同步站点 {site_id} 异常: {str(e)}")
            try:
                site = db.query(WordPressSite).filter(WordPressSite.id == site_id).first()
                if site:
                    site.sync_status = "failed"
                    site.error_message = f"同步异常: {str(e)}"
                    site.last_sync_at = utc_now()
                    site.syncing = False  # 同步完成，清除同步状态
                    db.commit()
            except:
                pass
    
    # 并发同步，限制并发数为3，避免过载
    semaphore = asyncio.Semaphore(3)
    
    async def sync_with_semaphore(site_id: int):
        async with semaphore:
            await sync_single_site(site_id)
    
    # 执行批量同步
    tasks = [sync_with_semaphore(site_id) for site_id in site_ids]
    await asyncio.gather(*tasks, return_exceptions=True)
    
    logger.info(f"批量同步完成: 成功 {success_count} 个，失败 {failed_count} 个")


@router.post("/{site_id}/test")
async def test_wordpress_connection(
    site_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """测试WordPress站点连接"""
    site = db.query(WordPressSite).filter(WordPressSite.id == site_id).first()
    if not site:
        raise HTTPException(status_code=404, detail="站点不存在")
    
    try:
        wp_service = WordPressService(site.url, site.wp_username, site.wp_app_password)
        result = await wp_service.test_connection()
        
        return {
            "success": result["success"],
            "message": "连接成功" if result["success"] else result["error"],
            "site_info": result.get("site_info") if result["success"] else None
        }
        
    except Exception as e:
        return {
            "success": False,
            "message": f"测试连接异常: {str(e)}"
        }


@router.put("/{site_id}", response_model=WordPressSiteResponse)
async def update_wordpress_site(
    site_id: int,
    site_data: WordPressSiteUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新WordPress站点"""
    site = db.query(WordPressSite).filter(WordPressSite.id == site_id).first()
    if not site:
        raise HTTPException(status_code=404, detail="站点不存在")
    
    # 验证分类字段，如果提供了category且为空则报错
    if site_data.category is not None and site_data.category.strip() == "":
        raise HTTPException(status_code=400, detail="站点分类不能为空")
    
    # 如果更新了认证信息，测试连接
    if site_data.wp_username or site_data.wp_app_password:
        username = site_data.wp_username if site_data.wp_username else site.wp_username
        password = site_data.wp_app_password if site_data.wp_app_password else site.wp_app_password
        
        wp_service = WordPressService(site.url, username, password)
        connection_test = await wp_service.test_connection()
        
        if not connection_test["success"]:
            raise HTTPException(status_code=400, detail=f"WordPress连接测试失败: {connection_test['error']}")
    
    # 更新字段
    for field, value in site_data.model_dump(exclude_unset=True).items():
        setattr(site, field, value)
    
    db.commit()
    db.refresh(site)
    
    return site


@router.get("/{site_id}/tags", response_model=List[Dict[str, Any]])
async def get_site_tags(
    site_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定站点的标签列表"""
    site = db.query(WordPressSite).filter(WordPressSite.id == site_id).first()
    if not site:
        raise HTTPException(status_code=404, detail="站点不存在")
    
    # 返回站点的标签信息
    if site.blog_tags:
        return site.blog_tags
    else:
        return []


@router.post("/{site_id}/tags/create")
async def create_site_tag(
    site_id: int,
    tag_data: Dict[str, str],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """为指定站点创建新的标签"""
    site = db.query(WordPressSite).filter(WordPressSite.id == site_id).first()
    if not site:
        raise HTTPException(status_code=404, detail="站点不存在")
    
    if not tag_data.get("name"):
        raise HTTPException(status_code=400, detail="标签名称不能为空")
    
    try:
        # 使用WordPress服务创建标签
        wp_service = WordPressService(site.url, site.wp_username, site.wp_app_password)
        result = await wp_service.create_tag(
            name=tag_data["name"],
            description=tag_data.get("description", ""),
            slug=tag_data.get("slug", "")
        )
        
        if result["success"]:
            # 创建成功后，更新站点的标签列表
            new_tag = result["data"]
            
            # 更新站点的blog_tags字段
            if site.blog_tags is None:
                site.blog_tags = []
            
            # 检查标签是否已存在于本地缓存中
            existing_tag = next((tag for tag in site.blog_tags if tag.get("id") == new_tag["id"]), None)
            if not existing_tag:
                site.blog_tags.append(new_tag)
                db.commit()
            
            return {
                "success": True,
                "data": new_tag,
                "message": f"标签'{tag_data['name']}'创建成功"
            }
        else:
            # 如果是标签已存在的错误，尝试从站点重新同步标签
            if result.get("error_code") == "TAG_EXISTS":
                # 重新获取站点标签信息
                tags_result = await wp_service.get_tags()
                if tags_result["success"]:
                    site.blog_tags = tags_result["data"]
                    db.commit()
                    
                    # 查找刚创建的标签（按名称查找）
                    existing_tag = next((tag for tag in tags_result["data"] if tag.get("name") == tag_data["name"]), None)
                    if existing_tag:
                        return {
                            "success": True,
                            "data": existing_tag,
                            "message": f"标签'{tag_data['name']}'已存在，已获取其信息"
                        }
            
            return {
                "success": False,
                "error": result["error"]
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建标签失败: {str(e)}")


 