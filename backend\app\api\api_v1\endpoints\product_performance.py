from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional, List, Dict, Any
from datetime import date, datetime, timedelta
import logging
from sqlalchemy import and_

from app.api import deps
from app.models.user import User
from app.models.alibaba_auth import Ali<PERSON>baAuth
from app.core.config import settings
from app.schemas.alibaba import ProductPerformanceRequest, ProductPerformanceDateRangeRequest
from app.services.alibaba_service import AlibabaService
from app.models.alibaba_product import AlibabaProduct

# 根据配置选择使用真实API还是模拟数据
if settings.ALIBABA_USE_REAL_API:
    from app.services.alibaba_product_service_real import AlibabaProductServiceReal
    product_service = AlibabaProductServiceReal()
    logger = logging.getLogger(__name__)
    logger.info("产品表现API使用真实阿里巴巴API服务")
else:
    from app.services.alibaba_product_service import AlibabaProductService
    product_service = AlibabaProductService()
    logger = logging.getLogger(__name__)
    logger.info("产品表现API使用模拟数据服务")

router = APIRouter()

@router.get("/performance")
def get_product_performance(
    statistics_type: str = Query(..., description="统计周期类型"),
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    product_ids: Optional[str] = Query(None, description="产品ID列表，逗号分隔"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取产品表现数据
    """
    try:
        # 解析产品ID列表
        product_id_list = None
        if product_ids:
            product_id_list = [pid.strip() for pid in product_ids.split(",") if pid.strip()]
        
        # 从本地数据库获取产品表现数据
        performance_data = product_service.get_product_performance(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            statistics_type=statistics_type,
            start_date=start_date,
            end_date=end_date,
            product_ids=product_id_list
        )
        
        return {
            "code": 200,
            "data": performance_data,
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取产品表现数据失败: {e}")
        raise HTTPException(status_code=500, detail="获取产品表现数据失败")

@router.get("/date-range")
def get_product_performance_date_range(
    statistics_type: Optional[str] = Query("day", description="统计周期类型"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取产品表现数据的可用时间范围
    """
    try:
        # 从本地数据库获取时间范围
        date_range = product_service.get_date_range(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            statistics_type=statistics_type
        )
        
        return {
            "code": 200,
            "data": date_range,
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取产品表现时间范围失败: {e}")
        raise HTTPException(status_code=500, detail="获取产品表现时间范围失败")

@router.get("/products")
def get_product_list(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取用户的产品列表
    """
    try:
        # 从本地数据库获取产品列表
        products = product_service.get_available_products(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id
        )
        
        # 转换为简单格式
        product_list = [
            {
                "id": product.product_id,
                "name": product.product_name,
                "title": product.product_title,
                "category": product.product_category,
                "status": product.product_status
            }
            for product in products
        ]
        
        return {
            "code": 200,
            "data": product_list,
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取产品列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取产品列表失败")

@router.post("/sync")
async def sync_product_data(
    statistics_type: str = Query(..., description="统计周期类型"),
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    product_ids: Optional[str] = Query(None, description="产品ID列表，逗号分隔"),
    force_update: bool = Query(False, description="是否强制更新"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    从阿里国际站同步产品表现数据
    """
    try:
        # 检查用户是否有阿里国际站授权
        auth_record = db.query(AlibabaAuth).filter(
            AlibabaAuth.user_id == current_user.id,
            AlibabaAuth.tenant_id == current_user.tenant_id,
            AlibabaAuth.is_active == True
        ).first()
        
        if not auth_record:
            raise HTTPException(
                status_code=400, 
                detail="请先完成阿里国际站授权"
            )
        
        # 检查token是否过期
        token_expires_at = auth_record.token_created_at + timedelta(seconds=auth_record.expires_in)
        if datetime.now() > token_expires_at:
            raise HTTPException(
                status_code=400,
                detail="阿里国际站访问令牌已过期，请重新授权"
            )
        
        # 解析产品ID列表
        product_ids_list = None
        if product_ids:
            product_ids_list = [pid.strip() for pid in product_ids.split(",") if pid.strip()]
        
        # 调用产品服务同步数据
        result = await product_service.sync_data_from_alibaba(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            statistics_type=statistics_type,
            start_date=start_date,
            end_date=end_date,
            product_ids=product_ids_list if product_ids_list else None,
            access_token=auth_record.access_token
        )
        
        # 检查是否需要配置产品ID
        if result.get("message") and "需要配置产品ID" in result.get("message", ""):
            result["setup_guide"] = {
                "message": "需要先配置产品ID才能获取表现数据",
                "guide_url": "/api/v1/alibaba-product/guide",
                "configure_url": "/api/v1/alibaba-product/configure"
            }
        
        logger.info(f"用户 {current_user.id} 同步产品表现数据成功: {result}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步产品表现数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"同步数据失败: {str(e)}")

@router.post("/configure", response_model=Dict[str, Any])
async def configure_alibaba_products(
    product_config: Dict[str, Any],
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
):
    """配置阿里巴巴产品ID列表"""
    try:
        product_service = AlibabaProductServiceReal()
        
        # 从请求中获取产品ID列表
        product_ids = product_config.get("product_ids", [])
        if not product_ids:
            raise HTTPException(status_code=400, detail="产品ID列表不能为空")
        
        # 构建产品数据
        products_data = []
        for product_id in product_ids:
            if isinstance(product_id, dict):
                # 如果是详细的产品信息
                products_data.append({
                    "product_id": str(product_id.get("id", product_id.get("product_id", ""))),
                    "product_name": product_id.get("name", product_id.get("product_name", "")),
                    "product_title": product_id.get("title", product_id.get("product_title", "")),
                    "product_category": product_id.get("category", ""),
                    "product_status": "active",
                    "description": product_id.get("description", "")
                })
            else:
                # 如果只是产品ID字符串
                products_data.append({
                    "product_id": str(product_id),
                    "product_name": f"产品 {product_id}",
                    "product_title": f"产品 {product_id}",
                    "product_category": "",
                    "product_status": "active",
                    "description": ""
                })
        
        # 保存产品配置
        saved_products = product_service.save_products(
            db, 
            current_user.id, 
            current_user.tenant_id,
            products_data
        )
        
        return {
            "success": True,
            "message": f"成功配置 {len(saved_products)} 个产品",
            "products": [
                {
                    "product_id": p.product_id,
                    "product_name": p.product_name,
                    "sync_time": p.sync_time.isoformat() if p.sync_time else None
                }
                for p in saved_products
            ]
        }
    except Exception as e:
        logger.error(f"配置产品失败: {e}")
        raise HTTPException(status_code=500, detail=f"配置产品失败: {str(e)}")

@router.get("/guide", response_model=Dict[str, Any])
async def get_product_setup_guide():
    """获取产品配置指南"""
    return {
        "title": "阿里巴巴产品配置指南",
        "description": "阿里巴巴客户数据API不提供产品列表查询功能，需要手动配置产品ID",
        "steps": [
            {
                "step": 1,
                "title": "登录阿里巴巴国际站",
                "description": "访问 https://www.alibaba.com 并登录您的商家账户"
            },
            {
                "step": 2,
                "title": "进入商品管理",
                "description": "在后台管理中找到'商品管理'或'Product Management'页面"
            },
            {
                "step": 3,
                "title": "获取产品ID",
                "description": "在商品列表中，每个商品都有唯一的产品ID，通常是数字格式"
            },
            {
                "step": 4,
                "title": "配置产品ID",
                "description": "将获取到的产品ID列表配置到系统中，支持批量配置"
            }
        ],
        "api_format": {
            "url": "/api/v1/alibaba-product/configure",
            "method": "POST",
            "example": {
                "product_ids": [
                    "123456789",
                    "987654321",
                    {
                        "id": "111222333",
                        "name": "Sample Product",
                        "title": "High Quality Sample Product",
                        "category": "Electronics"
                    }
                ]
            }
        },
        "note": "配置完成后，系统将使用这些产品ID调用阿里巴巴API获取表现数据"
    }

@router.get("/list", response_model=Dict[str, Any])
async def get_alibaba_product_list(
    page_size: int = Query(default=30, ge=1, le=30, description="每页大小"),
    current_page: int = Query(default=1, ge=1, description="当前页码"),
    status: Optional[str] = Query(None, description="商品状态：approved/auditing/tbd"),
    display: Optional[str] = Query(None, description="上下架状态：Y/N"),
    category_id: Optional[str] = Query(None, description="类目ID"),
    subject: Optional[str] = Query(None, description="商品名称（支持模糊匹配）"),
    sync_to_db: bool = Query(default=True, description="是否同步到数据库"),
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
):
    """获取阿里巴巴商品列表"""
    try:
        # 获取有效的访问令牌
        alibaba_service = AlibabaService(db)
        valid_token = alibaba_service.api_service.get_valid_token(
            db, current_user.id, current_user.tenant_id
        )
        
        if not valid_token:
            raise HTTPException(status_code=401, detail="未找到有效的阿里巴巴授权令牌")
        
        product_service = AlibabaProductServiceReal()
        
        # 获取商品列表
        result = await product_service.get_alibaba_product_list(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            access_token=valid_token,
            page_size=page_size,
            current_page=current_page,
            status=status,
            display=display,
            category_id=category_id,
            subject=subject,
            sync_to_db=sync_to_db
        )
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result.get("error", "获取商品列表失败"))
        
        logger.info(f"用户 {current_user.id} 获取商品列表成功: {result['total_count']} 个商品")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取商品列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取商品列表失败: {str(e)}")

@router.post("/auto-fetch", response_model=Dict[str, Any])
async def auto_fetch_alibaba_products(
    status: Optional[str] = Query(None, description="商品状态筛选"),
    display: Optional[str] = Query(None, description="上下架状态筛选"),
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
):
    """自动从阿里巴巴获取产品ID列表 - 动态获取所有页面"""
    try:
        # 获取有效的访问令牌
        alibaba_service = AlibabaService(db)
        valid_token = alibaba_service.api_service.get_valid_token(
            db, current_user.id, current_user.tenant_id
        )
        
        if not valid_token:
            raise HTTPException(status_code=401, detail="未找到有效的阿里巴巴授权令牌")
        
        product_service = AlibabaProductServiceReal()
        all_products = []
        page_size = 30
        total_items_from_api = 0
        total_pages_needed = 0
        
        logger.info(f"开始自动获取产品列表，筛选条件: status={status}, display={display}")
        
        # 第一步：获取第一页，获取总数信息
        logger.info("获取第一页，分析总商品数量...")
        first_page_result = await product_service.get_alibaba_product_list(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            access_token=valid_token,
            page_size=page_size,
            current_page=1,
            status=status,
            display=display,
            sync_to_db=True
        )
        
        if not first_page_result["success"]:
            raise HTTPException(status_code=400, detail=first_page_result.get("error", "获取第一页失败"))
        
        # 从第一页获取总数信息
        pagination = first_page_result.get("pagination", {})
        total_items_from_api = pagination.get("total_item", 0)
        total_pages_needed = (total_items_from_api + page_size - 1) // page_size  # 向上取整
        
        all_products.extend(first_page_result["products"])
        
        logger.info(f"API返回总商品数: {total_items_from_api}，需要获取: {total_pages_needed} 页，第1页已获取 {len(first_page_result['products'])} 个商品")
        
        # 第二步：如果需要更多页面，继续获取
        if total_pages_needed > 1:
            for page in range(2, total_pages_needed + 1):
                logger.info(f"正在获取第 {page}/{total_pages_needed} 页产品...")
                
                result = await product_service.get_alibaba_product_list(
                    db=db,
                    user_id=current_user.id,
                    tenant_id=current_user.tenant_id,
                    access_token=valid_token,
                    page_size=page_size,
                    current_page=page,
                    status=status,
                    display=display,
                    sync_to_db=True
                )
                
                if not result["success"]:
                    logger.warning(f"获取第{page}页失败: {result.get('error')}")
                    break
                
                products = result["products"]
                all_products.extend(products)
                
                logger.info(f"第 {page} 页获取到 {len(products)} 个商品，累计: {len(all_products)} 个")
                
                # 如果当前页返回的商品数量为0，提前结束
                if len(products) == 0:
                    logger.info(f"第 {page} 页无商品返回，提前结束获取")
                    break
                
                # 如果已获取的商品数量达到或超过总数，结束获取
                if len(all_products) >= total_items_from_api:
                    logger.info(f"已获取所有商品，累计: {len(all_products)} 个，API总数: {total_items_from_api}")
                    break
        
        # 获取最终统计信息
        total_products_in_db = db.query(AlibabaProduct).filter(
            and_(
                AlibabaProduct.user_id == current_user.id,
                AlibabaProduct.tenant_id == current_user.tenant_id
            )
        ).count()
        
        active_products_in_db = db.query(AlibabaProduct).filter(
            and_(
                AlibabaProduct.user_id == current_user.id,
                AlibabaProduct.tenant_id == current_user.tenant_id,
                AlibabaProduct.is_active == True
            )
        ).count()
        
        logger.info(f"用户 {current_user.id} 自动获取商品完成: 本次获取 {len(all_products)} 个，API总数 {total_items_from_api}，需要 {total_pages_needed} 页，数据库总计 {total_products_in_db} 个，活跃 {active_products_in_db} 个")
        
        return {
            "success": True,
            "fetched_products": len(all_products),
            "api_total_items": total_items_from_api,
            "pages_needed": total_pages_needed,
            "pages_fetched": min(len(all_products) // page_size + (1 if len(all_products) % page_size > 0 else 0), total_pages_needed),
            "total_products_in_db": total_products_in_db,
            "active_products_in_db": active_products_in_db,
            "message": f"成功获取并同步了 {len(all_products)} 个商品（API总数: {total_items_from_api}，共 {total_pages_needed} 页），数据库中共有 {total_products_in_db} 个产品（{active_products_in_db} 个活跃）"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"自动获取商品失败: {e}")
        raise HTTPException(status_code=500, detail=f"自动获取商品失败: {str(e)}") 