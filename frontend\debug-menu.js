// 调试菜单数据结构
import router from './src/router/index.js'

console.log('=== 菜单调试信息 ===')
console.log('路由配置:', router.options.routes)

const mainRoute = router.options.routes.find(route => route.path === '/')
if (mainRoute && mainRoute.children) {
  console.log('主路由子项数量:', mainRoute.children.length)
  
  mainRoute.children.forEach((child, index) => {
    console.log(`${index + 1}. ${child.meta?.title || '无标题'}`, {
      path: child.path,
      hasComponent: !!child.component,
      hasChildren: !!child.children,
      childrenCount: child.children?.length || 0,
      alwaysShow: child.alwaysShow,
      meta: child.meta
    })
    
    if (child.children) {
      child.children.forEach((subChild, subIndex) => {
        console.log(`  ${subIndex + 1}. ${subChild.meta?.title || '无标题'}`, {
          path: subChild.path,
          hasComponent: !!subChild.component,
          meta: subChild.meta
        })
      })
    }
  })
} else {
  console.log('未找到主路由或子路由')
} 