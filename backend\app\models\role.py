from sqlalchemy import Column, String, Integer, ForeignKey, DateTime, JSON

from sqlalchemy.orm import relationship
from app.db.session import Base
from app.utils.datetime_utils import utc_now

class Role(Base):
    """角色模型"""
    __tablename__ = "role"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    permissions = Column(JSON, nullable=False, default=list)
    
    # 租户关联
    tenant_id = Column(String(50), ForeignKey("tenant.id"), nullable=False)
    tenant = relationship("Tenant")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), default=utc_now)
    updated_at = Column(DateTime(timezone=True), onupdate=utc_now) 