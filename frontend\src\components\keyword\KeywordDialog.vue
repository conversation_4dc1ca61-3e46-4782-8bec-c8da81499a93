<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑关键词' : '添加关键词'"
    width="600px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="80px"
      size="default"
      @submit.prevent
    >
      <!-- 表单字段区域 -->
      <div class="form-fields">
        <el-form-item label="关键词" prop="keyword_name">
          <el-input
            v-model="formData.keyword_name"
            placeholder="请输入关键词"
            :disabled="isEdit"
            clearable
          />
        </el-form-item>

        <el-form-item label="意图" prop="intent">
          <el-select v-model="formData.intent" placeholder="选择关键词意图" style="width: 100%" clearable>
            <el-option label="信息型 (Informational)" value="Informational" />
            <el-option label="商业型 (Commercial)" value="Commercial" />
            <el-option label="导航型 (Navigational)" value="Navigational" />
            <el-option label="交易型 (Transactional)" value="Transactional" />
          </el-select>
        </el-form-item>

        <el-form-item label="搜索量" prop="volume">
          <el-input-number
            v-model="formData.volume"
            placeholder="搜索量"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="趋势" prop="trend">
          <el-input
            v-model="formData.trend"
            placeholder="趋势数据（JSON格式，如：[0.54,0.54,0.44,0.66,0.81,0.81,0.66,1.00,0.66,0.81,0.66,0.81]）"
            clearable
          />
          <div class="form-tip">
            输入12个月的趋势值，用逗号分隔，范围0-1
          </div>
        </el-form-item>

        <el-form-item label="关键词难度" prop="keyword_difficulty">
          <el-input-number
            v-model="formData.keyword_difficulty"
            placeholder="关键词难度（0-100）"
            :min="0"
            :max="100"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="CPC (USD)" prop="cpc_usd">
          <el-input-number
            v-model="formData.cpc_usd"
            placeholder="每次点击费用（美元）"
            :min="0"
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="竞争密度" prop="competitive_density">
          <el-input-number
            v-model="formData.competitive_density"
            placeholder="竞争密度（0-1）"
            :min="0"
            :max="1"
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="SERP特征" prop="serp_features">
          <el-input
            v-model="formData.serp_features"
            placeholder="SERP特征（JSON格式，如：[&quot;Sitelinks&quot;, &quot;Reviews&quot;, &quot;Image&quot;, &quot;Video&quot;]）"
            clearable
          />
          <div class="form-tip">
            输入SERP特征数组，如：["Sitelinks", "Reviews", "Image", "Video", "People also ask", "Related searches"]
          </div>
        </el-form-item>

        <el-form-item label="搜索结果数" prop="number_of_results">
          <el-input-number
            v-model="formData.number_of_results"
            placeholder="搜索结果数量"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="竞争度" prop="competition_level">
          <el-select v-model="formData.competition_level" placeholder="选择竞争度" style="width: 100%">
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="未知" value="unspecified" />
          </el-select>
        </el-form-item>

        <el-form-item label="国家" prop="location_ids">
          <el-select v-model="formData.location_ids" placeholder="选择国家" style="width: 100%">
            <el-option label="美国" value="2840" />
            <el-option label="英国" value="2826" />
            <el-option label="德国" value="2276" />
            <el-option label="法国" value="2250" />
            <el-option label="意大利" value="2380" />
            <el-option label="西班牙" value="2724" />
            <el-option label="加拿大" value="2124" />
            <el-option label="澳大利亚" value="2036" />
            <el-option label="中国" value="2156" />
            <el-option label="日本" value="2392" />
          </el-select>
          <div class="form-tip">
            常用：中国(2156)，美国(2840)，英国(2826)，日本(2392)
          </div>
        </el-form-item>

        <el-form-item label="分类" prop="category">
          <el-select
            v-model="formData.category"
            placeholder="请选择或输入分类"
            filterable
            allow-create
            default-first-option
            :reserve-keyword="false"
            style="width: 100%;"
          >
            <el-option
              v-for="category in categoryList"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="标签" prop="tags">
          <el-input
            v-model="formData.tags"
            placeholder="标签（逗号分隔）"
            clearable
          />
        </el-form-item>

        <!-- 高级字段 -->
        <el-collapse v-model="activeCollapse" class="advanced-fields">
          <el-collapse-item title="高级设置（兼容字段）" name="advanced">
            <el-form-item label="平均月搜索量" prop="avg_monthly_searches">
              <el-input-number
                v-model="formData.avg_monthly_searches"
                placeholder="平均月搜索量（兼容字段）"
                :min="0"
                style="width: 100%"
              />
              <div class="form-tip">
                兼容字段，建议使用上方的"搜索量"字段
              </div>
            </el-form-item>

            <el-form-item label="竞争指数" prop="competition_index">
              <el-input-number
                v-model="formData.competition_index"
                placeholder="0-100"
                :min="0"
                :max="100"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="低出价(微货币)" prop="low_bid_micros">
              <el-input-number
                v-model="formData.low_bid_micros"
                placeholder="低出价(微货币)"
                :min="0"
                style="width: 100%"
              />
              <div class="form-tip">
                注：1元 = 1,000,000微货币单位
              </div>
            </el-form-item>

            <el-form-item label="高出价(微货币)" prop="high_bid_micros">
              <el-input-number
                v-model="formData.high_bid_micros"
                placeholder="高出价(微货币)"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="货币代码" prop="currency_code">
              <el-select v-model="formData.currency_code" placeholder="选择货币" style="width: 100%">
                <el-option label="人民币 (CNY)" value="CNY" />
                <el-option label="美元 (USD)" value="USD" />
                <el-option label="欧元 (EUR)" value="EUR" />
                <el-option label="英镑 (GBP)" value="GBP" />
                <el-option label="日元 (JPY)" value="JPY" />
              </el-select>
            </el-form-item>

            <el-form-item label="语言代码" prop="language_code">
              <el-select v-model="formData.language_code" placeholder="选择语言" style="width: 100%">
                <el-option label="简体中文" value="zh-CN" />
                <el-option label="繁体中文" value="zh-TW" />
                <el-option label="英文" value="en" />
                <el-option label="日文" value="ja" />
                <el-option label="韩文" value="ko" />
              </el-select>
            </el-form-item>

            <el-form-item label="更新方式" prop="update_method">
              <el-select v-model="formData.update_method" placeholder="选择更新方式" style="width: 100%">
                <el-option label="手工" value="manual" />
                <el-option label="Google Ads API" value="google_ads_api" />
                <el-option label="批量导入" value="batch_import" />
              </el-select>
            </el-form-item>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { keywordService } from '@/services/keyword'

export default {
  name: 'KeywordDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    keywordData: {
      type: Object,
      default: null
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    const formRef = ref()
    const submitting = ref(false)
    const categoryList = ref([])
    const activeCollapse = ref([])

    // 计算属性
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })

    // 加载分类列表
    const loadCategories = async () => {
      try {
        const categories = await keywordService.getCategories()
        categoryList.value = categories || []
      } catch (error) {
        console.error('加载分类失败:', error)
        categoryList.value = []
      }
    }

    // 表单数据
    const formData = reactive({
      keyword_name: '',
      // 新字段
      intent: '',
      volume: null,
      trend: '',
      keyword_difficulty: null,
      cpc_usd: null,
      competitive_density: null,
      serp_features: '',
      number_of_results: null,
      // 原有字段
      avg_monthly_searches: null,
      competition_level: 'unspecified',
      competition_index: null,
      low_bid_micros: null,
      high_bid_micros: null,
      currency_code: 'CNY',
      language_code: 'zh-CN',
      location_ids: '2156',
      update_method: 'manual',
      tags: '',
      category: ''
    })

    // 表单验证规则
    const rules = {
      keyword_name: [
        { required: true, message: '请输入关键词名', trigger: 'blur' },
        { min: 1, max: 255, message: '长度在 1 到 255 个字符', trigger: 'blur' }
      ],
      competition_level: [
        { required: true, message: '请选择竞争级别', trigger: 'change' }
      ],
      currency_code: [
        { required: true, message: '请选择货币代码', trigger: 'change' }
      ],
      language_code: [
        { required: true, message: '请选择语言代码', trigger: 'change' }
      ],
      update_method: [
        { required: true, message: '请选择更新方式', trigger: 'change' }
      ]
    }

    // 监听数据变化
    watch(() => props.keywordData, (newData) => {
      if (newData && props.isEdit) {
        // 编辑模式，填充数据
        Object.assign(formData, {
          keyword_name: newData.keyword_name || '',
          // 新字段
          intent: newData.intent || '',
          volume: newData.volume,
          trend: newData.trend || '',
          keyword_difficulty: newData.keyword_difficulty,
          cpc_usd: newData.cpc_usd,
          competitive_density: newData.competitive_density,
          serp_features: newData.serp_features || '',
          number_of_results: newData.number_of_results,
          // 原有字段
          avg_monthly_searches: newData.avg_monthly_searches,
          competition_level: newData.competition_level || 'unspecified',
          competition_index: newData.competition_index,
          low_bid_micros: newData.low_bid_micros,
          high_bid_micros: newData.high_bid_micros,
          currency_code: newData.currency_code || 'CNY',
          language_code: newData.language_code || 'zh-CN',
          location_ids: newData.location_ids || '2156',
          update_method: newData.update_method || 'manual',
          tags: newData.tags || '',
          category: newData.category || ''
        })
      }
    }, { immediate: true })

    // 监听对话框显示
    watch(() => props.visible, (visible) => {
      if (visible && !props.isEdit) {
        // 新建模式，重置表单
        Object.assign(formData, {
          keyword_name: '',
          // 新字段
          intent: '',
          volume: null,
          trend: '',
          keyword_difficulty: null,
          cpc_usd: null,
          competitive_density: null,
          serp_features: '',
          number_of_results: null,
          // 原有字段
          avg_monthly_searches: null,
          competition_level: 'unspecified',
          competition_index: null,
          low_bid_micros: null,
          high_bid_micros: null,
          currency_code: 'CNY',
          language_code: 'zh-CN',
          location_ids: '2156',
          update_method: 'manual',
          tags: '',
          category: ''
        })
      }
      
      if (visible) {
        // 加载分类数据
        loadCategories()
        // 清除验证状态
        setTimeout(() => {
          formRef.value?.clearValidate()
        }, 100)
      }
    })

    // 关闭对话框
    const handleClose = () => {
      dialogVisible.value = false
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        // 验证表单
        await formRef.value.validate()

        submitting.value = true

        // 准备提交数据
        const submitData = { ...formData }
        
        // 过滤空值
        Object.keys(submitData).forEach(key => {
          if (submitData[key] === '' || submitData[key] === null || submitData[key] === undefined) {
            delete submitData[key]
          }
        })

        if (props.isEdit) {
          // 更新关键词
          await keywordService.updateKeyword(props.keywordData.id, submitData)
          ElMessage.success('更新成功')
        } else {
          // 创建关键词
          await keywordService.createKeyword(submitData)
          ElMessage.success('创建成功')
        }

        emit('success')
      } catch (error) {
        console.error('提交失败:', error)
        if (error.response?.data?.detail) {
          ElMessage.error(error.response.data.detail)
        } else {
          ElMessage.error('操作失败')
        }
      } finally {
        submitting.value = false
      }
    }

    return {
      formRef,
      submitting,
      dialogVisible,
      formData,
      rules,
      handleClose,
      handleSubmit,
      categoryList,
      loadCategories,
      activeCollapse
    }
  }
}
</script>

<style scoped>
.form-fields {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 8px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.advanced-fields {
  margin-top: 16px;
}

.advanced-fields :deep(.el-collapse-item__header) {
  font-size: 14px;
  color: #606266;
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 0 12px;
}

.advanced-fields :deep(.el-collapse-item__content) {
  padding-top: 16px;
}

.text-muted {
  color: #999;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}
</style>