@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul 2>&1
:: 设置控制台标题
title AI外贸运营系统 - 生产环境启动脚本

echo ========================================
echo     AI外贸运营系统 - 生产环境启动脚本
echo ========================================
echo.

:: 切换到项目根目录（CBEC子目录）
cd /d "%~dp0.."
set "CBEC_ROOT=%CD%"

:: 获取真实的项目根目录（CBEC的上级目录）
cd /d "%CBEC_ROOT%\.."
set "PROJECT_ROOT=%CD%"

set "VENV_ABS_PATH=%PROJECT_ROOT%\venv"
set "BACKEND_PATH=%CBEC_ROOT%\backend"
set "FRONTEND_PATH=%CBEC_ROOT%\frontend"
set "NGINX_CONF=%CBEC_ROOT%\nginx.conf"

echo 项目根目录: %PROJECT_ROOT%
echo CBEC子项目目录: %CBEC_ROOT%
echo 后端目录: %BACKEND_PATH%
echo 前端目录: %FRONTEND_PATH%
echo 虚拟环境: %VENV_ABS_PATH%
echo Nginx配置文件: %NGINX_CONF%
echo.

:: 检查必要文件
if not exist "%VENV_ABS_PATH%\Scripts\activate.bat" (
    echo 错误: 虚拟环境不存在: %VENV_ABS_PATH%
    pause
    exit /b 1
)

if not exist "%NGINX_CONF%" (
    echo 错误: Nginx配置文件不存在: %NGINX_CONF%
    pause
    exit /b 1
)

:: 检查nginx是否安装
nginx -v >nul 2>&1
if errorlevel 1 (
    echo 错误: Nginx未安装或不在PATH中
    echo 请安装Nginx并确保可在命令行中使用
    pause
    exit /b 1
)

echo 所有前置条件检查通过
echo.

:: 检测服务运行状态函数定义
echo 检查服务运行状态...
echo.

:: 检查后端服务 (端口5000)
set "BACKEND_RUNNING=false"
netstat -an | findstr ":5000" | findstr "LISTENING" >nul 2>&1
if not errorlevel 1 (
    set "BACKEND_RUNNING=true"
    echo [检测] 后端服务已运行 (端口5000已被监听)
) else (
    echo [检测] 后端服务未运行
)

:: 检查前端服务 (端口8080)
set "FRONTEND_RUNNING=false"
netstat -an | findstr ":8080" | findstr "LISTENING" >nul 2>&1
if not errorlevel 1 (
    set "FRONTEND_RUNNING=true"
    echo [检测] 前端服务已运行 (端口8080已被监听)
) else (
    echo [检测] 前端服务未运行
)

:: 检查Nginx服务 (端口8000)
set "NGINX_RUNNING=false"
netstat -an | findstr ":8000" | findstr "LISTENING" >nul 2>&1
if not errorlevel 1 (
    set "NGINX_RUNNING=true"
    echo [检测] Nginx服务已运行 (端口8000已被监听)
) else (
    echo [检测] Nginx服务未运行
)

echo.

:: 激活虚拟环境
echo 激活Python虚拟环境...
call "%VENV_ABS_PATH%\Scripts\activate.bat"
if errorlevel 1 (
    echo 错误: 虚拟环境激活失败
    pause
    exit /b 1
)

:: 设置环境变量
set NODE_ENV=production
set VUE_APP_API_BASE_URL=

echo 环境变量设置完成:
echo NODE_ENV=%NODE_ENV%
echo VUE_APP_API_BASE_URL=%VUE_APP_API_BASE_URL%
echo.

:: 启动后端服务
if "%BACKEND_RUNNING%"=="true" (
    echo [跳过] 后端服务已在运行，跳过启动
) else (
    echo 启动后端FastAPI服务...
    cd /d "%BACKEND_PATH%"
    start "AI外贸系统-后端" cmd /k "python main.py"
    echo 后端服务启动命令已执行，等待2秒...
    timeout /t 2 /nobreak >nul
)

:: 启动前端服务
if "%FRONTEND_RUNNING%"=="true" (
    echo [跳过] 前端服务已在运行，跳过启动
) else (
    echo 启动前端Vue服务...
    cd /d "%FRONTEND_PATH%"
    start "AI外贸系统-前端" cmd /k "npm run serve"
    echo 前端服务启动命令已执行，等待5秒...
    timeout /t 5 /nobreak >nul
)

:: 启动Nginx
if "%NGINX_RUNNING%"=="true" (
    echo [跳过] Nginx服务已在运行，跳过启动
) else (
    echo 启动Nginx代理服务...
    cd /d "%CBEC_ROOT%"
    start "AI外贸系统-Nginx" cmd /k "nginx -c %NGINX_CONF%"
    echo Nginx启动命令已执行
)

echo.

echo ========================================
echo         服务启动检查完成！
echo ========================================
echo.
echo 服务访问地址:
echo   主入口 (Nginx): http://localhost:8000
echo   前端直接访问: http://localhost:8080
echo   后端直接访问: http://localhost:5000
echo   API文档: http://localhost:5000/docs
echo.
echo 服务状态总结:
if "%BACKEND_RUNNING%"=="true" (
    echo   后端服务: 已运行 ^(跳过启动^)
) else (
    echo   后端服务: 新启动
)
if "%FRONTEND_RUNNING%"=="true" (
    echo   前端服务: 已运行 ^(跳过启动^)
) else (
    echo   前端服务: 新启动
)
if "%NGINX_RUNNING%"=="true" (
    echo   Nginx服务: 已运行 ^(跳过启动^)
) else (
    echo   Nginx服务: 新启动
)
echo.
echo 注意事项:
echo 1. 请确保所有端口(5000, 8080, 8000)未被占用
echo 2. 外网访问请使用 8000 端口
echo 3. 关闭服务请直接关闭对应的命令行窗口
echo.

pause 