-- 为 google_ads_config 表添加 OAuth 相关字段
-- 这个脚本用于升级现有数据库结构

-- 添加 OAuth 状态字段
ALTER TABLE `google_ads_config` 
ADD COLUMN `oauth_state` varchar(255) DEFAULT NULL COMMENT 'OAuth状态参数' AFTER `login_customer_id`,
ADD COLUMN `authorization_status` varchar(20) DEFAULT 'pending' COMMENT '授权状态: pending, authorized, expired, failed' AFTER `oauth_state`,
ADD COLUMN `last_auth_time` timestamp NULL DEFAULT NULL COMMENT '最后授权时间' AFTER `authorization_status`,
ADD COLUMN `access_token` varchar(500) DEFAULT NULL COMMENT '访问令牌（临时存储）' AFTER `last_auth_time`,
ADD COLUMN `token_expiry` timestamp NULL DEFAULT NULL COMMENT '令牌过期时间' AFTER `access_token`;

-- 更新现有记录的授权状态（如果已有刷新令牌则标记为已授权）
UPDATE `google_ads_config` 
SET `authorization_status` = 'authorized' 
WHERE `refresh_token` IS NOT NULL AND `refresh_token` != '';

-- 为授权状态字段添加索引以提高查询性能
ALTER TABLE `google_ads_config` 
ADD INDEX `idx_authorization_status` (`authorization_status`); 