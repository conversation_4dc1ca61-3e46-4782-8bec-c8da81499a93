"""update type field comment

Revision ID: update_type_field_comment
Revises: create_ai_tables
Create Date: 2024-12-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text


# revision identifiers, used by Alembic.
revision = 'update_type_field_comment'
down_revision = 'create_ai_tables'
branch_labels = None
depends_on = None


def upgrade():
    """更新type字段注释"""
    # 修改ai_articles表的type字段注释
    op.execute(text("""
    ALTER TABLE `ai_articles` 
    MODIFY COLUMN `type` varchar(50) NOT NULL COMMENT '博客分类ID'
    """))
    
    print("✅ ai_articles表type字段注释已更新为'博客分类ID'")


def downgrade():
    """回滚type字段注释"""
    # 回滚ai_articles表的type字段注释
    op.execute(text("""
    ALTER TABLE `ai_articles` 
    MODIFY COLUMN `type` varchar(50) NOT NULL COMMENT '类型，如Floor'
    """))
    
    print("✅ ai_articles表type字段注释已回滚为'类型，如Floor'") 