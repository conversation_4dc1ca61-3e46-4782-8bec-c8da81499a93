/* 现代化设计系统 */
:root {
  /* 主色调 */
  --primary-color: #409EFF;
  --primary-light: #66B1FF;
  --primary-dark: #337ECC;
  
  /* 辅助色彩 */
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  
  /* 中性色彩 */
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #C0C4CC;
  
  /* 背景色彩 */
  --bg-white: #FFFFFF;
  --bg-light: #F5F7FA;
  --bg-lighter: #FAFAFA;
  --bg-extra-light: #F2F6FC;
  
  /* 边框色彩 */
  --border-base: #DCDFE6;
  --border-light: #E4E7ED;
  --border-lighter: #EBEEF5;
  --border-extra-light: #F2F6FC;
  
  /* 阴影 */
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.15);
  
  /* 圆角 */
  --radius-base: 8px;
  --radius-small: 4px;
  --radius-large: 12px;
  --radius-round: 50%;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  /* 字体 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  /* 行高 */
  --line-height-base: 1.5;
  --line-height-tight: 1.25;
  --line-height-loose: 1.75;
  
  /* 过渡动画 */
  --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s ease-out;
  --transition-slow: all 0.5s ease-out;
  
  /* Z-index */
  --z-tooltip: 1000;
  --z-dropdown: 1001;
  --z-modal: 1002;
  --z-notification: 1003;
  --z-loading: 1004;
}

/* 暗黑主题 */
:root.dark {
  --text-primary: #E5EAF3;
  --text-regular: #CFD3DC;
  --text-secondary: #A3A6AD;
  --text-placeholder: #8D9095;
  
  --bg-white: #1D1E1F;
  --bg-light: #141414;
  --bg-lighter: #1A1A1A;
  --bg-extra-light: #1F1F1F;
  
  --border-base: #4C4D4F;
  --border-light: #414243;
  --border-lighter: #363637;
  --border-extra-light: #2B2B2C;
  
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.24), 0 0 6px rgba(0, 0, 0, 0.12);
  --shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.24);
  --shadow-dark: 0 4px 12px rgba(0, 0, 0, 0.32);
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--text-primary);
  background-color: var(--bg-light);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-lighter);
  border-radius: var(--radius-small);
}

::-webkit-scrollbar-thumb {
  background: var(--border-base);
  border-radius: var(--radius-small);
  transition: var(--transition-base);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* 选中文本样式 */
::selection {
  background: var(--primary-light);
  color: white;
}

/* 焦点样式 */
*:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 辅助类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }

.rounded { border-radius: var(--radius-base); }
.rounded-sm { border-radius: var(--radius-small); }
.rounded-lg { border-radius: var(--radius-large); }

.shadow { box-shadow: var(--shadow-base); }
.shadow-lg { box-shadow: var(--shadow-light); }

.transition { transition: var(--transition-base); }

/* 卡片样式 */
.card {
  background: var(--bg-white);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-base);
  padding: var(--spacing-lg);
  transition: var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-light);
  transform: translateY(-2px);
}

/* 按钮样式增强 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-base);
  text-decoration: none;
  gap: var(--spacing-xs);
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-light);
}

/* 表格样式 */
.table-modern {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-white);
  border-radius: var(--radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-base);
}

.table-modern th,
.table-modern td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--border-lighter);
}

.table-modern th {
  background: var(--bg-extra-light);
  font-weight: 600;
  color: var(--text-regular);
}

.table-modern tr:hover {
  background: var(--bg-extra-light);
}

/* 表单样式 */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-regular);
}

.form-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-base);
  border-radius: var(--radius-base);
  font-size: var(--font-size-sm);
  transition: var(--transition-base);
  background: var(--bg-white);
  color: var(--text-primary);
}

.form-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
}

/* 动画类 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

.fade-in {
  animation: fadeIn 0.5s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
  :root {
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 24px;
    --font-size-base: 14px;
  }
  
  .card {
    padding: var(--spacing-md);
  }
}

/* 打印样式 */
@media print {
  .sidebar-container,
  .header-container {
    display: none !important;
  }
  
  .main-container {
    margin-left: 0 !important;
  }
} 