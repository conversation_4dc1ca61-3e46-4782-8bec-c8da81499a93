import sys
import os
sys.path.append('./backend')

from sqlalchemy import create_engine, text
from backend.app.core.config import settings

def check_database_structure():
    engine = create_engine(settings.DATABASE_URL)
    conn = engine.connect()
    
    print("检查标签相关字段...")
    
    # 检查 ai_articles 表
    print("\n=== ai_articles 表 ===")
    result = conn.execute(text('DESCRIBE ai_articles'))
    tags_field_found = False
    for row in result:
        if 'tag' in row[0].lower():
            print(f"  ✓ {row[0]} - {row[1]}")
            tags_field_found = True
    if not tags_field_found:
        print("  ✗ 未找到标签字段")
    
    # 检查 scheduled_publish_plans 表
    print("\n=== scheduled_publish_plans 表 ===")
    result = conn.execute(text('DESCRIBE scheduled_publish_plans'))
    default_tags_field_found = False
    for row in result:
        if 'tag' in row[0].lower():
            print(f"  ✓ {row[0]} - {row[1]}")
            default_tags_field_found = True
    if not default_tags_field_found:
        print("  ✗ 未找到标签字段")
    
    # 检查 scheduled_publish_tasks 表
    print("\n=== scheduled_publish_tasks 表 ===")
    result = conn.execute(text('DESCRIBE scheduled_publish_tasks'))
    blog_tags_field_found = False
    for row in result:
        if 'tag' in row[0].lower():
            print(f"  ✓ {row[0]} - {row[1]}")
            blog_tags_field_found = True
    if not blog_tags_field_found:
        print("  ✗ 未找到标签字段")
    
    conn.close()
    
    # 总结
    print("\n=== 总结 ===")
    if tags_field_found and default_tags_field_found and blog_tags_field_found:
        print("✓ 所有必需的标签字段都已存在")
        return True
    else:
        print("✗ 某些标签字段缺失")
        return False

if __name__ == "__main__":
    try:
        check_database_structure()
    except Exception as e:
        print(f"检查数据库时出错: {e}") 
 