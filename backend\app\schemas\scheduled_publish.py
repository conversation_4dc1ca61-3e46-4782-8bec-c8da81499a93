from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, time


# 选词策略Schema
class KeywordStrategyConfig(BaseModel):
    intent: Optional[str] = Field(None, description="关键词意图筛选")
    volume_min: Optional[int] = Field(None, description="最小搜索量")
    volume_max: Optional[int] = Field(None, description="最大搜索量")
    difficulty_min: Optional[int] = Field(None, ge=0, le=100, description="最小难度(0-100)")
    difficulty_max: Optional[int] = Field(None, ge=0, le=100, description="最大难度(0-100)")
    cpc_min: Optional[float] = Field(None, ge=0, description="最小CPC(USD)")
    cpc_max: Optional[float] = Field(None, ge=0, description="最大CPC(USD)")
    competitive_density_min: Optional[float] = Field(None, ge=0, le=1, description="最小竞争密度(0-1)")
    competitive_density_max: Optional[float] = Field(None, ge=0, le=1, description="最大竞争密度(0-1)")
    countries: Optional[List[str]] = Field(None, description="国家列表")
    categories: Optional[List[str]] = Field(None, description="分类列表")


# 定时发布计划相关Schema
class ScheduledPublishPlanBase(BaseModel):
    plan_name: str = Field(..., description="任务计划名称")
    keyword_source: str = Field("custom", description="关键词来源：custom/library/strategy")
    keywords: Optional[str] = Field(None, description="自定义关键词，逗号分隔")
    keyword_categories: Optional[List[str]] = Field(None, description="选择的词库分类")
    keyword_selection_strategy: Optional[str] = Field("random_one", description="智能推荐策略：random_one等")

    # 选词策略配置
    use_keyword_strategy: bool = Field(False, description="是否使用选词策略")
    strategy_intent: Optional[str] = Field(None, description="策略-关键词意图筛选")
    strategy_volume_min: Optional[int] = Field(None, description="策略-最小搜索量")
    strategy_volume_max: Optional[int] = Field(None, description="策略-最大搜索量")
    strategy_difficulty_min: Optional[int] = Field(None, ge=0, le=100, description="策略-最小难度(0-100)")
    strategy_difficulty_max: Optional[int] = Field(None, ge=0, le=100, description="策略-最大难度(0-100)")
    strategy_cpc_min: Optional[float] = Field(None, ge=0, description="策略-最小CPC(USD)")
    strategy_cpc_max: Optional[float] = Field(None, ge=0, description="策略-最大CPC(USD)")
    strategy_competitive_density_min: Optional[float] = Field(None, ge=0, le=1, description="策略-最小竞争密度(0-1)")
    strategy_competitive_density_max: Optional[float] = Field(None, ge=0, le=1, description="策略-最大竞争密度(0-1)")
    strategy_countries: Optional[List[str]] = Field(None, description="策略-国家列表")
    strategy_categories: Optional[List[str]] = Field(None, description="策略-分类列表")

    site_categories: Optional[List[str]] = Field(None, description="选择的站点分类")
    selected_sites: Optional[List[int]] = Field(None, description="自定义选择的站点ID列表")
    default_blog_category: Optional[str] = Field(None, description="默认文章分类")
    default_blog_category_id: Optional[int] = Field(None, description="默认文章分类ID")
    default_blog_tags: Optional[str] = Field(None, description="默认博客标签ID，多个用逗号分隔")
    ai_model: Optional[str] = Field(None, description="AI模型")
    ai_config_id: Optional[int] = Field(None, description="AI配置ID")
    scheduled_time: datetime = Field(..., description="计划开始时间")
    end_time: Optional[datetime] = Field(None, description="任务结束时间")
    frequency_type: str = Field("once", description="发布频率类型：once/daily/weekly/custom")
    weekly_days: Optional[List[str]] = Field(None, description="每周执行日期（0-6，0为周日）")
    custom_interval_value: Optional[int] = Field(None, description="自定义间隔数值")
    custom_interval_unit: Optional[str] = Field(None, description="自定义间隔单位：hours/days/weeks")
    daily_time: Optional[time] = Field(None, description="每日执行时间（HH:MM格式）")
    max_executions: Optional[int] = Field(None, description="最大执行次数限制")


class ScheduledPublishPlanCreate(ScheduledPublishPlanBase):
    pass


class ScheduledPublishPlanUpdate(BaseModel):
    plan_name: Optional[str] = Field(None, description="任务计划名称")
    keyword_source: Optional[str] = Field(None, description="关键词来源：custom/library/strategy")
    keywords: Optional[str] = Field(None, description="自定义关键词，逗号分隔")
    keyword_categories: Optional[List[str]] = Field(None, description="选择的词库分类")
    keyword_selection_strategy: Optional[str] = Field(None, description="智能推荐策略：random_one等")

    # 选词策略配置
    use_keyword_strategy: Optional[bool] = Field(None, description="是否使用选词策略")
    strategy_intent: Optional[str] = Field(None, description="策略-关键词意图筛选")
    strategy_volume_min: Optional[int] = Field(None, description="策略-最小搜索量")
    strategy_volume_max: Optional[int] = Field(None, description="策略-最大搜索量")
    strategy_difficulty_min: Optional[int] = Field(None, ge=0, le=100, description="策略-最小难度(0-100)")
    strategy_difficulty_max: Optional[int] = Field(None, ge=0, le=100, description="策略-最大难度(0-100)")
    strategy_cpc_min: Optional[float] = Field(None, ge=0, description="策略-最小CPC(USD)")
    strategy_cpc_max: Optional[float] = Field(None, ge=0, description="策略-最大CPC(USD)")
    strategy_competitive_density_min: Optional[float] = Field(None, ge=0, le=1, description="策略-最小竞争密度(0-1)")
    strategy_competitive_density_max: Optional[float] = Field(None, ge=0, le=1, description="策略-最大竞争密度(0-1)")
    strategy_countries: Optional[List[str]] = Field(None, description="策略-国家列表")
    strategy_categories: Optional[List[str]] = Field(None, description="策略-分类列表")

    site_categories: Optional[List[str]] = Field(None, description="选择的站点分类")
    selected_sites: Optional[List[int]] = Field(None, description="自定义选择的站点ID列表")
    default_blog_category: Optional[str] = Field(None, description="默认文章分类")
    default_blog_category_id: Optional[int] = Field(None, description="默认文章分类ID")
    default_blog_tags: Optional[str] = Field(None, description="默认博客标签ID，多个用逗号分隔")
    ai_model: Optional[str] = Field(None, description="AI模型")
    ai_config_id: Optional[int] = Field(None, description="AI配置ID")
    scheduled_time: Optional[datetime] = Field(None, description="计划开始时间")
    end_time: Optional[datetime] = Field(None, description="任务结束时间")
    frequency_type: Optional[str] = Field(None, description="发布频率类型：once/daily/weekly/custom")
    weekly_days: Optional[List[str]] = Field(None, description="每周执行日期（0-6，0为周日）")
    custom_interval_value: Optional[int] = Field(None, description="自定义间隔数值")
    custom_interval_unit: Optional[str] = Field(None, description="自定义间隔单位：hours/days/weeks")
    daily_time: Optional[time] = Field(None, description="每日执行时间（HH:MM格式）")
    max_executions: Optional[int] = Field(None, description="最大执行次数限制")
    is_active: Optional[bool] = Field(None, description="是否启用")


class ScheduledPublishPlanResponse(ScheduledPublishPlanBase):
    id: int
    current_executions: int = Field(default=0, description="当前已执行次数")
    is_active: bool = Field(default=True, description="是否启用")
    created_at: datetime
    updated_at: datetime
    last_execution_time: Optional[datetime] = Field(None, description="上次执行时间")
    total_tasks: int = Field(default=0, description="总任务数")
    success_tasks: int = Field(default=0, description="成功任务数")

    class Config:
        from_attributes = True


# 定时发布任务相关Schema
class ScheduledPublishTaskBase(BaseModel):
    plan_id: int = Field(..., description="计划ID")
    keywords: str = Field(..., description="分配的关键词")
    wordpress_url: str = Field(..., description="WordPress站点URL")
    site_name: str = Field(..., description="站点名称")
    blog_category_id: Optional[int] = Field(None, description="文章分类ID")
    blog_category_name: Optional[str] = Field(None, description="文章分类名称")
    blog_tags: Optional[str] = Field(None, description="博客标签ID，多个用逗号分隔")
    ai_model: Optional[str] = Field(None, description="AI模型")
    execution_sequence: Optional[int] = Field(None, description="执行序号")
    scheduled_execution_time: Optional[datetime] = Field(None, description="计划执行时间")


class ScheduledPublishTaskCreate(ScheduledPublishTaskBase):
    pass


class ScheduledPublishTaskResponse(ScheduledPublishTaskBase):
    id: int
    status: str = Field(default="pending", description="任务状态")
    actual_keyword: Optional[str] = Field(None, description="实际选择的关键词（动态选词时使用）")
    queue_position: Optional[int] = Field(None, description="排队位置")
    execution_time: Optional[datetime] = Field(None, description="实际执行时间")
    completion_time: Optional[datetime] = Field(None, description="完成时间")
    ai_article_id: Optional[int] = Field(None, description="关联的AI文章ID")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 分页响应Schema
class ScheduledPublishPlanList(BaseModel):
    items: List[ScheduledPublishPlanResponse]
    total: int
    page: int
    size: int


class ScheduledPublishTaskList(BaseModel):
    items: List[ScheduledPublishTaskResponse]
    total: int
    page: int
    size: int


# 站点配置项Schema
class SiteConfigItem(BaseModel):
    site_id: int = Field(..., description="站点ID")
    site_name: str = Field(..., description="站点名称")
    site_url: str = Field(..., description="站点URL")
    keywords: List[str] = Field(..., description="分配的关键词列表")
    blog_category_id: Optional[int] = Field(None, description="文章分类ID")
    blog_category_name: Optional[str] = Field(None, description="文章分类名称")
    blog_tags: Optional[List[int]] = Field(None, description="博客标签ID列表")


# 批量任务生成请求Schema
class BatchTaskGenerateRequest(BaseModel):
    plan_name: str = Field(..., description="任务计划名称")
    keyword_source: str = Field(..., description="关键词来源：custom/library/strategy")
    custom_keywords: Optional[str] = Field(None, description="自定义关键词")
    keyword_categories: Optional[List[str]] = Field(None, description="词库分类")
    keyword_selection_strategy: Optional[str] = Field("random_one", description="智能推荐策略：random_one等")

    # 选词策略配置
    use_keyword_strategy: bool = Field(False, description="是否使用选词策略")
    strategy_intent: Optional[str] = Field(None, description="策略-关键词意图筛选")
    strategy_volume_min: Optional[int] = Field(None, description="策略-最小搜索量")
    strategy_volume_max: Optional[int] = Field(None, description="策略-最大搜索量")
    strategy_difficulty_min: Optional[int] = Field(None, ge=0, le=100, description="策略-最小难度(0-100)")
    strategy_difficulty_max: Optional[int] = Field(None, ge=0, le=100, description="策略-最大难度(0-100)")
    strategy_cpc_min: Optional[float] = Field(None, ge=0, description="策略-最小CPC(USD)")
    strategy_cpc_max: Optional[float] = Field(None, ge=0, description="策略-最大CPC(USD)")
    strategy_competitive_density_min: Optional[float] = Field(None, ge=0, le=1, description="策略-最小竞争密度(0-1)")
    strategy_competitive_density_max: Optional[float] = Field(None, ge=0, le=1, description="策略-最大竞争密度(0-1)")
    strategy_countries: Optional[List[str]] = Field(None, description="策略-国家列表")
    strategy_categories: Optional[List[str]] = Field(None, description="策略-分类列表")

    site_source: str = Field(..., description="站点来源：category/custom")
    site_categories: Optional[List[str]] = Field(None, description="站点分类")
    selected_sites: Optional[List[int]] = Field(None, description="选择的站点ID")
    site_configs: List[SiteConfigItem] = Field(..., description="站点配置详情")
    default_blog_tags: Optional[List[int]] = Field(None, description="默认博客标签ID列表")
    ai_model: Optional[str] = Field("", description="AI模型")
    ai_config_id: Optional[int] = Field(None, description="AI配置ID")
    scheduled_time: datetime = Field(..., description="计划开始时间")
    end_time: Optional[datetime] = Field(None, description="任务结束时间")
    frequency_type: str = Field("once", description="发布频率类型：once/daily/weekly/custom")
    weekly_days: Optional[List[str]] = Field(None, description="每周执行日期（0-6，0为周日）")
    custom_interval_value: Optional[int] = Field(None, description="自定义间隔数值")
    custom_interval_unit: Optional[str] = Field(None, description="自定义间隔单位：hours/days/weeks")
    daily_time: Optional[time] = Field(None, description="每日执行时间（HH:MM格式）")
    max_executions: Optional[int] = Field(None, description="最大执行次数限制")


# 队列状态响应Schema
class QueueStatusResponse(BaseModel):
    total_queued: int = Field(..., description="排队中的任务总数")
    running_tasks: int = Field(..., description="正在执行的任务数")
    available_workers: int = Field(..., description="可用工作者数量")
    estimated_wait_time: Optional[int] = Field(None, description="预估等待时间（秒）")


# 计划统计Schema
class PlanStatistics(BaseModel):
    plan_id: int
    plan_name: str
    total_tasks: int
    pending_tasks: int
    running_tasks: int
    success_tasks: int
    failed_tasks: int
    next_execution: Optional[datetime]
    frequency_description: Optional[str] = Field(None, description="频率描述")
    execution_progress: Optional[str] = Field(None, description="执行进度")
    execution_status: Optional[str] = Field(None, description="执行状态") 