from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum
import json
import re

class CompetitionLevel(str, Enum):
    low = "low"
    medium = "medium"
    high = "high"
    unspecified = "unspecified"

class UpdateMethod(str, Enum):
    google_ads_api = "google_ads_api"
    manual = "manual"
    batch_import = "batch_import"
    pytrends = "pytrends"

class ImportTaskStatus(str, Enum):
    pending = "pending"
    processing = "processing"
    completed = "completed"
    failed = "failed"
    cancelled = "cancelled"

class PyTrendsTaskStatus(str, Enum):
    pending = "pending"
    running = "running"
    completed = "completed"
    failed = "failed"
    cancelled = "cancelled"

# 基础模式
class KeywordLibraryBase(BaseModel):
    keyword_name: str = Field(..., max_length=255, description="关键词名称")

    # 新的关键词数据字段
    intent: Optional[str] = Field(None, max_length=100, description="关键词意图（Informational, Commercial等）")
    volume: Optional[int] = Field(None, description="搜索量")
    trend: Optional[str] = Field(None, description="趋势数据（12个月的趋势值数组，JSON格式）")
    keyword_difficulty: Optional[int] = Field(None, ge=0, le=100, description="关键词难度（0-100）")
    cpc_usd: Optional[float] = Field(None, ge=0, description="每次点击费用（美元）")
    competitive_density: Optional[float] = Field(None, ge=0, le=1, description="竞争密度（0-1）")
    serp_features: Optional[str] = Field(None, description="SERP特征（JSON数组格式）")
    number_of_results: Optional[int] = Field(None, description="搜索结果数量")

    # 保留原有字段以兼容现有数据
    avg_monthly_searches: Optional[int] = Field(None, description="平均每月搜索量（过去12个月）")
    monthly_searches: Optional[str] = Field(None, description="大致每月搜索量（JSON格式）")
    competition_level: CompetitionLevel = Field(CompetitionLevel.unspecified, description="竞争级别")
    competition_index: Optional[float] = Field(None, ge=0, le=100, description="竞争指数（0-100）")
    # PyTrends相关字段
    trend_interest: Optional[float] = Field(None, ge=0, le=100, description="Google Trends兴趣度（0-100）")
    trend_growth_rate: Optional[float] = Field(None, description="趋势增长率（%）")
    trend_region: Optional[str] = Field(None, max_length=10, description="趋势地区代码")
    trend_timeframe: Optional[str] = Field("today 12-m", max_length=20, description="趋势时间范围")
    trend_data: Optional[str] = Field(None, description="趋势数据（JSON格式）")
    low_bid_micros: Optional[int] = Field(None, description="出价第20百分位（微货币单位）")
    high_bid_micros: Optional[int] = Field(None, description="出价第80百分位（微货币单位）")
    currency_code: str = Field("CNY", max_length=3, description="货币代码")
    language_code: str = Field("zh-CN", max_length=10, description="语言代码")
    location_ids: Optional[str] = Field(None, description="地理位置ID列表（逗号分隔）")
    update_method: UpdateMethod = Field(UpdateMethod.manual, description="更新方式")
    category: Optional[str] = Field(None, max_length=100, description="关键词分类")
    tags: Optional[str] = Field(None, max_length=500, description="标签（逗号分隔）")

# 创建关键词
class KeywordLibraryCreate(KeywordLibraryBase):
    category: str = Field(..., max_length=100, description="关键词分类")

# 更新关键词
class KeywordLibraryUpdate(BaseModel):
    keyword_name: Optional[str] = Field(None, max_length=255)

    # 新的关键词数据字段
    intent: Optional[str] = Field(None, max_length=100)
    volume: Optional[int] = None
    trend: Optional[str] = None
    keyword_difficulty: Optional[int] = Field(None, ge=0, le=100)
    cpc_usd: Optional[float] = Field(None, ge=0)
    competitive_density: Optional[float] = Field(None, ge=0, le=1)
    serp_features: Optional[str] = None
    number_of_results: Optional[int] = None

    # 保留原有字段
    avg_monthly_searches: Optional[int] = None
    monthly_searches: Optional[str] = None
    competition_level: Optional[CompetitionLevel] = None
    competition_index: Optional[float] = Field(None, ge=0, le=100)
    # PyTrends相关字段
    trend_interest: Optional[float] = Field(None, ge=0, le=100)
    trend_growth_rate: Optional[float] = None
    trend_region: Optional[str] = Field(None, max_length=10)
    trend_timeframe: Optional[str] = Field(None, max_length=20)
    trend_data: Optional[str] = None
    low_bid_micros: Optional[int] = None
    high_bid_micros: Optional[int] = None
    currency_code: Optional[str] = Field(None, max_length=3)
    language_code: Optional[str] = Field(None, max_length=10)
    location_ids: Optional[str] = None
    update_method: Optional[UpdateMethod] = None
    category: Optional[str] = Field(None, max_length=100)
    tags: Optional[str] = Field(None, max_length=500)

# 关键词响应
class KeywordLibraryResponse(KeywordLibraryBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    # 计算属性
    tags_list: Optional[List[str]] = None
    location_ids_list: Optional[List[int]] = None
    monthly_searches_data: Optional[Dict[str, Any]] = None
    trend_data_parsed: Optional[Dict[str, Any]] = None  # 解析后的趋势数据

    # 新字段的解析属性
    trend_list: Optional[List[float]] = None  # 解析后的趋势数组
    serp_features_list: Optional[List[str]] = None  # 解析后的SERP特征数组
    
    @validator('tags_list', pre=True, always=True)
    def parse_tags(cls, v, values):
        tags = values.get('tags')
        if tags:
            return [tag.strip() for tag in tags.split(',') if tag.strip()]
        return []
    
    @validator('location_ids_list', pre=True, always=True)
    def parse_location_ids(cls, v, values):
        location_ids = values.get('location_ids')
        if location_ids:
            try:
                return [int(id.strip()) for id in location_ids.split(',') if id.strip().isdigit()]
            except:
                return []
        return []
    
    @validator('monthly_searches_data', pre=True, always=True)
    def parse_monthly_searches(cls, v, values):
        monthly_searches = values.get('monthly_searches')
        if monthly_searches:
            try:
                return json.loads(monthly_searches)
            except:
                return None
        return None

    @validator('trend_data_parsed', pre=True, always=True)
    def parse_trend_data(cls, v, values):
        trend_data = values.get('trend_data')
        if trend_data:
            try:
                return json.loads(trend_data)
            except:
                return None
        return None

    @validator('trend_list', pre=True, always=True)
    def parse_trend_list(cls, v, values):
        trend = values.get('trend')
        if trend:
            try:
                return json.loads(trend)
            except (json.JSONDecodeError, TypeError):
                return None
        return None

    @validator('serp_features_list', pre=True, always=True)
    def parse_serp_features(cls, v, values):
        serp_features = values.get('serp_features')
        if serp_features:
            try:
                return json.loads(serp_features)
            except (json.JSONDecodeError, TypeError):
                return None
        return None

    class Config:
        from_attributes = True

# Google Ads 配置
class GoogleAdsConfigBase(BaseModel):
    config_name: str = Field(..., max_length=100)
    customer_id: str = Field(..., max_length=20)
    developer_token: str = Field(..., max_length=255)
    api_key: Optional[str] = Field(None, max_length=255, description="Google API Key")
    client_id: str = Field(..., max_length=255)
    client_secret: str = Field(..., max_length=255)
    refresh_token: Optional[str] = Field(None, max_length=255)
    login_customer_id: Optional[str] = Field(None, max_length=20)
    oauth_state: Optional[str] = Field(None, max_length=255)
    authorization_status: str = Field("pending", max_length=20)
    last_auth_time: Optional[datetime] = None
    access_token: Optional[str] = Field(None, max_length=500)
    token_expiry: Optional[datetime] = None
    # 代理服务器配置
    use_proxy: bool = False
    proxy_host: Optional[str] = Field(None, max_length=255)
    proxy_port: Optional[int] = Field(None, ge=1, le=65535)
    proxy_username: Optional[str] = Field(None, max_length=255)
    proxy_password: Optional[str] = Field(None, max_length=255)
    proxy_type: str = Field("http", max_length=10)
    is_active: bool = True

    @validator('developer_token')
    def validate_developer_token(cls, v):
        if not v:
            raise ValueError('开发者令牌不能为空')
        return v

    @validator('api_key')
    def validate_api_key(cls, v):
        if v and not v.startswith('AIza'):
            raise ValueError('Google API Key必须以"AIza"开头')
        return v

class GoogleAdsConfigCreate(BaseModel):
    config_name: str = Field(..., max_length=100)
    customer_id: str = Field(..., max_length=20)
    developer_token: str = Field(..., max_length=255)
    api_key: Optional[str] = Field(None, max_length=255, description="Google API Key")
    client_id: str = Field(..., max_length=255)
    client_secret: str = Field(..., max_length=255)
    refresh_token: Optional[str] = Field(None, max_length=255)
    login_customer_id: Optional[str] = Field(None, max_length=20)
    # 代理服务器配置
    use_proxy: bool = False
    proxy_host: Optional[str] = Field(None, max_length=255)
    proxy_port: Optional[int] = Field(None, ge=1, le=65535)
    proxy_username: Optional[str] = Field(None, max_length=255)
    proxy_password: Optional[str] = Field(None, max_length=255)
    proxy_type: str = Field("http", max_length=10)
    is_active: bool = True

    @validator('developer_token')
    def validate_developer_token(cls, v):
        if not v:
            raise ValueError('开发者令牌不能为空')
        return v

    @validator('api_key')
    def validate_api_key(cls, v):
        if v and not v.startswith('AIza'):
            raise ValueError('Google API Key必须以"AIza"开头')
        return v

class GoogleAdsConfigUpdate(BaseModel):
    config_name: Optional[str] = Field(None, max_length=100)
    customer_id: Optional[str] = Field(None, max_length=20)
    developer_token: Optional[str] = Field(None, max_length=255)
    api_key: Optional[str] = Field(None, max_length=255)
    client_id: Optional[str] = Field(None, max_length=255)
    client_secret: Optional[str] = Field(None, max_length=255)
    refresh_token: Optional[str] = Field(None, max_length=255)
    login_customer_id: Optional[str] = Field(None, max_length=20)
    oauth_state: Optional[str] = Field(None, max_length=255)
    authorization_status: Optional[str] = Field(None, max_length=20)
    last_auth_time: Optional[datetime] = None
    access_token: Optional[str] = Field(None, max_length=500)
    token_expiry: Optional[datetime] = None
    # 代理服务器配置
    use_proxy: Optional[bool] = None
    proxy_host: Optional[str] = Field(None, max_length=255)
    proxy_port: Optional[int] = Field(None, ge=1, le=65535)
    proxy_username: Optional[str] = Field(None, max_length=255)
    proxy_password: Optional[str] = Field(None, max_length=255)
    proxy_type: Optional[str] = Field(None, max_length=10)
    is_active: Optional[bool] = None

class GoogleAdsConfigResponse(GoogleAdsConfigBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# 更新历史
class KeywordUpdateHistoryResponse(BaseModel):
    id: int
    keyword_id: int
    update_method: UpdateMethod
    old_data: Optional[Dict[str, Any]] = None
    new_data: Optional[Dict[str, Any]] = None
    batch_id: Optional[str] = None
    operator_id: Optional[int] = None
    operator_name: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True

# 导入任务
class KeywordImportTaskCreate(BaseModel):
    file_name: str
    file_path: str
    operator_id: Optional[int] = None
    operator_name: Optional[str] = None

class KeywordImportTaskResponse(BaseModel):
    id: int
    task_id: str
    file_name: str
    file_path: str
    total_count: int
    success_count: int
    failed_count: int
    progress: int
    status: ImportTaskStatus
    error_message: Optional[str] = None
    result_file_path: Optional[str] = None
    operator_id: Optional[int] = None
    operator_name: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# 批量操作
class KeywordBatchCreate(BaseModel):
    keywords: List[KeywordLibraryCreate]
    batch_id: Optional[str] = None
    operator_id: Optional[int] = None
    operator_name: Optional[str] = None

class KeywordBatchResponse(BaseModel):
    total_count: int
    success_count: int
    failed_count: int
    batch_id: str
    success_keywords: List[KeywordLibraryResponse]
    failed_keywords: List[Dict[str, Any]]

# Google Ads API 请求
class GoogleAdsKeywordRequest(BaseModel):
    seed_keywords: Optional[List[str]] = Field(None, description="种子关键词")
    url: Optional[str] = Field(None, description="网站URL")
    language_code: str = Field("zh", description="语言代码")
    location_ids: List[int] = Field([2156], description="地理位置ID列表，默认中国")
    include_adult_keywords: bool = Field(False, description="是否包含成人关键词")
    page_size: int = Field(100, description="每页结果数")

# 统计数据
class KeywordStatsResponse(BaseModel):
    total_keywords: int
    by_update_method: Dict[str, int]
    by_competition_level: Dict[str, int]
    avg_monthly_searches: Optional[float] = None
    top_keywords: List[KeywordLibraryResponse]
    # 新字段统计
    by_intent: Dict[str, int] = Field(default_factory=dict, description="按意图分布统计")
    by_difficulty: Dict[str, int] = Field(default_factory=dict, description="按难度分布统计")
    avg_cpc_usd: Optional[float] = Field(None, description="平均CPC（美元）")

# 搜索请求
class KeywordSearchRequest(BaseModel):
    keyword: Optional[str] = None
    tags: Optional[List[str]] = None
    competition_level: Optional[CompetitionLevel] = None
    update_method: Optional[UpdateMethod] = None
    location_ids: Optional[str] = None
    category: Optional[str] = None
    # 原有搜索量字段（兼容）
    min_searches: Optional[int] = None
    max_searches: Optional[int] = None
    # 新字段过滤
    intent: Optional[str] = Field(None, description="关键词意图过滤")
    min_volume: Optional[int] = Field(None, description="最小搜索量")
    max_volume: Optional[int] = Field(None, description="最大搜索量")
    min_difficulty: Optional[int] = Field(None, ge=0, le=100, description="最小关键词难度")
    max_difficulty: Optional[int] = Field(None, ge=0, le=100, description="最大关键词难度")
    min_cpc: Optional[float] = Field(None, ge=0, description="最小CPC（美元）")
    max_cpc: Optional[float] = Field(None, ge=0, description="最大CPC（美元）")
    competitive_density: Optional[int] = Field(None, ge=0, le=1, description="竞争密度过滤")
    # 分页和排序
    page: int = Field(1, ge=1)
    page_size: int = Field(20, ge=1, le=100)
    sort_by: str = Field("updated_at", description="排序字段")
    sort_order: str = Field("desc", description="排序方向: asc, desc")

# 分页响应
class KeywordPageResponse(BaseModel):
    items: List[KeywordLibraryResponse]
    total: int
    page: int
    page_size: int
    total_pages: int

# PyTrends配置
class PyTrendsConfigBase(BaseModel):
    config_name: str = Field(..., max_length=100)
    language: str = Field("zh-CN", max_length=5, description="语言设置")
    timezone: int = Field(480, description="时区偏移（分钟）")
    geo_location: str = Field("CN", max_length=5, description="地理位置代码")
    default_timeframe: str = Field("today 12-m", max_length=20, description="默认时间范围")
    max_keywords_per_batch: int = Field(5, ge=1, le=10, description="每批最大关键词数")
    request_delay: int = Field(2, ge=1, le=10, description="请求延迟（秒）")
    retry_attempts: int = Field(3, ge=1, le=5, description="重试次数")
    use_proxy: bool = Field(False, description="是否使用代理")
    proxy_host: Optional[str] = Field(None, max_length=255, description="代理主机")
    proxy_port: Optional[int] = Field(None, ge=1, le=65535, description="代理端口")
    proxy_username: Optional[str] = Field(None, max_length=255, description="代理用户名")
    proxy_password: Optional[str] = Field(None, max_length=255, description="代理密码")
    proxy_type: str = Field("http", max_length=10, description="代理类型")
    is_active: bool = True

class PyTrendsConfigCreate(PyTrendsConfigBase):
    pass

class PyTrendsConfigUpdate(BaseModel):
    config_name: Optional[str] = Field(None, max_length=100)
    language: Optional[str] = Field(None, max_length=5)
    timezone: Optional[int] = None
    geo_location: Optional[str] = Field(None, max_length=5)
    default_timeframe: Optional[str] = Field(None, max_length=20)
    max_keywords_per_batch: Optional[int] = Field(None, ge=1, le=10)
    request_delay: Optional[int] = Field(None, ge=1, le=10)
    retry_attempts: Optional[int] = Field(None, ge=1, le=5)
    use_proxy: Optional[bool] = None
    proxy_host: Optional[str] = Field(None, max_length=255)
    proxy_port: Optional[int] = Field(None, ge=1, le=65535)
    proxy_username: Optional[str] = Field(None, max_length=255)
    proxy_password: Optional[str] = Field(None, max_length=255)
    proxy_type: Optional[str] = Field(None, max_length=10)
    is_active: Optional[bool] = None

class PyTrendsConfigResponse(PyTrendsConfigBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# PyTrends API 请求
class PyTrendsKeywordRequest(BaseModel):
    seed_keywords: List[str] = Field(..., min_items=1, max_items=5, description="种子关键词")
    timeframe: str = Field("today 1-m", description="时间范围")  # 默认过去30天
    geo_location: str = Field("GB", description="地理位置代码")  # 默认英国
    category: Optional[str] = Field(None, description="分类名称")  # 改为字符串类型
    gprop: str = Field("", description="Google产品（留空表示网页搜索）")
    include_related: bool = Field(True, description="是否包含相关查询")
    include_rising: bool = Field(True, description="是否包含上升查询")

class PyTrendsTaskCreate(BaseModel):
    config_id: int = Field(..., description="PyTrends配置ID")
    seed_keywords: List[str] = Field(..., min_items=1, description="种子关键词")
    timeframe: str = Field("today 12-m", description="时间范围")
    geo_location: str = Field("CN", description="地理位置代码")
    category: int = Field(0, description="分类ID")
    include_related: bool = Field(True, description="包含相关查询")
    include_rising: bool = Field(True, description="包含上升查询")

class PyTrendsTaskResponse(BaseModel):
    id: int
    task_id: str
    config_id: int
    seed_keywords: str  # JSON string
    search_parameters: Optional[str] = None  # JSON string
    status: PyTrendsTaskStatus
    progress: int
    total_keywords: int
    processed_keywords: int
    success_keywords: int
    failed_keywords: int
    error_message: Optional[str] = None
    result_summary: Optional[str] = None  # JSON string
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# PyTrends结果
class PyTrendsResultItem(BaseModel):
    keyword: str
    interest_score: float = Field(..., ge=0, le=100, description="兴趣度分数")
    growth_rate: Optional[float] = Field(None, description="增长率")
    trend_data: Optional[Dict[str, Any]] = Field(None, description="趋势数据")
    related_queries: Optional[List[str]] = Field(None, description="相关查询")
    rising_queries: Optional[List[str]] = Field(None, description="上升查询")

class PyTrendsTaskStartResponse(BaseModel):
    task_id: str
    message: str
    status: str

class PyTrendsImportResult(BaseModel):
    total_count: int
    success_count: int
    failed_count: int
    task_id: str
    success_keywords: List[Dict[str, Any]]
    failed_keywords: List[Dict[str, Any]]

# 分类管理Schema
class KeywordCategoryCreate(BaseModel):
    """关键词分类创建模式"""
    name: str = Field(..., description="分类名称")


class KeywordCategoryUpdate(BaseModel):
    """关键词分类更新模式"""
    oldName: str = Field(..., description="原分类名称")
    newName: str = Field(..., description="新分类名称")


class KeywordCategoryDelete(BaseModel):
    """关键词分类删除模式"""
    name: str = Field(..., description="分类名称")


class KeywordCategoryResponse(BaseModel):
    """关键词分类操作响应模式"""
    message: str
    success: bool = True
    updated_count: Optional[int] = None
    deleted_placeholder: Optional[int] = None


class KeywordBatchUpdateCategory(BaseModel):
    """批量更新关键词分类模式"""
    keyword_ids: List[int] = Field(..., description="关键词ID列表")
    category: str = Field(..., description="新分类名称")


class KeywordBatchUpdateCategoryResponse(BaseModel):
    """批量更新关键词分类响应模式"""
    message: str
    success: bool = True
    updated_count: int
    keyword_ids: List[int] 