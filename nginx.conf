worker_processes auto;
worker_rlimit_nofile 1024;

events {
    worker_connections 1024;
    # 移除epoll配置，让nginx自动选择适合Windows的事件模型
    multi_accept on;
}

http {
    # 直接定义MIME类型，避免依赖外部文件
    types {
        text/html                             html htm shtml;
        text/css                              css;
        application/javascript                js;
        application/json                      json;
        image/gif                            gif;
        image/jpeg                           jpeg jpg;
        image/png                            png;
        image/svg+xml                        svg svgz;
        image/x-icon                         ico;
        font/woff                            woff;
        font/woff2                           woff2;
        application/x-font-ttf               ttf;
        application/vnd.ms-fontobject        eot;
    }
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    # 日志文件 - 使用相对路径，nginx会自动创建目录
    access_log logs/access.log main;
    error_log logs/error.log warn;

    # 基础设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 10240;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/x-javascript
        application/xml+rss
        application/javascript
        application/json;

    # 上游后端服务
    upstream backend {
        server 127.0.0.1:5000;
        keepalive 32;
    }

    # 上游前端服务
    upstream frontend {
        server 127.0.0.1:8080;
        keepalive 32;
    }

    # 主服务器配置
    server {
        listen 8000;
        server_name localhost;

        # 设置客户端最大请求体大小
        client_max_body_size 50M;

        # 代理超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # API路由 - 代理到后端FastAPI服务
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # CORS设置
            add_header Access-Control-Allow-Origin $http_origin always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
            add_header Access-Control-Allow-Credentials true always;

            # 处理预检请求
            if ($request_method = 'OPTIONS') {
                return 204;
            }
        }

        # 健康检查
        location /health {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 静态文件优化
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 静态资源缓存
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # 前端路由 - 代理到Vue开发服务器或静态文件
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 支持WebSocket（如果需要）
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # 错误页面
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
} 