import asyncio
import random
from datetime import datetime, timedelta, time
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.date import DateTrigger
from apscheduler.triggers.cron import <PERSON><PERSON><PERSON>rigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor

from ..models.scheduled_publish import ScheduledPublishPlan, ScheduledPublishTask, TaskQueue
from ..models.wordpress_site import WordPressSite
from ..models.keyword_library import KeywordLibrary
from ..models.ai_config import AIConfig
from ..models.ai_article import AIArticle
from ..schemas.ai_article import DIFYWorkflowRequest
from ..schemas.scheduled_publish import SiteConfigItem, QueueStatusResponse, PlanStatistics
from .dify_service import DIFYService
from .event_manager import trigger_queue_status_update, trigger_plan_list_update, trigger_task_status_update


class ScheduledPublishService:
    def __init__(self, db: Session):
        self.db = db
        self.max_concurrent_workers = 5  # 最大并行工作者数量
        self.scheduler = None  # APScheduler实例
        self._scheduler_initialized = False

    async def generate_tasks_from_config(self, plan_id: int, site_configs: List[SiteConfigItem]) -> List[ScheduledPublishTask]:
        """根据站点配置生成定时发布任务"""
        tasks = []
        
        for site_config in site_configs:
            # 构建博客标签字符串（逗号分隔的ID）
            blog_tags_str = None
            if site_config.blog_tags:
                blog_tags_str = ",".join(map(str, site_config.blog_tags))
            
            # 为每个站点创建任务
            task = ScheduledPublishTask(
                plan_id=plan_id,
                keywords=",".join(site_config.keywords),
                wordpress_url=site_config.site_url,
                site_name=site_config.site_name,
                blog_category_id=site_config.blog_category_id,
                blog_category_name=site_config.blog_category_name,
                blog_tags=blog_tags_str,
                status="pending"
            )
            
            self.db.add(task)
            tasks.append(task)
        
        self.db.commit()
        
        # 刷新任务以获取ID
        for task in tasks:
            self.db.refresh(task)
        
        # 更新计划的任务统计
        plan = self.db.query(ScheduledPublishPlan).filter(ScheduledPublishPlan.id == plan_id).first()
        if plan:
            plan.total_tasks = len(tasks)
            if plan.success_tasks is None:
                plan.success_tasks = 0
            self.db.commit()
        
        return tasks

    async def add_tasks_to_queue(self, task_ids: List[int]):
        """将任务添加到队列中"""
        # 获取当前最高优先级
        max_priority = self.db.query(TaskQueue.priority).order_by(desc(TaskQueue.priority)).first()
        next_priority = (max_priority[0] if max_priority else 0) + 1
        
        for task_id in task_ids:
            queue_item = TaskQueue(
                task_id=task_id,
                priority=next_priority,
                status="queued"
            )
            self.db.add(queue_item)
            
            # 更新任务状态
            task = self.db.query(ScheduledPublishTask).filter(ScheduledPublishTask.id == task_id).first()
            if task:
                task.status = "queued"
        
        self.db.commit()

        # 触发队列状态更新事件
        queue_status = await self.get_queue_status()
        trigger_queue_status_update({
            "total_queued": queue_status.total_queued,
            "running_tasks": queue_status.running_tasks,
            "available_workers": queue_status.available_workers,
            "estimated_wait_time": queue_status.estimated_wait_time
        })

    async def get_queue_status(self) -> QueueStatusResponse:
        """获取队列状态"""
        queued_count = self.db.query(TaskQueue).filter(TaskQueue.status == "queued").count()
        running_count = self.db.query(TaskQueue).filter(TaskQueue.status == "running").count()
        
        # 估算等待时间（每个任务大约需要5分钟）
        estimated_wait = 0
        if queued_count > 0:
            estimated_wait = (queued_count // self.max_concurrent_workers) * 300  # 5分钟 = 300秒
        
        return QueueStatusResponse(
            total_queued=queued_count,
            running_tasks=running_count,
            available_workers=max(0, self.max_concurrent_workers - running_count),
            estimated_wait_time=estimated_wait
        )

    async def start_queue_worker(self):
        """启动队列工作者"""
        from ..db.session import SessionLocal
        
        logger = __import__('logging').getLogger(__name__)
        logger.info("队列工作者启动...")
        
        while True:
            db = SessionLocal()
            try:
                # 检查是否有可用的工作者位置
                running_count = db.query(TaskQueue).filter(TaskQueue.status == "running").count()
                
                logger.debug(f"当前运行任务数: {running_count}, 最大并行数: {self.max_concurrent_workers}")
                
                if running_count < self.max_concurrent_workers:
                    # 获取下一个待处理的任务
                    queue_item = db.query(TaskQueue).filter(
                        TaskQueue.status == "queued"
                    ).order_by(TaskQueue.priority).first()
                    
                    if queue_item:
                        logger.info(f"发现待处理任务: {queue_item.task_id}, 启动执行...")
                        # 启动任务执行（不等待完成）
                        asyncio.create_task(self._execute_task_with_new_db(queue_item.task_id))
                
                # 等待10秒后再次检查
                await asyncio.sleep(10)
                
            except Exception as e:
                logger.error(f"队列工作者错误: {e}")
                await asyncio.sleep(30)  # 错误时等待更长时间
            finally:
                db.close()

    async def _execute_task_with_new_db(self, task_id: int):
        """使用新的数据库连接执行任务"""
        from ..db.session import SessionLocal
        
        db = SessionLocal()
        try:
            # 重新获取队列项和任务
            queue_item = db.query(TaskQueue).filter(TaskQueue.task_id == task_id).first()
            if not queue_item:
                return
            
            await self._execute_task_internal(queue_item, db)
        finally:
            db.close()

    async def _execute_task_internal(self, queue_item: TaskQueue, db: Session):
        """执行单个任务的内部实现"""
        import logging
        logger = logging.getLogger(__name__)
        
        try:
            task_id = queue_item.task_id
            
            # 更新队列状态
            queue_item.status = "running"
            queue_item.started_at = datetime.utcnow()
            queue_item.worker_id = f"worker_{datetime.utcnow().timestamp()}"
            
            # 更新任务状态
            task = db.query(ScheduledPublishTask).filter(ScheduledPublishTask.id == task_id).first()
            if not task:
                logger.error(f"任务 {task_id} 不存在")
                return
            
            task.status = "running"
            task.execution_time = datetime.utcnow()
            db.commit()
            
            logger.info(f"开始执行任务 {task_id}: {task.keywords} -> {task.site_name}")
            
            # 获取计划信息
            plan = db.query(ScheduledPublishPlan).filter(ScheduledPublishPlan.id == task.plan_id).first()
            if not plan:
                raise Exception("找不到关联的发布计划")
            
            # 动态分配关键词
            final_keywords = task.keywords

            # 判断是否需要动态选词
            use_dynamic_selection = False

            # 检查是否使用智能策略选词
            if plan.use_keyword_strategy:
                use_dynamic_selection = True
                logger.info(f"使用智能策略选词模式")

            # 检查是否使用智能推荐选词
            elif plan.keyword_categories and plan.keyword_selection_strategy == 'random_one':
                use_dynamic_selection = True
                logger.info(f"使用智能推荐选词模式")

            # 检查是否是传统动态选词（兼容旧版本）
            elif plan.keyword_categories and task.keywords and (task.keywords.startswith('[') and task.keywords.endswith(']')):
                use_dynamic_selection = True
                logger.info(f"使用传统动态选词模式")

            # 执行动态选词
            if use_dynamic_selection:
                try:
                    service = ScheduledPublishService(db)
                    selected_keyword = service.select_keyword_by_strategy(plan)
                    if selected_keyword:
                        final_keywords = selected_keyword
                        task.actual_keyword = selected_keyword  # 记录实际选择的关键词

                        # 立即保存actual_keyword到数据库
                        db.commit()
                        logger.info(f"已保存实际选择的关键词: {selected_keyword}")

                        # 根据选词类型记录不同的日志
                        if plan.use_keyword_strategy and plan.keyword_selection_strategy == 'random_one':
                            logger.info(f"智能策略+推荐选词: {selected_keyword} (原关键词: {task.keywords})")
                        elif plan.use_keyword_strategy:
                            logger.info(f"智能策略选词: {selected_keyword} (原关键词: {task.keywords})")
                        elif plan.keyword_selection_strategy == 'random_one':
                            logger.info(f"智能推荐选词: {selected_keyword} (原关键词: {task.keywords})")
                        else:
                            logger.info(f"动态选词: {selected_keyword} (原关键词: {task.keywords})")
                    else:
                        logger.warning(f"动态选词未找到符合条件的关键词，使用原关键词: {task.keywords}")
                except Exception as e:
                    logger.error(f"动态选词失败，使用原关键词: {e}")
            else:
                logger.info(f"使用预设关键词: {final_keywords}")
            
            # 获取WordPress站点信息
            wordpress_site = db.query(WordPressSite).filter(
                WordPressSite.url == task.wordpress_url,
                WordPressSite.is_active == True
            ).first()
            
            if not wordpress_site:
                raise Exception(f"找不到活跃的WordPress站点: {task.wordpress_url}")
            
            # 获取AI配置
            ai_config = None
            if plan.ai_config_id:
                ai_config = db.query(AIConfig).filter(AIConfig.id == plan.ai_config_id).first()
            
            if not ai_config:
                # 使用默认DIFY配置
                ai_config = db.query(AIConfig).filter(
                    AIConfig.service_type == "DIFY",
                    AIConfig.is_active == True
                ).first()
                
                if not ai_config:
                    raise Exception("未找到可用的AI配置")
            
            # 构建请求数据
            dify_request = DIFYWorkflowRequest(
                keywords=final_keywords,
                wordpress_url=task.wordpress_url,
                type=task.blog_category_id or 1,  # 默认分类ID
                tags=task.blog_tags,  # 添加博客标签
                model=task.ai_model or plan.ai_model,
                wp_user=wordpress_site.wp_username,
                wp_pwd=wordpress_site.wp_app_password
            )
            
            # 调用内部AI发文接口（不需要用户验证）
            from ..api.api_v1.endpoints.ai_article import _generate_article_internal

            # 创建文章记录
            article = await _generate_article_internal(
                request=dify_request,
                ai_config_id=ai_config.id,
                db=db
            )
            
            # 关联AI文章ID
            task.ai_article_id = article["id"]
            db.commit()
            
            # 等待文章生成完成
            while True:
                # 查询文章状态
                article = db.query(AIArticle).filter(AIArticle.id == task.ai_article_id).first()
                if not article:
                    raise Exception("文章记录不存在")
                
                # 更新任务状态
                if article.status == "published":
                    task.status = "success"
                    task.completion_time = datetime.utcnow()
                    # 更新队列状态为完成
                    queue_item.status = "completed"
                    logger.info(f"任务 {task_id} 执行成功并已发布")
                    break
                elif article.status == "failed":
                    task.status = "failed"
                    task.error_message = article.error_message
                    task.completion_time = datetime.utcnow()
                    # 更新队列状态为失败
                    queue_item.status = "failed"
                    logger.error(f"任务 {task_id} 执行失败: {article.error_message}")
                    break
                elif article.status in ["pending", "generating"]:
                    # 继续等待
                    await asyncio.sleep(5)  # 每5秒检查一次
                else:
                    # 未知状态
                    task.status = "failed"
                    task.error_message = f"未知的文章状态: {article.status}"
                    task.completion_time = datetime.utcnow()
                    # 更新队列状态为失败
                    queue_item.status = "failed"
                    logger.error(f"任务 {task_id} 状态异常: {article.status}")
                    break
            
            # 重新计算计划的总体统计
            self._update_plan_statistics(plan)

            db.commit()

            # 触发任务状态更新事件
            trigger_task_status_update({
                "task_id": task_id,
                "status": task.status,
                "plan_id": task.plan_id
            })

            # 触发队列状态更新事件
            queue_status = await self.get_queue_status()
            trigger_queue_status_update({
                "total_queued": queue_status.total_queued,
                "running_tasks": queue_status.running_tasks,
                "available_workers": queue_status.available_workers,
                "estimated_wait_time": queue_status.estimated_wait_time
            })
            
        except Exception as e:
            # 更新任务状态为失败
            task.status = "failed"
            task.error_message = str(e)
            task.completion_time = datetime.utcnow()
            # 更新队列状态为失败
            queue_item.status = "failed"
            db.commit()

            # 触发任务状态更新事件
            trigger_task_status_update({
                "task_id": task_id,
                "status": task.status,
                "plan_id": task.plan_id,
                "error_message": str(e)
            })

            # 触发队列状态更新事件
            queue_status = await self.get_queue_status()
            trigger_queue_status_update({
                "total_queued": queue_status.total_queued,
                "running_tasks": queue_status.running_tasks,
                "available_workers": queue_status.available_workers,
                "estimated_wait_time": queue_status.estimated_wait_time
            })

            logger.error(f"任务执行失败 {task_id}: {e}")

    async def execute_plan_tasks(self, plan_id: int):
        """执行指定计划的所有任务"""
        # 获取计划的所有pending任务
        tasks = self.db.query(ScheduledPublishTask).filter(
            ScheduledPublishTask.plan_id == plan_id,
            ScheduledPublishTask.status == "pending"
        ).all()
        
        if tasks:
            task_ids = [task.id for task in tasks]
            await self.add_tasks_to_queue(task_ids)

    async def get_plan_statistics(self) -> List[PlanStatistics]:
        """获取所有计划的统计信息"""
        plans = self.db.query(ScheduledPublishPlan).all()
        statistics = []
        
        for plan in plans:
            # 统计各状态任务数量
            tasks = self.db.query(ScheduledPublishTask).filter(ScheduledPublishTask.plan_id == plan.id).all()
            
            total_tasks = len(tasks)
            pending_tasks = len([t for t in tasks if t.status == "pending"])
            running_tasks = len([t for t in tasks if t.status == "running"])
            success_tasks = len([t for t in tasks if t.status == "success"])
            failed_tasks = len([t for t in tasks if t.status == "failed"])
            
            # 下次执行时间使用计划中的next_execution_time字段
            next_execution = plan.next_execution_time
            
            statistics.append(PlanStatistics(
                plan_id=plan.id,
                plan_name=plan.plan_name,
                total_tasks=total_tasks,
                pending_tasks=pending_tasks,
                running_tasks=running_tasks,
                success_tasks=success_tasks,
                failed_tasks=failed_tasks,
                next_execution=next_execution
            ))
        
        return statistics

    def _initialize_scheduler(self):
        """初始化APScheduler"""
        if self._scheduler_initialized:
            return

        # 配置调度器
        jobstores = {
            'default': MemoryJobStore()
        }
        executors = {
            'default': AsyncIOExecutor()
        }
        job_defaults = {
            'coalesce': False,
            'max_instances': 3
        }

        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='UTC'
        )

        self._scheduler_initialized = True

    async def start_scheduler(self):
        """启动定时任务调度器 - 使用APScheduler"""
        import logging
        logger = logging.getLogger(__name__)
        logger.info("定时任务调度器启动...")

        # 初始化调度器
        self._initialize_scheduler()

        # 启动调度器
        self.scheduler.start()
        logger.info("APScheduler已启动")

        # 加载所有活跃计划
        await self._load_active_plans()

        # 保持调度器运行
        try:
            while True:
                await asyncio.sleep(60)  # 每分钟检查一次是否有新计划或计划变更
                await self._sync_plans()
        except Exception as e:
            logger.error(f"定时调度器错误: {e}")
        finally:
            if self.scheduler and self.scheduler.running:
                self.scheduler.shutdown()

    async def _load_active_plans(self):
        """加载所有活跃的计划到调度器"""
        import logging
        logger = logging.getLogger(__name__)

        from ..db.session import SessionLocal
        db = SessionLocal()
        try:
            active_plans = db.query(ScheduledPublishPlan).filter(
                ScheduledPublishPlan.is_active == True,
                ScheduledPublishPlan.next_execution_time.isnot(None)
            ).all()

            logger.info(f"加载 {len(active_plans)} 个活跃计划到调度器")

            for plan in active_plans:
                await self._schedule_plan(plan, db)

        finally:
            db.close()

    async def _sync_plans(self):
        """同步计划变更"""
        import logging
        logger = logging.getLogger(__name__)

        from ..db.session import SessionLocal
        db = SessionLocal()
        try:
            # 获取所有活跃计划
            active_plans = db.query(ScheduledPublishPlan).filter(
                ScheduledPublishPlan.is_active == True
            ).all()

            # 获取当前调度器中的任务ID
            current_job_ids = {job.id for job in self.scheduler.get_jobs()}

            # 应该存在的任务ID
            expected_job_ids = {f"plan_{plan.id}" for plan in active_plans if plan.next_execution_time}

            # 删除不再需要的任务
            for job_id in current_job_ids - expected_job_ids:
                self.scheduler.remove_job(job_id)
                logger.info(f"移除过期任务: {job_id}")

            # 添加新任务或更新现有任务
            for plan in active_plans:
                if plan.next_execution_time:
                    job_id = f"plan_{plan.id}"
                    if job_id not in current_job_ids:
                        await self._schedule_plan(plan, db)
                        logger.info(f"添加新任务: {job_id}")
                    else:
                        # 检查执行时间是否有变化
                        job = self.scheduler.get_job(job_id)
                        if job and job.next_run_time != plan.next_execution_time:
                            # 重新调度
                            self.scheduler.remove_job(job_id)
                            await self._schedule_plan(plan, db)
                            logger.info(f"更新任务时间: {job_id}")

        except Exception as e:
            logger.error(f"同步计划时出错: {e}")
        finally:
            db.close()

    async def _schedule_plan(self, plan: ScheduledPublishPlan, db: Session):
        """为单个计划创建调度任务"""
        import logging
        logger = logging.getLogger(__name__)

        if not plan.next_execution_time:
            return

        job_id = f"plan_{plan.id}"

        # 移除现有任务（如果存在）
        if self.scheduler.get_job(job_id):
            self.scheduler.remove_job(job_id)

        # 将前端的本地时间转换为UTC时间进行调度
        from pytz import timezone

        # 假设前端发送的是北京时间，转换为UTC
        beijing_tz = timezone('Asia/Shanghai')
        utc_tz = timezone('UTC')

        # 如果时间没有时区信息，假设是北京时间
        if plan.next_execution_time.tzinfo is None:
            # 将naive datetime当作北京时间
            beijing_time = beijing_tz.localize(plan.next_execution_time)
            utc_time = beijing_time.astimezone(utc_tz).replace(tzinfo=None)
        else:
            utc_time = plan.next_execution_time

        # 创建新的调度任务
        self.scheduler.add_job(
            func=self._execute_plan,
            trigger=DateTrigger(run_date=utc_time),
            args=[plan.id],
            id=job_id,
            name=f"执行计划: {plan.plan_name}",
            replace_existing=True
        )

        logger.info(f"已调度计划 '{plan.plan_name}' (ID: {plan.id})，北京时间: {plan.next_execution_time}，UTC时间: {utc_time}")

    async def _execute_plan(self, plan_id: int):
        """执行指定的计划 - 由APScheduler调用"""
        import logging
        logger = logging.getLogger(__name__)

        from ..db.session import SessionLocal
        db = SessionLocal()
        try:
            plan = db.query(ScheduledPublishPlan).filter(ScheduledPublishPlan.id == plan_id).first()
            if not plan:
                logger.error(f"计划 {plan_id} 不存在")
                return

            if not plan.is_active:
                logger.warning(f"计划 {plan.plan_name} (ID: {plan_id}) 已被禁用，跳过执行")
                return

            logger.info(f"开始执行计划: {plan.plan_name} (ID: {plan_id})")
            current_time = datetime.utcnow()

            task_ids = []

            # 对于"once"类型的计划，只在首次执行时处理pending任务
            if plan.frequency_type == "once":
                if plan.current_executions == 0:
                    # 首次执行：获取pending任务
                    pending_tasks = db.query(ScheduledPublishTask).filter(
                        ScheduledPublishTask.plan_id == plan_id,
                        ScheduledPublishTask.status == "pending"
                    ).all()

                    if pending_tasks:
                        for task in pending_tasks:
                            task.scheduled_execution_time = current_time
                            task_ids.append(task.id)
                        logger.info(f"计划 {plan.plan_name} 首次执行，找到 {len(task_ids)} 个待执行任务")
                    else:
                        logger.warning(f"计划 {plan.plan_name} 首次执行但没有找到待执行任务")
                else:
                    logger.info(f"计划 {plan.plan_name} 已执行过，跳过")
            else:
                # 对于重复执行的计划，每次生成新任务
                logger.info(f"计划 {plan.plan_name} 重复执行，生成新任务...")
                task_ids = await self._generate_new_tasks_for_plan(plan, db, current_time)

            if task_ids:
                db.commit()

                # 加入执行队列
                service = ScheduledPublishService(db)
                await service.add_tasks_to_queue(task_ids)

                logger.info(f"计划 {plan.plan_name} 的 {len(task_ids)} 个任务已加入执行队列")

                # 更新计划统计
                plan.total_tasks += len(task_ids)

            # 更新计划的执行统计和下次执行时间
            plan.last_execution_time = current_time
            plan.current_executions += 1

            # 计算下次执行时间
            await self.update_plan_next_execution(plan_id)

            # 重新调度下次执行
            updated_plan = db.query(ScheduledPublishPlan).filter(ScheduledPublishPlan.id == plan_id).first()
            if updated_plan and updated_plan.next_execution_time:
                await self._schedule_plan(updated_plan, db)

            db.commit()
            logger.info(f"计划 {plan.plan_name} 执行完成，下次执行时间: {updated_plan.next_execution_time if updated_plan else 'None'}")

            # 触发计划列表更新事件
            trigger_plan_list_update({
                "plan_id": plan_id,
                "action": "executed",
                "next_execution_time": updated_plan.next_execution_time.isoformat() if updated_plan and updated_plan.next_execution_time else None
            })

        except Exception as e:
            logger.error(f"执行计划 {plan_id} 时出错: {e}")
        finally:
            db.close()

    async def _generate_new_tasks_for_plan(self, plan: ScheduledPublishPlan, db: Session, current_time: datetime) -> List[int]:
        """为计划生成新任务"""
        task_ids = []

        # 获取站点配置
        sites = []
        if plan.site_categories:
            # 按分类获取站点
            import json
            categories = plan.site_categories if isinstance(plan.site_categories, list) else json.loads(plan.site_categories)
            sites = db.query(WordPressSite).filter(
                WordPressSite.category.in_(categories),
                WordPressSite.is_active == True
            ).all()
        elif plan.selected_sites:
            # 按选择的站点ID获取
            import json
            site_ids = plan.selected_sites if isinstance(plan.selected_sites, list) else json.loads(plan.selected_sites)
            sites = db.query(WordPressSite).filter(
                WordPressSite.id.in_(site_ids),
                WordPressSite.is_active == True
            ).all()

        if sites:
            # 生成新任务
            for site in sites:
                # 根据计划配置确定关键词
                if plan.use_keyword_strategy or (plan.keyword_categories and plan.keyword_selection_strategy == 'random_one'):
                    # 智能选词：使用占位符，实际关键词在执行时动态选择
                    if plan.use_keyword_strategy and plan.keyword_selection_strategy == 'random_one':
                        task_keywords = "[智能策略+推荐选词]"
                    elif plan.use_keyword_strategy:
                        task_keywords = "[智能策略选词]"
                    else:
                        task_keywords = "[智能推荐选词]"
                elif plan.keyword_categories:
                    # 传统词库选词
                    task_keywords = "[词库动态选词]"
                else:
                    # 自定义关键词
                    task_keywords = plan.keywords or "未设置关键词"

                # 获取站点的默认博客分类
                blog_category_id = None
                blog_category_name = None
                if site.blog_categories and len(site.blog_categories) > 0:
                    blog_category_id = site.blog_categories[0].get('id')
                    blog_category_name = site.blog_categories[0].get('name')

                new_task = ScheduledPublishTask(
                    plan_id=plan.id,
                    keywords=task_keywords,
                    wordpress_url=site.url,
                    site_name=site.name,
                    blog_category_id=blog_category_id,
                    blog_category_name=blog_category_name,
                    blog_tags=None,
                    ai_model=plan.ai_model,
                    status="pending",
                    scheduled_execution_time=current_time,
                    execution_sequence=plan.current_executions + 1
                )
                db.add(new_task)
                db.flush()  # 获取ID
                task_ids.append(new_task.id)

        return task_ids

    def distribute_keywords_to_sites(self, keywords: List[str], sites: List[Dict], keywords_per_site: int = 3) -> Dict[int, List[str]]:
        """将关键词随机分配给站点"""
        result = {}
        
        # 如果关键词数量不够，重复使用
        if len(keywords) < len(sites) * keywords_per_site:
            keywords = keywords * ((len(sites) * keywords_per_site // len(keywords)) + 1)
        
        # 随机打乱关键词
        shuffled_keywords = keywords.copy()
        random.shuffle(shuffled_keywords)
        
        # 分配关键词
        keyword_index = 0
        for site in sites:
            site_keywords = []
            for _ in range(keywords_per_site):
                if keyword_index < len(shuffled_keywords):
                    site_keywords.append(shuffled_keywords[keyword_index])
                    keyword_index += 1
                else:
                    # 如果关键词用完了，从头开始
                    keyword_index = 0
                    site_keywords.append(shuffled_keywords[keyword_index])
                    keyword_index += 1
            
            result[site['id']] = site_keywords
        
        return result

    def get_keywords_by_strategy(self, plan: ScheduledPublishPlan, limit: int = 100) -> List[str]:
        """根据选词策略筛选关键词"""
        if not plan.use_keyword_strategy:
            return []

        # 构建查询条件
        query = self.db.query(KeywordLibrary.keyword_name).filter(
            ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
        )

        # 意图筛选
        if plan.strategy_intent:
            query = query.filter(KeywordLibrary.intent.contains(plan.strategy_intent))

        # 搜索量范围筛选
        if plan.strategy_volume_min is not None:
            query = query.filter(KeywordLibrary.volume >= plan.strategy_volume_min)
        if plan.strategy_volume_max is not None:
            query = query.filter(KeywordLibrary.volume <= plan.strategy_volume_max)

        # 难度范围筛选
        if plan.strategy_difficulty_min is not None:
            query = query.filter(KeywordLibrary.keyword_difficulty >= plan.strategy_difficulty_min)
        if plan.strategy_difficulty_max is not None:
            query = query.filter(KeywordLibrary.keyword_difficulty <= plan.strategy_difficulty_max)

        # CPC范围筛选
        if plan.strategy_cpc_min is not None:
            query = query.filter(KeywordLibrary.cpc_usd >= plan.strategy_cpc_min)
        if plan.strategy_cpc_max is not None:
            query = query.filter(KeywordLibrary.cpc_usd <= plan.strategy_cpc_max)

        # 竞争密度范围筛选
        if plan.strategy_competitive_density_min is not None:
            query = query.filter(KeywordLibrary.competitive_density >= plan.strategy_competitive_density_min)
        if plan.strategy_competitive_density_max is not None:
            query = query.filter(KeywordLibrary.competitive_density <= plan.strategy_competitive_density_max)

        # 国家筛选
        if plan.strategy_countries:
            import json
            countries = plan.strategy_countries if isinstance(plan.strategy_countries, list) else json.loads(plan.strategy_countries)
            if countries:
                # 构建国家筛选条件
                country_conditions = []
                for country in countries:
                    # 支持多种匹配方式
                    if country.lower() == 'global':
                        # 全球：匹配空值、空字符串或包含global/全球的记录
                        country_conditions.append(
                            or_(
                                KeywordLibrary.location_ids.is_(None),     # 空值
                                KeywordLibrary.location_ids == '',         # 空字符串
                                KeywordLibrary.location_ids.ilike('%global%'),  # 包含global
                                KeywordLibrary.location_ids.ilike('%全球%')      # 包含全球
                            )
                        )
                    else:
                        # 其他国家使用contains匹配，同时排除空值
                        country_conditions.append(
                            and_(
                                KeywordLibrary.location_ids.is_not(None),
                                KeywordLibrary.location_ids != '',
                                KeywordLibrary.location_ids.contains(country)
                            )
                        )
                if country_conditions:
                    query = query.filter(or_(*country_conditions))

        # 分类筛选
        if plan.strategy_categories:
            import json
            categories = plan.strategy_categories if isinstance(plan.strategy_categories, list) else json.loads(plan.strategy_categories)
            if categories:
                query = query.filter(KeywordLibrary.category.in_(categories))

        # 获取结果
        keywords = query.limit(limit).all()
        return [kw[0] for kw in keywords]

    def select_keyword_by_strategy(self, plan: ScheduledPublishPlan) -> str:
        """根据策略和智能推荐选择关键词"""
        # 如果使用智能策略筛选
        if plan.use_keyword_strategy:
            keywords = self.get_keywords_by_strategy(plan, limit=1000)  # 获取更多候选词
        else:
            # 如果只是从词库分类选择，获取分类下的所有关键词
            keywords = self.get_keywords_by_categories(plan)

        if not keywords:
            return None

        # 根据智能推荐策略选择关键词
        selection_strategy = plan.keyword_selection_strategy or "random_one"

        if selection_strategy == "random_one":
            return random.choice(keywords)
        # 未来可以添加更多策略，如：
        # elif selection_strategy == "high_volume":
        #     return self.select_high_volume_keyword(keywords)
        # elif selection_strategy == "low_difficulty":
        #     return self.select_low_difficulty_keyword(keywords)
        else:
            # 默认随机选择
            return random.choice(keywords)

    def get_keywords_by_categories(self, plan: ScheduledPublishPlan) -> List[str]:
        """从词库分类获取关键词"""
        if not plan.keyword_categories:
            return []

        import json
        categories = plan.keyword_categories if isinstance(plan.keyword_categories, list) else json.loads(plan.keyword_categories)

        keywords = []
        for category in categories:
            category_keywords = self.db.query(KeywordLibrary.keyword_name).filter(
                KeywordLibrary.category == category,
                ~KeywordLibrary.keyword_name.startswith("_category_placeholder_")
            ).limit(100).all()
            keywords.extend([kw[0] for kw in category_keywords])

        return keywords

    async def auto_generate_site_configs_from_categories(
        self, 
        keyword_categories: List[str], 
        site_categories: List[str],
        keywords_per_site: int = 3
    ) -> List[SiteConfigItem]:
        """根据分类自动生成站点配置"""
        
        # 获取关键词
        all_keywords = []
        for category in keyword_categories:
            keywords = self.db.query(KeywordLibrary.keyword_name).filter(
                KeywordLibrary.category == category
            ).limit(100).all()  # 每个分类最多100个关键词
            all_keywords.extend([kw[0] for kw in keywords])
        
        # 获取站点
        sites = self.db.query(WordPressSite).filter(
            WordPressSite.category.in_(site_categories),
            WordPressSite.is_active == True
        ).all()
        
        # 分配关键词
        site_data = [{"id": site.id, "name": site.name, "url": site.url} for site in sites]
        keyword_distribution = self.distribute_keywords_to_sites(all_keywords, site_data, keywords_per_site)
        
        # 生成站点配置
        site_configs = []
        for site in sites:
            keywords = keyword_distribution.get(site.id, [])
            
            # 获取默认博客分类
            default_category = None
            default_category_id = None
            if site.blog_categories:
                categories = site.blog_categories
                if categories and len(categories) > 0:
                    default_category = categories[0].get('name')
                    default_category_id = categories[0].get('id')
            
            site_configs.append(SiteConfigItem(
                site_id=site.id,
                site_name=site.name,
                site_url=site.url,
                keywords=keywords,
                blog_category_id=default_category_id,
                blog_category_name=default_category
            ))
        
        return site_configs

    async def update_task_status(self, task_id: int):
        """更新任务状态"""
        import logging
        logger = logging.getLogger(__name__)
        
        task = self.db.query(ScheduledPublishTask).filter(ScheduledPublishTask.id == task_id).first()
        if not task or not task.ai_article_id:
            return
        
        # 获取关联的AI文章
        article = self.db.query(AIArticle).filter(AIArticle.id == task.ai_article_id).first()
        if not article:
            return
        
        # 获取计划信息
        plan = self.db.query(ScheduledPublishPlan).filter(ScheduledPublishPlan.id == task.plan_id).first()
        if not plan:
            return
        
        # 获取队列项
        queue_item = self.db.query(TaskQueue).filter(TaskQueue.task_id == task_id).first()
        
        # 记录原始状态
        original_task_status = task.status
        
        # 根据AI文章状态更新任务状态
        if article.status == "published" and task.status != "success":
            task.status = "success"
            task.completion_time = datetime.utcnow()
            # 更新队列状态
            if queue_item:
                queue_item.status = "completed"
            logger.info(f"任务 {task_id} 状态更新：文章已发布")
        elif article.status == "failed" and task.status != "failed":
            task.status = "failed"
            task.error_message = article.error_message
            task.completion_time = datetime.utcnow()
            # 更新队列状态
            if queue_item:
                queue_item.status = "failed"
            logger.error(f"任务 {task_id} 状态更新：文章生成失败")
        elif article.status in ["pending", "generating"] and task.status not in ["running", "queued"]:
            task.status = "running"
            # 更新队列状态
            if queue_item:
                queue_item.status = "running"
            logger.info(f"任务 {task_id} 状态更新：文章生成中")
        
        # 重新计算计划的总体统计
        self._update_plan_statistics(plan)

        self.db.commit()

        # 如果任务状态发生了变化，触发事件
        if original_task_status != task.status:
            # 触发任务状态更新事件
            trigger_task_status_update({
                "task_id": task_id,
                "status": task.status,
                "plan_id": task.plan_id,
                "completion_time": task.completion_time.isoformat() if task.completion_time else None,
                "error_message": task.error_message
            })

            # 触发队列状态更新事件
            queue_status = await self.get_queue_status()
            trigger_queue_status_update({
                "total_queued": queue_status.total_queued,
                "running_tasks": queue_status.running_tasks,
                "available_workers": queue_status.available_workers,
                "estimated_wait_time": queue_status.estimated_wait_time
            })

            # 如果任务完成，触发计划列表更新事件
            if task.status in ["success", "failed"]:
                trigger_plan_list_update({
                    "plan_id": task.plan_id,
                    "action": "task_completed",
                    "task_id": task_id,
                    "task_status": task.status
                })
    
    def _update_plan_statistics(self, plan: ScheduledPublishPlan):
        """重新计算并更新计划的统计信息"""
        # 获取该计划的所有任务
        tasks = self.db.query(ScheduledPublishTask).filter(ScheduledPublishTask.plan_id == plan.id).all()
        
        # 统计各状态任务数量
        total_tasks = len(tasks)
        success_tasks = len([t for t in tasks if t.status == "success"])
        
        # 更新计划统计
        plan.total_tasks = total_tasks
        plan.success_tasks = success_tasks
        
        # 如果有任务执行完成，更新上次执行时间
        completed_tasks = [t for t in tasks if t.completion_time is not None]
        if completed_tasks:
            latest_completion = max(completed_tasks, key=lambda t: t.completion_time)
            plan.last_execution_time = latest_completion.completion_time

    async def cleanup_completed_queue_items(self):
        """清理已完成的队列项"""
        import logging
        logger = logging.getLogger(__name__)
        
        # 删除状态为 completed 或 failed 且创建时间超过24小时的队列项
        from datetime import datetime, timedelta
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        
        completed_items = self.db.query(TaskQueue).filter(
            TaskQueue.status.in_(["completed", "failed"]),
            TaskQueue.created_at < cutoff_time
        ).all()
        
        for item in completed_items:
            self.db.delete(item)
            logger.info(f"清理已完成的队列项: 任务ID {item.task_id}, 状态 {item.status}")
        
        if completed_items:
            self.db.commit()
            logger.info(f"总共清理了 {len(completed_items)} 个已完成的队列项")
    
    def _calculate_next_execution_time(self, plan: ScheduledPublishPlan) -> datetime:
        """计算下次执行时间"""
        current_time = datetime.utcnow()
        
        if plan.frequency_type == "once":
            # 仅执行一次
            if plan.current_executions > 0:
                return None
            
            # 如果计划时间已过期且从未执行，立即执行
            if plan.scheduled_time <= current_time:
                return current_time
            else:
                return plan.scheduled_time
        
        elif plan.frequency_type == "daily":
            # 每天执行 - 计算下一个执行时间
            if plan.current_executions == 0:
                # 首次执行：如果计划时间未到，使用计划时间；否则立即执行
                if plan.scheduled_time > current_time:
                    return plan.scheduled_time
                else:
                    return current_time
            
            # 后续执行：计算下一天的执行时间
            next_date = current_time.date() + timedelta(days=1)
            if plan.daily_time:
                next_execution = datetime.combine(next_date, plan.daily_time)
            else:
                # 如果没有设置时间，使用计划开始时间的时间部分
                plan_time = plan.scheduled_time.time()
                next_execution = datetime.combine(next_date, plan_time)
            return next_execution
        
        elif plan.frequency_type == "weekly":
            # 每周执行
            if not plan.weekly_days:
                return None
            
            if plan.current_executions == 0:
                # 首次执行：如果计划时间未到，使用计划时间；否则立即执行
                if plan.scheduled_time > current_time:
                    return plan.scheduled_time
                else:
                    return current_time
            
            # 后续执行：找到下一个执行日期
            current_weekday = current_time.weekday()  # 0=Monday, 6=Sunday
            # 转换为0=Sunday, 1=Monday的格式
            current_weekday = (current_weekday + 1) % 7
            
            # 将字符串转换为整数并排序
            weekly_days = sorted([int(day) for day in plan.weekly_days])
            
            # 查找下一个执行日
            next_weekday = None
            for day in weekly_days:
                if day > current_weekday:
                    next_weekday = day
                    break
            
            if next_weekday is None:
                # 如果本周没有更多执行日，取下周的第一个
                next_weekday = weekly_days[0]
                days_ahead = (7 - current_weekday) + next_weekday
            else:
                days_ahead = next_weekday - current_weekday
            
            next_date = current_time.date() + timedelta(days=days_ahead)
            if plan.daily_time:
                next_execution = datetime.combine(next_date, plan.daily_time)
            else:
                plan_time = plan.scheduled_time.time()
                next_execution = datetime.combine(next_date, plan_time)
            return next_execution
        
        elif plan.frequency_type == "custom":
            # 自定义间隔
            if not plan.custom_interval_value or not plan.custom_interval_unit:
                return None
            
            if plan.current_executions == 0:
                # 首次执行：如果计划时间未到，使用计划时间；否则立即执行
                if plan.scheduled_time > current_time:
                    return plan.scheduled_time
                else:
                    return current_time
            
            # 后续执行：基于当前时间计算间隔
            if plan.custom_interval_unit == "hours":
                next_execution = current_time + timedelta(hours=plan.custom_interval_value)
            elif plan.custom_interval_unit == "days":
                next_execution = current_time + timedelta(days=plan.custom_interval_value)
            elif plan.custom_interval_unit == "weeks":
                next_execution = current_time + timedelta(weeks=plan.custom_interval_value)
            else:
                return None
            
            return next_execution
        
        return None

    async def update_plan_next_execution(self, plan_id: int):
        """更新计划的下次执行时间"""
        plan = self.db.query(ScheduledPublishPlan).filter(ScheduledPublishPlan.id == plan_id).first()
        if not plan:
            return
        
        # 检查是否超出最大执行次数
        if plan.max_executions and plan.current_executions >= plan.max_executions:
            plan.next_execution_time = None
            plan.is_active = False
            self.db.commit()
            return
        
        # 检查是否超出结束时间
        if plan.end_time and datetime.utcnow() > plan.end_time:
            plan.next_execution_time = None
            plan.is_active = False
            self.db.commit()
            return
        
        # 计算下次执行时间
        next_execution = self._calculate_next_execution_time(plan)
        
        # 检查下次执行时间是否在结束时间之前
        if next_execution and plan.end_time and next_execution > plan.end_time:
            next_execution = None
            plan.is_active = False
        
        plan.next_execution_time = next_execution
        self.db.commit()

    async def check_running_tasks(self):
        """检查所有运行中的任务状态"""
        running_tasks = self.db.query(ScheduledPublishTask).filter(
            ScheduledPublishTask.status.in_(["running", "queued"])
        ).all()
        
        for task in running_tasks:
            await self.update_task_status(task.id)
        
        # 清理已完成的队列项
        await self.cleanup_completed_queue_items()

    async def add_plan_to_scheduler(self, plan_id: int):
        """添加计划到调度器"""
        if not self._scheduler_initialized:
            return

        from ..db.session import SessionLocal
        db = SessionLocal()
        try:
            plan = db.query(ScheduledPublishPlan).filter(ScheduledPublishPlan.id == plan_id).first()
            if plan and plan.is_active and plan.next_execution_time:
                await self._schedule_plan(plan, db)
        finally:
            db.close()

    async def remove_plan_from_scheduler(self, plan_id: int):
        """从调度器中移除计划"""
        if not self._scheduler_initialized:
            return

        job_id = f"plan_{plan_id}"
        if self.scheduler.get_job(job_id):
            self.scheduler.remove_job(job_id)

    async def update_plan_in_scheduler(self, plan_id: int):
        """更新调度器中的计划"""
        if not self._scheduler_initialized:
            return

        # 先移除旧的任务
        await self.remove_plan_from_scheduler(plan_id)
        # 再添加新的任务
        await self.add_plan_to_scheduler(plan_id)

    def get_scheduler_status(self) -> dict:
        """获取调度器状态"""
        if not self._scheduler_initialized or not self.scheduler:
            return {
                "running": False,
                "jobs_count": 0,
                "jobs": []
            }

        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })

        return {
            "running": self.scheduler.running,
            "jobs_count": len(jobs),
            "jobs": jobs
        }

