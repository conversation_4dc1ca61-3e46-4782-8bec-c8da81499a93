-- 更新竞争密度字段类型为DECIMAL
-- 将competitive_density从INT改为DECIMAL(3,2)以支持小数值

-- 备份现有数据（可选）
-- CREATE TABLE keyword_library_backup AS SELECT * FROM keyword_library;

-- 更新字段类型
ALTER TABLE `keyword_library` 
MODIFY COLUMN `competitive_density` DECIMAL(3,2) NULL COMMENT '竞争密度（0-1）';

-- 验证更新结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    NUMERIC_PRECISION,
    NUMERIC_SCALE,
    IS_NULLABLE,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'keyword_library' 
    AND COLUMN_NAME = 'competitive_density';

-- 检查现有数据
SELECT 
    competitive_density,
    COUNT(*) as count
FROM keyword_library 
WHERE competitive_density IS NOT NULL
GROUP BY competitive_density
ORDER BY competitive_density;
