#!/usr/bin/env python3
"""
生成密码哈希脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.security import get_password_hash, verify_password

def generate_hash():
    """生成admin123的密码哈希"""
    password = "admin123"
    hash_value = get_password_hash(password)
    
    print(f"密码: {password}")
    print(f"哈希: {hash_value}")
    
    # 验证哈希是否正确
    if verify_password(password, hash_value):
        print("哈希验证成功！")
        return hash_value
    else:
        print("哈希验证失败！")
        return None

if __name__ == "__main__":
    hash_value = generate_hash()
    if hash_value:
        print(f"\n请在SQL脚本中使用以下哈希值:")
        print(f"'{hash_value}'")
    else:
        print("生成哈希失败")
        sys.exit(1) 