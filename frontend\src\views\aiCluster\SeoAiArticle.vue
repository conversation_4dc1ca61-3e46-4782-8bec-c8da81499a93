<template>
  <div class="seo-ai-article" :class="{ 'sidebar-collapsed': isCollapse }">
    <!-- 主要内容区域 - 左右布局 -->
    <div class="main-content">
      <!-- 左侧表单区域 -->
      <div class="form-section">
        <el-card class="form-card">
          <template #header>
            <div class="form-header">
              <el-icon class="header-icon"><MagicStick /></el-icon>
              <span>AI发文</span>
            </div>
          </template>
                
          <el-form 
            ref="generateFormRef" 
            :model="generateForm" 
            :rules="generateRules" 
            label-width="90px"
            class="generate-form"
            :key="formKey"
          >
            <div class="form-content">
              <el-form-item label="关键词" prop="keywords">
                <el-input 
                  v-model="generateForm.keywords" 
                  type="textarea"
                  :rows="3"
                  placeholder="请输入产品关键词，多个关键词用逗号分隔"
                  class="keyword-input"
                  resize="none"
                />
                <div class="keyword-actions">
                  <el-button 
                    type="primary" 
                    @click="openKeywordSelector"
                    class="keyword-select-btn"
                    size="small"
                  >
                    <el-icon><Collection /></el-icon>
                    从库选择
                  </el-button>
                </div>
              </el-form-item>
              
              <el-form-item label="站点" prop="wordpress_url">
                <el-select 
                  v-model="generateForm.wordpress_url" 
                  placeholder="选择已绑定站点" 
                  style="width: 100%;"
                  @change="onSiteChange"
                  filterable
                >
                  <el-option 
                    v-for="site in wordPressSites" 
                    :key="site.id" 
                    :label="site.name" 
                    :value="site.url"
                  >
                    <div class="site-option">
                      <div class="site-name">{{ site.name }}</div>
                      <div class="site-url">{{ site.url }}</div>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="文章分类" prop="blog_category_id">
                <el-select 
                  v-model="generateForm.blog_category_id" 
                  placeholder="请先选择站点" 
                  style="width: 100%;"
                  :disabled="!selectedSite || !selectedSite.blog_categories?.length"
                  clearable
                >
                  <el-option 
                    v-for="category in blogCategories" 
                    :key="category.id" 
                    :label="category.name" 
                    :value="category.id"
                  >
                    <div class="category-option">
                      <div class="category-name">{{ category.name }}</div>
                      <div class="category-info">
                        <span class="category-count">{{ category.count }}篇文章</span>
                        <span v-if="category.description" class="category-desc">{{ category.description }}</span>
                      </div>
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
              
              <el-form-item label="文章标签">
                <el-select 
                  v-model="generateForm.blog_tags" 
                  placeholder="选择现有标签或输入新标签名称" 
                  style="width: 100%;"
                  :disabled="!selectedSite"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  filterable
                  clearable
                  allow-create
                  default-first-option
                  :max-collapse-tags="2"
                  :filter-method="filterTags"
                  :reserve-keyword="false"
                >
                  <el-option 
                    v-for="tag in filteredBlogTags" 
                    :key="tag.id" 
                    :label="tag.name" 
                    :value="tag.id"
                  >
                    <div class="tag-option">
                      <div class="tag-name">{{ tag.name }}</div>
                      <div class="tag-info">
                        <span class="tag-count">{{ tag.count }}篇文章</span>
                        <span v-if="tag.description" class="tag-desc">{{ tag.description }}</span>
                      </div>
                    </div>
                  </el-option>
                </el-select>
                <div class="form-tip">
                  选择现有标签或输入新标签名称，新标签将在提交时自动创建
                </div>
              </el-form-item>
              
              <!-- AI模型选择 - 暂时隐藏 -->
              <el-form-item label="AI模型" v-if="false">
                <el-select v-model="generateForm.model" placeholder="选择模型（可选）" style="width: 100%;">
                  <el-option label="不指定" value="" />
                  <el-option label="WanX" value="WanX" />
                  <el-option label="Jimeng" value="Jimeng" />
                </el-select>
              </el-form-item>

              <!-- 图片上传 - 暂时隐藏 -->
              <el-form-item label="图片" v-if="false">
                <el-upload
                  class="compact-upload"
                  drag
                  :action="uploadAction"
                  :headers="uploadHeaders"
                  :on-success="handleUploadSuccess"
                  :on-error="handleUploadError"
                  :before-upload="beforeUpload"
                  :file-list="fileList"
                  :limit="1"
                  accept="image/*"
                >
                  <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                  <div class="el-upload__text">
                    拖拽或<em>点击上传</em>
                  </div>
                  <template #tip>
                    <div class="el-upload__tip">
                      jpg/png文件，≤2MB
                    </div>
                  </template>
                </el-upload>
              </el-form-item>

              <!-- AI配置选择 - 暂时隐藏 -->
              <el-form-item label="AI配置" v-if="false">
                <el-select v-model="generateForm.ai_config_id" placeholder="选择配置（可选）" style="width: 100%;">
                  <el-option label="使用默认配置" :value="null" />
                  <el-option
                    v-for="config in aiConfigs"
                    :key="config.id"
                    :label="config.name"
                    :value="config.id"
                    :disabled="!config.is_active"
                  />
                </el-select>
              </el-form-item>
            </div>
            
            <!-- 固定按钮区域 -->
            <div class="form-buttons">
              <el-button 
                type="primary" 
                @click="generateArticle" 
                :loading="generating"
                :disabled="!isFormValid"
                class="form-button"
              >
                <el-icon><MagicStick /></el-icon>
                {{ generating ? '生成中...' : '开始生成' }}
              </el-button>
              <el-button 
                @click="resetGenerateForm" 
                class="form-button"
              >
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </div>
          </el-form>
        </el-card>
      </div>

      <!-- 右侧文章列表区域 -->
      <div class="list-section">
        <el-card class="list-card">
      <template #header>
        <el-row justify="space-between" align="middle">
          <el-col :span="12">
            <div class="list-header">
              <el-icon class="header-icon"><DocumentCopy /></el-icon>
              <span>文章记录</span>
            </div>
          </el-col>
          <el-col :span="12" style="text-align: right;">
            <el-input
              v-model="searchKeywords"
              placeholder="搜索关键词"
              style="width: 200px; margin-right: 10px;"
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px;" @change="loadArticles">
              <el-option label="全部" value="" />
              <el-option label="生成中" value="generating" />
              <el-option label="成功" value="success" />
              <el-option label="失败" value="failed" />
              <el-option label="已发布" value="published" />
            </el-select>
          </el-col>
        </el-row>
      </template>
      
          <el-table :data="articles" v-loading="loading" style="width: 100%">
            <el-table-column prop="keywords" label="关键词" min-width="180" />
            <el-table-column prop="wordpress_url" label="站点" min-width="150" show-overflow-tooltip />
            <el-table-column prop="type" label="类型" width="80" />
            <el-table-column prop="model" label="模型" width="80" />
        <el-table-column label="状态" min-width="200">
          <template #default="scope">
            <div>
              <div style="display: flex; align-items: center; gap: 8px;">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
                <!-- 显示实时监控指示器 -->
                <el-tooltip v-if="pollingTimers.has(scope.row.id)" content="正在实时监控状态" placement="top">
                  <el-icon color="#67C23A" class="monitoring-indicator">
                    <Loading />
                  </el-icon>
                </el-tooltip>
              </div>
              <!-- 显示实时进度信息 -->
              <div v-if="scope.row.status === 'generating' && scope.row.error_message && scope.row.error_message.startsWith('进度:')" 
                   class="progress-info">
                <el-icon class="is-loading"><Loading /></el-icon>
                {{ scope.row.error_message.replace('进度:', '') }}
              </div>
              <!-- 显示workflow_run_id -->
              <div v-if="scope.row.workflow_run_id" class="workflow-id">
                <span class="id-label">ID: </span>
                <el-tooltip :content="scope.row.workflow_run_id" placement="top">
                  <span class="id-value">{{ scope.row.workflow_run_id.substring(0, 8) }}...</span>
                </el-tooltip>
              </div>
            </div>
          </template>
        </el-table-column>
            <el-table-column prop="title" label="生成标题" min-width="200" show-overflow-tooltip />
            <el-table-column label="链接" width="100">
              <template #default="scope">
                <a v-if="scope.row.article_url" :href="scope.row.article_url" target="_blank" class="link">
                  查看
                </a>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="140">
              <template #default="scope">
                {{ formatDateTime(scope.row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="160" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="viewDetails(scope.row)">
              详情
            </el-button>
            <el-button 
              v-if="scope.row.status === 'generating'" 
              type="text" 
              size="small" 
              danger 
              @click="stopGeneration(scope.row)"
            >
              停止
            </el-button>
            <el-button type="text" size="small" danger @click="deleteArticle(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div style="text-align: right; margin-top: 20px;">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
        </div>
        </el-card>
      </div>
    </div>

    <!-- 关键词选择对话框 -->
    <el-dialog
      v-model="showKeywordSelector"
      title="选择关键词"
      width="80%"
      :before-close="handleCloseKeywordSelector"
      append-to-body
    >
      <div class="keyword-selector">
        <!-- 搜索筛选 -->
        <div class="search-section">
          <el-row :gutter="16">
            <el-col :span="8">
              <el-input
                v-model="keywordSearchForm.keyword"
                placeholder="搜索关键词"
                clearable
                @input="handleKeywordSearch"
                @clear="handleKeywordSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
            <el-col :span="4">
              <el-select 
                v-model="keywordSearchForm.country" 
                placeholder="国家" 
                clearable
                @change="handleKeywordSearch"
              >
                <el-option label="全部" value="" />
                <el-option label="美国" value="US" />
                <el-option label="英国" value="UK" />
                <el-option label="德国" value="DE" />
                <el-option label="法国" value="FR" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select 
                v-model="keywordSearchForm.competition" 
                placeholder="竞争度" 
                clearable
                @change="handleKeywordSearch"
              >
                <el-option label="全部" value="" />
                <el-option label="低" value="low" />
                <el-option label="中" value="medium" />
                <el-option label="高" value="high" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select 
                v-model="keywordSearchForm.category" 
                placeholder="分类" 
                clearable
                @change="handleKeywordSearch"
              >
                <el-option label="全部" value="" />
                <el-option 
                  v-for="category in keywordCategoryList" 
                  :key="category" 
                  :label="category" 
                  :value="category" 
                />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="resetKeywordSearch">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </el-col>
          </el-row>
        </div>
        
        <!-- 关键词表格 -->
        <div class="keyword-table-section">
          <el-table
            :data="keywordTableData"
            v-loading="keywordLoading"
            @selection-change="handleKeywordSelectionChange"
            height="400"
            ref="keywordTableRef"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="keyword_name" label="关键词" min-width="150" />
            <el-table-column prop="avg_monthly_searches" label="搜索量" width="100">
              <template #default="{ row }">
                {{ formatNumber(row.avg_monthly_searches) }}
              </template>
            </el-table-column>
            <el-table-column prop="competition_level" label="竞争度" width="80">
              <template #default="{ row }">
                <el-tag
                  :type="row.competition_level === 'high' ? 'danger' : row.competition_level === 'medium' ? 'warning' : 'success'"
                  size="small"
                >
                  {{ row.competition_level === 'high' ? '高' : row.competition_level === 'medium' ? '中' : '低' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="location_ids" label="国家" width="80" />
            <el-table-column prop="category" label="分类" width="100">
              <template #default="{ row }">
                <el-tag v-if="row.category" type="primary" size="small">
                  {{ row.category }}
                </el-tag>
                <span v-else class="text-muted">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="tags" label="标签" width="120" />
          </el-table>
        </div>
        
        <!-- 分页 -->
        <div class="keyword-pagination-section">
          <el-pagination
            v-model:current-page="keywordPagination.page"
            v-model:page-size="keywordPagination.limit"
            :page-sizes="[10, 20, 50, 100]"
            :total="keywordPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleKeywordSizeChange"
            @current-change="handleKeywordCurrentChange"
          />
        </div>
        
        <!-- 已选择的关键词 -->
        <div class="selected-keywords-section" v-if="selectedKeywords.length > 0">
          <h4>已选择的关键词 ({{ selectedKeywords.length }})</h4>
          <div class="selected-keywords-list">
            <el-tag 
              v-for="keyword in selectedKeywords" 
              :key="keyword.id"
              closable
              @close="removeSelectedKeyword(keyword)"
              class="selected-keyword-tag"
            >
              {{ keyword.keyword_name }}
            </el-tag>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showKeywordSelector = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmSelectKeywords"
            :disabled="selectedKeywords.length === 0"
          >
            确定选择 ({{ selectedKeywords.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文章详情对话框 -->
    <el-dialog
      v-model="showDetailsDialog"
      :title="selectedArticle?.title || '文章详情'"
      width="60%"
      :before-close="handleCloseDetailsDialog"
    >
      <div v-if="selectedArticle">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="关键词">
            {{ selectedArticle.keywords }}
          </el-descriptions-item>
          <el-descriptions-item label="WordPress站点">
            {{ selectedArticle.wordpress_url }}
          </el-descriptions-item>
          <el-descriptions-item label="类型">
            {{ selectedArticle.type }}
          </el-descriptions-item>
          <el-descriptions-item label="AI模型">
            {{ selectedArticle.model || '默认' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <div>
              <el-tag :type="getStatusType(selectedArticle.status)">
                {{ getStatusText(selectedArticle.status) }}
              </el-tag>
              <!-- 在详情中也显示实时进度 -->
              <div v-if="selectedArticle.status === 'generating' && selectedArticle.error_message && selectedArticle.error_message.startsWith('进度:')" 
                   class="progress-info">
                <el-icon class="is-loading"><Loading /></el-icon>
                {{ selectedArticle.error_message.replace('进度:', '') }}
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(selectedArticle.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="生成标题" :span="2">
            {{ selectedArticle.title || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="发布链接" :span="2">
            <a v-if="selectedArticle.article_url" :href="selectedArticle.article_url" target="_blank" class="link">
              {{ selectedArticle.article_url }}
            </a>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="文章ID">
            {{ selectedArticle.article_id || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="特色图片ID">
            {{ selectedArticle.featured_image_id || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="工作流ID" v-if="selectedArticle.workflow_run_id">
            <el-tooltip :content="selectedArticle.workflow_run_id" placement="top">
              <span style="font-family: monospace; color: #409EFF;">{{ selectedArticle.workflow_run_id }}</span>
            </el-tooltip>
          </el-descriptions-item>
          <el-descriptions-item label="任务ID" v-if="selectedArticle.task_id">
            <span style="font-family: monospace; color: #909399;">{{ selectedArticle.task_id }}</span>
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedArticle.error_message && !selectedArticle.error_message.startsWith('进度:')" style="margin-top: 20px;">
          <h4>错误信息：</h4>
          <el-alert 
            :title="selectedArticle.error_message" 
            type="error" 
            :closable="false"
            show-icon
          />
        </div>
        
        <div v-if="selectedArticle.error_message && selectedArticle.error_message.startsWith('进度:')" style="margin-top: 20px;">
          <h4>当前进度：</h4>
          <el-alert 
            :title="selectedArticle.error_message.replace('进度:', '')" 
            type="info" 
            :closable="false"
            show-icon
          >
            <template #default>
              <div style="display: flex; align-items: center; gap: 8px;">
                <el-icon class="is-loading"><Loading /></el-icon>
                工作流正在执行中，请耐心等待...
              </div>
            </template>
          </el-alert>
        </div>
        
        <div v-if="selectedArticle.result_text" style="margin-top: 20px;">
          <h4>完整结果：</h4>
          <el-input 
            v-model="selectedArticle.result_text" 
            type="textarea" 
            :rows="6" 
            readonly
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, MagicStick, UploadFilled, Loading, DocumentCopy, Collection, Refresh } from '@element-plus/icons-vue'
import { useStore } from 'vuex'
import apiClient from '@/utils/request'
import { formatDateTime as formatDateTimeWithTimezone } from '@/utils/timezone'

export default defineComponent({
  name: 'SeoAiArticle',
  components: {
    Search,
    MagicStick,
    UploadFilled,
    Loading,
    DocumentCopy,
    Collection,
    Refresh
  },
  setup() {
    const store = useStore()
    const loading = ref(false)
    const generating = ref(false)
    const showDetailsDialog = ref(false)
    const searchKeywords = ref('')
    const statusFilter = ref('')
    const articles = ref([])
    const selectedArticle = ref(null)
    const aiConfigs = ref([])
    const fileList = ref([])
    const generateFormRef = ref()
    const wordPressSites = ref([])
    const selectedSite = ref(null)
    const blogCategories = ref([])
    const blogTags = ref([])
    const filteredBlogTags = ref([])
    const tagsLoading = ref(false)
    const formKey = ref(0)
    
    // 关键词选择器相关数据
    const showKeywordSelector = ref(false)
    const keywordLoading = ref(false)
    const keywordTableData = ref([])
    const selectedKeywords = ref([])
    const keywordCategoryList = ref([])
    const keywordTableRef = ref(null)
    
    // 定时器管理
    const pollingTimers = ref(new Map()) // 存储所有轮询定时器
    
    // 获取侧边栏收起状态
    const isCollapse = computed(() => store.state.isCollapse)
    
    // 表单验证状态
    const isFormValid = computed(() => {
      return generateForm.keywords && 
             generateForm.wordpress_url && 
             generateForm.blog_category_id
    })
    
    const pagination = reactive({
      page: 1,
      size: 10,
      total: 0
    })
    
    const generateForm = reactive({
      keywords: '',
      wordpress_url: '',
      model: '',
      image_url: '',
      ai_config_id: null,
      wp_user: '',
      wp_pwd: '',
      blog_category_id: null,
      blog_tags: []
    })
    
    const generateRules = {
      keywords: [
        { required: true, message: '请输入关键词', trigger: 'blur' }
      ],
      wordpress_url: [
        { required: true, message: '请选择WordPress站点', trigger: 'change' }
      ],
      blog_category_id: [
        { required: true, message: '请选择文章分类', trigger: 'change' }
      ]
    }
    
    // 上传相关
    const uploadAction = computed(() => `/api/v1/ai-article/upload-image`)
    const uploadHeaders = computed(() => ({
      Authorization: `Bearer ${localStorage.getItem('token')}`
    }))
    
    // 关键词搜索表单
    const keywordSearchForm = reactive({
      keyword: '',
      country: '',
      competition: '',
      category: ''
    })
    
    // 关键词分页
    const keywordPagination = reactive({
      page: 1,
      limit: 20,
      total: 0
    })
    
    // 加载AI配置
    const loadAiConfigs = async () => {
      try {
        const response = await apiClient.get('/v1/ai-config/', {
          params: { size: 100, is_active: true }
        })
        
        // 检查响应数据结构（request.js已经处理了response.data）
        if (response && response.items) {
          aiConfigs.value = response.items
        } else {
          console.warn('AI配置API返回数据格式异常:', response)
          aiConfigs.value = []
        }
      } catch (error) {
        console.error('加载AI配置失败:', error)
        aiConfigs.value = []
        // 如果是认证错误，可以提示用户
        if (error.response && error.response.status === 401) {
          ElMessage.warning('请先登录系统')
          // 可以跳转到登录页面
          // this.$router.push('/login')
        } else if (error.response && error.response.status === 422) {
          ElMessage.warning('请求参数错误')
        } else if (error.response) {
          ElMessage.error(`API错误: ${error.response.status}`)
        }
      }
    }

    // 加载WordPress站点
    const loadWordPressSites = async () => {
      try {
        const response = await apiClient.get('/v1/wordpress-site/', {
          params: { size: 100, is_active: true }
        })
        
        if (response && response.items) {
          wordPressSites.value = response.items
        } else {
          console.warn('WordPress站点API返回数据格式异常:', response)
          wordPressSites.value = []
        }
      } catch (error) {
        console.error('加载WordPress站点失败:', error)
        wordPressSites.value = []
        if (error.response && error.response.status === 401) {
          ElMessage.warning('请先登录系统')
        } else if (error.response) {
          ElMessage.error(`加载站点失败: ${error.response.status}`)
        }
      }
    }

    // 站点选择变化处理
    const onSiteChange = (siteUrl) => {
      selectedSite.value = wordPressSites.value.find(site => site.url === siteUrl)
      if (selectedSite.value) {
        console.log('站点选择变化:', selectedSite.value.name, selectedSite.value)
        
        // 自动填充WordPress认证信息
        generateForm.wp_user = selectedSite.value.wp_username
        generateForm.wp_pwd = selectedSite.value.wp_app_password
        
        // 加载该站点的文章分类
        blogCategories.value = selectedSite.value.blog_categories || []
        console.log('加载文章分类:', blogCategories.value.length, '个')
        
        // 加载该站点的文章标签
        blogTags.value = selectedSite.value.blog_tags || []
        filteredBlogTags.value = selectedSite.value.blog_tags || []
        console.log('加载文章标签:', blogTags.value.length, '个')
        
        // 清空之前选择的文章分类和标签
        generateForm.blog_category_id = null
        generateForm.blog_tags = []
      } else {
        console.log('清空站点选择')
        // 清空文章分类和标签
        blogCategories.value = []
        blogTags.value = []
        filteredBlogTags.value = []
        generateForm.blog_category_id = null
        generateForm.blog_tags = []
      }
    }
    
    // 标签过滤方法
    const filterTags = (query) => {
      if (!query) {
        filteredBlogTags.value = blogTags.value
        return
      }
      
      // 过滤现有标签
      filteredBlogTags.value = blogTags.value.filter(tag => 
        tag.name.toLowerCase().includes(query.toLowerCase()) ||
        (tag.description && tag.description.toLowerCase().includes(query.toLowerCase()))
      )
    }

    // 创建新标签的方法
    const createNewTags = async (newTagNames, siteId) => {
      const createdTags = []
      
      for (const tagName of newTagNames) {
        try {
          const response = await apiClient.post(`/v1/wordpress-site/${siteId}/tags/create`, {
            name: tagName,
            description: `通过AI发文自动创建的标签`
          })
          
          if (response.success && response.data) {
            createdTags.push(response.data)
            ElMessage.success(`标签 "${tagName}" 创建成功`)
          }
        } catch (error) {
          console.error(`创建标签 "${tagName}" 失败:`, error)
          ElMessage.error(`创建标签 "${tagName}" 失败: ${error.response?.data?.error || error.message}`)
          throw error
        }
      }
      
      return createdTags
    }
    
    // 加载文章列表
    const loadArticles = async () => {
      loading.value = true
      try {
        const params = {
          page: pagination.page,
          size: pagination.size,
          keywords: searchKeywords.value || undefined,
          status: statusFilter.value || undefined
        }
        
        const response = await apiClient.get('/v1/ai-article/', { params })
        
        // 检查响应数据结构（request.js已经处理了response.data）
        if (response && response.items) {
          articles.value = response.items
          pagination.total = response.total
        } else {
          console.warn('AI文章API返回数据格式异常:', response)
          articles.value = []
          pagination.total = 0
        }
      } catch (error) {
        ElMessage.error('加载文章列表失败: ' + error.message)
      } finally {
        loading.value = false
      }
    }
    
    // 搜索处理
    let searchTimer = null
    const handleSearch = () => {
      clearTimeout(searchTimer)
      searchTimer = setTimeout(() => {
        pagination.page = 1
        loadArticles()
      }, 500)
    }
    
    // 分页处理
    const handleSizeChange = (size) => {
      pagination.size = size
      pagination.page = 1
      loadArticles()
    }
    
    const handleCurrentChange = (page) => {
      pagination.page = page
      loadArticles()
    }
    
    // 生成文章
    const generateArticle = async () => {
      if (!generateFormRef.value) return
      
      try {
        await generateFormRef.value.validate()
        generating.value = true
        
        // 处理标签：检查是否有新标签需要创建
        let finalBlogTags = [...(generateForm.blog_tags || [])]
        
        if (finalBlogTags.length > 0 && selectedSite.value) {
          // 分离现有标签ID和新标签名称
          const existingTagIds = finalBlogTags.filter(tag => typeof tag === 'number')
          const newTagNames = finalBlogTags.filter(tag => typeof tag === 'string' && tag.trim() !== '')
          
          // 如果有新标签需要创建
          if (newTagNames.length > 0) {
            try {
              ElMessage.info(`正在创建 ${newTagNames.length} 个新标签...`)
              const createdTags = await createNewTags(newTagNames, selectedSite.value.id)
              
              // 更新本地标签列表
              blogTags.value.push(...createdTags)
              filteredBlogTags.value.push(...createdTags)
              
              // 更新站点标签列表
              if (selectedSite.value.blog_tags) {
                selectedSite.value.blog_tags.push(...createdTags)
              } else {
                selectedSite.value.blog_tags = [...createdTags]
              }
              
              // 合并现有标签ID和新创建的标签ID
              finalBlogTags = [...existingTagIds, ...createdTags.map(tag => tag.id)]
            } catch (error) {
              ElMessage.error('创建新标签失败，无法继续生成文章')
              return
            }
          } else {
            finalBlogTags = existingTagIds
          }
        }
        
        // 将标签ID数组转换为逗号分隔的字符串
        const tagsString = finalBlogTags && finalBlogTags.length > 0 
          ? finalBlogTags.join(',') 
          : undefined
        
        // 构建请求数据，将blog_category_id作为type传递给DIFY
        const requestData = {
          keywords: generateForm.keywords,
          wordpress_url: generateForm.wordpress_url,
          type: generateForm.blog_category_id, // 将blog分类ID作为type传递
          tags: tagsString, // 添加标签字符串
          model: generateForm.model || undefined,
          image_url: generateForm.image_url || undefined,
          wp_user: generateForm.wp_user,
          wp_pwd: generateForm.wp_pwd
        }
        
        const response = await apiClient.post('/v1/ai-article/generate', requestData, {
          params: generateForm.ai_config_id ? { ai_config_id: generateForm.ai_config_id } : undefined
        })
        
        ElMessage.success('文章生成任务已启动，请稍后查看结果')
        await resetGenerateForm()
        await loadArticles()
        
        // 开始轮询检查状态
        const timer = pollArticleStatus(response.id)
        pollingTimers.value.set(response.id, timer)
      } catch (error) {
        ElMessage.error('启动生成失败: ' + error.message)
      } finally {
        generating.value = false
      }
    }
    
    // 轮询文章状态 - 优化版：只更新特定文章状态，不刷新整个列表
    const pollArticleStatus = (articleId) => {
      const timer = setInterval(async () => {
        try {
          const response = await apiClient.get(`/v1/ai-article/${articleId}/status`)
          const statusData = response
          
          // 在现有列表中找到对应的文章并更新其状态
          const articleIndex = articles.value.findIndex(article => article.id === articleId)
          if (articleIndex !== -1) {
            // 直接更新文章对象的状态相关字段
            Object.assign(articles.value[articleIndex], {
              status: statusData.status,
              title: statusData.title,
              article_url: statusData.article_url,
              error_message: statusData.error_message,
              workflow_run_id: statusData.workflow_run_id,
              task_id: statusData.task_id,
              updated_at: statusData.updated_at
            })
          }
          
          // 同时更新详情对话框中的数据（如果正在查看该文章）
          if (selectedArticle.value && selectedArticle.value.id === articleId) {
            Object.assign(selectedArticle.value, {
              status: statusData.status,
              title: statusData.title,
              article_url: statusData.article_url,
              error_message: statusData.error_message,
              workflow_run_id: statusData.workflow_run_id,
              task_id: statusData.task_id,
              updated_at: statusData.updated_at
            })
          }
          
          // 检查是否完成
          if (statusData.status === 'success' || statusData.status === 'failed' || statusData.status === 'published') {
            clearInterval(timer)
            pollingTimers.value.delete(articleId) // 从管理器中移除
            
            if (statusData.status === 'published') {
              ElMessage.success('文章已成功发布！')
            } else if (statusData.status === 'failed') {
              ElMessage.error('文章生成失败，请查看详情')
            } else if (statusData.status === 'success') {
              ElMessage.success('文章生成成功！')
            }
          }
        } catch (error) {
          clearInterval(timer)
          pollingTimers.value.delete(articleId) // 从管理器中移除
          console.error('轮询状态失败:', error)
        }
      }, 3000) // 调整为每3秒检查一次，减少服务器压力
      
      // 存储定时器，便于需要时清理
      return timer
    }
    
    // 重置生成表单
    const resetGenerateForm = async () => {
        Object.assign(generateForm, {
          keywords: '',
          wordpress_url: '',
          model: '',
          image_url: '',
          ai_config_id: null,
          wp_user: '',
          wp_pwd: '',
        blog_category_id: null,
        blog_tags: []
        })
        selectedSite.value = null
        blogCategories.value = []
      blogTags.value = []
      filteredBlogTags.value = []
        fileList.value = []
      
      // 强制重新渲染表单以清除验证状态
      formKey.value++
      
      // 等待DOM更新后再清除验证状态
      await nextTick()
      if (generateFormRef.value) {
        generateFormRef.value.clearValidate()
        
        // 额外延迟确保验证状态完全清除
        setTimeout(() => {
        if (generateFormRef.value) {
          generateFormRef.value.clearValidate()
          }
        }, 100)
        }
      }
    
    // 文件上传处理
    const beforeUpload = (file) => {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        ElMessage.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        ElMessage.error('图片大小不能超过 2MB!')
        return false
      }
      return true
    }
    
    const handleUploadSuccess = (response) => {
      if (response.status === 'success') {
        generateForm.image_url = response.file_id
        ElMessage.success('图片上传成功')
      } else {
        ElMessage.error('图片上传失败: ' + response.message)
      }
    }
    
    const handleUploadError = (error) => {
      ElMessage.error('图片上传失败: ' + error.message)
    }
    
    // 查看详情
    const viewDetails = (article) => {
      selectedArticle.value = article
      showDetailsDialog.value = true
    }
    
    // 停止生成
    const stopGeneration = async (article) => {
      try {
        await ElMessageBox.confirm('确定要停止文章生成监控吗？\n注意：这只会停止状态轮询，已启动的生成任务仍会继续。', '确认停止', {
          type: 'warning'
        })
        
        // 停止对应文章的轮询定时器
        const timer = pollingTimers.value.get(article.id)
        if (timer) {
          clearInterval(timer)
          pollingTimers.value.delete(article.id)
          ElMessage.success('已停止状态监控')
        } else {
          ElMessage.info('该文章当前没有活跃的状态监控')
        }
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('停止失败: ' + error.message)
        }
      }
    }
    
    // 删除文章
    const deleteArticle = async (article) => {
      try {
        await ElMessageBox.confirm('确定要删除这条记录吗？', '确认删除', {
          type: 'warning'
        })
        
        await apiClient.delete(`/v1/ai-article/${article.id}`)
        ElMessage.success('删除成功')
        await loadArticles()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败: ' + error.message)
        }
      }
    }
    
    // 状态相关
    const getStatusType = (status) => {
      const statusMap = {
        pending: 'info',
        generating: 'warning',
        success: 'success',
        failed: 'danger',
        published: 'success'
      }
      return statusMap[status] || 'info'
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        pending: '待生成',
        generating: '生成中',
        success: '生成成功',
        failed: '生成失败',
        published: '已发布'
      }
      return statusMap[status] || status
    }
    
    // 格式化日期时间 - 使用全局时区处理函数
    const formatDateTime = (dateTime) => {
      return formatDateTimeWithTimezone(dateTime)
    }
    

    
    // 清理所有定时器的函数
    const clearAllTimers = () => {
      pollingTimers.value.forEach((timer, articleId) => {
        clearInterval(timer)
        console.log(`清理文章 ${articleId} 的轮询定时器`)
      })
      pollingTimers.value.clear()
    }

    // 关键词选择器相关方法
    // 加载关键词列表
    const loadKeywords = async () => {
      keywordLoading.value = true
      try {
        const searchRequest = {
          keyword: keywordSearchForm.keyword || undefined,
          competition_level: keywordSearchForm.competition || undefined,
          category: keywordSearchForm.category || undefined,
          page: keywordPagination.page,
          page_size: keywordPagination.limit
        }
        
        // 过滤空值
        Object.keys(searchRequest).forEach(key => {
          if (searchRequest[key] === '' || searchRequest[key] === null || searchRequest[key] === undefined) {
            delete searchRequest[key]
          }
        })

        const response = await apiClient.post('/v1/keyword-library/keywords/search', searchRequest)
        
        if (response.items) {
          keywordTableData.value = response.items
          keywordPagination.total = response.total || 0
        } else {
          keywordTableData.value = []
          keywordPagination.total = 0
        }
      } catch (error) {
        console.error('加载关键词列表失败:', error)
        ElMessage.error('加载关键词列表失败')
      } finally {
        keywordLoading.value = false
      }
    }

    // 加载关键词分类列表
    const loadKeywordCategories = async () => {
      try {
        const response = await apiClient.get('/v1/keyword-library/categories')
        
        if (Array.isArray(response)) {
          keywordCategoryList.value = response
        } else if (response.data && Array.isArray(response.data)) {
          keywordCategoryList.value = response.data
        } else {
          keywordCategoryList.value = []
        }
      } catch (error) {
        console.error('加载关键词分类失败:', error)
        keywordCategoryList.value = []
      }
    }

    // 关键词搜索
    let keywordSearchTimer = null
    const handleKeywordSearch = () => {
      if (keywordSearchTimer) {
        clearTimeout(keywordSearchTimer)
      }
      
      keywordSearchTimer = setTimeout(() => {
        keywordPagination.page = 1
        loadKeywords()
      }, 500)
    }

    // 重置关键词搜索
    const resetKeywordSearch = () => {
      Object.assign(keywordSearchForm, {
        keyword: '',
        country: '',
        competition: '',
        category: ''
      })
      keywordPagination.page = 1
      loadKeywords()
    }

    // 关键词分页处理
    const handleKeywordSizeChange = (size) => {
      keywordPagination.limit = size
      keywordPagination.page = 1
      loadKeywords()
    }

    const handleKeywordCurrentChange = (page) => {
      keywordPagination.page = page
      loadKeywords()
    }

    // 关键词选择处理
    const handleKeywordSelectionChange = (selection) => {
      selectedKeywords.value = selection
    }

    // 移除已选择的关键词
    const removeSelectedKeyword = (keyword) => {
      const index = selectedKeywords.value.findIndex(k => k.id === keyword.id)
      if (index > -1) {
        selectedKeywords.value.splice(index, 1)
        // 同时更新表格选中状态
        if (keywordTableRef.value) {
          keywordTableRef.value.toggleRowSelection(keyword, false)
        }
      }
    }

    // 确认选择关键词
    const confirmSelectKeywords = () => {
      if (selectedKeywords.value.length === 0) {
        ElMessage.warning('请选择关键词')
        return
      }
      
      // 获取选中关键词的名称
      const keywordNames = selectedKeywords.value.map(k => k.keyword_name)
      
      // 合并到现有关键词中
      const currentKeywords = generateForm.keywords.trim()
      let newKeywords = ''
      
      if (currentKeywords) {
        newKeywords = currentKeywords + ', ' + keywordNames.join(', ')
      } else {
        newKeywords = keywordNames.join(', ')
      }
      
      generateForm.keywords = newKeywords
      
      // 关闭对话框并清空选择
      showKeywordSelector.value = false
      selectedKeywords.value = []
      
      ElMessage.success(`已添加 ${keywordNames.length} 个关键词`)
    }

    // 关闭关键词选择器
    const handleCloseKeywordSelector = () => {
      showKeywordSelector.value = false
      selectedKeywords.value = []
    }

    // 关闭文章详情对话框
    const handleCloseDetailsDialog = () => {
      showDetailsDialog.value = false
      selectedArticle.value = null
    }

    // 格式化数字
    const formatNumber = (num) => {
      if (!num) return '0'
      return num.toLocaleString()
    }

    // 打开关键词选择器
    const openKeywordSelector = () => {
      showKeywordSelector.value = true
      loadKeywords()
      loadKeywordCategories()
    }

    onMounted(() => {
      loadAiConfigs()
      loadWordPressSites()
      loadArticles()
      // 当用户点击关键词选择器时再加载关键词数据，提高初始加载性能
    })
    
    // 组件卸载时清理所有定时器
    onUnmounted(() => {
      clearAllTimers()
    })
    
    return {
      isCollapse,
      isFormValid,
      loading,
      generating,
      showDetailsDialog,
      searchKeywords,
      statusFilter,
      articles,
      selectedArticle,
      aiConfigs,
      fileList,
      pagination,
      generateForm,
      generateRules,
      generateFormRef,
      uploadAction,
      uploadHeaders,
      loadArticles,
      loadWordPressSites,
      handleSearch,
      handleSizeChange,
      handleCurrentChange,
      generateArticle,
      resetGenerateForm,
      beforeUpload,
      handleUploadSuccess,
      handleUploadError,
      viewDetails,
      stopGeneration,
      deleteArticle,
      getStatusType,
      getStatusText,
      formatDateTime,
      wordPressSites,
      selectedSite,
      onSiteChange,
      blogCategories,
      blogTags,
      filteredBlogTags,
      tagsLoading,
      filterTags,
      createNewTags,
      formKey,
      pollingTimers,
      showKeywordSelector,
      keywordLoading,
      keywordTableData,
      selectedKeywords,
      keywordCategoryList,
      keywordTableRef,
      keywordSearchForm,
      keywordPagination,
      formatNumber,
      loadKeywords,
      loadKeywordCategories,
      handleKeywordSearch,
      resetKeywordSearch,
      handleKeywordSizeChange,
      handleKeywordCurrentChange,
      handleKeywordSelectionChange,
      removeSelectedKeyword,
      confirmSelectKeywords,
      handleCloseKeywordSelector,
      handleCloseDetailsDialog,
      openKeywordSelector
    }
  }
})
</script>

<style scoped>
.seo-ai-article {
  padding: 0;
  margin: 0;
  width: calc(100vw - 236px); /* 减去侧边栏宽度 + 左边距 */
  height: calc(100vh - 72px); /* 减去header高度 + 上边距 */
  overflow: hidden;
  background: #f5f5f5;
  position: fixed;
  top: 72px; /* header高度 + 上边距8px */
  left: 236px; /* 侧边栏宽度 + 左边距16px */
  z-index: 1;
}

/* 侧边栏收起状态适配 */
.seo-ai-article.sidebar-collapsed {
  width: calc(100vw - 80px); /* 减去收起侧边栏宽度 + 左边距 */
  left: 80px; /* 收起侧边栏宽度 + 左边距16px */
}

/* 主要布局 */
.main-content {
  display: flex;
  gap: 16px;
  height: 100%;
  padding: 16px;
  box-sizing: border-box;
  overflow: hidden; /* 防止内容溢出 */
}

/* 左侧表单区域 */
.form-section {
  flex: 0 0 380px; /* 稍微增加宽度到380px */
  min-width: 380px;
}

.form-card {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
}

.form-card :deep(.el-card__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.form-card :deep(.el-card__body) {
  padding: 20px;
  height: calc(100% - 76px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.generate-form {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.form-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  margin-right: -8px;
  min-height: 0;
}

.form-buttons {
  flex-shrink: 0;
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
  display: flex;
  gap: 12px;
  justify-content: space-between;
  height: 52px;
  align-items: center;
}

/* 滚动条美化 */
.form-content::-webkit-scrollbar {
  width: 6px;
}

.form-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.form-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.form-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.generate-form .el-form-item {
  margin-bottom: 20px;
}

.generate-form .el-form-item:last-child {
  margin-bottom: 0;
  padding-top: 16px;
}

/* 右侧列表区域 */
.list-section {
  flex: 1;
  min-width: 0; /* 允许收缩 */
}

.list-card {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: none;
}

.list-card :deep(.el-card__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.list-card :deep(.el-card__body) {
  padding: 24px;
  height: calc(100% - 76px);
  display: flex;
  flex-direction: column;
}

.list-card :deep(.el-table) {
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
}

.list-card :deep(.el-table__body-wrapper) {
  overflow-y: auto;
  max-height: none; /* 移除固定高度限制 */
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.link {
  color: #409eff;
  text-decoration: none;
}

.link:hover {
  text-decoration: underline;
}

/* 监控指示器和状态相关样式 */
.monitoring-indicator {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.progress-info {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.workflow-id {
  margin-top: 5px;
  font-size: 11px;
  color: #909399;
}

.id-label {
  color: #606266;
}

.id-value {
  font-family: monospace;
  color: #409EFF;
  cursor: pointer;
}

.id-value:hover {
  color: #66B1FF;
}

/* 关键词输入区域样式 */
.keyword-input {
  width: 100%;
}

.keyword-input :deep(.el-textarea__inner) {
  height: 80px !important;
  min-height: 80px !important;
  resize: none !important;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 关键词操作按钮区域 */
.keyword-actions {
  margin-top: 8px;
  display: flex;
  justify-content: flex-start;
}

.keyword-actions .keyword-select-btn {
  margin-top: 0;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.keyword-actions .keyword-select-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
}

/* 操作按钮区域样式 */
.button-form-item {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
  background: white;
  flex-shrink: 0;
}

.action-buttons {
  display: flex;
  gap: 12px;
  width: 100%;
}

.action-button {
  flex: 1;
  height: 44px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
}

.primary-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  color: white;
}

.primary-button:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.5);
}

.primary-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.secondary-button {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #6c757d;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.secondary-button:hover {
  background: #e9ecef;
  border-color: #adb5bd;
  color: #495057;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.secondary-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

/* 关键词选择器对话框样式 */
.keyword-selector {
  padding: 0;
}

.keyword-selector .search-section {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.keyword-table-section {
  margin-bottom: 20px;
}

.keyword-pagination-section {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.selected-keywords-section {
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #e1f5fe;
}

.selected-keywords-section h4 {
  margin: 0 0 12px 0;
  color: #1976d2;
  font-size: 14px;
  font-weight: 600;
}

.selected-keywords-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-keyword-tag {
  margin: 0;
}

.text-muted {
  color: #999;
}

/* 重复的CSS规则已被移除，使用上面已定义的规则 */

.id-label {
  color: #606266;
}

.id-value {
  font-family: monospace;
  color: #409EFF;
  cursor: pointer;
}

.form-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.form-header .header-icon {
  font-size: 20px;
  color: white;
}

.list-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.list-header .header-icon {
  font-size: 20px;
  color: white;
}

/* 表单元素样式优化 */
.generate-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.generate-form :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.generate-form :deep(.el-textarea__inner) {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.generate-form :deep(.el-button) {
  border-radius: 8px;
  font-weight: 600;
}

.generate-form :deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.generate-form :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.5);
}

/* 上传组件样式优化 */
.compact-upload {
  width: 100%;
}

.compact-upload :deep(.el-upload-dragger) {
  width: 100%;
  height: 80px;
  border-radius: 6px;
}

.compact-upload :deep(.el-upload__text) {
  font-size: 12px;
  margin-top: 8px;
}

.compact-upload :deep(.el-upload__tip) {
  font-size: 11px;
  color: #909399;
  margin-top: 4px;
}

.compact-upload :deep(.el-icon--upload) {
  font-size: 24px;
  margin-bottom: 4px;
}

/* 表格样式优化 */
.list-card :deep(.el-table__header) {
  background-color: #fafafa;
}

.list-card :deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 600;
  color: #303133;
}

.list-card :deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

.list-card :deep(.el-table__row:hover) {
  background-color: #f8f9ff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .form-section {
    flex: 0 0 320px;
    min-width: 320px;
  }
}

@media (max-width: 768px) {
  .seo-ai-article {
    width: calc(100vw - 16px); /* 移动端保留小边距 */
    left: 8px; /* 移动端小左边距 */
    top: 72px; /* 移动端小上边距 */
    height: calc(100vh - 80px);
  }
  
  .main-content {
    flex-direction: column;
    gap: 12px;
    padding: 12px;
  }
  
  .form-section {
    flex: none;
    min-width: auto;
  }
  
  .list-section {
    flex: none;
    height: 60vh;
  }
}

/* 处理浏览器缩放和各种屏幕尺寸 */
@media (min-width: 769px) and (max-width: 1024px) {
  .form-section {
    flex: 0 0 320px;
    min-width: 320px;
  }
}

@media (min-width: 1025px) and (max-width: 1440px) {
  .form-section {
    flex: 0 0 360px;
    min-width: 360px;
  }
}

@media (min-width: 1441px) and (max-width: 1920px) {
  .form-section {
    flex: 0 0 380px;
    min-width: 380px;
  }
}

/* 文章分类选择器样式 */
.category-option {
  display: flex;
  flex-direction: column;
  padding: 4px 0;
}

.category-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.category-info {
  display: flex;
  gap: 8px;
  font-size: 12px;
}

.category-count {
  color: #909399;
  background: #f0f2f5;
  padding: 1px 4px;
  border-radius: 2px;
}

.category-desc {
  color: #909399;
  font-size: 12px;
}

/* 超宽屏适配 */
@media (min-width: 1921px) {
  .form-section {
    flex: 0 0 420px;
    min-width: 420px;
  }
}

/* 站点选择样式 */
.site-option {
  padding: 4px 0;
}

.site-option .site-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.site-option .site-url {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

/* 处理高DPI屏幕和浏览器缩放 */
@media (-webkit-min-device-pixel-ratio: 1.5), (min-resolution: 144dpi) {
  .seo-ai-article {
    /* 高DPI屏幕下保持布局稳定 */
    transform: translateZ(0); /* 启用硬件加速 */
  }
}

/* 确保在任何缩放比例下都不会出现滚动条 */
.main-content {
  min-height: 0; /* 允许flex项目收缩 */
}

.form-card :deep(.el-card__body),
.list-card :deep(.el-card__body) {
  min-height: 0; /* 允许内容区域收缩 */
}

.form-button {
  flex: 1;
  height: 36px;
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.form-buttons :deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.35);
}

.form-buttons :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.form-buttons :deep(.el-button:not(.el-button--primary)) {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #6c757d;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.form-buttons :deep(.el-button:not(.el-button--primary):hover) {
  background: #e9ecef;
  border-color: #adb5bd;
  color: #495057;
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
}

/* 文件列表卡片样式 */
.tag-option {
  padding: 4px 0;
}

.tag-option .tag-name {
  font-weight: 500;
  margin-bottom: 2px;
}

.tag-option .tag-info {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.tag-option .tag-count {
  color: #67C23A;
}

.tag-option .tag-desc {
  color: #909399;
  font-size: 12px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

/* 确保多选标签显示正常 */
.el-select__tags {
  max-width: calc(100% - 30px);
}
</style> 