<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时区修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background-color: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>时区修复测试</h1>
    
    <div class="test-section">
        <h2>测试环境变量读取</h2>
        <div id="env-test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>测试UTC时间格式化</h2>
        <div id="format-test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>测试表格格式化函数</h2>
        <div id="table-test-results"></div>
    </div>

    <script type="module">
        // 模拟环境变量
        window.import = {
            meta: {
                env: {
                    VITE_SYSTEM_TIMEZONE_OFFSET: '8',
                    VITE_SYSTEM_TIMEZONE_NAME: 'Asia/Shanghai'
                }
            }
        };

        // 导入时区工具函数
        import { 
            getSystemTimezoneConfig, 
            formatDateTime, 
            formatTableDateTime,
            parseUTCString,
            formatToSystemTimezone
        } from './src/utils/timezone.js';

        // 测试环境变量读取
        function testEnvironmentVariables() {
            try {
                const config = getSystemTimezoneConfig();
                document.getElementById('env-test-results').innerHTML = `
                    <div class="result success">
                        <strong>环境变量读取成功:</strong><br>
                        时区偏移: ${config.offset} 小时<br>
                        时区名称: ${config.name}<br>
                        偏移字符串: ${config.offsetString}
                    </div>
                `;
            } catch (error) {
                document.getElementById('env-test-results').innerHTML = `
                    <div class="result error">
                        <strong>环境变量读取失败:</strong> ${error.message}
                    </div>
                `;
            }
        }

        // 测试UTC时间格式化
        function testUTCFormatting() {
            const testCases = [
                '2024-01-01T00:00:00Z',  // UTC午夜 -> 北京时间上午8点
                '2024-01-01T08:00:00Z',  // UTC上午8点 -> 北京时间下午4点
                '2024-01-01T16:00:00Z',  // UTC下午4点 -> 北京时间午夜
                '2024-12-25T12:00:00Z'   // UTC中午 -> 北京时间晚上8点
            ];

            let results = '<h4>UTC时间格式化结果:</h4>';
            
            testCases.forEach(utcTime => {
                try {
                    const formatted = formatDateTime(utcTime);
                    const expectedBeijingHour = (parseInt(utcTime.split('T')[1].split(':')[0]) + 8) % 24;
                    
                    results += `
                        <div class="result">
                            <strong>UTC时间:</strong> ${utcTime}<br>
                            <strong>格式化结果:</strong> ${formatted}<br>
                            <strong>预期北京时间小时:</strong> ${expectedBeijingHour}
                        </div>
                    `;
                } catch (error) {
                    results += `
                        <div class="result error">
                            <strong>格式化失败:</strong> ${utcTime} - ${error.message}
                        </div>
                    `;
                }
            });
            
            document.getElementById('format-test-results').innerHTML = results;
        }

        // 测试表格格式化函数
        function testTableFormatting() {
            // 模拟表格数据
            const tableData = [
                { name: '任务1', scheduled_time: '2024-01-01T08:00:00Z' },
                { name: '任务2', scheduled_time: '2024-01-01T12:00:00Z' },
                { name: '任务3', scheduled_time: '2024-01-01T16:00:00Z' }
            ];

            let results = '<h4>表格格式化结果:</h4>';
            results += '<table border="1" style="width:100%; border-collapse: collapse;">';
            results += '<tr><th>任务名称</th><th>开始时间(UTC)</th><th>开始时间(北京)</th></tr>';
            
            tableData.forEach(row => {
                const formatted = formatTableDateTime(row, null, row.scheduled_time);
                results += `
                    <tr>
                        <td>${row.name}</td>
                        <td>${row.scheduled_time}</td>
                        <td>${formatted}</td>
                    </tr>
                `;
            });
            
            results += '</table>';
            document.getElementById('table-test-results').innerHTML = results;
        }

        // 运行所有测试
        window.addEventListener('load', () => {
            testEnvironmentVariables();
            testUTCFormatting();
            testTableFormatting();
        });

        // 导出测试函数到全局作用域
        window.testEnvironmentVariables = testEnvironmentVariables;
        window.testUTCFormatting = testUTCFormatting;
        window.testTableFormatting = testTableFormatting;
    </script>
</body>
</html>
