<template>
  <el-dialog
    v-model="dialogVisible"
    title="关键词库统计信息"
    width="900px"
    :before-close="handleClose"
  >
    <div class="stats-content" v-loading="loading">
      <!-- 概览卡片 -->
      <div class="overview-cards">
        <div class="stat-card">
          <div class="stat-icon total">
            <el-icon><DataLine /></el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ statsData.total_keywords || 0 }}</h3>
            <p>关键词总数</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon searches">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ formatNumber(statsData.avg_monthly_searches) || '--' }}</h3>
            <p>平均月搜索量</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon manual">
            <el-icon><Edit /></el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ statsData.by_update_method?.manual || 0 }}</h3>
            <p>手工添加</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon google-ads">
            <el-icon><Connection /></el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ statsData.by_update_method?.google_ads_api || 0 }}</h3>
            <p>Google Ads 导入</p>
          </div>
        </div>
      </div>

      <!-- 统计表格区域 -->
      <div class="stats-section">
        <!-- 更新方式分布 -->
        <div class="stats-container">
          <h4>按更新方式分布</h4>
          <el-table
            :data="updateMethodData"
            border
            stripe
            style="width: 100%"
          >
            <el-table-column prop="method" label="更新方式" />
            <el-table-column prop="count" label="数量" align="right" />
            <el-table-column prop="percentage" label="占比" align="right" />
          </el-table>
        </div>

        <!-- 竞争级别分布 -->
        <div class="stats-container">
          <h4>按竞争级别分布</h4>
          <el-table
            :data="competitionLevelData"
            border
            stripe
            style="width: 100%"
          >
            <el-table-column prop="level" label="竞争级别" />
            <el-table-column prop="count" label="数量" align="right" />
            <el-table-column prop="percentage" label="占比" align="right" />
          </el-table>
        </div>
      </div>

      <!-- 热门关键词 -->
      <div class="top-keywords-section">
        <h4>热门关键词 Top 10</h4>
        <el-table
          :data="statsData.top_keywords || []"
          border
          stripe
          style="width: 100%"
        >
          <el-table-column prop="keyword_name" label="关键词" width="200" />
          <el-table-column prop="avg_monthly_searches" label="月搜索量" width="120" align="right">
            <template #default="scope">
              {{ formatNumber(scope.row.avg_monthly_searches) }}
            </template>
          </el-table-column>
          <el-table-column prop="competition_level" label="竞争级别" width="100" align="center">
            <template #default="scope">
              <el-tag
                :type="getCompetitionTagType(scope.row.competition_level)"
                size="small"
              >
                {{ getCompetitionText(scope.row.competition_level) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="competition_index" label="竞争指数" width="100" align="right">
            <template #default="scope">
              <span v-if="scope.row.competition_index">
                {{ scope.row.competition_index.toFixed(1) }}
              </span>
              <span v-else class="text-muted">--</span>
            </template>
          </el-table-column>
          <el-table-column prop="tags" label="标签" min-width="150">
            <template #default="scope">
              <div v-if="scope.row.tags && scope.row.tags.length">
                <el-tag
                  v-for="tag in scope.row.tags.split(',').slice(0, 3)"
                  :key="tag"
                  size="small"
                  style="margin-right: 5px"
                >
                  {{ tag.trim() }}
                </el-tag>
                <span v-if="scope.row.tags.split(',').length > 3" class="text-muted">
                  +{{ scope.row.tags.split(',').length - 3 }}
                </span>
              </div>
              <span v-else class="text-muted">--</span>
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" label="更新时间" width="160">
            <template #default="scope">
              {{ formatDateTime(scope.row.updated_at) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="refreshStats" icon="el-icon-refresh">刷新</el-button>
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  DataLine,
  TrendCharts,
  Edit,
  Connection
} from '@element-plus/icons-vue'
import { formatNumber, formatDateTime } from '@/utils/format'
import { keywordService } from '@/services/keyword'

export default {
  name: 'StatsDialog',
  components: {
    DataLine,
    TrendCharts,
    Edit,
    Connection
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const loading = ref(false)

    // 计算属性
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })

    // 统计数据
    const statsData = reactive({
      total_keywords: 0,
      avg_monthly_searches: null,
      by_update_method: {},
      by_competition_level: {},
      top_keywords: []
    })

    // 计算更新方式分布数据
    const updateMethodData = computed(() => {
      const data = []
      const total = statsData.total_keywords || 0
      
      const methodMap = {
        manual: '手工添加',
        google_ads_api: 'Google Ads API',
        batch_import: '批量导入'
      }

      Object.keys(methodMap).forEach(key => {
        const count = statsData.by_update_method[key] || 0
        const percentage = total > 0 ? ((count / total) * 100).toFixed(1) + '%' : '0%'
        data.push({
          method: methodMap[key],
          count,
          percentage
        })
      })

      return data
    })

    // 计算竞争级别分布数据
    const competitionLevelData = computed(() => {
      const data = []
      const total = statsData.total_keywords || 0
      
      const levelMap = {
        low: '低',
        medium: '中',
        high: '高',
        unspecified: '未知'
      }

      Object.keys(levelMap).forEach(key => {
        const count = statsData.by_competition_level[key] || 0
        const percentage = total > 0 ? ((count / total) * 100).toFixed(1) + '%' : '0%'
        data.push({
          level: levelMap[key],
          count,
          percentage
        })
      })

      return data
    })

    // 监听对话框显示
    watch(() => props.visible, (visible) => {
      if (visible) {
        loadStats()
      }
    })

    // 加载统计数据
    const loadStats = async () => {
      loading.value = true
      try {
        const response = await keywordService.getStats()
        Object.assign(statsData, response)
      } catch (error) {
        console.error('加载统计数据失败:', error)
        ElMessage.error('加载统计数据失败')
      } finally {
        loading.value = false
      }
    }

    // 刷新统计数据
    const refreshStats = () => {
      loadStats()
    }

    // 关闭对话框
    const handleClose = () => {
      dialogVisible.value = false
    }

    // 辅助函数
    const getCompetitionTagType = (level) => {
      const typeMap = {
        low: 'success',
        medium: 'warning',
        high: 'danger',
        unspecified: 'info'
      }
      return typeMap[level] || 'info'
    }

    const getCompetitionText = (level) => {
      const textMap = {
        low: '低',
        medium: '中',
        high: '高',
        unspecified: '未知'
      }
      return textMap[level] || '未知'
    }

    return {
      loading,
      dialogVisible,
      statsData,
      updateMethodData,
      competitionLevelData,
      loadStats,
      refreshStats,
      handleClose,
      getCompetitionTagType,
      getCompetitionText,
      formatNumber,
      formatDateTime
    }
  }
}
</script>

<style scoped>
.stats-content {
  padding: 20px 0;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: #409EFF;
}

.stat-icon.searches {
  background: #67C23A;
}

.stat-icon.manual {
  background: #E6A23C;
}

.stat-icon.google-ads {
  background: #F56C6C;
}

.stat-info h3 {
  margin: 0 0 5px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.stat-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.stats-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.stats-container h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #333;
}

.top-keywords-section h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #333;
}

.text-muted {
  color: #999;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

@media (max-width: 768px) {
  .stats-section {
    grid-template-columns: 1fr;
  }
  
  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style> 