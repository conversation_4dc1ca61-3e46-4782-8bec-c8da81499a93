from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

class AlibabaAuthURL(BaseModel):
    """授权URL响应模式"""
    auth_url: str = Field(..., description="授权URL")
    state: Optional[str] = Field(None, description="状态参数")

class AlibabaCallback(BaseModel):
    """授权回调请求模式"""
    code: str = Field(..., description="授权码")
    state: Optional[str] = Field(None, description="状态参数")

class AlibabaAuthInfo(BaseModel):
    """阿里巴巴授权信息响应模式"""
    id: int
    user_id: int
    tenant_id: str
    alibaba_account: Optional[str] = None
    country: Optional[str] = None
    account_platform: Optional[str] = None
    havana_id: Optional[str] = None
    is_active: bool
    token_created_at: Optional[datetime] = None
    last_refresh_at: Optional[datetime] = None
    expires_in: Optional[int] = None
    refresh_expires_in: Optional[int] = None
    
    class Config:
        from_attributes = True

class AlibabaAuthResponse(BaseModel):
    """授权成功响应模式"""
    message: str = Field(..., description="响应消息")
    auth_info: AlibabaAuthInfo = Field(..., description="授权信息")

class AlibabaTokenStatus(BaseModel):
    """Token状态响应模式"""
    is_valid: bool = Field(..., description="Token是否有效")
    account: Optional[str] = Field(None, description="关联的阿里巴巴账号")
    expires_at: Optional[datetime] = Field(None, description="Token过期时间")
    refresh_expires_at: Optional[datetime] = Field(None, description="刷新Token过期时间")

# 产品表现相关模型
class ProductPerformanceRequest(BaseModel):
    """产品表现数据请求模式"""
    statistics_type: str = Field(..., description="统计周期：day/week/month")
    stat_date: str = Field(..., description="统计日期")
    product_ids: List[str] = Field(..., description="产品ID列表")

class ProductPerformanceDateRangeRequest(BaseModel):
    """产品表现时间范围请求模式"""
    statistics_type: Optional[str] = Field("day", description="统计周期类型")

# 关键词表现相关模型
class KeywordDateRange(BaseModel):
    """关键词数据时间范围"""
    start_date: str = Field(..., description="开始日期")
    end_date: str = Field(..., description="结束日期")

class KeywordProperties(BaseModel):
    """关键词查询属性"""
    keyword: Optional[str] = Field(None, description="关键词")
    is_p4p: Optional[str] = Field("ALL", description="是否P4P推广")
    keywords_in_use: Optional[str] = Field("ALL", description="是否已设置为关键词")
    keywords_viewed: Optional[str] = Field("ALL", description="是否有曝光")
    order_by_field: Optional[str] = Field("sumShowCnt", description="排序字段")
    order_by_mode: Optional[str] = Field("desc", description="排序方式")
    limit: Optional[int] = Field(20, description="返回数据大小")
    offset: Optional[int] = Field(0, description="数据偏移")

class KeywordPerformanceWeekRequest(BaseModel):
    """关键词周统计请求模式"""
    date_range: KeywordDateRange = Field(..., description="数据时间范围")
    properties: KeywordProperties = Field(..., description="查询属性")

class KeywordPerformanceMonthRequest(BaseModel):
    """关键词月统计请求模式"""
    properties: KeywordProperties = Field(..., description="查询属性") 