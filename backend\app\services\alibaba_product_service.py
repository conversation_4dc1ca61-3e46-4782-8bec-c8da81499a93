from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc, func
from datetime import date, datetime, timedelta
from decimal import Decimal
import logging
import random

from app.models.alibaba_product import AlibabaProductPerformance, AlibabaProduct
from app.services.alibaba_service import AlibabaAPIService
from app.utils.datetime_utils import utc_now, to_iso_string

logger = logging.getLogger(__name__)

class AlibabaProductService:
    """阿里巴巴产品表现服务"""
    
    def __init__(self):
        self.alibaba_api = AlibabaAPIService()

    def get_product_performance(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str,
        start_date: date,
        end_date: date,
        product_ids: Optional[List[str]] = None
    ) -> List[AlibabaProductPerformance]:
        """获取产品表现数据"""
        query = db.query(AlibabaProductPerformance).filter(
            and_(
                AlibabaProductPerformance.user_id == user_id,
                AlibabaProductPerformance.tenant_id == tenant_id,
                AlibabaProductPerformance.statistics_type == statistics_type,
                AlibabaProductPerformance.stat_date >= start_date,
                AlibabaProductPerformance.stat_date <= end_date
            )
        )
        
        if product_ids:
            query = query.filter(AlibabaProductPerformance.product_id.in_(product_ids))
            
        return query.order_by(desc(AlibabaProductPerformance.impression)).all()

    def get_available_products(
        self,
        db: Session,
        user_id: int,
        tenant_id: str
    ) -> List[AlibabaProduct]:
        """获取可用的产品列表"""
        return db.query(AlibabaProduct).filter(
            and_(
                AlibabaProduct.user_id == user_id,
                AlibabaProduct.tenant_id == tenant_id,
                AlibabaProduct.is_active == True
            )
        ).all()

    def get_date_range(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str
    ) -> Dict[str, Any]:
        """获取可用的时间范围"""
        result = db.query(
            func.min(AlibabaProductPerformance.stat_date).label('start_date'),
            func.max(AlibabaProductPerformance.stat_date).label('end_date')
        ).filter(
            and_(
                AlibabaProductPerformance.user_id == user_id,
                AlibabaProductPerformance.tenant_id == tenant_id,
                AlibabaProductPerformance.statistics_type == statistics_type
            )
        ).first()
        
        if result.start_date and result.end_date:
            return {
                "start_date": result.start_date.isoformat(),
                "end_date": result.end_date.isoformat()
            }
        else:
            # 如果没有数据，返回最近30天
            end_date = date.today()
            start_date = end_date - timedelta(days=30)
            return {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            }

    def save_product_performance(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        performance_data: Dict[str, Any],
        product_id: str,
        product_name: str,
        statistics_type: str,
        stat_date: date
    ) -> AlibabaProductPerformance:
        """保存产品表现数据"""
        # 检查是否已存在
        existing = db.query(AlibabaProductPerformance).filter(
            and_(
                AlibabaProductPerformance.user_id == user_id,
                AlibabaProductPerformance.tenant_id == tenant_id,
                AlibabaProductPerformance.product_id == product_id,
                AlibabaProductPerformance.statistics_type == statistics_type,
                AlibabaProductPerformance.stat_date == stat_date
            )
        ).first()
        
        if existing:
            # 更新现有记录
            existing.product_name = product_name
            existing.impression = performance_data.get("impression", 0)
            existing.click = performance_data.get("click", 0)
            existing.visitor = performance_data.get("visitor", 0)
            existing.order_count = performance_data.get("order_count", 0)
            existing.bookmark = performance_data.get("bookmark", 0)
            existing.compare = performance_data.get("compare", 0)
            existing.share = performance_data.get("share", 0)
            existing.feedback = performance_data.get("feedback", 0)
            existing.ctr = Decimal(str(performance_data.get("ctr", 0)))
            existing.conversion_rate = Decimal(str(performance_data.get("conversion_rate", 0)))
            existing.sync_time = utc_now()
            db_performance = existing
        else:
            # 创建新记录
            db_performance = AlibabaProductPerformance(
                user_id=user_id,
                tenant_id=tenant_id,
                product_id=product_id,
                product_name=product_name,
                statistics_type=statistics_type,
                stat_date=stat_date,
                impression=performance_data.get("impression", 0),
                click=performance_data.get("click", 0),
                visitor=performance_data.get("visitor", 0),
                order_count=performance_data.get("order_count", 0),
                bookmark=performance_data.get("bookmark", 0),
                compare=performance_data.get("compare", 0),
                share=performance_data.get("share", 0),
                feedback=performance_data.get("feedback", 0),
                ctr=Decimal(str(performance_data.get("ctr", 0))),
                conversion_rate=Decimal(str(performance_data.get("conversion_rate", 0))),
                sync_time=utc_now(),
                data_source="mock_api"
            )
            db.add(db_performance)
        
        db.commit()
        db.refresh(db_performance)
        return db_performance

    def save_products(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        products_data: List[Dict[str, Any]]
    ) -> List[AlibabaProduct]:
        """保存产品列表"""
        saved_products = []
        
        for product_data in products_data:
            # 检查是否已存在
            existing = db.query(AlibabaProduct).filter(
                and_(
                    AlibabaProduct.user_id == user_id,
                    AlibabaProduct.tenant_id == tenant_id,
                    AlibabaProduct.product_id == product_data["product_id"]
                )
            ).first()
            
            if existing:
                # 更新现有记录
                existing.product_name = product_data.get("product_name", "")
                existing.product_title = product_data.get("product_title", "")
                existing.product_category = product_data.get("product_category", "")
                existing.product_status = product_data.get("product_status", "")
                existing.product_url = product_data.get("product_url", "")
                existing.main_image_url = product_data.get("main_image_url", "")
                existing.description = product_data.get("description", "")
                existing.sync_time = utc_now()
                saved_products.append(existing)
            else:
                # 创建新记录
                db_product = AlibabaProduct(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    product_id=product_data["product_id"],
                    product_name=product_data.get("product_name", ""),
                    product_title=product_data.get("product_title", ""),
                    product_category=product_data.get("product_category", ""),
                    product_status=product_data.get("product_status", "active"),
                    product_url=product_data.get("product_url", ""),
                    main_image_url=product_data.get("main_image_url", ""),
                    description=product_data.get("description", ""),
                    sync_time=utc_now(),
                    is_active=True
                )
                db.add(db_product)
                saved_products.append(db_product)
        
        db.commit()
        return saved_products

    async def sync_data_from_alibaba(
        self,
        db: Session,
        user_id: int,
        tenant_id: str,
        statistics_type: str,
        start_date: date,
        end_date: date,
        product_ids: Optional[List[str]] = None,
        access_token: str = None
    ) -> Dict[str, int]:
        """从阿里国际站同步产品表现数据"""
        try:
            # 1. 模拟产品列表数据
            mock_products = [
                {"product_id": "1001", "product_name": "智能手机支架", "product_title": "多功能调节手机支架", "product_category": "手机配件"},
                {"product_id": "1002", "product_name": "蓝牙耳机", "product_title": "无线蓝牙立体声耳机", "product_category": "音频设备"},
                {"product_id": "1003", "product_name": "充电宝", "product_title": "20000mAh大容量移动电源", "product_category": "充电设备"},
                {"product_id": "1004", "product_name": "手机壳", "product_title": "防摔透明手机保护壳", "product_category": "手机配件"},
                {"product_id": "1005", "product_name": "数据线", "product_title": "快充USB-C数据线", "product_category": "充电设备"},
                {"product_id": "1006", "product_name": "无线充电器", "product_title": "10W快速无线充电板", "product_category": "充电设备"},
                {"product_id": "1007", "product_name": "移动硬盘", "product_title": "1TB便携式移动硬盘", "product_category": "存储设备"},
                {"product_id": "1008", "product_name": "USB集线器", "product_title": "4口USB3.0集线器", "product_category": "电脑配件"},
                {"product_id": "1009", "product_name": "键盘", "product_title": "机械键盘RGB背光", "product_category": "电脑配件"},
                {"product_id": "1010", "product_name": "鼠标", "product_title": "无线游戏鼠标", "product_category": "电脑配件"}
            ]
            
            # 如果指定了产品ID，则只同步指定产品
            if product_ids:
                mock_products = [p for p in mock_products if p["product_id"] in product_ids]
            
            # 保存产品列表
            saved_products = self.save_products(db, user_id, tenant_id, mock_products)
            
            # 2. 为每个产品生成表现数据
            saved_performance = []
            current_date = start_date
            
            while current_date <= end_date:
                for product in saved_products:
                    # 生成模拟表现数据
                    base_impression = random.randint(1000, 10000)
                    click = random.randint(50, base_impression // 10)
                    visitor = random.randint(30, click)
                    order_count = random.randint(0, visitor // 10)
                    
                    mock_performance = {
                        "impression": base_impression,
                        "click": click,
                        "visitor": visitor,
                        "order_count": order_count,
                        "bookmark": random.randint(0, click // 20),
                        "compare": random.randint(0, click // 30),
                        "share": random.randint(0, click // 40),
                        "feedback": random.randint(0, click // 25),
                        "ctr": round((click / base_impression) * 100, 4) if base_impression > 0 else 0,
                        "conversion_rate": round((order_count / click) * 100, 4) if click > 0 else 0
                    }
                    
                    performance = self.save_product_performance(
                        db, user_id, tenant_id, mock_performance,
                        product.product_id, product.product_name,
                        statistics_type, current_date
                    )
                    saved_performance.append(performance)
                
                # 根据统计类型递增日期
                if statistics_type == "day":
                    current_date += timedelta(days=1)
                elif statistics_type == "week":
                    current_date += timedelta(days=7)
                elif statistics_type == "month":
                    # 简化处理，每月递增30天
                    current_date += timedelta(days=30)
            
            return {
                "products": len(saved_products),
                "performance_records": len(saved_performance)
            }
            
        except Exception as e:
            logger.error(f"同步产品表现数据失败: {e}")
            raise 