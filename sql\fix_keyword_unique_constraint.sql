-- 修复关键词库唯一约束，支持关键词名+国家的组合唯一性
-- 这样可以支持同一个关键词在不同国家的数据

-- 1. 删除原有的单一关键词名唯一约束
ALTER TABLE keyword_library DROP INDEX uk_keyword_name;

-- 2. 添加新的组合唯一约束：关键词名 + 国家
-- 使用IFNULL确保location_ids为NULL时使用'global'作为默认值
ALTER TABLE keyword_library 
ADD CONSTRAINT uk_keyword_name_location 
UNIQUE (keyword_name, location_ids);

-- 3. 为了处理NULL值的情况，我们需要创建一个更复杂的约束
-- 先删除刚创建的约束
ALTER TABLE keyword_library DROP INDEX uk_keyword_name_location;

-- 4. 添加一个计算列来处理NULL值
ALTER TABLE keyword_library 
ADD COLUMN location_key VARCHAR(255) 
GENERATED ALWAYS AS (IFNULL(location_ids, 'global')) STORED;

-- 5. 在计算列上创建组合唯一约束
ALTER TABLE keyword_library 
ADD CONSTRAINT uk_keyword_name_location_key 
UNIQUE (keyword_name, location_key);

-- 6. 为新字段添加索引以提高查询性能
CREATE INDEX idx_location_key ON keyword_library(location_key);
