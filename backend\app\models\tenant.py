from sqlalchemy import Boolean, Column, String, Text, DateTime, func
from sqlalchemy.orm import relationship
from app.db.session import Base
from app.utils.datetime_utils import utc_now

class Tenant(Base):
    """租户模型"""
    __tablename__ = "tenant"
    
    id = Column(String(50), primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), default=utc_now)
    updated_at = Column(DateTime(timezone=True), onupdate=utc_now) 