-- 关键词库表
CREATE TABLE `keyword_library` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `keyword_name` varchar(255) NOT NULL COMMENT '关键词名称',
  `avg_monthly_searches` bigint(20) DEFAULT NULL COMMENT '平均每月搜索量（过去12个月）',
  `monthly_searches` varchar(500) DEFAULT NULL COMMENT '大致每月搜索量（JSON格式存储每月数据）',
  `competition_level` enum('LOW','MEDIUM','HIGH','UNSPECIFIED') DEFAULT 'UNSPECIFIED' COMMENT '竞争级别',
  `competition_index` decimal(5,2) DEFAULT NULL COMMENT '竞争指数（0-100）',
  `low_bid_micros` bigint(20) DEFAULT NULL COMMENT '出价第20百分位（微货币单位）',
  `high_bid_micros` bigint(20) DEFAULT NULL COMMENT '出价第80百分位（微货币单位）',
  `currency_code` varchar(3) DEFAULT 'CNY' COMMENT '货币代码',
  `language_code` varchar(10) DEFAULT 'zh-CN' COMMENT '语言代码',
  `location_ids` varchar(255) DEFAULT NULL COMMENT '地理位置ID列表（逗号分隔）',
  `update_method` enum('google_ads_api','manual','batch_import') NOT NULL DEFAULT 'manual' COMMENT '更新方式',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签（逗号分隔）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_keyword_name` (`keyword_name`),
  KEY `idx_keyword_name` (`keyword_name`),
  KEY `idx_competition_level` (`competition_level`),
  KEY `idx_avg_monthly_searches` (`avg_monthly_searches`),
  KEY `idx_update_method` (`update_method`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`),
  FULLTEXT KEY `idx_keyword_tags_fulltext` (`keyword_name`, `tags`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关键词库表';

-- Google Ads API 配置表
CREATE TABLE `google_ads_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `customer_id` varchar(20) NOT NULL COMMENT 'Google Ads 客户ID',
  `developer_token` varchar(255) NOT NULL COMMENT '开发者令牌',
  `client_id` varchar(255) NOT NULL COMMENT '客户端ID',
  `client_secret` varchar(255) NOT NULL COMMENT '客户端密钥',
  `refresh_token` varchar(255) DEFAULT NULL COMMENT '刷新令牌',
  `login_customer_id` varchar(20) DEFAULT NULL COMMENT '登录客户ID',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_name` (`config_name`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Google Ads API配置表';

-- 关键词更新历史表
CREATE TABLE `keyword_update_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `keyword_id` int(11) NOT NULL COMMENT '关键词ID',
  `update_method` enum('google_ads_api','manual','batch_import') NOT NULL COMMENT '更新方式',
  `old_data` json DEFAULT NULL COMMENT '更新前的数据（JSON格式）',
  `new_data` json DEFAULT NULL COMMENT '更新后的数据（JSON格式）',
  `batch_id` varchar(100) DEFAULT NULL COMMENT '批量操作ID',
  `operator_id` int(11) DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作人姓名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_keyword_id` (`keyword_id`),
  KEY `idx_update_method` (`update_method`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`keyword_id`) REFERENCES `keyword_library` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关键词更新历史表';

-- 批量导入任务表
CREATE TABLE `keyword_import_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` varchar(100) NOT NULL COMMENT '任务ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `total_count` int(11) DEFAULT 0 COMMENT '总记录数',
  `success_count` int(11) DEFAULT 0 COMMENT '成功数',
  `failed_count` int(11) DEFAULT 0 COMMENT '失败数',
  `status` enum('pending','processing','completed','failed') DEFAULT 'pending' COMMENT '任务状态',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `result_file_path` varchar(500) DEFAULT NULL COMMENT '结果文件路径',
  `operator_id` int(11) DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) DEFAULT NULL COMMENT '操作人姓名',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关键词批量导入任务表';

-- 创建视图：关键词统计
CREATE VIEW `v_keyword_stats` AS
SELECT 
  update_method,
  COUNT(*) as total_count,
  AVG(avg_monthly_searches) as avg_searches,
  MIN(avg_monthly_searches) as min_searches,
  MAX(avg_monthly_searches) as max_searches,
  COUNT(CASE WHEN competition_level = 'LOW' THEN 1 END) as low_competition_count,
  COUNT(CASE WHEN competition_level = 'MEDIUM' THEN 1 END) as medium_competition_count,
  COUNT(CASE WHEN competition_level = 'HIGH' THEN 1 END) as high_competition_count
FROM keyword_library
GROUP BY update_method;

-- 创建视图：热门关键词
CREATE VIEW `v_popular_keywords` AS
SELECT 
  id,
  keyword_name,
  avg_monthly_searches,
  competition_level,
  competition_index,
  tags,
  updated_at
FROM keyword_library
WHERE avg_monthly_searches IS NOT NULL
ORDER BY avg_monthly_searches DESC
LIMIT 100; 