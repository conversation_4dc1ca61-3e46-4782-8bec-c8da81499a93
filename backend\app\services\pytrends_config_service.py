import logging
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException

from ..models.keyword_library import PyTrendsConfig
from ..schemas.keyword_library import (
    PyTrendsConfigCreate, PyTrendsConfigUpdate, PyTrendsConfigResponse
)

# 可选导入 TrendsPy 服务（替代PyTrends）
try:
    from .trendspy_service import TrendsPyService
    TRENDSPY_AVAILABLE = True
except ImportError:
    TrendsPyService = None
    TRENDSPY_AVAILABLE = False

# 保留PyTrends作为备用
try:
    from .pytrends_service import PyTrendsService, PYTRENDS_AVAILABLE
except ImportError:
    PyTrendsService = None
    PYTRENDS_AVAILABLE = False

logger = logging.getLogger(__name__)

class PyTrendsConfigService:
    def __init__(self, db: Session):
        self.db = db
    
    def create_config(self, config_data: PyTrendsConfigCreate) -> PyTrendsConfig:
        """创建PyTrends配置"""
        try:
            # 检查配置名称是否已存在
            existing_config = self.db.query(PyTrendsConfig).filter(
                PyTrendsConfig.config_name == config_data.config_name
            ).first()
            
            if existing_config:
                raise HTTPException(
                    status_code=400,
                    detail=f"配置名称 '{config_data.config_name}' 已存在"
                )
            
            # 创建新配置
            db_config = PyTrendsConfig(**config_data.dict())
            self.db.add(db_config)
            self.db.commit()
            self.db.refresh(db_config)
            
            logger.info(f"创建PyTrends配置成功: {config_data.config_name}")
            return db_config
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"创建PyTrends配置失败: {e}")
            raise HTTPException(status_code=500, detail=f"创建配置失败: {str(e)}")
    
    def get_config(self, config_id: int) -> PyTrendsConfig:
        """获取PyTrends配置"""
        config = self.db.query(PyTrendsConfig).filter(
            PyTrendsConfig.id == config_id
        ).first()
        
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        return config
    
    def get_configs(self, active_only: bool = True) -> List[PyTrendsConfig]:
        """获取PyTrends配置列表"""
        query = self.db.query(PyTrendsConfig)
        
        if active_only:
            query = query.filter(PyTrendsConfig.is_active == True)
        
        return query.order_by(PyTrendsConfig.created_at.desc()).all()
    
    def update_config(
        self, 
        config_id: int, 
        config_data: PyTrendsConfigUpdate
    ) -> PyTrendsConfig:
        """更新PyTrends配置"""
        try:
            config = self.get_config(config_id)
            
            # 检查配置名称冲突（如果更新了名称）
            if config_data.config_name and config_data.config_name != config.config_name:
                existing_config = self.db.query(PyTrendsConfig).filter(
                    PyTrendsConfig.config_name == config_data.config_name,
                    PyTrendsConfig.id != config_id
                ).first()
                
                if existing_config:
                    raise HTTPException(
                        status_code=400,
                        detail=f"配置名称 '{config_data.config_name}' 已存在"
                    )
            
            # 更新字段
            update_data = config_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(config, field, value)
            
            self.db.commit()
            self.db.refresh(config)
            
            logger.info(f"更新PyTrends配置成功: {config.config_name}")
            return config
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新PyTrends配置失败: {e}")
            raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")
    
    def delete_config(self, config_id: int) -> bool:
        """删除PyTrends配置"""
        try:
            config = self.get_config(config_id)
            
            self.db.delete(config)
            self.db.commit()
            
            logger.info(f"删除PyTrends配置成功: {config.config_name}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"删除PyTrends配置失败: {e}")
            raise HTTPException(status_code=500, detail=f"删除配置失败: {str(e)}")
    
    def test_connection(self, config_id: int) -> Dict[str, Any]:
        """测试TrendsPy/PyTrends连接"""
        config = self.get_config(config_id)
        
        # 构建配置字典
        service_config = {
            "language": config.language,
            "timezone": config.timezone,
            "geo_location": config.geo_location,
            "default_timeframe": config.default_timeframe,
            "max_keywords_per_batch": config.max_keywords_per_batch,
            "request_delay": config.request_delay,
            "retry_attempts": config.retry_attempts
        }
        
        # 代理配置
        proxy_config = None
        if config.use_proxy and config.proxy_host:
            proxy_config = {
                'use_proxy': config.use_proxy,
                'proxy_host': config.proxy_host,
                'proxy_port': config.proxy_port,
                'proxy_username': config.proxy_username,
                'proxy_password': config.proxy_password,
                'proxy_type': config.proxy_type
            }
        
        # 优先使用TrendsPy，失败时使用PyTrends作为备用
        if TRENDSPY_AVAILABLE:
            try:
                logger.info(f"使用TrendsPy测试连接: {config.config_name}")
                trendspy_service = TrendsPyService(service_config, proxy_config)
                result = trendspy_service.test_connection()
                
                if result['available']:
                    result.update({
                        'service_used': 'trendspy',
                        'message': f'TrendsPy连接成功: {config.config_name}',
                        'success': True
                    })
                    logger.info(f"TrendsPy连接测试成功: {config.config_name}")
                    return result
                else:
                    logger.warning(f"TrendsPy连接测试失败，尝试PyTrends备用: {config.config_name}")
            except Exception as e:
                logger.warning(f"TrendsPy连接测试异常，尝试PyTrends备用: {e}")
        
        # 使用PyTrends作为备用
        if PYTRENDS_AVAILABLE:
            try:
                logger.info(f"使用PyTrends备用服务测试连接: {config.config_name}")
                pytrends_service = PyTrendsService(service_config, proxy_config)
                result = pytrends_service.test_connection()
                
                result.update({
                    'service_used': 'pytrends',
                    'message': f'PyTrends连接成功: {config.config_name}' if result.get('success') else f'PyTrends连接失败: {config.config_name}',
                    'success': result.get('success', False)
                })
                
                logger.info(f"PyTrends连接测试完成: {config.config_name}, 结果: {result['success']}")
                return result
                
            except Exception as e:
                logger.error(f"PyTrends连接测试失败: {e}")
                return {
                    'success': False,
                    'available': False,
                    'service_used': 'pytrends',
                    'message': f'PyTrends连接测试失败: {str(e)}',
                    'error': str(e)
                }
        
        # 两个服务都不可用
        error_msg = "TrendsPy和PyTrends都不可用。请安装: pip install trendspy 或 pip install pytrends"
        logger.error(error_msg)
        raise HTTPException(status_code=503, detail=error_msg)
    
    def get_default_config(self) -> PyTrendsConfig:
        """获取默认配置（第一个激活的配置）"""
        config = self.db.query(PyTrendsConfig).filter(
            PyTrendsConfig.is_active == True
        ).order_by(PyTrendsConfig.created_at.asc()).first()
        
        if not config:
            raise HTTPException(status_code=404, detail="没有可用的PyTrends配置")
        
        return config
    
    def toggle_active_status(self, config_id: int) -> PyTrendsConfig:
        """切换配置的激活状态"""
        try:
            config = self.get_config(config_id)
            config.is_active = not config.is_active
            
            self.db.commit()
            self.db.refresh(config)
            
            status = "激活" if config.is_active else "禁用"
            logger.info(f"{status}PyTrends配置: {config.config_name}")
            return config
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"切换PyTrends配置状态失败: {e}")
            raise HTTPException(status_code=500, detail=f"切换状态失败: {str(e)}")
    
    def get_config_stats(self, config_id: int) -> Dict[str, Any]:
        """获取配置的使用统计"""
        try:
            config = self.get_config(config_id)
            
            # 导入这些模型用于统计
            from ..models.keyword_library import PyTrendsTaskHistory, KeywordLibrary
            
            # 统计任务数量
            total_tasks = self.db.query(PyTrendsTaskHistory).filter(
                PyTrendsTaskHistory.config_id == config_id
            ).count()
            
            completed_tasks = self.db.query(PyTrendsTaskHistory).filter(
                PyTrendsTaskHistory.config_id == config_id,
                PyTrendsTaskHistory.status == "completed"
            ).count()
            
            failed_tasks = self.db.query(PyTrendsTaskHistory).filter(
                PyTrendsTaskHistory.config_id == config_id,
                PyTrendsTaskHistory.status == "failed"
            ).count()
            
            # 统计导入的关键词数量
            imported_keywords = self.db.query(KeywordLibrary).filter(
                KeywordLibrary.update_method == "pytrends"
            ).count()
            
            # 最近任务
            recent_task = self.db.query(PyTrendsTaskHistory).filter(
                PyTrendsTaskHistory.config_id == config_id
            ).order_by(PyTrendsTaskHistory.created_at.desc()).first()
            
            return {
                'config_name': config.config_name,
                'total_tasks': total_tasks,
                'completed_tasks': completed_tasks,
                'failed_tasks': failed_tasks,
                'success_rate': round((completed_tasks / total_tasks * 100) if total_tasks > 0 else 0, 2),
                'imported_keywords': imported_keywords,
                'last_used': recent_task.created_at if recent_task else None,
                'is_active': config.is_active
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取配置统计失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}") 