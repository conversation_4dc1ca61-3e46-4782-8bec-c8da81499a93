"""
增强的趋势服务
集成TrendsPy作为PyTrends相关查询功能的替代方案
解决PyTrends的'list index out of range'错误
"""
import json
import time
import logging
import os
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import pandas as pd
import requests

# 导入现有的PyTrends服务
from .pytrends_service import PyTrendsService

# 导入TrendsPy
try:
    from trendspy import Trends
    TRENDSPY_AVAILABLE = True
except ImportError:
    Trends = None
    TRENDSPY_AVAILABLE = False

# 初始化日志记录器
logger = logging.getLogger(__name__)

class TrendsPyHandler:
    """TrendsPy处理器，专门处理相关查询功能"""
    
    def __init__(self, proxy_config: Dict[str, Any] = None):
        """
        初始化TrendsPy处理器
        
        Args:
            proxy_config: 代理配置字典
        """
        if not TRENDSPY_AVAILABLE:
            raise ImportError("trendspy库未安装，请运行: pip install trendspy")
        
        self.proxy_config = proxy_config or {}
        
        # 设置代理环境变量（如果配置了代理）
        self._setup_proxy_env()
        
        # 初始化TrendsPy客户端
        self._init_client()
        
        # referer策略（用于绕过quota限制）
        self.referer_strategies = [
            {'referer': 'https://www.google.com/'},
            {'referer': 'https://trends.google.com/'},
            {'referer': 'https://www.google.com.hk/'},
            {'referer': 'https://www.google.co.uk/'},
            {'referer': 'https://www.google.de/'},
        ]
        
        # 重试配置
        self.max_retries = 3
        self.base_delay = 2
    
    def _setup_proxy_env(self):
        """设置代理环境变量"""
        if self.proxy_config.get('use_proxy') and self.proxy_config.get('proxy_host'):
            proxy_url = self._build_proxy_url()
            os.environ['HTTP_PROXY'] = proxy_url
            os.environ['HTTPS_PROXY'] = proxy_url
            logger.info(f"设置TrendsPy代理环境变量: {proxy_url}")
    
    def _build_proxy_url(self) -> str:
        """构建代理URL"""
        proxy_type = self.proxy_config.get('proxy_type', 'http')
        host = self.proxy_config.get('proxy_host')
        port = self.proxy_config.get('proxy_port')
        username = self.proxy_config.get('proxy_username')
        password = self.proxy_config.get('proxy_password')
        
        if username and password:
            return f"{proxy_type}://{username}:{password}@{host}:{port}"
        else:
            return f"{proxy_type}://{host}:{port}"
    
    def _init_client(self):
        """初始化TrendsPy客户端"""
        try:
            # 如果配置了代理，使用代理初始化
            if self.proxy_config.get('use_proxy') and self.proxy_config.get('proxy_host'):
                proxy_dict = {
                    'http': self._build_proxy_url(),
                    'https': self._build_proxy_url()
                }
                self.tr = Trends(proxy=proxy_dict)
                logger.info(f"TrendsPy客户端初始化成功（使用代理）")
            else:
                self.tr = Trends()
                logger.info(f"TrendsPy客户端初始化成功（不使用代理）")
                
        except Exception as e:
            logger.error(f"TrendsPy客户端初始化失败: {e}")
            raise
    
    def _is_valid_data(self, data) -> bool:
        """检查数据是否有效"""
        if data is None:
            return False
        
        if isinstance(data, dict):
            # 检查字典中是否有有效的DataFrame
            for key, value in data.items():
                if isinstance(value, pd.DataFrame) and not value.empty:
                    return True
            return False
        elif isinstance(data, pd.DataFrame):
            return not data.empty
        elif isinstance(data, list):
            return len(data) > 0
        else:
            return bool(data)
    
    def _convert_trendspy_data(self, data: Dict[str, pd.DataFrame], keyword: str) -> Dict[str, Any]:
        """
        将TrendsPy数据格式转换为与PyTrends兼容的格式
        
        Args:
            data: TrendsPy返回的数据字典
            keyword: 关键词
            
        Returns:
            转换后的数据字典
        """
        result = {}
        
        if not data or not isinstance(data, dict):
            return result
        
        # 处理top和rising数据
        for category in ['top', 'rising']:
            if category in data and isinstance(data[category], pd.DataFrame) and not data[category].empty:
                df = data[category]
                
                # 转换为PyTrends兼容的格式
                records = []
                for _, row in df.iterrows():
                    record = {}
                    for col in df.columns:
                        record[col] = row[col]
                    records.append(record)
                
                result[category] = records
                logger.debug(f"转换 {keyword} 的 {category} 数据: {len(records)} 条记录")
        
        return result
    
    def get_related_queries_safe(self, keyword: str) -> Optional[Dict[str, Any]]:
        """
        安全获取相关查询，带完善的错误处理和重试机制
        
        Args:
            keyword: 关键词
            
        Returns:
            相关查询数据字典，失败时返回None
        """
        logger.info(f"TrendsPy获取关键词 '{keyword}' 的相关查询")
        
        for attempt in range(self.max_retries):
            logger.debug(f"尝试 {attempt + 1}/{self.max_retries}")
            
            # 尝试不同的referer策略
            for i, headers in enumerate(self.referer_strategies):
                try:
                    logger.debug(f"使用referer策略 {i+1}: {headers['referer']}")
                    
                    # 获取相关查询
                    related = self.tr.related_queries(keyword, headers=headers)
                    
                    # 验证数据有效性
                    if self._is_valid_data(related):
                        logger.info(f"成功获取关键词 '{keyword}' 的相关查询")
                        
                        # 转换数据格式
                        converted_data = self._convert_trendspy_data(related, keyword)
                        
                        # 记录数据统计
                        total_count = 0
                        for category, records in converted_data.items():
                            total_count += len(records)
                            logger.debug(f"{keyword} {category} 查询: {len(records)} 条")
                        
                        logger.info(f"关键词 '{keyword}' 共获取 {total_count} 条相关查询")
                        return converted_data
                    else:
                        logger.debug(f"referer策略 {i+1} 返回空数据，尝试下一个...")
                        continue
                        
                except Exception as e:
                    error_msg = str(e).lower()
                    if "quota exceeded" in error_msg:
                        logger.debug(f"referer策略 {i+1} 遇到quota限制，尝试下一个...")
                        time.sleep(1)  # 短暂延迟
                        continue
                    else:
                        logger.warning(f"referer策略 {i+1} 出错: {e}")
                        continue
            
            # 如果所有referer策略都失败，等待后重试
            if attempt < self.max_retries - 1:
                wait_time = self.base_delay * (2 ** attempt)  # 指数退避
                logger.info(f"所有referer策略失败，等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
        
        logger.warning(f"经过 {self.max_retries} 次尝试仍无法获取关键词 '{keyword}' 的相关查询")
        return None
    
    def get_related_topics_safe(self, keyword: str) -> Optional[Dict[str, Any]]:
        """
        安全获取相关主题，带完善的错误处理和重试机制
        
        Args:
            keyword: 关键词
            
        Returns:
            相关主题数据字典，失败时返回None
        """
        logger.info(f"TrendsPy获取关键词 '{keyword}' 的相关主题")
        
        for attempt in range(self.max_retries):
            logger.debug(f"尝试 {attempt + 1}/{self.max_retries}")
            
            # 尝试不同的referer策略
            for i, headers in enumerate(self.referer_strategies):
                try:
                    logger.debug(f"使用referer策略 {i+1}: {headers['referer']}")
                    
                    # 获取相关主题
                    related = self.tr.related_topics(keyword, headers=headers)
                    
                    # 验证数据有效性
                    if self._is_valid_data(related):
                        logger.info(f"成功获取关键词 '{keyword}' 的相关主题")
                        
                        # 转换数据格式
                        converted_data = self._convert_trendspy_data(related, keyword)
                        
                        # 记录数据统计
                        total_count = 0
                        for category, records in converted_data.items():
                            total_count += len(records)
                            logger.debug(f"{keyword} {category} 主题: {len(records)} 条")
                        
                        logger.info(f"关键词 '{keyword}' 共获取 {total_count} 条相关主题")
                        return converted_data
                    else:
                        logger.debug(f"referer策略 {i+1} 返回空数据，尝试下一个...")
                        continue
                        
                except Exception as e:
                    error_msg = str(e).lower()
                    if "quota exceeded" in error_msg:
                        logger.debug(f"referer策略 {i+1} 遇到quota限制，尝试下一个...")
                        time.sleep(1)  # 短暂延迟
                        continue
                    else:
                        logger.warning(f"referer策略 {i+1} 出错: {e}")
                        continue
            
            # 如果所有referer策略都失败，等待后重试
            if attempt < self.max_retries - 1:
                wait_time = self.base_delay * (2 ** attempt)  # 指数退避
                logger.info(f"所有referer策略失败，等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
        
        logger.warning(f"经过 {self.max_retries} 次尝试仍无法获取关键词 '{keyword}' 的相关主题")
        return None

class EnhancedTrendsService:
    """
    增强的趋势服务
    集成PyTrends和TrendsPy，提供最佳的数据获取体验
    """
    
    def __init__(self, config: Dict[str, Any] = None, proxy_config: Dict[str, Any] = None):
        """
        初始化增强趋势服务
        
        Args:
            config: 服务配置字典
            proxy_config: 代理配置字典
        """
        self.config = config or {}
        self.proxy_config = proxy_config or {}
        
        # 初始化PyTrends服务（主要用于核心功能）
        try:
            self.pytrends_service = PyTrendsService(config=config, proxy_config=proxy_config)
            self.pytrends_available = True
            logger.info("PyTrends服务初始化成功")
        except Exception as e:
            logger.warning(f"PyTrends服务初始化失败: {e}")
            self.pytrends_service = None
            self.pytrends_available = False
        
        # 初始化TrendsPy处理器（专门用于相关查询）
        try:
            self.trendspy_handler = TrendsPyHandler(proxy_config=proxy_config)
            self.trendspy_available = True
            logger.info("TrendsPy处理器初始化成功")
        except Exception as e:
            logger.warning(f"TrendsPy处理器初始化失败: {e}")
            self.trendspy_handler = None
            self.trendspy_available = False
        
        # 策略配置
        self.use_trendspy_for_related = self.config.get('use_trendspy_for_related', True)
        self.fallback_enabled = self.config.get('fallback_enabled', True)
        
        logger.info(f"增强趋势服务初始化完成")
        logger.info(f"PyTrends可用: {self.pytrends_available}")
        logger.info(f"TrendsPy可用: {self.trendspy_available}")
        logger.info(f"相关查询优先使用TrendsPy: {self.use_trendspy_for_related}")
        logger.info(f"启用降级机制: {self.fallback_enabled}")
    
    def get_interest_over_time(
        self,
        keywords: List[str],
        timeframe: str = None,
        geo: str = None,
        category: int = 0,
        gprop: str = ''
    ) -> Dict[str, Any]:
        """
        获取关键词的时间趋势数据
        优先使用PyTrends，失败时降级到TrendsPy
        """
        # 优先使用PyTrends
        if self.pytrends_available:
            try:
                result = self.pytrends_service.get_interest_over_time(
                    keywords=keywords,
                    timeframe=timeframe,
                    geo=geo,
                    category=category,
                    gprop=gprop
                )
                logger.info(f"PyTrends成功获取兴趣度数据: {keywords}")
                return result
            except Exception as e:
                logger.warning(f"PyTrends获取兴趣度失败: {e}")
                if not self.fallback_enabled or not self.trendspy_available:
                    raise
        
        # 降级到TrendsPy
        if self.trendspy_available:
            try:
                logger.info(f"使用TrendsPy获取兴趣度数据: {keywords}")
                # 注意：这里需要根据TrendsPy的API调整参数
                result = {
                    'keywords': keywords,
                    'interest_over_time': {},
                    'source': 'trendspy'
                }
                
                for keyword in keywords:
                    try:
                        # 使用TrendsPy获取兴趣度数据
                        interest_data = self.trendspy_handler.tr.interest_over_time(keyword)
                        if interest_data is not None and not interest_data.empty:
                            result['interest_over_time'][keyword] = interest_data.to_dict('records')
                    except Exception as e:
                        logger.warning(f"TrendsPy获取关键词 {keyword} 兴趣度失败: {e}")
                        continue
                
                logger.info(f"TrendsPy成功获取兴趣度数据: {keywords}")
                return result
            except Exception as e:
                logger.error(f"TrendsPy获取兴趣度也失败: {e}")
                raise
        
        raise RuntimeError("所有趋势服务都不可用")
    
    def get_related_topics_and_queries(
        self,
        keywords: List[str],
        timeframe: str = None,
        geo: str = None,
        category: int = 0
    ) -> Dict[str, Any]:
        """
        获取相关主题和查询
        优先使用TrendsPy（解决list index out of range问题），失败时降级到PyTrends
        """
        # 优先使用TrendsPy（专门解决相关查询问题）
        if self.trendspy_available and self.use_trendspy_for_related:
            try:
                logger.info(f"使用TrendsPy获取相关主题和查询: {keywords}")
                
                result = {
                    'keywords': keywords,
                    'related_topics': {},
                    'related_queries': {},
                    'rising_topics': {},
                    'rising_queries': {},
                    'source': 'trendspy'
                }
                
                success_count = 0
                
                for keyword in keywords:
                    try:
                        # 获取相关查询
                        queries_data = self.trendspy_handler.get_related_queries_safe(keyword)
                        if queries_data:
                            if 'top' in queries_data:
                                result['related_queries'][keyword] = queries_data['top']
                            if 'rising' in queries_data:
                                result['rising_queries'][keyword] = queries_data['rising']
                        
                        # 添加延迟避免频率限制
                        time.sleep(2)
                        
                        # 获取相关主题
                        topics_data = self.trendspy_handler.get_related_topics_safe(keyword)
                        if topics_data:
                            if 'top' in topics_data:
                                result['related_topics'][keyword] = topics_data['top']
                            if 'rising' in topics_data:
                                result['rising_topics'][keyword] = topics_data['rising']
                        
                        # 如果至少获取到一种数据，算作成功
                        if queries_data or topics_data:
                            success_count += 1
                        
                        # 关键词间延迟
                        if keyword != keywords[-1]:  # 不是最后一个关键词
                            time.sleep(3)
                    
                    except Exception as e:
                        logger.warning(f"TrendsPy获取关键词 {keyword} 的相关数据失败: {e}")
                        continue
                
                if success_count > 0:
                    logger.info(f"TrendsPy成功获取 {success_count}/{len(keywords)} 个关键词的相关数据")
                    return result
                else:
                    logger.warning("TrendsPy未能获取任何关键词的相关数据")
                    if not self.fallback_enabled or not self.pytrends_available:
                        return result  # 返回空结果而不是抛出异常
                
            except Exception as e:
                logger.warning(f"TrendsPy获取相关数据失败: {e}")
                if not self.fallback_enabled or not self.pytrends_available:
                    raise
        
        # 降级到PyTrends
        if self.pytrends_available:
            try:
                logger.info(f"降级到PyTrends获取相关主题和查询: {keywords}")
                result = self.pytrends_service.get_related_topics_and_queries(
                    keywords=keywords,
                    timeframe=timeframe,
                    geo=geo,
                    category=category
                )
                result['source'] = 'pytrends'
                logger.info(f"PyTrends成功获取相关数据: {keywords}")
                return result
            except Exception as e:
                logger.error(f"PyTrends获取相关数据也失败: {e}")
                # 返回空结果而不是抛出异常
                return {
                    'keywords': keywords,
                    'related_topics': {},
                    'related_queries': {},
                    'rising_topics': {},
                    'rising_queries': {},
                    'source': 'none',
                    'error': str(e)
                }
        
        raise RuntimeError("所有趋势服务都不可用")
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试服务连接状态
        
        Returns:
            连接状态字典
        """
        result = {
            'timestamp': datetime.now().isoformat(),
            'pytrends': {'available': False, 'status': 'unknown'},
            'trendspy': {'available': False, 'status': 'unknown'},
            'overall_status': 'unknown'
        }
        
        # 测试PyTrends
        if self.pytrends_available:
            try:
                pytrends_result = self.pytrends_service.test_connection()
                result['pytrends'] = pytrends_result
            except Exception as e:
                result['pytrends'] = {
                    'available': False,
                    'status': 'error',
                    'error': str(e)
                }
        
        # 测试TrendsPy
        if self.trendspy_available:
            try:
                # 简单测试TrendsPy连接
                test_data = self.trendspy_handler.tr.interest_over_time('test')
                result['trendspy'] = {
                    'available': True,
                    'status': 'connected',
                    'message': 'TrendsPy连接正常'
                }
            except Exception as e:
                result['trendspy'] = {
                    'available': False,
                    'status': 'error',
                    'error': str(e)
                }
        
        # 确定整体状态
        pytrends_ok = result['pytrends'].get('available', False)
        trendspy_ok = result['trendspy'].get('available', False)
        
        if pytrends_ok and trendspy_ok:
            result['overall_status'] = 'excellent'
        elif pytrends_ok or trendspy_ok:
            result['overall_status'] = 'good'
        else:
            result['overall_status'] = 'poor'
        
        return result 