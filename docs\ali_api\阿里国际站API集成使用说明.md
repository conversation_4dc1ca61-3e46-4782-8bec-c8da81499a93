# 阿里国际站API集成使用说明

## 概述

本系统已成功集成阿里国际站API卖家授权功能，支持OAuth2.0标准授权流程。用户可以通过授权获取访问令牌，用于调用阿里国际站的各种API接口。

## 配置信息

- **App Key**: 502750
- **App Secret**: a7cd4a24d3081fd89ae6233f127ba461
- **回调地址**: http://localhost:8000/api/v1/alibaba/callback

## 功能特性

### 1. 授权管理
- 生成授权URL
- 处理授权回调
- 保存访问令牌和刷新令牌
- 查询授权状态
- 撤销授权

### 2. Token管理
- 自动刷新过期的访问令牌
- Token状态监控
- 手动刷新Token
- Token有效期管理

### 3. 安全特性
- 状态参数防CSRF攻击
- Token加密存储
- 多租户支持
- 访问令牌自动续期

## API接口说明

### 1. 获取授权URL
```http
GET /api/v1/alibaba/auth-url
Authorization: Bearer <your_token>
```

**响应示例:**
```json
{
  "auth_url": "https://open-api.alibaba.com/oauth/authorize?response_type=code&force_auth=true&redirect_uri=http://localhost:8000/api/v1/alibaba/callback&client_id=502750&state=1_abc123",
  "state": "1_abc123"
}
```

### 2. 授权回调处理
```http
GET /api/v1/alibaba/callback?code=<auth_code>&state=<state>
```

### 3. API方式处理回调
```http
POST /api/v1/alibaba/callback
Authorization: Bearer <your_token>
Content-Type: application/json

{
  "code": "授权码",
  "state": "状态参数"
}
```

### 4. 查询授权信息
```http
GET /api/v1/alibaba/auth-info
Authorization: Bearer <your_token>
```

**响应示例:**
```json
{
  "id": 1,
  "user_id": 1,
  "tenant_id": "default",
  "alibaba_account": "<EMAIL>",
  "country": "CN",
  "account_platform": "seller_center",
  "havana_id": "*********",
  "is_active": true,
  "token_created_at": "2024-01-01T10:00:00Z",
  "last_refresh_at": null,
  "expires_in": ********,
  "refresh_expires_in": ********
}
```

### 5. 查询Token状态
```http
GET /api/v1/alibaba/token-status
Authorization: Bearer <your_token>
```

**响应示例:**
```json
{
  "is_valid": true,
  "account": "<EMAIL>",
  "expires_at": "2024-12-31T10:00:00Z",
  "refresh_expires_at": "2024-12-31T10:00:00Z"
}
```

### 6. 刷新访问令牌
```http
POST /api/v1/alibaba/refresh-token
Authorization: Bearer <your_token>
```

### 7. 撤销授权
```http
DELETE /api/v1/alibaba/revoke
Authorization: Bearer <your_token>
```

## 使用流程

### 第一步：获取授权URL
用户登录系统后，调用获取授权URL接口，系统会生成包含用户ID和随机状态的授权链接。

### 第二步：用户授权
用户在新窗口中打开授权URL，会跳转到阿里国际站授权页面。用户需要：
1. 输入阿里巴巴账号和密码
2. 点击"授权"按钮

### 第三步：处理回调
授权成功后，阿里国际站会重定向到我们的回调地址，携带授权码(code)。系统会：
1. 验证状态参数
2. 使用授权码换取访问令牌
3. 保存授权信息到数据库

### 第四步：使用API
获得授权后，系统可以：
1. 使用访问令牌调用阿里国际站API
2. 自动刷新过期的令牌
3. 监控令牌状态

## 前端集成

系统提供了Vue.js前端组件 `AlibabaAuth.vue`，包含：

### 功能按钮
- **获取授权URL**: 生成授权链接
- **查询授权状态**: 获取当前授权信息
- **刷新Token**: 手动刷新访问令牌
- **撤销授权**: 取消阿里国际站授权

### 状态显示
- 阿里巴巴账号信息
- Token有效状态
- 过期时间
- 最后刷新时间

### 授权指导
- 清晰的授权步骤说明
- 一键打开授权页面
- 复制授权链接功能

## 数据库结构

### alibaba_auth表
```sql
CREATE TABLE alibaba_auth (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT,
  tenant_id VARCHAR(50),
  access_token TEXT,
  refresh_token TEXT,
  expires_in INT,
  refresh_expires_in INT,
  token_created_at DATETIME,
  alibaba_account VARCHAR(255),
  country VARCHAR(10),
  account_platform VARCHAR(50),
  havana_id VARCHAR(100),
  is_active BOOLEAN DEFAULT TRUE,
  last_refresh_at DATETIME,
  auth_scope VARCHAR(500),
  code VARCHAR(500),
  request_id VARCHAR(100),
  trace_id VARCHAR(100),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 错误处理

### 常见错误
1. **授权码过期**: 授权码有效期很短，需要立即使用
2. **Token过期**: 系统会自动尝试刷新，如果刷新失败需要重新授权
3. **网络错误**: 检查与阿里国际站的网络连接
4. **签名错误**: 检查App Key和App Secret配置

### 错误响应示例
```json
{
  "detail": "获取访问令牌失败: 授权码已过期"
}
```

## 安全注意事项

1. **保护App Secret**: 不要在前端暴露App Secret
2. **验证回调**: 使用state参数防止CSRF攻击
3. **Token安全**: 访问令牌应加密存储
4. **HTTPS**: 生产环境必须使用HTTPS
5. **定期更新**: 建议定期更新App Secret

## 监控和日志

系统会记录以下日志：
- 授权URL生成
- 访问令牌获取
- Token刷新操作
- 授权撤销
- 错误信息

## 扩展功能

基于授权后获得的访问令牌，可以进一步集成：
1. 商品信息同步
2. 订单管理
3. 库存同步
4. 物流跟踪
5. 数据分析

## 测试方法

1. 启动后端服务: `python main.py`
2. 访问前端授权页面
3. 点击"获取授权URL"
4. 在新窗口完成阿里巴巴授权
5. 查看授权状态和Token信息

## 技术支持

如有问题，请检查：
1. 后端服务是否正常运行
2. 数据库连接是否正常
3. 阿里国际站API配置是否正确
4. 网络连接是否稳定

## 更新日志

- **v1.0**: 完成基础OAuth2.0授权流程
- 支持访问令牌获取和刷新
- 提供前端授权界面
- 实现多租户支持 