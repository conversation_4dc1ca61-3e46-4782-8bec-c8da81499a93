-- 定时发布频率配置数据库迁移脚本（生产环境版本）
-- 执行时间：2024-01-XX
-- 说明：为定时发布计划表添加结束时间和发布频率配置字段

-- 检查表是否存在
SELECT 'Starting migration for scheduled_publish_plans frequency configuration...' as status;

-- 1. 为 scheduled_publish_plans 表添加新的频率配置字段
ALTER TABLE `scheduled_publish_plans` 
ADD COLUMN IF NOT EXISTS `end_time` datetime NULL COMMENT '任务结束时间' AFTER `scheduled_time`,
ADD COLUMN IF NOT EXISTS `frequency_type` varchar(20) NOT NULL DEFAULT 'once' COMMENT '发布频率类型：once/daily/weekly/custom' AFTER `end_time`,
ADD COLUMN IF NOT EXISTS `weekly_days` json NULL COMMENT '每周执行日期（0-6，0为周日）' AFTER `frequency_type`,
ADD COLUMN IF NOT EXISTS `custom_interval_value` int NULL COMMENT '自定义间隔数值' AFTER `weekly_days`,
ADD COLUMN IF NOT EXISTS `custom_interval_unit` varchar(10) NULL COMMENT '自定义间隔单位：hours/days/weeks' AFTER `custom_interval_value`,
ADD COLUMN IF NOT EXISTS `daily_time` time NULL COMMENT '每日执行时间（HH:MM格式）' AFTER `custom_interval_unit`,
ADD COLUMN IF NOT EXISTS `max_executions` int NULL COMMENT '最大执行次数限制' AFTER `daily_time`,
ADD COLUMN IF NOT EXISTS `current_executions` int NOT NULL DEFAULT 0 COMMENT '当前已执行次数' AFTER `max_executions`,
ADD COLUMN IF NOT EXISTS `next_execution_time` datetime NULL COMMENT '下次执行时间' AFTER `current_executions`;

-- 2. 添加新字段的索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'scheduled_publish_plans' 
     AND index_name = 'idx_frequency_type') = 0,
    'ALTER TABLE `scheduled_publish_plans` ADD INDEX `idx_frequency_type` (`frequency_type`)',
    'SELECT "Index idx_frequency_type already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'scheduled_publish_plans' 
     AND index_name = 'idx_end_time') = 0,
    'ALTER TABLE `scheduled_publish_plans` ADD INDEX `idx_end_time` (`end_time`)',
    'SELECT "Index idx_end_time already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'scheduled_publish_plans' 
     AND index_name = 'idx_next_execution') = 0,
    'ALTER TABLE `scheduled_publish_plans` ADD INDEX `idx_next_execution` (`next_execution_time`)',
    'SELECT "Index idx_next_execution already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'scheduled_publish_plans' 
     AND index_name = 'idx_active_next_execution') = 0,
    'ALTER TABLE `scheduled_publish_plans` ADD INDEX `idx_active_next_execution` (`is_active`, `next_execution_time`)',
    'SELECT "Index idx_active_next_execution already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 为 scheduled_publish_tasks 表添加执行次数相关字段
ALTER TABLE `scheduled_publish_tasks`
ADD COLUMN IF NOT EXISTS `execution_sequence` int NULL COMMENT '执行序号（第几次执行）' AFTER `plan_id`,
ADD COLUMN IF NOT EXISTS `scheduled_execution_time` datetime NULL COMMENT '计划执行时间' AFTER `execution_sequence`;

-- 4. 添加任务表新字段的索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'scheduled_publish_tasks' 
     AND index_name = 'idx_scheduled_execution') = 0,
    'ALTER TABLE `scheduled_publish_tasks` ADD INDEX `idx_scheduled_execution` (`scheduled_execution_time`)',
    'SELECT "Index idx_scheduled_execution already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE table_schema = DATABASE() 
     AND table_name = 'scheduled_publish_tasks' 
     AND index_name = 'idx_plan_sequence') = 0,
    'ALTER TABLE `scheduled_publish_tasks` ADD INDEX `idx_plan_sequence` (`plan_id`, `execution_sequence`)',
    'SELECT "Index idx_plan_sequence already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 更新现有数据的默认值
UPDATE `scheduled_publish_plans` 
SET 
    `frequency_type` = 'once',
    `current_executions` = 0,
    `next_execution_time` = `scheduled_time`
WHERE (`frequency_type` IS NULL OR `frequency_type` = '') 
AND `scheduled_time` IS NOT NULL;

-- 6. 验证迁移结果
SELECT 
    'Migration completed successfully!' as status,
    COUNT(*) as total_plans,
    SUM(CASE WHEN frequency_type = 'once' THEN 1 ELSE 0 END) as once_plans,
    SUM(CASE WHEN frequency_type = 'daily' THEN 1 ELSE 0 END) as daily_plans,
    SUM(CASE WHEN frequency_type = 'weekly' THEN 1 ELSE 0 END) as weekly_plans,
    SUM(CASE WHEN frequency_type = 'custom' THEN 1 ELSE 0 END) as custom_plans
FROM `scheduled_publish_plans`;

-- 显示新增的字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'scheduled_publish_plans'
AND COLUMN_NAME IN (
    'end_time', 'frequency_type', 'weekly_days', 'custom_interval_value', 
    'custom_interval_unit', 'daily_time', 'max_executions', 
    'current_executions', 'next_execution_time'
)
ORDER BY ORDINAL_POSITION;

SELECT 'Frequency configuration migration completed successfully!' as final_status; 