# 定时任务频率配置功能说明

## 功能概述

本次更新为AI站群系统的定时发布功能添加了完整的频率配置支持，包括：
- 任务结束时间设置
- 多种发布频率选项（每天、每周、自定义间隔）
- 最大执行次数限制
- 自动任务调度和状态管理

## 新增功能特性

### 1. 发布频率类型
- **仅执行一次 (once)**: 传统的一次性任务
- **每天 (daily)**: 每天在指定时间执行
- **每周 (weekly)**: 每周在指定日期执行
- **自定义间隔 (custom)**: 按小时、天或周的自定义间隔执行

### 2. 时间控制
- **开始时间**: 任务首次执行时间
- **结束时间**: 任务停止执行的截止时间（可选）
- **每日执行时间**: 对于每日任务，指定具体的执行时间点
- **最大执行次数**: 限制任务的总执行次数

### 3. 智能调度
- 自动计算下次执行时间
- 支持任务状态自动更新
- 过期任务自动禁用
- 执行次数自动统计

## 数据库变更

### 新增字段 (scheduled_publish_plans 表)
```sql
end_time                datetime        -- 任务结束时间
frequency_type          varchar(20)     -- 发布频率类型
weekly_days             json           -- 每周执行日期
custom_interval_value   int            -- 自定义间隔数值
custom_interval_unit    varchar(10)    -- 自定义间隔单位
daily_time              time           -- 每日执行时间
max_executions          int            -- 最大执行次数
current_executions      int            -- 当前已执行次数
next_execution_time     datetime       -- 下次执行时间
```

### 新增字段 (scheduled_publish_tasks 表)
```sql
execution_sequence      int            -- 执行序号
scheduled_execution_time datetime      -- 计划执行时间
```

## 部署步骤

### 1. 数据库迁移
执行以下SQL脚本之一：

**生产环境（推荐）**:
```bash
mysql -u username -p database_name < migrate_frequency_config.sql
```

**开发环境（包含示例数据和存储过程）**:
```bash
mysql -u username -p database_name < add_frequency_config_to_scheduled_publish.sql
```

### 2. 后端代码更新
- 更新模型文件: `app/models/scheduled_publish.py`
- 更新Schema文件: `app/schemas/scheduled_publish.py`
- 重启后端服务

### 3. 前端代码更新
- 更新定时任务页面: `frontend/src/views/aiCluster/ScheduledPublish.vue`
- 重新构建前端应用

## 使用指南

### 1. 创建每日任务
```javascript
{
  "plan_name": "每日AI文章发布",
  "frequency_type": "daily",
  "daily_time": "09:00",
  "scheduled_time": "2024-01-15T09:00:00",
  "end_time": "2024-02-15T23:59:59",
  "max_executions": 30
}
```

### 2. 创建每周任务
```javascript
{
  "plan_name": "每周技术分享",
  "frequency_type": "weekly",
  "weekly_days": ["1", "3", "5"], // 周一、三、五
  "scheduled_time": "2024-01-15T10:00:00",
  "max_executions": 50
}
```

### 3. 创建自定义间隔任务
```javascript
{
  "plan_name": "每3天发布一次",
  "frequency_type": "custom",
  "custom_interval_value": 3,
  "custom_interval_unit": "days",
  "scheduled_time": "2024-01-15T14:00:00",
  "end_time": "2024-06-15T23:59:59"
}
```

## API 接口更新

### 创建计划接口
```
POST /api/v1/scheduled-publish/generate-batch-tasks
```

新增请求参数：
- `end_time`: 结束时间
- `frequency_type`: 频率类型
- `weekly_days`: 每周执行日期
- `custom_interval_value`: 自定义间隔数值
- `custom_interval_unit`: 自定义间隔单位
- `daily_time`: 每日执行时间
- `max_executions`: 最大执行次数

### 计划列表接口
```
GET /api/v1/scheduled-publish/plans
```

新增响应字段：
- `current_executions`: 当前执行次数
- `next_execution_time`: 下次执行时间
- `frequency_type`: 频率类型
- 其他频率配置字段

## 前端界面更新

### 1. 频率配置区域
- 单选按钮选择频率类型
- 根据选择动态显示相关配置项
- 实时验证配置有效性

### 2. 时间配置
- 开始时间和结束时间选择器
- 每日执行时间选择器
- 最大执行次数输入框

### 3. 每周配置
- 复选框选择执行日期
- 支持多选（周一到周日）

### 4. 自定义间隔配置
- 数值输入框 + 单位选择器
- 支持小时、天、周三种单位

### 5. 任务列表增强
- 显示频率类型标签
- 显示执行进度
- 显示下次执行时间

## 注意事项

### 1. 兼容性
- 现有的一次性任务会自动设置为 `frequency_type = 'once'`
- 不影响现有任务的执行

### 2. 性能考虑
- 添加了必要的数据库索引
- 定时任务调度器需要定期清理过期任务

### 3. 数据验证
- 结束时间必须晚于开始时间
- 自定义间隔值必须大于0
- 每周任务至少选择一天

### 4. 错误处理
- 过期任务自动禁用
- 达到最大执行次数的任务自动停止
- 无效配置会在创建时被拒绝

## 测试建议

### 1. 功能测试
- 创建各种类型的定时任务
- 验证时间计算的准确性
- 测试任务自动停止机制

### 2. 边界测试
- 测试极端时间值
- 测试大量并发任务
- 测试网络异常情况

### 3. 性能测试
- 大量任务的调度性能
- 数据库查询性能
- 前端界面响应速度

## 故障排除

### 1. 常见问题
- **任务不执行**: 检查 `is_active` 状态和 `next_execution_time`
- **时间计算错误**: 验证时区设置和时间格式
- **界面显示异常**: 检查前端数据格式和API响应

### 2. 日志查看
- 后端服务日志: 查看任务调度和执行日志
- 数据库日志: 查看SQL执行情况
- 前端控制台: 查看API调用和错误信息

### 3. 数据修复
```sql
-- 重新计算下次执行时间
CALL CalculateNextExecutionTime(plan_id);

-- 清理过期任务
CALL CleanupExpiredPlans();

-- 重置任务状态
UPDATE scheduled_publish_plans SET is_active = 1 WHERE id = ?;
```

## 版本信息
- 功能版本: v2.0
- 数据库版本: v2.1
- 兼容性: 向后兼容 v1.x

## 联系支持
如有问题请联系开发团队或查看系统日志获取详细错误信息。 