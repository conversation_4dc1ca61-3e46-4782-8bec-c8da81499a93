-- 清理关键词相关的测试数据以解决枚举值冲突
USE cbec;

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 清理关键词更新历史表
TRUNCATE TABLE keyword_update_history;

-- 清理关键词库表
TRUNCATE TABLE keyword_library;

-- 清理关键词导入任务表
TRUNCATE TABLE keyword_import_tasks;

-- 重置自增ID
ALTER TABLE keyword_update_history AUTO_INCREMENT = 1;
ALTER TABLE keyword_library AUTO_INCREMENT = 1;
ALTER TABLE keyword_import_tasks AUTO_INCREMENT = 1;

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 验证清理结果
SELECT COUNT(*) as keyword_library_count FROM keyword_library;
SELECT COUNT(*) as keyword_history_count FROM keyword_update_history;
SELECT COUNT(*) as import_tasks_count FROM keyword_import_tasks; 