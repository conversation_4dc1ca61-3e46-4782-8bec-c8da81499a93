from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from ....db.session import get_db
from ....models.ai_config import AIConfig
from ....schemas.ai_config import AIConfigCreate, AIConfigUpdate, AIConfigResponse, AIConfigList
from ....api.deps import get_current_user
from ....models.user import User

router = APIRouter()


@router.get("/", response_model=AIConfigList)
async def get_ai_configs(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索配置名称"),
    service_type: Optional[str] = Query(None, description="服务类型筛选"),
    is_active: Optional[bool] = Query(None, description="是否启用筛选"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取AI配置列表"""
    query = db.query(AIConfig)
    
    # 搜索过滤
    if search:
        query = query.filter(AIConfig.name.contains(search))
    
    if service_type:
        query = query.filter(AIConfig.service_type == service_type)
    
    if is_active is not None:
        query = query.filter(AIConfig.is_active == is_active)
    
    # 计算总数
    total = query.count()
    
    # 分页
    offset = (page - 1) * size
    items = query.offset(offset).limit(size).all()
    
    return AIConfigList(
        items=items,
        total=total,
        page=page,
        size=size
    )


@router.get("/{config_id}", response_model=AIConfigResponse)
async def get_ai_config(
    config_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取单个AI配置"""
    config = db.query(AIConfig).filter(AIConfig.id == config_id).first()
    if not config:
        raise HTTPException(status_code=404, detail="AI配置不存在")
    return config


@router.post("/", response_model=AIConfigResponse)
async def create_ai_config(
    config_data: AIConfigCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建AI配置"""
    # 检查配置名称是否已存在
    existing = db.query(AIConfig).filter(AIConfig.name == config_data.name).first()
    if existing:
        raise HTTPException(status_code=400, detail="配置名称已存在")
    
    config = AIConfig(**config_data.dict())
    db.add(config)
    db.commit()
    db.refresh(config)
    return config


@router.put("/{config_id}", response_model=AIConfigResponse)
async def update_ai_config(
    config_id: int,
    config_data: AIConfigUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新AI配置"""
    config = db.query(AIConfig).filter(AIConfig.id == config_id).first()
    if not config:
        raise HTTPException(status_code=404, detail="AI配置不存在")
    
    # 检查名称是否与其他配置冲突
    if config_data.name and config_data.name != config.name:
        existing = db.query(AIConfig).filter(
            AIConfig.name == config_data.name,
            AIConfig.id != config_id
        ).first()
        if existing:
            raise HTTPException(status_code=400, detail="配置名称已存在")
    
    # 更新字段
    update_data = config_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(config, field, value)
    
    db.commit()
    db.refresh(config)
    return config


@router.delete("/{config_id}")
async def delete_ai_config(
    config_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除AI配置"""
    config = db.query(AIConfig).filter(AIConfig.id == config_id).first()
    if not config:
        raise HTTPException(status_code=404, detail="AI配置不存在")
    
    db.delete(config)
    db.commit()
    return {"message": "AI配置已删除"}


@router.get("/service-types/", response_model=List[str])
async def get_service_types(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有服务类型"""
    result = db.query(AIConfig.service_type).distinct().all()
    return [item[0] for item in result if item[0]]


@router.post("/{config_id}/test")
async def test_ai_config(
    config_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """测试AI配置连接"""
    config = db.query(AIConfig).filter(AIConfig.id == config_id).first()
    if not config:
        raise HTTPException(status_code=404, detail="AI配置不存在")
    
    try:
        # 这里可以添加实际的连接测试逻辑
        # 例如调用DIFY的健康检查接口
        return {"status": "success", "message": "连接测试成功"}
    except Exception as e:
        return {"status": "error", "message": f"连接测试失败: {str(e)}"} 