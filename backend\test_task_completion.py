#!/usr/bin/env python3
"""
测试任务完成事件触发机制
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app.models.scheduled_publish import ScheduledPublishTask, TaskQueue
from app.models.ai_article import AIArticle
from app.services.event_manager import trigger_task_status_update, trigger_queue_status_update
from datetime import datetime


async def test_task_completion_events():
    """测试任务完成事件触发"""
    print("开始测试任务完成事件触发...")
    
    db = SessionLocal()
    try:
        # 查找一个运行中的任务
        running_task = db.query(ScheduledPublishTask).filter(
            ScheduledPublishTask.status.in_(["running", "queued"])
        ).first()
        
        if not running_task:
            print("没有找到运行中的任务，查找现有计划...")
            # 查找现有的计划
            from app.models.scheduled_publish import ScheduledPublishPlan
            existing_plan = db.query(ScheduledPublishPlan).first()
            if not existing_plan:
                print("没有找到现有计划，无法创建测试任务")
                return

            # 创建一个测试任务
            test_task = ScheduledPublishTask(
                plan_id=existing_plan.id,  # 使用现有计划ID
                keywords="测试关键词",
                wordpress_url="https://test.com",
                site_name="测试站点",
                status="running",
                execution_time=datetime.utcnow()
            )
            db.add(test_task)
            db.commit()
            db.refresh(test_task)
            running_task = test_task
            print(f"创建了测试任务 ID: {running_task.id}, 计划ID: {existing_plan.id}")
        
        print(f"找到任务 ID: {running_task.id}, 状态: {running_task.status}")
        
        # 模拟任务完成
        print("模拟任务完成...")
        running_task.status = "success"
        running_task.completion_time = datetime.utcnow()
        
        # 更新队列状态
        queue_item = db.query(TaskQueue).filter(TaskQueue.task_id == running_task.id).first()
        if queue_item:
            queue_item.status = "completed"
            print(f"更新队列项状态为 completed")
        
        db.commit()
        
        # 触发任务状态更新事件
        print("触发任务状态更新事件...")
        trigger_task_status_update({
            "task_id": running_task.id,
            "status": running_task.status,
            "plan_id": running_task.plan_id,
            "completion_time": running_task.completion_time.isoformat(),
            "error_message": None
        })
        
        # 触发队列状态更新事件
        print("触发队列状态更新事件...")
        trigger_queue_status_update({
            "total_queued": 0,
            "running_tasks": 0,
            "available_workers": 5,
            "estimated_wait_time": 0
        })
        
        print("事件触发完成！")
        print(f"任务 {running_task.id} 已标记为完成")
        
        # 等待一下让事件处理
        await asyncio.sleep(2)
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(test_task_completion_events())
