"""change type field to integer

Revision ID: change_type_to_integer
Revises: update_type_field_comment
Create Date: 2024-12-19 11:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text


# revision identifiers, used by Alembic.
revision = 'change_type_to_integer'
down_revision = 'update_type_field_comment'
branch_labels = None
depends_on = None


def upgrade():
    """将type字段从varchar改为int类型"""
    # 修改ai_articles表的type字段类型
    op.execute(text("""
    ALTER TABLE `ai_articles` 
    MODIFY COLUMN `type` int NOT NULL COMMENT '博客分类ID'
    """))
    
    print("✅ ai_articles表type字段类型已修改为int")


def downgrade():
    """回滚type字段类型为varchar"""
    # 回滚ai_articles表的type字段类型
    op.execute(text("""
    ALTER TABLE `ai_articles` 
    MODIFY COLUMN `type` varchar(50) NOT NULL COMMENT '博客分类ID'
    """))
    
    print("✅ ai_articles表type字段类型已回滚为varchar(50)") 