# Google Ads API 开发者令牌问题解决指南

## 问题描述

在测试连接时遇到以下错误：
```
ERROR:app.services.google_ads_service:连接测试失败: 'CustomerServiceClient' object has no attribute 'get_customer'
Request made: ClientCustomerId: None, Host: googleads.googleapis.com, Method: /google.ads.googleads.v19.services.CustomerService/ListAccessibleCustomers, RequestId: A4dTFPB4fP4SaYfghKsHGQ, IsFault: True, FaultMessage: The developer token is not valid.
```

## 根本原因

错误的根本原因是**开发者令牌（Developer Token）无效**，这是Google Ads API认证的必需组件。

## Google Ads API 认证要素

Google Ads API需要以下认证要素：

1. **OAuth 2.0 访问令牌** - 通过`Authorization: Bearer ACCESS_TOKEN`头提供
2. **开发者令牌** - 通过`developer-token: DEVELOPER_TOKEN`头提供 ⚠️ **当前问题**
3. **客户ID** - 在API请求URL中指定
4. **登录客户ID**（可选）- 通过`login-customer-id: MANAGER_CUSTOMER_ID`头提供

## 开发者令牌问题分析

### 什么是开发者令牌？
- 22位字母数字字符串
- 允许应用连接到Google Ads API
- 每个公司通常只分配一个开发者令牌
- 具有访问级别（测试、基础、标准）

### 可能的问题
1. **令牌无效或过期**
2. **令牌访问级别不足**
3. **令牌与Google API Console项目不匹配**
4. **令牌未正确配置**

## 解决方案

### 1. 检查开发者令牌

访问Google Ads管理员账户的API中心：
1. 登录 [Google Ads](https://ads.google.com)
2. 进入**管理员** > **API中心**
3. 查看**开发者令牌**部分
4. 确认令牌状态和访问级别

### 2. 开发者令牌访问级别

| 访问级别 | 描述 | 每日操作限制 |
|---------|------|-------------|
| 测试访问 | 只能访问测试账户 | 15,000 操作/天 |
| 基础访问 | 可访问测试和生产账户 | 15,000 操作/天 |
| 标准访问 | 可访问测试和生产账户 | 无限制操作/天 |

### 3. 申请新的开发者令牌

如果没有有效的开发者令牌：

1. **创建或选择Google Ads管理员账户**
2. **访问API中心**：https://ads.google.com/aw/apicenter
3. **填写API访问表单**：
   - 确保公司网站URL有效
   - 提供有效的API联系邮箱
   - 接受条款和条件
4. **等待审核**（可能需要几天到几周）

### 4. 配置验证

确保您的配置包含：
```python
config = {
    "developer_token": "YOUR_22_CHARACTER_DEVELOPER_TOKEN",
    "client_id": "YOUR_OAUTH_CLIENT_ID",
    "client_secret": "YOUR_OAUTH_CLIENT_SECRET", 
    "refresh_token": "YOUR_REFRESH_TOKEN",
    "customer_id": "1234567890",  # 移除连字符
    "login_customer_id": "0987654321"  # 可选，管理员账户ID
}
```

### 5. 测试账户选项

如果需要立即测试，可以：
1. 创建Google Ads测试账户
2. 使用测试访问级别的开发者令牌
3. 测试账户不投放真实广告

## 常见问题

### Q: 我的公司已经有开发者令牌了吗？
A: Google通常每个公司只分配一个开发者令牌。检查是否有同事已经申请过。

### Q: 我可以使用第三方应用的令牌吗？
A: 不可以。每个应用需要自己的开发者令牌。

### Q: 令牌审核需要多长时间？
A: 通常几天到几周，取决于申请的复杂性和Google的审核队列。

### Q: 测试访问足够吗？
A: 对于开发和测试，是的。但生产环境需要基础或标准访问。

## 后续步骤

1. **立即行动**：检查现有开发者令牌状态
2. **如需申请**：准备申请新的开发者令牌
3. **等待期间**：可以继续开发其他功能
4. **获得令牌后**：更新配置并重新测试

## 相关链接

- [Google Ads API开发者令牌申请](https://developers.google.com/google-ads/api/docs/get-started/dev-token)
- [Google Ads API访问级别](https://developers.google.com/google-ads/api/docs/access-levels)
- [Google Ads API认证指南](https://developers.google.com/google-ads/api/docs/oauth/overview)

---

**注意**：开发者令牌是访问Google Ads API的前提条件。没有有效的开发者令牌，无法进行API调用。这不是代码问题，而是配置和权限问题。 