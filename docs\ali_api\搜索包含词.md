# 搜索包含词

搜索包含词

GET/POST

alibaba.mydata.seller.opendata.getconkeyword

描述：国际站商家的关键词服务

## 参数

|  名称  |  类型  |  是否必须  |  描述  |
| --- | --- | --- | --- |
|  query  |  String  |  否  |  查询关键词  |
|  params  |  Object  |  否  |  查询其他参数  |

## 响应参数

|  名称  |  类型  |  描述  |
| --- | --- | --- |
|  result\_list  |  Object\[\]  |  结果列表  |
|  allow\_add\_p4p  |  Boolean  |  是否允许添加P4P  |
|  seller\_cnt\_crc  |  String  |  竞对商家数量环比  |
|  is\_p4p\_keyword  |  Boolean  |  是否是P4P词  |
|  click\_rate\_crc  |  String  |  点击率环比  |
|  query\_raw  |  String  |  原始词  |
|  pv  |  String  |  曝光数量  |
|  click\_rate  |  String  |  点击率  |
|  pv\_list  |  Number\[\]  |  曝光历史  |
|  pv\_crc  |  String  |  曝光环比  |
|  seller\_cnt  |  String  |  竞对商家数量  |
|  total\_count  |  Number  |  总共条数  |

## 错误码

|  错误码  |  错误信息  |  解决方案  |
| --- | --- | --- |
|  没有数据  |  |  |

GET/POSTalibaba.mydata.seller.opendata.getconkeyword

*   PYTHON
    

```PYTHON
client = iop.IopClient(url, appkey ,appSecret)
request = iop.IopRequest('alibaba.mydata.seller.opendata.getconkeyword')
request.add_api_param('query', 'generator')
request.add_api_param('params', '{     \"pageSize\": \"1\",     \"pageNO\": \"1\",     \"nd\": \"7d\",     \"orderBy\" : \"pv\",     \"order\" :  \"desc\",     \"terminalType\": \"TOTAL\",     \"countryId\": \"TOTAL\"   }')
response = client.execute(request, access_token)
print(response.type)
print(response.body)

```

*   非精简返回
    

---
---
```json
{
  "code": "0",
  "alibaba_mydata_seller_opendata_getconkeyword_response": {
    "result_list": [
      {
        "allow_add_p4p": "true",
        "pv_crc": "0.5",
        "query_raw": "generator",
        "seller_cnt": "3",
        "pv_list": [
          1,
          3,
          2
        ],
        "pv": "5.0",
        "total_count": "8000",
        "click_rate": "0.5",
        "click_rate_crc": "0.5",
        "seller_cnt_crc": "0.3",
        "is_p4p_keyword": "true"
      }
    ]
  },
  "request_id": "0ba2887315178178017221014"
}
```