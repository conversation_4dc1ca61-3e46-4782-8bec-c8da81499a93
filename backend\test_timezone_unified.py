#!/usr/bin/env python3
"""
测试UTC + 前端本地化时区统一方案

验证整个系统的时区处理是否正确
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from datetime import datetime, timezone, timedelta

# 设置环境变量测试
os.environ['SYSTEM_TIMEZONE_OFFSET'] = '8'  # 中国时区

# 导入我们的时间工具函数
try:
    from app.utils.datetime_utils import (
        utc_now, to_utc, from_system_timezone, to_iso_string, 
        parse_iso_string, format_for_log, get_system_timezone_info
    )
    print("✅ 成功导入时间工具函数")
except ImportError as e:
    print(f"❌ 无法导入时间工具函数: {e}")
    sys.exit(1)

def test_basic_functions():
    """测试基本时间函数"""
    print("\n=== 基本时间函数测试 ===")
    
    # 1. 测试获取当前UTC时间
    print("1. 当前UTC时间:")
    utc_time = utc_now()
    print(f"   UTC时间: {utc_time}")
    print(f"   ISO格式: {to_iso_string(utc_time)}")
    print(f"   时区信息: {utc_time.tzinfo}")
    
    # 2. 测试系统时区配置
    print("\n2. 系统时区配置:")
    tz_info = get_system_timezone_info()
    for key, value in tz_info.items():
        print(f"   {key}: {value}")
    
    # 3. 测试时间转换
    print("\n3. 时间转换测试:")
    test_time = datetime(2024, 1, 1, 18, 0, 0)  # 无时区信息
    print(f"   原始时间: {test_time}")
    utc_converted = to_utc(test_time)
    print(f"   转为UTC: {utc_converted}")
    iso_string = to_iso_string(utc_converted)
    print(f"   ISO格式: {iso_string}")

def test_api_simulation():
    """模拟API接口的时间处理"""
    print("\n=== API接口时间处理模拟 ===")
    
    # 模拟后端API返回数据
    current_utc = utc_now()
    api_response = {
        "id": 1,
        "name": "测试任务",
        "created_at": to_iso_string(current_utc),
        "updated_at": to_iso_string(current_utc),
        "scheduled_time": to_iso_string(current_utc + timedelta(hours=1))
    }
    
    print("1. 后端API返回数据:")
    for key, value in api_response.items():
        if key.endswith('_at') or key.endswith('_time'):
            print(f"   {key}: {value}")
    
    # 模拟前端接收和显示
    print("\n2. 前端解析和显示:")
    for key, value in api_response.items():
        if key.endswith('_at') or key.endswith('_time'):
            # 模拟JavaScript的Date解析
            parsed_date = parse_iso_string(value)
            # 模拟前端显示（转换为系统时区）
            display_time = format_for_log(parsed_date)
            print(f"   {key}: {display_time}")

def test_database_simulation():
    """模拟数据库存储和读取"""
    print("\n=== 数据库存储模拟 ===")
    
    # 模拟数据库存储
    print("1. 数据库存储:")
    store_time = utc_now()
    print(f"   存储时间: {store_time}")
    print(f"   存储格式: {store_time.isoformat()}")
    
    # 模拟数据库读取
    print("\n2. 数据库读取:")
    # 假设从数据库读取的时间字符串
    db_time_str = store_time.isoformat()
    print(f"   读取字符串: {db_time_str}")
    
    # 解析为datetime对象
    parsed_time = datetime.fromisoformat(db_time_str)
    print(f"   解析时间: {parsed_time}")
    
    # 转换为API返回格式
    api_format = to_iso_string(parsed_time)
    print(f"   API格式: {api_format}")

def test_timezone_changes():
    """测试时区配置变更"""
    print("\n=== 时区配置变更测试 ===")
    
    # 测试不同时区配置
    test_timezones = [
        ('0', 'UTC'),
        ('8', '中国时区'),
        ('-5', '美国东部时区'),
        ('9', '日本时区')
    ]
    
    base_utc_time = utc_now()
    
    for offset, name in test_timezones:
        offset_sign = '+' if int(offset) >= 0 else ''
        print(f"\n{name} (UTC{offset_sign}{offset}小时):")
        
        # 临时设置环境变量
        original_offset = os.environ.get('SYSTEM_TIMEZONE_OFFSET', '8')
        os.environ['SYSTEM_TIMEZONE_OFFSET'] = offset
        
        # 重新导入模块以获取新的时区配置
        import importlib
        import app.utils.datetime_utils
        importlib.reload(app.utils.datetime_utils)
        
        from app.utils.datetime_utils import format_for_log, get_system_timezone_info
        
        # 显示时区信息
        tz_info = get_system_timezone_info()
        print(f"   时区配置: {tz_info['timezone_name']}")
        print(f"   UTC时间: {base_utc_time}")
        print(f"   本地显示: {format_for_log(base_utc_time)}")
        
        # 恢复原始设置
        os.environ['SYSTEM_TIMEZONE_OFFSET'] = original_offset

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 边界情况测试 ===")
    
    # 测试None值
    print("1. None值处理:")
    print(f"   to_utc(None): {to_utc(None)}")
    print(f"   to_iso_string(None): {to_iso_string(None)}")
    print(f"   parse_iso_string(None): {parse_iso_string(None)}")
    
    # 测试无效字符串
    print("\n2. 无效字符串处理:")
    try:
        result = parse_iso_string("invalid-time-string")
        print(f"   解析结果: {result}")
    except ValueError as e:
        print(f"   预期错误: {e}")
    
    # 测试不同格式的时间字符串
    print("\n3. 不同格式时间字符串:")
    test_strings = [
        "2024-01-01T10:00:00Z",  # UTC格式
        "2024-01-01T18:00:00+08:00",  # 带时区格式
        "2024-01-01T10:00:00",  # 无时区格式
    ]
    
    for time_str in test_strings:
        try:
            parsed = parse_iso_string(time_str)
            iso_format = to_iso_string(parsed)
            print(f"   输入: {time_str}")
            print(f"   解析: {parsed}")
            print(f"   标准化: {iso_format}")
            print()
        except Exception as e:
            print(f"   输入: {time_str} - 错误: {e}")

if __name__ == "__main__":
    print("🕐 UTC + 前端本地化时区统一方案测试")
    print("=" * 60)
    
    try:
        test_basic_functions()
        test_api_simulation()
        test_database_simulation()
        test_timezone_changes()
        test_edge_cases()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成！")
        print("\n📝 方案总结:")
        print("- 后端统一使用UTC时间存储和传输")
        print("- API返回ISO格式的UTC时间（以Z结尾）")
        print("- 前端根据环境变量配置自动转换为指定时区显示")
        print("- 数据库存储带时区信息的UTC时间")
        print("- 支持全局时区配置，修改环境变量即可改变显示时区")
        print("- 日志显示系统配置时区便于调试")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
