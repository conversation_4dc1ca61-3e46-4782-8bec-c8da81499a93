const { defineConfig } = require('@vue/cli-service')
const path = require('path')
const webpack = require('webpack')
const fs = require('fs')

// 手动加载 .env 文件
const envPath = path.resolve(__dirname, '.env')
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8')
  const envLines = envContent.split('\n')
  envLines.forEach(line => {
    const trimmedLine = line.trim()
    if (trimmedLine && !trimmedLine.startsWith('#')) {
      const [key, value] = trimmedLine.split('=')
      if (key && value) {
        process.env[key.trim()] = value.trim()
      }
    }
  })
  console.log('手动加载 .env 文件完成')
}

// 从环境变量读取配置
const FRONTEND_PORT = process.env.FRONTEND_PORT || 8080
const API_BASE_URL = process.env.VUE_APP_API_BASE_URL || 'http://localhost:5000'

// 读取时区配置环境变量
const SYSTEM_TIMEZONE_OFFSET = process.env.VUE_APP_SYSTEM_TIMEZONE_OFFSET || '8'
const SYSTEM_TIMEZONE_NAME = process.env.VUE_APP_SYSTEM_TIMEZONE_NAME || 'Asia/Shanghai'

console.log('Vue CLI 配置 - 所有环境变量:', {
  NODE_ENV: process.env.NODE_ENV,
  VUE_APP_SYSTEM_TIMEZONE_OFFSET: SYSTEM_TIMEZONE_OFFSET,
  VUE_APP_SYSTEM_TIMEZONE_NAME: SYSTEM_TIMEZONE_NAME,
  从env文件读取: {
    VUE_APP_SYSTEM_TIMEZONE_OFFSET: process.env.VUE_APP_SYSTEM_TIMEZONE_OFFSET,
    VUE_APP_SYSTEM_TIMEZONE_NAME: process.env.VUE_APP_SYSTEM_TIMEZONE_NAME
  }
})

// 获取项目根目录（D:\LP01\AI\AICBEC）
const projectRoot = path.resolve(__dirname, '../..')

module.exports = defineConfig({
  transpileDependencies: true,
  
  // 生产环境配置
  publicPath: process.env.NODE_ENV === 'production' ? '/' : '/',
  
  devServer: {
    port: FRONTEND_PORT,
    host: '0.0.0.0',
    
    // 明确允许的主机列表，解决Invalid Host header问题
    allowedHosts: 'all', // 更简洁的配置，允许所有主机
    
    // 彻底禁用客户端
    client: false,
    
    // 禁用热重载和实时重载
    hot: false,
    liveReload: false,
    
    // 禁用WebSocket服务器
    webSocketServer: false,
    
    // 代理配置 - 仅在开发环境使用
    proxy: process.env.NODE_ENV === 'development'
? {
      '/api': {
        target: API_BASE_URL,
        changeOrigin: true,
        secure: false
      }
    }
: {}
  },
  
  configureWebpack: {
    // 完全禁用webpack-dev-server客户端注入
    devServer: {
      client: false,
      hot: false,
      liveReload: false
    },

    // 注入环境变量到浏览器环境
    plugins: [
      new webpack.DefinePlugin({
        // 注入全局变量（不带引号的变量名）
        VUE_APP_SYSTEM_TIMEZONE_OFFSET: JSON.stringify(SYSTEM_TIMEZONE_OFFSET),
        VUE_APP_SYSTEM_TIMEZONE_NAME: JSON.stringify(SYSTEM_TIMEZONE_NAME),
        // 注入到 process.env（模拟 Node.js 环境）
        'process.env': JSON.stringify({
          VUE_APP_SYSTEM_TIMEZONE_OFFSET: SYSTEM_TIMEZONE_OFFSET,
          VUE_APP_SYSTEM_TIMEZONE_NAME: SYSTEM_TIMEZONE_NAME,
          NODE_ENV: process.env.NODE_ENV || 'development'
        })
      })
    ],

    resolve: {
      modules: [
        path.join(projectRoot, 'node_modules'),
        'node_modules'
      ],
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    },
    resolveLoader: {
      modules: [
        path.join(projectRoot, 'node_modules'),
        'node_modules'
      ]
    }
  },
  
  // 修改链式配置来禁用HMR插件
  chainWebpack: config => {
    // 完全禁用HMR
    config.plugins.delete('hmr')
    
    // 如果存在，也删除webpack-dev-server客户端相关插件
    if (config.plugins.has('webpack-dev-server')) {
      config.plugins.delete('webpack-dev-server')
    }
  }
})
