# type字段数据类型修改总结

## 📋 修改内容

将手动AI发布功能中的type字段从字符串类型改为数字类型，确保传递给DIFY的是数字。

## 🔧 已完成的代码修改

### 1. 后端Schema (`backend/app/schemas/ai_article.py`)
```python
# 修改前：type: str = Field(..., description="博客分类ID")
# 修改后：type: int = Field(..., description="博客分类ID")
```

### 2. 数据库模型 (`backend/app/models/ai_article.py`)
```python
# 修改前：type = Column(String(50), nullable=False, comment="博客分类ID")
# 修改后：type = Column(Integer, nullable=False, comment="博客分类ID")
```

### 3. DIFY服务 (`backend/app/services/dify_service.py`)
```python
# 修改前：type_param: str = "Company"
# 修改后：type_param: int = 1
```

### 4. 前端代码 (`frontend/src/views/aiCluster/SeoAiArticle.vue`)
```javascript
// 修改前：type: generateForm.blog_category_id.toString()
// 修改后：type: generateForm.blog_category_id
```

## 🗄️ 数据库修改

### 执行SQL脚本
运行 `modify_type_field.sql` 中的SQL语句来修改数据库字段类型。

### 核心SQL语句
```sql
ALTER TABLE `ai_articles` 
MODIFY COLUMN `type` int NOT NULL COMMENT '博客分类ID';
```

## ✅ 验证要点

1. **前端**: 确认传递的是数字而不是字符串
2. **后端**: API接收到整数类型的type参数
3. **数据库**: type字段已改为int类型
4. **DIFY**: 接收到数字类型的type参数

## 🎯 数据流程

```
前端博客分类ID (数字) → 后端API (int) → 数据库 (int) → DIFY (数字)
```

---
**修改完成**: 代码已更新，执行SQL脚本完成数据库修改即可 