from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, Date, BigInteger, DECIMAL, Enum
from sqlalchemy.sql import func
from app.models.base import BaseModel

class AlibabaProductPerformance(BaseModel):
    """阿里巴巴产品表现数据模型"""
    __tablename__ = "alibaba_product_performance"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(50), index=True, comment="租户ID")
    user_id = Column(Integer, index=True, comment="关联的用户ID")
    
    # 产品基本信息
    product_id = Column(String(50), index=True, comment="产品ID")
    product_name = Column(String(500), comment="产品名称")
    
    # 统计周期信息
    statistics_type = Column(Enum("day", "week", "month", name="product_statistics_type_enum"), comment="统计周期类型")
    stat_date = Column(Date, comment="统计日期")
    
    # 表现数据
    impression = Column(BigInteger, default=0, comment="曝光量")
    click = Column(BigInteger, default=0, comment="点击量")
    visitor = Column(BigInteger, default=0, comment="访客数")
    order_count = Column(BigInteger, default=0, comment="订单数")
    bookmark = Column(BigInteger, default=0, comment="收藏数")
    compare = Column(BigInteger, default=0, comment="对比数")
    share = Column(BigInteger, default=0, comment="分享数")
    feedback = Column(BigInteger, default=0, comment="反馈数")
    
    # 计算指标
    ctr = Column(DECIMAL(8,4), default=0.0000, comment="点击率 (Click Through Rate)")
    conversion_rate = Column(DECIMAL(8,4), default=0.0000, comment="转化率")
    
    # 数据同步信息
    sync_time = Column(DateTime, default=func.now(), comment="数据同步时间")
    data_source = Column(String(50), default="alibaba_api", comment="数据来源")
    
    class Config:
        table = True

class AlibabaProductKeywordEffect(BaseModel):
    """阿里巴巴产品关键词效果模型"""
    __tablename__ = "alibaba_product_keyword_effects"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(50), index=True, comment="租户ID")
    user_id = Column(Integer, index=True, comment="关联的用户ID")
    
    # 关联产品表现
    product_performance_id = Column(Integer, index=True, comment="关联的产品表现记录ID")
    
    # 关键词信息
    keyword = Column(String(200), index=True, comment="关键词")
    keyword_type = Column(String(50), comment="关键词类型")
    
    # 关键词效果数据
    keyword_impression = Column(BigInteger, default=0, comment="关键词曝光量")
    keyword_click = Column(BigInteger, default=0, comment="关键词点击量")
    keyword_ctr = Column(DECIMAL(8,4), default=0.0000, comment="关键词点击率")
    
    class Config:
        table = True

class AlibabaProductDateRange(BaseModel):
    """阿里巴巴产品数据可用时间范围模型"""
    __tablename__ = "alibaba_product_date_ranges"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(50), index=True, comment="租户ID")
    user_id = Column(Integer, index=True, comment="关联的用户ID")
    
    # 时间范围信息
    statistics_type = Column(Enum("day", "week", "month", name="date_range_statistics_type_enum"), comment="统计周期类型")
    start_date = Column(Date, comment="可用数据开始日期")
    end_date = Column(Date, comment="可用数据结束日期")
    
    # 更新信息
    last_updated = Column(DateTime, default=func.now(), comment="最后更新时间")
    is_active = Column(Boolean, default=True, comment="是否有效")
    
    class Config:
        table = True

class AlibabaProduct(BaseModel):
    """阿里巴巴产品基础信息模型"""
    __tablename__ = "alibaba_products"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(50), index=True, comment="租户ID")
    user_id = Column(Integer, index=True, comment="关联的用户ID")
    
    # 产品基本信息
    product_id = Column(String(50), unique=True, index=True, comment="产品ID")
    product_name = Column(String(500), comment="产品名称")
    product_title = Column(String(1000), comment="产品标题")
    product_category = Column(String(200), comment="产品分类")
    product_status = Column(String(50), comment="产品状态")
    
    # 产品详情
    product_url = Column(String(1000), comment="产品链接")
    main_image_url = Column(String(1000), comment="主图链接")
    description = Column(Text, comment="产品描述")
    
    # 同步信息
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    sync_time = Column(DateTime, default=func.now(), comment="最后同步时间")
    is_active = Column(Boolean, default=True, comment="是否有效")
    
    class Config:
        table = True 