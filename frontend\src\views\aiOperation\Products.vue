<template>
  <div class="products">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>阿里巴巴商品管理</h2>
      <div class="header-actions">
        <el-button 
          type="primary" 
          :icon="Refresh" 
          @click="handleAutoFetch"
          :loading="autoFetching">
          自动获取商品
        </el-button>
        <el-button 
          :icon="RefreshRight" 
          @click="refreshList"
          :loading="loading">
          刷新列表
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="商品名称">
          <el-input
            v-model="searchForm.subject"
            placeholder="请输入商品名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="商品状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="审核通过" value="approved" />
            <el-option label="审核中" value="auditing" />
            <el-option label="审核退回" value="tbd" />
          </el-select>
        </el-form-item>
        <el-form-item label="上架状态">
          <el-select v-model="searchForm.display" placeholder="请选择上架状态" clearable>
            <el-option label="已上架" value="Y" />
            <el-option label="已下架" value="N" />
          </el-select>
        </el-form-item>
        <el-form-item label="类目ID">
          <el-input
            v-model="searchForm.category_id"
            placeholder="请输入类目ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 商品列表 -->
    <el-card class="table-card">
      <el-table
        :data="productList"
        :loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="商品图片" width="100">
          <template #default="{ row }">
            <el-image
              :src="row.main_image_url"
              fit="cover"
              style="width: 60px; height: 60px; border-radius: 4px;"
              :preview-src-list="row.main_image_url ? [row.main_image_url] : []"
              lazy
            >
              <template #error>
                <div class="image-slot">
                  <el-icon><Picture /></el-icon>
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        
        <el-table-column prop="product_id" label="商品ID" width="120" />
        
        <el-table-column label="商品信息" min-width="250">
          <template #default="{ row }">
            <div class="product-info">
              <div class="product-title">{{ row.product_name }}</div>
              <div class="product-keywords" v-if="row.keywords">
                <el-tag size="small" v-for="keyword in getKeywordTags(row.keywords)" :key="keyword">
                  {{ keyword }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getStatusType(row.product_status)" 
              size="small">
              {{ getStatusText(row.product_status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="上架状态" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="row.display === 'Y' ? 'success' : 'info'" 
              size="small">
              {{ row.display === 'Y' ? '已上架' : '已下架' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="product_type" label="商品类型" width="100" />
        <el-table-column prop="group_name" label="分组" width="120" />
        <el-table-column prop="owner_member" label="负责人" width="120" />
        
        <el-table-column label="修改时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.gmt_modified) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button 
              link 
              type="primary" 
              @click="handleViewDetail(row)">
              查看详情
            </el-button>
            <el-button 
              link 
              type="primary" 
              @click="handleViewPerformance(row)">
              查看表现
            </el-button>
            <el-button 
              link 
              type="primary" 
              @click="handleOpenProduct(row)"
              v-if="row.product_url">
              访问商品
            </el-button>
      </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 30]"
          :total="pagination.total_item"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 商品详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="商品详情"
      width="800px"
      destroy-on-close
    >
      <div v-if="selectedProduct" class="product-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="商品ID">{{ selectedProduct.product_id }}</el-descriptions-item>
          <el-descriptions-item label="商品名称">{{ selectedProduct.product_name }}</el-descriptions-item>
          <el-descriptions-item label="商品状态">
            <el-tag :type="getStatusType(selectedProduct.product_status)">
              {{ getStatusText(selectedProduct.product_status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="上架状态">
            <el-tag :type="selectedProduct.display === 'Y' ? 'success' : 'info'">
              {{ selectedProduct.display === 'Y' ? '已上架' : '已下架' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="商品类型">{{ selectedProduct.product_type }}</el-descriptions-item>
          <el-descriptions-item label="语言">{{ selectedProduct.language }}</el-descriptions-item>
          <el-descriptions-item label="类目ID">{{ selectedProduct.product_category }}</el-descriptions-item>
          <el-descriptions-item label="分组">{{ selectedProduct.group_name }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{ selectedProduct.owner_member }}</el-descriptions-item>
          <el-descriptions-item label="型号">{{ selectedProduct.red_model }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedProduct.gmt_create) }}</el-descriptions-item>
          <el-descriptions-item label="修改时间">{{ formatDate(selectedProduct.gmt_modified) }}</el-descriptions-item>
          <el-descriptions-item label="关键词" :span="2">{{ selectedProduct.keywords }}</el-descriptions-item>
          <el-descriptions-item label="商品链接" :span="2">
            <el-link :href="selectedProduct.product_url" target="_blank" type="primary">
              {{ selectedProduct.product_url }}
            </el-link>
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="product-image" v-if="selectedProduct.main_image_url">
          <h4>商品图片</h4>
          <el-image
            :src="selectedProduct.main_image_url"
            fit="contain"
            style="width: 300px; max-height: 300px;"
            :preview-src-list="[selectedProduct.main_image_url]"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
/* eslint-env browser */
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, RefreshRight, Picture } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import axios from 'axios'
import { formatDateTime } from '@/utils/timezone'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const autoFetching = ref(false)
const productList = ref([])
const selectedProducts = ref([])
const detailDialogVisible = ref(false)
const selectedProduct = ref(null)

// 搜索表单
const searchForm = reactive({
  subject: '',
  status: '',
  display: '',
  category_id: ''
})

// 分页信息
const pagination = reactive({
  current_page: 1,
  page_size: 20,
  total_item: 0
})

// 方法
const loadProductList = async () => {
  loading.value = true
  try {
    const params = {
      page_size: pagination.page_size,
      current_page: pagination.current_page,
      sync_to_db: true
    }
    
    // 添加搜索条件
    if (searchForm.subject) params.subject = searchForm.subject
    if (searchForm.status) params.status = searchForm.status
    if (searchForm.display) params.display = searchForm.display
    if (searchForm.category_id) params.category_id = searchForm.category_id
    
    const response = await axios.get('/api/v1/alibaba-product/list', { params })
    
    if (response.data.success) {
      productList.value = response.data.products
      pagination.current_page = response.data.pagination.current_page
      pagination.page_size = response.data.pagination.page_size
      pagination.total_item = response.data.pagination.total_item
    } else {
      ElMessage.error(response.data.error || '获取商品列表失败')
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
    ElMessage.error('获取商品列表失败，请检查网络连接和授权状态')
  } finally {
    loading.value = false
  }
}

const handleAutoFetch = async () => {
  try {
    const result = await ElMessageBox.confirm(
      '将自动从阿里巴巴获取最新的商品列表，系统会先分析总商品数量，然后获取所有必要的页面，这可能需要几分钟时间。是否继续？',
      '确认自动获取',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    if (result === 'confirm') {
      autoFetching.value = true
      try {
        const response = await axios.post('/api/v1/alibaba-product/auto-fetch', {}, {
          params: {
            status: searchForm.status,
            display: searchForm.display
          }
        })
        
        if (response.data.success) {
          ElMessage.success(response.data.message)
          await loadProductList() // 重新加载列表
        } else {
          ElMessage.error(response.data.error || '自动获取失败')
        }
      } catch (error) {
        console.error('自动获取商品失败:', error)
        ElMessage.error('自动获取失败，请检查网络连接和授权状态')
      } finally {
        autoFetching.value = false
      }
    }
  } catch {
    // 用户取消
  }
}

const refreshList = () => {
  loadProductList()
}

const handleSearch = () => {
  pagination.current_page = 1
  loadProductList()
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.current_page = 1
  loadProductList()
}

const handleSizeChange = (val) => {
  pagination.page_size = val
  pagination.current_page = 1
  loadProductList()
}

const handleCurrentChange = (val) => {
  pagination.current_page = val
  loadProductList()
}

const handleSelectionChange = (val) => {
  selectedProducts.value = val
}

const handleViewDetail = (row) => {
  selectedProduct.value = row
  detailDialogVisible.value = true
}

const handleViewPerformance = (row) => {
  // 跳转到产品表现页面，传递产品ID
  router.push({
    path: '/ai-report/product-performance',
    query: { product_ids: row.product_id }
  })
}

const handleOpenProduct = (row) => {
  if (row.product_url) {
    window.open(row.product_url, '_blank')
  }
}

// 工具方法
const getStatusType = (status) => {
  const typeMap = {
    approved: 'success',
    auditing: 'warning',
    tbd: 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    approved: '审核通过',
    auditing: '审核中',
    tbd: '审核退回'
  }
  return textMap[status] || status
}

const getKeywordTags = (keywords) => {
  if (!keywords) return []
  return keywords.split(', ').slice(0, 3) // 只显示前3个关键词
}

const formatDate = (dateStr) => {
  return formatDateTime(dateStr) || '-'
}

// 生命周期
onMounted(() => {
  loadProductList()
})
</script>

<style scoped>
.products {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-title {
  font-weight: 500;
  color: #303133;
  line-height: 1.4;
}

.product-keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.product-keywords .el-tag {
  font-size: 12px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.product-detail {
  max-height: 600px;
  overflow-y: auto;
}

.product-image {
  margin-top: 20px;
  text-align: center;
}

.product-image h4 {
  margin-bottom: 10px;
  color: #303133;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .header-actions {
    justify-content: center;
  }
}
</style>
