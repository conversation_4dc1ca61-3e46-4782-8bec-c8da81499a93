<template>
  <el-dialog
    v-model="dialogVisible"
    title="从 Google Ads 导入关键词"
    width="700px"
    :before-close="handleClose"
  >
    <div class="google-ads-content">
      <!-- 配置选择 -->
      <div class="config-section">
        <h4>选择 Google Ads 配置</h4>
        <div class="config-selector">
          <el-select
            v-model="selectedConfigId"
            placeholder="请选择 Google Ads 配置"
            style="flex: 1"
            @change="handleConfigChange"
          >
            <el-option
              v-for="config in configList"
              :key="config.id"
              :label="config.config_name"
              :value="config.id"
            >
              <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
              <span>{{ config.config_name }}</span>
                <div style="display: flex; align-items: center; gap: 8px;">
                  <el-tag 
                    :type="getOAuthStatusType(config.authorization_status, config.token_expired)"
                    size="small"
                  >
                    {{ getOAuthStatusText(config.authorization_status, config.token_expired) }}
                  </el-tag>
                  <span style="color: #8492a6; font-size: 12px">
                {{ config.customer_id }}
              </span>
                </div>
              </div>
            </el-option>
          </el-select>
          <el-button @click="loadConfigs" icon="el-icon-refresh" :loading="configsLoading">
            刷新配置
          </el-button>
          <el-button @click="showConfigDialog = true" icon="el-icon-setting">
            管理配置
          </el-button>
          <el-button 
            @click="testConnection" 
            :loading="testing"
            :disabled="!selectedConfigId"
            icon="el-icon-connection"
          >
            测试连接
          </el-button>
        </div>

        <!-- 选中配置的OAuth状态详情 -->
        <div v-if="selectedConfigId && selectedConfig" class="selected-config-info">
          <div class="config-oauth-status">
            <span class="status-label">OAuth状态：</span>
            <el-tag 
              :type="getOAuthStatusType(selectedConfig.authorization_status, selectedConfig.token_expired)"
              size="medium"
            >
              {{ getOAuthStatusText(selectedConfig.authorization_status, selectedConfig.token_expired) }}
            </el-tag>
            <span v-if="selectedConfig.last_auth_time" class="auth-time">
              最后授权：{{ formatAuthTime(selectedConfig.last_auth_time) }}
            </span>
          </div>
          
          <!-- 授权操作按钮 -->
          <div v-if="selectedConfig.authorization_status !== 'authorized'" class="oauth-actions">
            <el-alert
              type="warning"
              :title="`配置 '${selectedConfig.config_name}' 尚未完成OAuth授权`"
              description="请先完成OAuth授权才能使用Google Ads API导入功能"
              show-icon
              :closable="false"
              style="margin: 10px 0;"
            />
            <el-button 
              type="warning" 
              @click="startQuickOAuth"
              :loading="quickAuthLoading"
              icon="el-icon-key"
            >
              {{ selectedConfig.authorization_status === 'pending' ? '立即授权' : '重新授权' }}
            </el-button>
          </div>
          
          <!-- 令牌过期提醒 -->
          <div v-else-if="isTokenExpired(selectedConfig)" class="oauth-actions">
            <el-alert
              type="warning"
              title="访问令牌已过期"
              description="请刷新访问令牌以继续使用Google Ads API"
              show-icon
              :closable="false"
              style="margin: 10px 0;"
            />
            <el-button 
              @click="refreshQuickToken"
              :loading="quickRefreshLoading"
              icon="el-icon-refresh"
            >
              刷新令牌
            </el-button>
          </div>
        </div>
      </div>

      <!-- 导入参数 -->
      <div v-if="selectedConfigId && selectedConfig && selectedConfig.authorization_status === 'authorized' && !isTokenExpired(selectedConfig)" class="import-params">
        <h4>导入参数</h4>
        <el-form :model="importForm" :rules="rules" ref="formRef" label-width="120px">
          <el-form-item label="种子关键词" prop="seed_keywords">
            <el-input
              v-model="seedKeywordsText"
              type="textarea"
              :rows="3"
              placeholder="请输入种子关键词，每行一个或用逗号分隔"
            />
            <div class="help-text">
              <small class="text-muted">
                输入相关的种子关键词，Google 会基于这些词生成相关关键词建议
              </small>
            </div>
          </el-form-item>

          <el-form-item label="网站URL" prop="url">
            <el-input
              v-model="importForm.url"
              placeholder="https://example.com"
            />
            <div class="help-text">
              <small class="text-muted">
                可选：输入网站URL，Google 会分析网站内容生成相关关键词
              </small>
            </div>
          </el-form-item>

          <el-form-item label="语言" prop="language_code">
            <el-select v-model="importForm.language_code" placeholder="选择语言">
              <el-option label="简体中文" value="zh" />
              <el-option label="繁体中文" value="zh-TW" />
              <el-option label="英文" value="en" />
              <el-option label="日文" value="ja" />
              <el-option label="韩文" value="ko" />
            </el-select>
          </el-form-item>

          <el-form-item label="地理位置" prop="location_ids">
            <el-select
              v-model="importForm.location_ids"
              multiple
              placeholder="选择目标地区"
              style="width: 100%"
            >
              <el-option label="中国" :value="2156" />
              <el-option label="美国" :value="2840" />
              <el-option label="英国" :value="2826" />
              <el-option label="日本" :value="2392" />
              <el-option label="韩国" :value="2410" />
              <el-option label="德国" :value="2276" />
              <el-option label="法国" :value="2250" />
              <el-option label="加拿大" :value="2124" />
              <el-option label="澳大利亚" :value="2036" />
            </el-select>
          </el-form-item>

          <el-form-item label="获取数量" prop="page_size">
            <el-input-number
              v-model="importForm.page_size"
              :min="10"
              :max="1000"
              :step="10"
              style="width: 100%"
            />
            <div class="help-text">
              <small class="text-muted">
                每次获取的关键词数量，建议不超过500条
              </small>
            </div>
          </el-form-item>

          <el-form-item label="包含成人内容">
            <el-switch
              v-model="importForm.include_adult_keywords"
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 未授权提示 -->
      <div v-else-if="selectedConfigId && selectedConfig && selectedConfig.authorization_status !== 'authorized'" class="auth-required-notice">
        <el-empty
          image="https://shadow.elemecdn.com/app/element/hamburger.9cf7b091-55e9-11e9-a976-7f4d0b07eef6.png"
          image-size="100"
          description="请先完成OAuth授权"
        >
          <el-button type="primary" @click="startQuickOAuth" :loading="quickAuthLoading">
            开始授权
          </el-button>
        </el-empty>
      </div>

      <!-- 导入进度 -->
      <div v-if="importProgress.show" class="progress-section">
        <h4>导入进度</h4>
        <div class="progress-info">
          <el-progress
            :percentage="importProgress.percentage"
            :status="importProgress.status"
            :stroke-width="10"
          />
          <p class="progress-text">{{ importProgress.text }}</p>
          <div v-if="importProgress.result" class="progress-result">
            <el-tag type="success">成功: {{ importProgress.result.success_count }}</el-tag>
            <el-tag type="danger" style="margin-left: 10px">失败: {{ importProgress.result.failed_count }}</el-tag>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleImport" 
          :loading="importing"
          :disabled="!selectedConfigId || (!importForm.seed_keywords?.length && !importForm.url)"
        >
          开始导入
        </el-button>
      </span>
    </template>

    <!-- Google Ads 配置管理对话框 -->
    <GoogleAdsConfigDialog
      v-model:visible="showConfigDialog"
      @success="loadConfigs"
    />
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { keywordService, googleAdsConfigService } from '@/services/keyword'
import GoogleAdsConfigDialog from './GoogleAdsConfigDialog.vue'

export default {
  name: 'GoogleAdsImportDialog',
  components: {
    GoogleAdsConfigDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    const formRef = ref()
    const importing = ref(false)
    const testing = ref(false)
    const configList = ref([])
    const selectedConfigId = ref(null)
    const showConfigDialog = ref(false)
    const seedKeywordsText = ref('')
    
    // OAuth相关状态
    const quickAuthLoading = ref(false)
    const quickRefreshLoading = ref(false)
    const configsLoading = ref(false)

    // 计算属性
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })

    // 选中的配置
    const selectedConfig = computed(() => {
      return configList.value.find(config => config.id === selectedConfigId.value)
    })

    // 导入表单
    const importForm = reactive({
      seed_keywords: [],
      url: '',
      language_code: 'zh',
      location_ids: [2156], // 默认中国
      page_size: 100,
      include_adult_keywords: false
    })

    // 导入进度
    const importProgress = reactive({
      show: false,
      percentage: 0,
      status: '',
      text: '',
      result: null
    })

    // 表单验证规则
    const rules = {
      seed_keywords: [
        {
          validator: (rule, value, callback) => {
            if (!importForm.seed_keywords?.length && !importForm.url) {
              callback(new Error('请输入种子关键词或网站URL'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ],
      language_code: [
        { required: true, message: '请选择语言', trigger: 'change' }
      ],
      location_ids: [
        { required: true, message: '请选择地理位置', trigger: 'change' }
      ],
      page_size: [
        { required: true, message: '请输入获取数量', trigger: 'blur' }
      ]
    }

    // OAuth状态相关方法
    const getOAuthStatusType = (status, tokenExpired = false) => {
      // 如果已授权但令牌过期，显示警告状态
      if (status === 'authorized' && tokenExpired === true) {
        return 'warning'
      }
      switch (status) {
        case 'authorized': return 'success'
        case 'pending': return 'warning'
        case 'expired': return 'danger'
        case 'failed': return 'danger'
        default: return 'info'
      }
    }

    const getOAuthStatusText = (status, tokenExpired = false) => {
      // 如果已授权但令牌过期，显示过期状态
      if (status === 'authorized' && tokenExpired === true) {
        return '令牌已过期'
      }
      switch (status) {
        case 'authorized': return '已授权'
        case 'pending': return '待授权'
        case 'expired': return '已过期'
        case 'failed': return '授权失败'
        default: return '未知'
      }
    }

    const formatAuthTime = (timeStr) => {
      if (!timeStr) return ''
      try {
        const date = new Date(timeStr)
        return date.toLocaleString('zh-CN')
      } catch (e) {
        return timeStr
      }
    }

    // 检查令牌是否过期
    const isTokenExpired = (config) => {
      // 优先使用后端返回的token_expired字段
      if (config && typeof config.token_expired === 'boolean') {
        return config.token_expired
      }
      
      // 备用逻辑：检查token_expiry时间
      if (!config || !config.token_expiry) return false
      try {
        const expiry = new Date(config.token_expiry)
        return expiry < new Date()
      } catch (e) {
        return false
      }
    }

    // 快速OAuth授权
    const startQuickOAuth = async () => {
      if (!selectedConfig.value) return
      
      try {
        quickAuthLoading.value = true
        const result = await googleAdsConfigService.startOAuthAuthorization(selectedConfig.value.id)
        
        // 打开授权页面
        const authWindow = window.open(
          result.authorization_url,
          'google_oauth',
          'width=600,height=700,scrollbars=yes,resizable=yes'
        )

        // 监听授权窗口关闭
        const checkClosed = setInterval(() => {
          if (authWindow.closed) {
            clearInterval(checkClosed)
            // 授权窗口关闭后，刷新OAuth状态
            refreshSelectedConfigStatus()
          }
        }, 1000)

        ElMessage.info('已打开授权页面，请完成Google账户授权')
      } catch (error) {
        console.error('启动OAuth授权失败:', error)
        ElMessage.error(error.response?.data?.detail || '启动授权失败')
      } finally {
        quickAuthLoading.value = false
      }
    }

    // 快速刷新令牌
    const refreshQuickToken = async () => {
      if (!selectedConfig.value) return
      
      try {
        quickRefreshLoading.value = true
        const result = await googleAdsConfigService.refreshOAuthToken(selectedConfig.value.id)
        
        if (result.success) {
          ElMessage.success('令牌刷新成功')
          await refreshSelectedConfigStatus()
        }
      } catch (error) {
        console.error('刷新令牌失败:', error)
        ElMessage.error(error.response?.data?.detail || '刷新令牌失败')
      } finally {
        quickRefreshLoading.value = false
      }
    }

    // 刷新选中配置的OAuth状态
    const refreshSelectedConfigStatus = async () => {
      if (!selectedConfig.value) return
      
      try {
        const status = await googleAdsConfigService.getOAuthStatus(selectedConfig.value.id)
        
        // 更新configList中对应配置的状态
        const configIndex = configList.value.findIndex(config => config.id === selectedConfig.value.id)
        if (configIndex !== -1) {
          Object.assign(configList.value[configIndex], {
            authorization_status: status.authorization_status,
            last_auth_time: status.last_auth_time,
            token_expiry: status.token_expiry,
            token_expired: status.token_expired,
            has_refresh_token: status.has_refresh_token
          })
        }

        if (status.authorization_status === 'authorized' && !status.token_expired) {
          ElMessage.success('OAuth授权成功！现在可以使用Google Ads API导入功能')
        }
      } catch (error) {
        console.error('获取OAuth状态失败:', error)
        ElMessage.error('获取OAuth状态失败: ' + (error.response?.data?.detail || error.message))
      }
    }

    // 监听种子关键词文本变化
    watch(() => seedKeywordsText.value, (newText) => {
      if (newText) {
        // 按行分割或按逗号分割
        const keywords = newText
          .split(/[\n,]/)
          .map(k => k.trim())
          .filter(k => k.length > 0)
        importForm.seed_keywords = keywords
      } else {
        importForm.seed_keywords = []
      }
    })

    // 加载配置列表
    const loadConfigs = async () => {
      configsLoading.value = true
      try {
        console.log('开始加载Google Ads配置列表...')
        const configs = await googleAdsConfigService.getConfigs()
        console.log('获取到的配置:', configs)
        
        // 确保configs是数组
        const configArray = Array.isArray(configs) ? configs : []
        console.log('配置数组长度:', configArray.length)
        
        // 为每个配置获取OAuth状态
        for (const config of configArray) {
          try {
            console.log(`获取配置${config.id}的OAuth状态...`)
            const oauthStatus = await googleAdsConfigService.getOAuthStatus(config.id)
            console.log(`配置${config.id}的OAuth状态:`, oauthStatus)
            
            // 更新配置的OAuth相关字段
            config.authorization_status = oauthStatus.authorization_status
            config.last_auth_time = oauthStatus.last_auth_time
            config.token_expiry = oauthStatus.token_expiry
            config.token_expired = oauthStatus.token_expired
            config.has_refresh_token = oauthStatus.has_refresh_token
          } catch (error) {
            console.error(`获取配置${config.id}的OAuth状态失败:`, error)
            config.authorization_status = 'pending'
            config.token_expired = false
            config.has_refresh_token = false
          }
        }
        
        configList.value = configArray
        console.log('配置列表加载完成，共', configArray.length, '个配置')
        
        // 如果有配置但没有选中任何配置，自动选择第一个
        if (configArray.length > 0 && !selectedConfigId.value) {
          selectedConfigId.value = configArray[0].id
          console.log('自动选择第一个配置:', configArray[0].config_name)
        }
      } catch (error) {
        console.error('加载配置列表失败:', error)
        ElMessage.error('加载配置列表失败: ' + (error.response?.data?.detail || error.message))
        // 确保在出错时也设置为空数组
        configList.value = []
      } finally {
        configsLoading.value = false
      }
    }

    // 配置变化处理
    const handleConfigChange = async (configId) => {
      if (configId) {
        // 配置改变时刷新OAuth状态
        await refreshSelectedConfigStatus()
      }
    }

    // 测试连接
    const testConnection = async () => {
      if (!selectedConfigId.value) {
        ElMessage.warning('请先选择配置')
        return
      }

      try {
        testing.value = true
        const result = await googleAdsConfigService.testConnection(selectedConfigId.value)
        
        if (result.success) {
          ElMessage.success('连接测试成功')
        } else {
          ElMessage.error(result.message || '连接测试失败')
        }
      } catch (error) {
        console.error('连接测试失败:', error)
        ElMessage.error('连接测试失败')
      } finally {
        testing.value = false
      }
    }

    // 开始导入
    const handleImport = async () => {
      // 检查授权状态
      if (!selectedConfig.value || selectedConfig.value.authorization_status !== 'authorized') {
        ElMessage.error('请先完成OAuth授权')
        return
      }

      // 检查令牌是否过期
      if (isTokenExpired(selectedConfig.value)) {
        ElMessage.error('访问令牌已过期，请刷新令牌')
        return
      }

      try {
        // 验证表单
        await formRef.value.validate()

        importing.value = true
        
        // 显示进度
        importProgress.show = true
        importProgress.percentage = 20
        importProgress.status = ''
        importProgress.text = '正在从 Google Ads API 获取关键词...'
        importProgress.result = null

        // 准备导入数据
        const importData = {
          seed_keywords: importForm.seed_keywords.length > 0 ? importForm.seed_keywords : null,
          url: importForm.url || null,
          language_code: importForm.language_code,
          location_ids: importForm.location_ids,
          page_size: importForm.page_size,
          include_adult_keywords: importForm.include_adult_keywords
        }

        // 调用导入API
        const result = await keywordService.importFromGoogleAds(selectedConfigId.value, importData)

        // 更新进度
        importProgress.percentage = 100
        importProgress.status = 'success'
        importProgress.text = '导入完成！'
        importProgress.result = {
          success_count: result.success_count,
          failed_count: result.failed_count
        }

        ElMessage.success(`导入完成！成功 ${result.success_count} 条，失败 ${result.failed_count} 条`)
        
        // 延迟关闭
        setTimeout(() => {
          emit('success')
        }, 2000)
      } catch (error) {
        console.error('导入失败:', error)
        importing.value = false
        importProgress.show = false
        
        if (error.response?.data?.detail) {
          ElMessage.error(error.response.data.detail)
        } else {
          ElMessage.error('导入失败')
        }
      } finally {
        importing.value = false
      }
    }

    // 关闭对话框
    const handleClose = () => {
      // 重置状态
      selectedConfigId.value = null
      seedKeywordsText.value = ''
      importProgress.show = false
      importProgress.percentage = 0
      importProgress.result = null
      
      // 重置表单
      Object.assign(importForm, {
        seed_keywords: [],
        url: '',
        language_code: 'zh',
        location_ids: [2156],
        page_size: 100,
        include_adult_keywords: false
      })
      
      emit('update:visible', false)
    }

    // 监听对话框打开
    watch(() => props.visible, (visible, oldVisible) => {
      console.log('对话框状态变化:', { visible, oldVisible })
      if (visible && !oldVisible) {
        console.log('对话框首次打开，开始加载配置...')
        // 清空当前状态
        configList.value = []
        selectedConfigId.value = null
        // 加载配置
        loadConfigs()
      }
    }, { immediate: true })

    // 检查OAuth成功回调
    const checkOAuthSuccess = () => {
      const urlParams = new URLSearchParams(window.location.search)
      if (urlParams.get('oauth_success') === '1') {
        ElMessage.success('OAuth授权成功！')
        // 清除URL参数
        window.history.replaceState({}, document.title, window.location.pathname)
        // 刷新配置列表
        loadConfigs()
      }
    }

    onMounted(() => {
      checkOAuthSuccess()
    })

    return {
      formRef,
      importing,
      testing,
      configList,
      selectedConfigId,
      selectedConfig,
      showConfigDialog,
      seedKeywordsText,
      quickAuthLoading,
      quickRefreshLoading,
      configsLoading,
      dialogVisible,
      importForm,
      importProgress,
      rules,
      getOAuthStatusType,
      getOAuthStatusText,
      formatAuthTime,
      isTokenExpired,
      startQuickOAuth,
      refreshQuickToken,
      loadConfigs,
      handleConfigChange,
      testConnection,
      handleImport,
      handleClose
    }
  }
}
</script>

<style scoped>
.google-ads-content {
  min-height: 400px;
}

.config-section {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.config-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-weight: 600;
}

.config-selector {
  display: flex;
  gap: 10px;
  align-items: center;
}

.selected-config-info {
  margin-top: 15px;
  padding: 12px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.config-oauth-status {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.status-label {
  font-weight: 500;
  color: #606266;
}

.auth-time {
  color: #909399;
  font-size: 12px;
}

.oauth-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.auth-required-notice {
  margin: 30px 0;
  text-align: center;
}

.import-params {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fff;
}

.import-params h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-weight: 600;
}

.progress-section {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #f9f9f9;
}

.progress-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-weight: 600;
}

.progress-info {
  text-align: center;
}

.progress-text {
  margin: 10px 0;
  color: #606266;
}

.progress-result {
  margin-top: 10px;
}

.help-text {
  margin-top: 5px;
}

.text-muted {
  color: #909399;
}

/* 修复选择器下拉选项的布局 */
:deep(.el-select-dropdown__item) {
  padding: 8px 20px;
  line-height: normal;
}

:deep(.el-select-dropdown__item > div) {
  width: 100%;
}

/* 确保tag在选择器中正确显示 */
.el-select .el-tag {
  margin: 0 4px 0 0;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .config-selector {
    flex-direction: column;
    align-items: stretch;
}

  .config-selector .el-select {
    margin-bottom: 10px;
  }
  
  .oauth-actions {
    flex-direction: column;
    align-items: stretch;
  }
}
</style> 