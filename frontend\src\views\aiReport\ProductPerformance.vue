<template>
  <div class="product-performance">
    <div class="page-header">
      <h2>产品表现</h2>
      <p>查看产品在阿里国际站的表现数据，包括曝光、点击、访客等关键指标</p>
    </div>

    <!-- 筛选条件 -->
    <el-card style="margin-bottom: 20px;">
      <template #header>
        <span>筛选条件</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8">
          <div class="filter-item">
            <label>统计周期：</label>
            <el-select v-model="filters.statisticsType" @change="handleStatisticsTypeChange">
              <el-option label="日" value="day" />
              <el-option label="周" value="week" />
              <el-option label="月" value="month" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="8">
          <div class="filter-item">
            <label>时间范围：</label>
            <!-- 当统计周期为"日"时，使用单日期选择器 -->
            <el-date-picker
              v-if="filters.statisticsType === 'day'"
              v-model="filters.singleDate"
              type="date"
              placeholder="选择日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDateForMain"
              @change="handleSingleDateChange"
            />
            <!-- 当统计周期为"周"时，使用周选择器 -->
            <el-date-picker
              v-else-if="filters.statisticsType === 'week'"
              v-model="filters.singleDate"
              type="week"
              placeholder="选择周"
              format="YYYY 第 ww 周"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDateForMain"
              @change="handleSingleDateChange"
            />
            <!-- 当统计周期为"月"时，使用月选择器 -->
            <el-date-picker
              v-else-if="filters.statisticsType === 'month'"
              v-model="filters.singleDate"
              type="month"
              placeholder="选择月份"
              format="YYYY年MM月"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDateForMain"
              @change="handleSingleDateChange"
            />
            <!-- 其他情况使用日期范围选择器（保留兼容性） -->
            <el-date-picker
              v-else
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDateForMain"
              @change="handleDateRangeChange"
            />
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="8">
          <div class="filter-item">
            <label>产品选择：</label>
            <el-select
              v-model="filters.productIds"
              multiple
              filterable
              remote
              reserve-keyword
              placeholder="搜索并选择产品"
              style="width: 100%"
              :remote-method="searchProducts"
              :loading="productSearchLoading"
              @change="handleProductChange"
              @focus="handleProductFocus"
              @visible-change="handleSelectVisibleChange"
            >
              <el-option
                v-for="product in productList"
                :key="product.product_id"
                :label="`${product.product_name} (ID: ${product.product_id})`"
                :value="product.product_id"
              >
                <div style="display: flex; justify-content: space-between; align-items: center;">
                  <span style="flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{ product.product_name }}</span>
                  <span style="color: #8492a6; font-size: 12px; margin-left: 8px;">ID: {{ product.product_id }}</span>
                </div>
              </el-option>
              
              <!-- 加载更多选项 -->
              <el-option
                v-if="productPagination.hasMore && !productSearchLoading"
                :key="'load-more'"
                value=""
                disabled
                style="text-align: center; color: #409eff; cursor: pointer;"
                @click.stop="loadMoreProducts"
              >
                <span style="color: #409eff;">点击加载更多...</span>
              </el-option>
              
              <!-- 加载中选项 -->
              <el-option
                v-if="productSearchLoading && productList.length > 0"
                :key="'loading'"
                value=""
                disabled
                style="text-align: center;"
              >
                <span style="color: #909399;">加载中...</span>
              </el-option>
              
              <!-- 无更多数据选项 -->
              <el-option
                v-if="!productPagination.hasMore && productList.length > 0 && !productSearchLoading"
                :key="'no-more'"
                value=""
                disabled
                style="text-align: center;"
              >
                <span style="color: #909399;">已加载全部产品</span>
              </el-option>
              
              <!-- 空数据提示 -->
              <el-option
                v-if="productList.length === 0 && !productSearchLoading"
                :key="'no-data'"
                value=""
                disabled
                style="text-align: center;"
              >
                <span style="color: #909399;">暂无产品数据</span>
              </el-option>
            </el-select>
          </div>
        </el-col>
      </el-row>
      
      <div style="text-align: right; margin-top: 10px;">
        <el-button type="primary" @click="loadData" :loading="loading">
          <el-icon><Search /></el-icon>
          查询
        </el-button>
        <el-button type="success" @click="showSyncDialog" :loading="syncLoading">
          <el-icon><Download /></el-icon>
          同步数据
        </el-button>
        <el-button @click="resetFilters">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </div>
    </el-card>

    <!-- 数据同步对话框 -->
    <el-dialog
      v-model="syncDialogVisible"
      title="同步产品表现数据"
      width="600px"
    >
      <el-form :model="syncForm" label-width="100px">
        <el-form-item label="统计周期">
          <el-select v-model="syncForm.statisticsType" style="width: 100%" @change="handleSyncStatisticsTypeChange">
            <el-option label="日" value="day" />
            <el-option label="周" value="week" />
            <el-option label="月" value="month" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <!-- 当统计周期为"日"时，使用单日期选择器 -->
          <el-date-picker
            v-if="syncForm.statisticsType === 'day'"
            v-model="syncForm.singleDate"
            type="date"
            placeholder="选择日期"
            format="YYYY年MM月DD日"
            value-format="YYYY-MM-DD"
            style="width: 100%"
            :disabled-date="getSyncDisabledDate"
          />
          <!-- 当统计周期为"周"时，使用周选择器 -->
          <el-date-picker
            v-else-if="syncForm.statisticsType === 'week'"
            v-model="syncForm.singleDate"
            type="week"
            placeholder="选择周"
            format="YYYY年第ww周"
            value-format="YYYY-MM-DD"
            style="width: 100%"
            :disabled-date="getSyncDisabledDate"
          />
          <!-- 当统计周期为"月"时，使用月选择器 -->
          <el-date-picker
            v-else-if="syncForm.statisticsType === 'month'"
            v-model="syncForm.singleDate"
            type="month"
            placeholder="选择月份"
            format="YYYY年MM月"
            value-format="YYYY-MM-DD"
            style="width: 100%"
            :disabled-date="getSyncDisabledDate"
          />
          <!-- 其他情况使用日期范围选择器（保留兼容性） -->
          <el-date-picker
            v-else
            v-model="syncForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY年MM月DD日"
            value-format="YYYY-MM-DD"
            style="width: 100%"
            :disabled-date="getSyncDisabledDate"
          />
        </el-form-item>
        <el-form-item label="产品选择">
          <el-select
            v-model="syncForm.productIds"
            multiple
            filterable
            remote
            reserve-keyword
            placeholder="搜索并选择要同步的产品，留空则同步所有产品"
            style="width: 100%"
            :remote-method="searchProducts"
            :loading="productSearchLoading"
            @visible-change="handleSelectVisibleChange"
          >
            <el-option
              v-for="product in productList"
              :key="product.product_id"
              :label="`${product.product_name} (ID: ${product.product_id})`"
              :value="product.product_id"
            >
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span style="flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{ product.product_name }}</span>
                <span style="color: #8492a6; font-size: 12px; margin-left: 8px;">ID: {{ product.product_id }}</span>
              </div>
            </el-option>
            
            <!-- 加载更多选项 -->
            <el-option
              v-if="productPagination.hasMore && !productSearchLoading"
              :key="'load-more'"
              value=""
              disabled
              style="text-align: center; color: #409eff; cursor: pointer;"
              @click.stop="loadMoreProducts"
            >
              <span style="color: #409eff;">点击加载更多...</span>
            </el-option>
            
            <!-- 加载中选项 -->
            <el-option
              v-if="productSearchLoading && productList.length > 0"
              :key="'loading'"
              value=""
              disabled
              style="text-align: center;"
            >
              <span style="color: #909399;">加载中...</span>
            </el-option>
            
            <!-- 无更多数据选项 -->
            <el-option
              v-if="!productPagination.hasMore && productList.length > 0 && !productSearchLoading"
              :key="'no-more'"
              value=""
              disabled
              style="text-align: center;"
            >
              <span style="color: #909399;">已加载全部产品</span>
            </el-option>
            
            <!-- 空数据提示 -->
            <el-option
              v-if="productList.length === 0 && !productSearchLoading"
              :key="'no-data'"
              value=""
              disabled
              style="text-align: center;"
            >
              <span style="color: #909399;">暂无产品数据</span>
            </el-option>
          </el-select>
          <div style="font-size: 12px; color: #909399; margin-top: 4px;">
            <span v-if="syncForm.productIds.length === 0">
              <el-icon style="color: #67c23a; margin-right: 4px;"><InfoFilled /></el-icon>
              当前将同步所有产品数据，系统会自动分批处理（每批20个产品）
            </span>
            <span v-else>
              已选择 {{ syncForm.productIds.length }} 个产品进行同步
            </span>
          </div>
        </el-form-item>
        <el-form-item label="强制更新">
          <el-switch v-model="syncForm.forceUpdate" />
          <div style="font-size: 12px; color: #909399; margin-top: 4px;">
            开启后将覆盖已有的数据
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="syncDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSync" :loading="syncLoading">
          开始同步
        </el-button>
      </template>
    </el-dialog>

    <!-- 统计概览 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <div class="stat-card">
            <div class="stat-number">{{ overview.totalImpression || 0 }}</div>
            <div class="stat-label">总曝光量</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <div class="stat-card">
            <div class="stat-number">{{ overview.totalClick || 0 }}</div>
            <div class="stat-label">总点击量</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <div class="stat-card">
            <div class="stat-number">{{ overview.totalVisitor || 0 }}</div>
            <div class="stat-label">总访客数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <div class="stat-card">
            <div class="stat-number">{{ overview.totalOrder || 0 }}</div>
            <div class="stat-label">总订单数</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 产品表现详情表格 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>产品表现详情</span>
          <el-button type="text" @click="exportData">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </template>
      
      <el-table 
        :data="tableData" 
        style="width: 100%"
        v-loading="loading"
        :default-sort="{ prop: 'impression', order: 'descending' }"
      >
        <el-table-column prop="productId" label="产品ID" width="120" sortable />
        <el-table-column prop="productName" label="产品名称" min-width="200" />
        <el-table-column prop="impression" label="曝光量" width="120" sortable />
        <el-table-column prop="click" label="点击量" width="120" sortable />
        <el-table-column prop="visitor" label="访客数" width="120" sortable />
        <el-table-column prop="order" label="订单数" width="120" sortable />
        <el-table-column prop="bookmark" label="收藏数" width="120" sortable />
        <el-table-column prop="compare" label="对比数" width="120" sortable />
        <el-table-column prop="share" label="分享数" width="120" sortable />
        <el-table-column prop="fb" label="反馈数" width="120" sortable />
        <el-table-column label="点击率" width="120">
          <template #default="scope">
            {{ calculateCTR(scope.row.click, scope.row.impression) }}
          </template>
        </el-table-column>
        <el-table-column label="转化率" width="120">
          <template #default="scope">
            {{ calculateConversionRate(scope.row.order, scope.row.visitor) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="viewDetails(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div style="text-align: right; margin-top: 20px;">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 产品详情弹窗 -->
    <el-dialog v-model="showDetails" title="产品表现详情" width="800px">
      <div v-if="selectedProduct">
        <h4>产品信息</h4>
        <p><strong>产品ID：</strong>{{ selectedProduct.productId }}</p>
        <p><strong>产品名称：</strong>{{ selectedProduct.productName }}</p>
        
        <h4 style="margin-top: 20px;">关键词来源</h4>
        <el-table :data="selectedProduct.keywordEffects" style="width: 100%">
          <el-table-column prop="keyword" label="关键词" />
          <el-table-column label="操作" width="100">
            <template #default>
              <el-button type="text" size="small">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download, InfoFilled } from '@element-plus/icons-vue'
import { productPerformanceApi, productsApi } from '../../services/alibaba'

export default defineComponent({
  name: 'ProductPerformance',
  components: {
    Search,
    Refresh,
    Download,
    InfoFilled
  },
  setup() {
    const loading = ref(false)
    const syncLoading = ref(false)
    const productSearchLoading = ref(false)
    const showDetails = ref(false)
    const selectedProduct = ref(null)
    const syncDialogVisible = ref(false)
    
    // 筛选条件
    const filters = ref({
      statisticsType: 'day',
      dateRange: [],
      singleDate: '',
      productIds: []
    })
    
    // 同步表单
    const syncForm = ref({
      statisticsType: 'day',
      dateRange: [],
      singleDate: '',
      productIds: [],
      forceUpdate: false
    })
    
    // 可用时间范围（按统计周期分别存储）
    const availableDateRange = ref({
      day: { startDate: '', endDate: '' },
      week: { startDate: '', endDate: '' },
      month: { startDate: '', endDate: '' }
    })
    
    // 产品列表
    const productList = ref([])
    
    // 产品分页状态
    const productPagination = ref({
      currentPage: 1,
      pageSize: 20,
      hasMore: true,
      total: 0
    })
    
    // 产品搜索状态
    const productSearchState = ref({
      keyword: '',
      isSearching: false
    })
    
    // 表格数据
    const tableData = ref([])
    const allTableData = ref([]) // 存储所有数据，用于分页
    
    // 统计概览
    const overview = computed(() => {
      return {
        totalImpression: allTableData.value.reduce((sum, item) => sum + (item.impression || 0), 0),
        totalClick: allTableData.value.reduce((sum, item) => sum + (item.click || 0), 0),
        totalVisitor: allTableData.value.reduce((sum, item) => sum + (item.visitor || 0), 0),
        totalOrder: allTableData.value.reduce((sum, item) => sum + (item.order || 0), 0)
      }
    })
    
    // 分页
    const pagination = ref({
      page: 1,
      size: 10, // 修改为10条一页
      total: 0
    })

    // 计算点击率
    const calculateCTR = (click, impression) => {
      if (!impression || impression === 0) return '0%'
      return ((click / impression) * 100).toFixed(2) + '%'
    }

    // 计算转化率
    const calculateConversionRate = (order, visitor) => {
      if (!visitor || visitor === 0) return '0%'
      return ((order / visitor) * 100).toFixed(2) + '%'
    }

    // 分页数据计算
    const updatePaginatedData = () => {
      const start = (pagination.value.page - 1) * pagination.value.size
      const end = start + pagination.value.size
      tableData.value = allTableData.value.slice(start, end)
      pagination.value.total = allTableData.value.length
    }

    // 加载数据
    const loadData = async () => {
      // 主页面查询本地数据库，根据统计周期设置合适的默认时间
      if (!filters.value.singleDate) {
        const today = new Date()
        
        if (filters.value.statisticsType === 'day') {
          // 日选择器：默认选择昨天
          const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
          filters.value.singleDate = yesterday.toISOString().split('T')[0]
        } else if (filters.value.statisticsType === 'week') {
          // 周选择器：默认选择上周的第一天（周日）
          const lastWeekStart = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
          const day = lastWeekStart.getDay()
          const diff = lastWeekStart.getDate() - day
          lastWeekStart.setDate(diff)
          filters.value.singleDate = lastWeekStart.toISOString().split('T')[0]
        } else if (filters.value.statisticsType === 'month') {
          // 月选择器：默认选择上月的第一天
          const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
          filters.value.singleDate = lastMonth.toISOString().split('T')[0]
        } else {
          // 其他情况：默认选择昨天
          const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
          filters.value.singleDate = yesterday.toISOString().split('T')[0]
        }
      }
      
      try {
        loading.value = true
        
        // 调用后端接口获取产品表现数据
        // 注意：只传递单个日期，不传递范围
        // 阿里巴巴API会根据statistics_type和stat_date自动处理统计维度
        const response = await productPerformanceApi.getPerformanceData({
          statistics_type: filters.value.statisticsType,
          start_date: filters.value.singleDate, // 实际上对应API的stat_date
          end_date: filters.value.singleDate, // 保持兼容性，后端会使用start_date
          product_ids: filters.value.productIds.join(',')
        })
        
        if (response.code === 200) {
          allTableData.value = response.data.map(item => ({
            productId: item.product_id,
            productName: item.product_name,
            impression: item.impression,
            click: item.click,
            visitor: item.visitor,
            order: item.order_count,
            bookmark: item.bookmark,
            compare: item.compare,
            share: item.share,
            fb: item.feedback,
            keywordEffects: []
          }))
          
          // 重置到第一页并更新分页数据
          pagination.value.page = 1
          updatePaginatedData()
        } else {
          ElMessage.error(response.message || '获取数据失败')
        }
      } catch (error) {
        console.error('获取产品表现数据失败:', error)
        ElMessage.error('获取产品表现数据失败')
      } finally {
        loading.value = false
      }
    }
    
    // 加载产品列表
    const loadProductList = async (params = {}, isLoadMore = false) => {
      try {
        productSearchLoading.value = true
        
        // 如果不是加载更多，重置分页
        if (!isLoadMore) {
          productPagination.value.currentPage = 1
          productPagination.value.hasMore = true
        }
        
        const response = await productsApi.getProductList({
          page_size: productPagination.value.pageSize,
          current_page: productPagination.value.currentPage,
          sync_to_db: false, // 仅获取列表，不同步到数据库
          ...params
        })
        
        if (response.success) {
          const newProducts = response.products || []
          
          if (isLoadMore) {
            // 加载更多：追加到现有列表，避免重复
            const existingIds = new Set(productList.value.map(p => p.product_id))
            const uniqueNewProducts = newProducts.filter(p => !existingIds.has(p.product_id))
            productList.value.push(...uniqueNewProducts)
          } else {
            // 新搜索或初始加载：替换列表
            productList.value = newProducts
          }
          
          // 更新分页状态
          productPagination.value.total = response.pagination?.total_item || 0
          productPagination.value.hasMore = newProducts.length === productPagination.value.pageSize && 
            productList.value.length < productPagination.value.total
          
          if (isLoadMore) {
            productPagination.value.currentPage++
          }
        } else {
          if (response.error && !response.error.includes('未找到有效的阿里巴巴授权令牌')) {
            ElMessage.error(response.error || '获取产品列表失败')
          }
          // 如果获取失败，标记为没有更多数据
          productPagination.value.hasMore = false
        }
      } catch (error) {
        console.error('获取产品列表失败:', error)
        if (error.response && error.response.status !== 401) {
          ElMessage.error('获取产品列表失败')
        }
        productPagination.value.hasMore = false
      } finally {
        productSearchLoading.value = false
      }
    }

    // 加载更多产品
    const loadMoreProducts = async () => {
      if (productPagination.value.hasMore && !productSearchLoading.value) {
        const searchParams = productSearchState.value.keyword
          ? { subject: productSearchState.value.keyword }
          : {}
        await loadProductList(searchParams, true)
      }
    }

    // 搜索产品
    const searchProducts = async (query) => {
      productSearchState.value.keyword = query || ''
      productSearchState.value.isSearching = !!query
      
      const searchParams = query
        ? { subject: query.trim() }
        : {}
      await loadProductList(searchParams, false)
    }

    // 产品选择器获得焦点时加载产品列表
    const handleProductFocus = async () => {
      if (productList.value.length === 0) {
        await loadProductList()
      }
    }

    // 选择器可见性变化处理
    const handleSelectVisibleChange = async (visible) => {
      if (visible && productList.value.length === 0) {
        await loadProductList()
      }
    }

    // 获取可用时间范围
    const loadDateRange = async (specificType = null) => {
      try {
        // 如果指定了特定类型，只获取该类型的时间范围
        if (specificType) {
          const response = await productPerformanceApi.getDateRange(specificType)
          if (response.code === 200) {
            availableDateRange.value[specificType] = {
              startDate: response.data.start_date || '',
              endDate: response.data.end_date || ''
            }
          }
        } else {
          // 获取所有三个维度的时间范围
          const types = ['day', 'week', 'month']
          for (const type of types) {
            try {
              const response = await productPerformanceApi.getDateRange(type)
              if (response.code === 200) {
                availableDateRange.value[type] = {
                  startDate: response.data.start_date || '',
                  endDate: response.data.end_date || ''
                }
              }
            } catch (error) {
              console.error(`获取${type}时间范围失败:`, error)
              // 如果获取失败，设置默认范围
              const endDate = new Date()
              const startDate = new Date(endDate.getTime() - (type === 'day' ? 30 : type === 'week' ? 90 : 365) * 24 * 60 * 60 * 1000)
              availableDateRange.value[type] = {
                startDate: startDate.toISOString().split('T')[0],
                endDate: endDate.toISOString().split('T')[0]
              }
            }
          }
        }
      } catch (error) {
        console.error('获取时间范围失败:', error)
      }
    }

    // 统计周期变化
    const handleStatisticsTypeChange = async () => {
      // 清空时间选择
      filters.value.singleDate = ''
      
      // 根据统计周期设置合适的默认时间
      const today = new Date()
      
      if (filters.value.statisticsType === 'day') {
        // 日选择器：默认选择昨天
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
        filters.value.singleDate = yesterday.toISOString().split('T')[0]
      } else if (filters.value.statisticsType === 'week') {
        // 周选择器：默认选择上周的第一天（周日）
        const lastWeekStart = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
        const day = lastWeekStart.getDay()
        const diff = lastWeekStart.getDate() - day
        lastWeekStart.setDate(diff)
        filters.value.singleDate = lastWeekStart.toISOString().split('T')[0]
      } else if (filters.value.statisticsType === 'month') {
        // 月选择器：默认选择上月的第一天
        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
        filters.value.singleDate = lastMonth.toISOString().split('T')[0]
      } else {
        // 其他情况：默认选择昨天
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
        filters.value.singleDate = yesterday.toISOString().split('T')[0]
      }
    }

    // 时间范围变化
    const handleDateRangeChange = () => {
      // 时间范围变化时可以触发其他逻辑
    }

    // 单日期变化
    const handleSingleDateChange = () => {
      // 单日期变化时可以触发其他逻辑
    }

    // 产品选择变化
    const handleProductChange = () => {
      // 产品选择变化时可以触发其他逻辑
    }

    // 重置筛选条件
    const resetFilters = () => {
      filters.value = {
        statisticsType: 'day',
        singleDate: '',
        productIds: []
      }
      allTableData.value = []
      tableData.value = []
      pagination.value.page = 1
      pagination.value.total = 0
      
      // 重置产品相关状态
      productList.value = []
      productPagination.value = {
        currentPage: 1,
        pageSize: 20,
        hasMore: true,
        total: 0
      }
      productSearchState.value = {
        keyword: '',
        isSearching: false
      }
      
      // 根据统计周期设置合适的默认时间（日选择器默认昨天）
      const today = new Date()
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      filters.value.singleDate = yesterday.toISOString().split('T')[0]
    }

    // 分页大小变化
    const handleSizeChange = (val) => {
      pagination.value.size = val
      pagination.value.page = 1 // 重置到第一页
      updatePaginatedData()
    }

    // 当前页变化
    const handleCurrentChange = (val) => {
      pagination.value.page = val
      updatePaginatedData()
    }

    // 查看详情
    const viewDetails = (row) => {
      selectedProduct.value = row
      showDetails.value = true
    }

    // 导出数据
    const exportData = () => {
      ElMessage.info('导出功能开发中...')
    }

    // 显示数据同步对话框
    const showSyncDialog = async () => {
      // 初始化同步表单
      syncForm.value = {
        statisticsType: filters.value.statisticsType,
        singleDate: '', // 先清空，等获取API时间范围后再设置
        productIds: [...filters.value.productIds],
        forceUpdate: false
      }
      
      // 获取同步所需的时间范围限制
      await loadDateRange(syncForm.value.statisticsType)
      
      // 验证主页面的时间是否在API允许的范围内
      const currentRange = availableDateRange.value[syncForm.value.statisticsType]
      let validDate = null
      
      if (currentRange && currentRange.startDate && currentRange.endDate) {
        const mainPageDate = filters.value.singleDate
        if (mainPageDate) {
          const targetDate = new Date(mainPageDate)
          const startDate = new Date(currentRange.startDate)
          const endDate = new Date(currentRange.endDate)
          
          // 检查主页面日期是否在API允许范围内
          if (targetDate >= startDate && targetDate <= endDate) {
            validDate = mainPageDate
          }
        }
        
        // 如果主页面日期无效或为空，使用API范围内的最新日期
        if (!validDate) {
          validDate = currentRange.endDate
        }
      } else {
        // 如果API时间范围获取失败，使用当前日期
        validDate = new Date().toISOString().split('T')[0]
      }
      
      syncForm.value.singleDate = validDate
      syncDialogVisible.value = true
    }

    // 处理数据同步
    const handleSync = async () => {
      // 检查是否选择了日期
      if (!syncForm.value.singleDate) {
        ElMessage.warning('请选择日期')
        return
      }
      
      try {
        syncLoading.value = true
        
        // 显示同步开始提示
        if (syncForm.value.productIds.length === 0) {
          ElMessage.info('开始同步所有产品数据，系统将自动分批处理...')
        } else {
          ElMessage.info(`开始同步 ${syncForm.value.productIds.length} 个产品的数据...`)
        }
        
        // 调用同步API
        // 注意：只传递单个日期，不传递范围
        // 阿里巴巴API会根据statistics_type和stat_date自动处理统计维度
        const response = await productPerformanceApi.syncData({
          statistics_type: syncForm.value.statisticsType,
          start_date: syncForm.value.singleDate, // 实际上对应API的stat_date
          end_date: syncForm.value.singleDate, // 保持兼容性，后端会使用start_date
          product_ids: syncForm.value.productIds.join(','),
          force_update: syncForm.value.forceUpdate
        })
        
        if (response.code === 200) {
          const { products, performance_records: performanceRecords } = response.data
          let successMsg = `数据同步成功！`
          
          if (syncForm.value.productIds.length === 0) {
            successMsg += `系统自动检测并同步了 ${products} 个产品，共 ${performanceRecords} 条表现记录`
          } else {
            successMsg += `同步了 ${products} 个产品，${performanceRecords} 条表现记录`
          }
          
          if (products > 20) {
            successMsg += `（系统已自动分批处理，每批20个产品）`
          }
          
          ElMessage.success(successMsg)
          syncDialogVisible.value = false
          
          // 刷新当前页面数据
          await loadData()
          await loadProductList()
        } else {
          if (response.message && response.message.includes('需要配置产品ID')) {
            ElMessage.warning('当前账户还没有配置产品数据，请先添加产品后再进行同步')
          } else {
            ElMessage.error(response.message || '数据同步失败')
          }
        }
      } catch (error) {
        console.error('数据同步失败:', error)
        if (error.response && error.response.status === 400) {
          ElMessage.error(error.response.data.detail || '数据同步失败，请检查阿里国际站授权状态')
        } else {
          ElMessage.error('数据同步失败，请稍后重试')
        }
      } finally {
        syncLoading.value = false
      }
    }

    // 同步表单统计周期变化
    const handleSyncStatisticsTypeChange = async () => {
      // 清空同步表单时间选择
      syncForm.value.singleDate = ''
      
      // 获取新统计周期的时间范围限制
      await loadDateRange(syncForm.value.statisticsType)
      
      // 设置默认时间
      const currentRange = availableDateRange.value[syncForm.value.statisticsType]
      if (currentRange && currentRange.startDate && currentRange.endDate) {
        // 优先使用API范围内的最新日期
        syncForm.value.singleDate = currentRange.endDate
      } else {
        // 如果API时间范围获取失败，使用当前日期作为后备
        syncForm.value.singleDate = new Date().toISOString().split('T')[0]
      }
    }

    // 获取同步对话框的禁用日期逻辑
    const getSyncDisabledDate = (time) => {
      // 根据统计周期获取对应的可用时间范围
      const currentRange = availableDateRange.value[syncForm.value.statisticsType]
      if (!currentRange || !currentRange.startDate || !currentRange.endDate) {
        return false
      }
      
      const targetDate = new Date(time)
      const startDate = new Date(currentRange.startDate)
      const endDate = new Date(currentRange.endDate)
      
      if (syncForm.value.statisticsType === 'month') {
        // 月份选择器：比较年月
        const targetYear = targetDate.getFullYear()
        const targetMonth = targetDate.getMonth() // 0-11
        const startYear = startDate.getFullYear()
        const startMonth = startDate.getMonth()
        const endYear = endDate.getFullYear()
        const endMonth = endDate.getMonth()
        
        // 检查是否在可用范围内
        const isBeforeStart = targetYear < startYear || (targetYear === startYear && targetMonth < startMonth)
        const isAfterEnd = targetYear > endYear || (targetYear === endYear && targetMonth > endMonth)
        
        return isBeforeStart || isAfterEnd
      } else if (syncForm.value.statisticsType === 'week') {
        // 周选择器：比较周的开始日期
        const getWeekStart = (date) => {
          const d = new Date(date)
          const day = d.getDay()
          const diff = d.getDate() - day
          return new Date(d.setDate(diff))
        }
        
        const targetWeekStart = getWeekStart(targetDate)
        const startWeekStart = getWeekStart(startDate)
        const endWeekStart = getWeekStart(endDate)
        
        return targetWeekStart < startWeekStart || targetWeekStart > endWeekStart
      } else {
        // 日期选择器：精确比较日期
        return targetDate < startDate || targetDate > endDate
      }
    }

    // 主页面时间选择器的禁用日期逻辑
    const disabledDateForMain = (time) => {
      const today = new Date()
      today.setHours(0, 0, 0, 0) // 重置时间到当日开始
      const targetDate = new Date(time)
      targetDate.setHours(0, 0, 0, 0)
      
      if (filters.value.statisticsType === 'day') {
        // 日选择器：只能选择昨天及之前的日期（不能选择今天和未来）
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
        return targetDate > yesterday
      } else if (filters.value.statisticsType === 'week') {
        // 周选择器：只能选择上周及之前的周
        const getWeekStart = (date) => {
          const d = new Date(date)
          const day = d.getDay()
          const diff = d.getDate() - day // 周日为0，周一为1
          return new Date(d.setDate(diff))
        }
        
        const currentWeekStart = getWeekStart(today)
        const targetWeekStart = getWeekStart(targetDate)
        
        // 不能选择本周和未来的周
        return targetWeekStart >= currentWeekStart
      } else if (filters.value.statisticsType === 'month') {
        // 月选择器：只能选择上月及之前的月份
        const currentYear = today.getFullYear()
        const currentMonth = today.getMonth() // 0-11
        const targetYear = targetDate.getFullYear()
        const targetMonth = targetDate.getMonth()
        
        // 不能选择本月和未来的月份
        return targetYear > currentYear || (targetYear === currentYear && targetMonth >= currentMonth)
      } else {
        // 日期范围选择器：不能选择今天和未来日期
        return targetDate >= today
      }
    }

    onMounted(async () => {
      // 加载产品列表
      await loadProductList()
      
      // 根据统计周期设置合适的默认时间（日选择器默认昨天）
      const today = new Date()
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      filters.value.singleDate = yesterday.toISOString().split('T')[0]
      
      // 默认加载数据
      await loadData()
    })

    return {
      loading,
      syncLoading,
      productSearchLoading,
      showDetails,
      selectedProduct,
      syncDialogVisible,
      filters,
      syncForm,
      availableDateRange,
      productList,
      tableData,
      allTableData,
      overview,
      pagination,
      calculateCTR,
      calculateConversionRate,
      loadData,
      updatePaginatedData,
      handleStatisticsTypeChange,
      handleDateRangeChange,
      handleSingleDateChange,
      handleProductChange,
      resetFilters,
      handleSizeChange,
      handleCurrentChange,
      viewDetails,
      exportData,
      showSyncDialog,
      handleSync,
      searchProducts,
      handleProductFocus,
      handleSelectVisibleChange,
      loadMoreProducts,
      productPagination,
      productSearchState,
      getSyncDisabledDate,
      handleSyncStatisticsTypeChange,
      disabledDateForMain
    }
  }
})
</script>

<style scoped>
.product-performance {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.filter-item label {
  display: inline-block;
  width: 80px;
  text-align: right;
  margin-right: 10px;
  font-size: 14px;
  color: #606266;
}

.stat-card {
  text-align: center;
  padding: 20px 0;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@media (max-width: 768px) {
  .filter-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-item label {
    width: auto;
    margin-bottom: 5px;
  }
}
</style> 