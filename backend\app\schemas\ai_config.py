from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime


class AIConfigBase(BaseModel):
    """AI配置基础模式"""
    name: str = Field(..., description="配置名称")
    service_type: str = Field(..., description="服务类型，如DIFY")
    api_key: str = Field(..., description="API密钥")
    base_url: Optional[str] = Field(None, description="API基础URL")
    model_name: Optional[str] = Field(None, description="模型名称")
    max_tokens: int = Field(4000, description="最大token数")
    temperature: float = Field(0.7, description="创造性参数")
    is_active: bool = Field(True, description="是否启用")
    description: Optional[str] = Field(None, description="配置描述")


class AIConfigCreate(AIConfigBase):
    """AI配置创建模式"""
    pass


class AIConfigUpdate(BaseModel):
    """AI配置更新模式"""
    name: Optional[str] = Field(None, description="配置名称")
    service_type: Optional[str] = Field(None, description="服务类型")
    api_key: Optional[str] = Field(None, description="API密钥")
    base_url: Optional[str] = Field(None, description="API基础URL")
    model_name: Optional[str] = Field(None, description="模型名称")
    max_tokens: Optional[int] = Field(None, description="最大token数")
    temperature: Optional[float] = Field(None, description="创造性参数")
    is_active: Optional[bool] = Field(None, description="是否启用")
    description: Optional[str] = Field(None, description="配置描述")


class AIConfigResponse(AIConfigBase):
    """AI配置响应模式"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AIConfigList(BaseModel):
    """AI配置列表模式"""
    items: list[AIConfigResponse]
    total: int
    page: int
    size: int 