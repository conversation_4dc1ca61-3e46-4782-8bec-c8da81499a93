from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, DateTime
from sqlalchemy.orm import relationship
from app.db.session import Base
from app.utils.datetime_utils import utc_now

class User(Base):
    """用户模型"""
    __tablename__ = "user"
    
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100))
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)
    
    # 租户关联
    tenant_id = Column(String(50), ForeignKey("tenant.id"), nullable=False)
    tenant = relationship("Tenant")
    
    # 角色关联
    role_id = Column(Integer, ForeignKey("role.id"), nullable=True)
    role = relationship("Role")
    
    # 时间戳 - 使用UTC时间统一方案
    created_at = Column(DateTime(timezone=True), nullable=False, default=utc_now)
    updated_at = Column(DateTime(timezone=True), nullable=True, onupdate=utc_now)