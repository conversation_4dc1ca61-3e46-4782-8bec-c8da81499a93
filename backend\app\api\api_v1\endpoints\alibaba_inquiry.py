from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import date, timedelta

from app.api import deps
from app.models.user import User
from app.models.alibaba_auth import Ali<PERSON>baAuth
from app.core.config import settings
from app.schemas.alibaba_inquiry import (
    AlibabaDataPeriod,
    AlibabaTopIndustry,
    AlibabaInquiryPerformance,
    AlibabaInquirySummary,
    InquiryStatisticsRequest,
    InquiryStatisticsResponse,
    DashboardData,
    BatchUpdateRequest
)
from app.utils.datetime_utils import utc_now, to_iso_string
import logging

# 根据配置选择使用真实API还是模拟数据
if settings.ALIBABA_USE_REAL_API:
    from app.services.alibaba_inquiry_service_real import AlibabaInquiryServiceReal
    inquiry_service = AlibabaInquiryServiceReal()
    logger = logging.getLogger(__name__)
    logger.info("询盘统计API使用真实阿里巴巴API服务")
else:
    from app.services.alibaba_inquiry_service import AlibabaInquiryService
    inquiry_service = AlibabaInquiryService()
    logger = logging.getLogger(__name__)
    logger.info("询盘统计API使用模拟数据服务")

router = APIRouter()

@router.get("/data-periods", response_model=List[AlibabaDataPeriod])
def get_data_periods(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> List[AlibabaDataPeriod]:
    """
    获取当前用户的可用数据周期
    """
    try:
        periods = inquiry_service.get_data_periods(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id
        )
        return periods
    except Exception as e:
        logger.error(f"获取数据周期失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据周期失败: {str(e)}")

@router.get("/dashboard", response_model=DashboardData)
def get_dashboard_data(
    days: int = Query(30, description="查询天数", ge=1, le=365),
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> DashboardData:
    """
    获取仪表板数据
    """
    try:
        dashboard_data = inquiry_service.get_dashboard_data(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            days=days
        )
        return dashboard_data
    except Exception as e:
        logger.error(f"获取仪表板数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取仪表板数据失败: {str(e)}")

@router.get("/industries", response_model=List[AlibabaTopIndustry])
def get_top_industries(
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> List[AlibabaTopIndustry]:
    """
    获取TOP行业列表
    """
    try:
        industries = inquiry_service.get_top_industries(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            start_date=start_date,
            end_date=end_date
        )
        return industries
    except Exception as e:
        logger.error(f"获取TOP行业列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取TOP行业列表失败: {str(e)}")

@router.get("/performance", response_model=List[AlibabaInquiryPerformance])
def get_inquiry_performance(
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    industry_id: Optional[str] = Query(None, description="特定行业ID"),
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> List[AlibabaInquiryPerformance]:
    """
    获取询盘流量行业表现数据
    """
    try:
        performance_data = inquiry_service.get_inquiry_performance(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            start_date=start_date,
            end_date=end_date,
            industry_id=industry_id
        )
        return performance_data
    except Exception as e:
        logger.error(f"获取询盘流量行业表现数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取询盘流量行业表现数据失败: {str(e)}")

@router.get("/statistics", response_model=InquiryStatisticsResponse)
def get_inquiry_statistics(
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    industry_id: Optional[str] = Query(None, description="特定行业ID"),
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> InquiryStatisticsResponse:
    """
    获取完整的询盘统计数据
    """
    try:
        # 获取数据周期
        data_periods = inquiry_service.get_data_periods(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id
        )
        
        # 获取TOP行业列表
        top_industries = inquiry_service.get_top_industries(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            start_date=start_date,
            end_date=end_date
        )
        
        # 获取表现数据
        performance_data = inquiry_service.get_inquiry_performance(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            start_date=start_date,
            end_date=end_date,
            industry_id=industry_id
        )
        
        # 计算汇总统计
        summary = inquiry_service.calculate_summary(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            start_date=start_date,
            end_date=end_date,
            summary_type="custom"
        )
        
        return InquiryStatisticsResponse(
            data_periods=data_periods,
            top_industries=top_industries,
            performance_data=performance_data,
            summary=summary
        )
        
    except Exception as e:
        logger.error(f"获取询盘统计数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取询盘统计数据失败: {str(e)}")

@router.post("/sync", response_model=dict)
async def sync_data_from_alibaba(
    request: BatchUpdateRequest,
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> dict:
    """
    从阿里国际站同步数据
    """
    try:
        # 检查用户是否有阿里国际站授权
        auth_record = db.query(AlibabaAuth).filter(
            AlibabaAuth.user_id == current_user.id,
            AlibabaAuth.tenant_id == current_user.tenant_id,
            AlibabaAuth.is_active == True
        ).first()
        
        if not auth_record:
            raise HTTPException(
                status_code=400, 
                detail="请先完成阿里国际站授权"
            )
        
        # 检查token是否过期
        token_expires_at = auth_record.token_created_at + timedelta(seconds=auth_record.expires_in)
        if utc_now() > token_expires_at:
            raise HTTPException(
                status_code=400,
                detail="阿里国际站访问令牌已过期，请重新授权"
            )
        
        # 同步数据
        result = await inquiry_service.sync_data_from_alibaba(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            start_date=request.date_range.start_date,
            end_date=request.date_range.end_date,
            access_token=auth_record.access_token
        )
        
        logger.info(f"用户 {current_user.id} 同步阿里询盘数据成功: {result}")
        
        return {
            "message": "数据同步成功",
            "sync_result": result,
            "sync_time": to_iso_string(utc_now())
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步阿里询盘数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"同步数据失败: {str(e)}")

@router.get("/summary", response_model=AlibabaInquirySummary)
def get_summary(
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    summary_type: str = Query("custom", description="汇总类型"),
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> AlibabaInquirySummary:
    """
    获取汇总统计数据
    """
    try:
        summary = inquiry_service.calculate_summary(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            start_date=start_date,
            end_date=end_date,
            summary_type=summary_type
        )
        return summary
    except Exception as e:
        logger.error(f"获取汇总统计数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取汇总统计数据失败: {str(e)}") 