# 获取商品列表

商品查询

GET/POST

alibaba.icbu.product.list

描述：根据类目ID和商品名称查询商品概要信息。结果以修改时间倒序返回，支持分页，每页最多30个。每次调用都是独立的请求，不记录调用的上下文。

## 参数

|  名称  |  类型  |  是否必须  |  描述  |
| --- | --- | --- | --- |
|  id\_list  |  String\[\]  |  否  |  要批量查询的商品明文id，最多不超过20个id  |
|  owner\_member  |  String  |  否  |  商品负责人loginId  |
|  status  |  String  |  否  |  商品状态。approved:审核通过，auditing:审核中，tbd:审核退回  |
|  display  |  String  |  否  |  上下架状态。Y:上架，N:下架  |
|  category\_id  |  Number  |  否  |  类目ID  |
|  current\_page  |  Number  |  否  |  当前页  |
|  page\_size  |  Number  |  否  |  每页大小，最大30  |
|  subject  |  String  |  否  |  商品名称，支持模糊匹配  |
|  language  |  String  |  是  |  商品语种，目前只支持ENGLISH  |
|  group\_id3  |  Number  |  否  |  商品三级分组id，可选填。若填写-1 则表示取回的商品没有三级分组，不填入代表取回的商品不关心它的三级分组，填写对应的group id将返回这个分组下的商品  |
|  group\_id2  |  Number  |  否  |  商品二级分组id，可选填。若填写-1 则表示取回的商品没有二级分组，不填入代表取回的商品不关系它的二级分组，填写对应的group id将返回这个分组下的商品  |
|  group\_id1  |  Number  |  否  |  商品一级分组id，可选填。若填写0 则表示取回的商品没有一级分组，不填入代表取回的商品不关心它的一级分组，填写对应的group id将返回这个分组下的商品  |
|  id  |  Number  |  否  |  商品明文id  |
|  gmt\_modified\_to  |  String  |  否  |  最晚修改时间，格式yyyy-MM-dd HH:mm:ss  |
|  gmt\_modified\_from  |  String  |  否  |  最早修改时间，格式yyyy-MM-dd HH:mm:ss  |

## 响应参数

|  名称  |  类型  |  描述  |
| --- | --- | --- |
|  current\_page  |  Number  |  当前页  |
|  page\_size  |  Number  |  每页大小  |
|  products  |  Object\[\]  |  商品概要信息列表  |
|  sub\_market\_type  |  String  |  traded / customization  |
|  id  |  Number  |  商品明文ID  |
|  group\_id  |  Number  |  分组ID  |
|  group\_name  |  String  |  分组名称  |
|  subject  |  String  |  商品名称  |
|  status  |  String  |  商品状态  |
|  keywords  |  String\[\]  |  关键词  |
|  main\_image  |  Object  |  商品的主图  |
|  images  |  String\[\]  |  alibaba图片中心的图片URL列表，请使用alibaba.icbu.photobank.upload接口上传图片  |
|  watermark  |  Boolean  |  是否打水印，是(true)或否(false)  |
|  watermark\_frame  |  String  |  水印是否有边框，有边框(Y)或者无边框(N)  |
|  watermark\_position  |  String  |  水印位置，在中间(center)或者在底部(bottom)  |
|  product\_id  |  String  |  产品混淆id  |
|  product\_type  |  String  |  sourcing或者wholesale  |
|  language  |  String  |  english  |
|  display  |  String  |  Y表示上架，N表示下架  |
|  owner\_member\_display\_name  |  String  |  james  |
|  category\_id  |  Number  |  1234  |
|  is\_specific  |  Boolean  |  true  |
|  is\_rts  |  Boolean  |  true  |
|  pc\_detail\_url  |  String  |  https://www.alibaba.com/product-detail/Eco-Friendly-100-Biodegradable-Cornstarch-Trash\_60832548452.html?spm=a2700.galleryofferlist.normalList.12.6c612db4ueHAW2&fullFirstScreen=true  |
|  smart\_edit  |  Boolean  |  true  |
|  gmt\_create  |  String  |  2020-12-22 12:00:00  |
|  gmt\_modified  |  String  |  2020-12-22 12:00:00  |
|  red\_model  |  String  |  CK001  |
|  total\_item  |  Number  |  总数  |

## 错误码

|  错误码  |  错误信息  |  解决方案  |
| --- | --- | --- |
|  没有数据  |  |  |

## 示例

请求

GET/POST

alibaba.icbu.product.list

```json
client = iop.IopClient(url, appkey ,appSecret)
request = iop.IopRequest('alibaba.icbu.product.list')
request.add_api_param('id_list', '[234,123,567]')
request.add_api_param('owner_member', 'user1')
request.add_api_param('status', 'approved')
request.add_api_param('display', 'Y')
request.add_api_param('category_id', '123')
request.add_api_param('current_page', '1')
request.add_api_param('page_size', '10')
request.add_api_param('subject', 'mp3')
request.add_api_param('language', 'ENGLISH')
request.add_api_param('group_id3', '111')
request.add_api_param('group_id2', '222')
request.add_api_param('group_id1', '333')
request.add_api_param('id', '6377362837')
request.add_api_param('gmt_modified_to', '2020-12-22 12:00:00')
request.add_api_param('gmt_modified_from', '2020-12-22 12:00:00')
response = client.execute(request, access_token)
print(response.type)
print(response.body)
```

响应示例

```json
{
  "code": "0",
  "alibaba_icbu_product_list_response": {
    "total_item": "123",
    "current_page": "2",
    "page_size": "20",
    "products": [
      {
        "gmt_create": "创建时间",
        "keywords": [
          "mp3",
          "player"
        ],
        "owner_member_display_name": "产品负责人",
        "group_name": "best seller",
        "subject": "mp3 player",
        "main_image": {
          "watermark_position": "center",
          "images": [
            "http://g03.s.alicdn.com/kf/HTB1PYE9IpXXXXbsXVXXq6xXFXXXg/200042360/HTB1PYE9IpXXXXbsXVXXq6xXFXXXg.jpg",
            "http://g01.s.alicdn.com/kf/HTB1tNhsIFXXXXb2XXXXq6xXFXXX9/200042360/HTB1tNhsIFXXXXb2XXXXq6xXFXXX9.jpg"
          ],
          "watermark": "true",
          "watermark_frame": "N"
        },
        "display": "是否上下架",
        "is_specific": "是否是规格品",
        "pc_detail_url": "商品详情链接",
        "language": "语言种类",
        "gmt_modified": "修改时间",
        "is_rts": "是否是rts品",
        "product_type": "产品类型",
        "category_id": "类目id",
        "red_model": "型号",
        "group_id": "456",
        "smart_edit": "是否智能编辑",
        "product_id": "6edvniewvnewovn",
        "sub_market_type": "子市场类型",
        "id": "123",
        "status": "approved"
      }
    ]
  },
  "request_id": "0ba2887315178178017221014"
}
```