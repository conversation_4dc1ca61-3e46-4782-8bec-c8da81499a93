from sqlalchemy import Column, Integer, String, Text, DateTime, Enum, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from .base import Base


class ArticleStatus(enum.Enum):
    """文章状态枚举"""
    PENDING = "pending"        # 待生成
    GENERATING = "generating"  # 生成中
    SUCCESS = "success"        # 生成成功
    FAILED = "failed"          # 生成失败
    PUBLISHED = "published"    # 已发布


class AIArticle(Base):
    """AI文章模型"""
    __tablename__ = "ai_articles"

    id = Column(Integer, primary_key=True, index=True)
    keywords = Column(String(500), nullable=False, comment="关键词")
    wordpress_url = Column(String(255), nullable=False, comment="WordPress站点URL")
    type = Column(Integer, nullable=False, comment="博客分类ID")
    tags = Column(String(255), nullable=True, comment="博客标签ID，多个用逗号分隔")
    model = Column(String(50), nullable=True, comment="AI模型，WanX或Jimeng")
    image_url = Column(String(500), nullable=True, comment="上传的图片URL")
    
    # 生成结果
    title = Column(String(255), nullable=True, comment="生成的文章标题")
    article_url = Column(String(500), nullable=True, comment="发布后的文章链接")
    article_id = Column(String(100), nullable=True, comment="WordPress文章ID")
    featured_image_id = Column(String(100), nullable=True, comment="特色图片ID")
    
    # 状态和错误信息
    status = Column(String(20), default="pending", comment="文章状态")
    error_message = Column(Text, nullable=True, comment="错误信息")
    result_text = Column(Text, nullable=True, comment="完整返回结果")
    
    # DIFY相关
    workflow_run_id = Column(String(100), nullable=True, comment="DIFY工作流运行ID")
    task_id = Column(String(100), nullable=True, comment="DIFY任务ID")
    
    # AI配置关联
    ai_config_id = Column(Integer, ForeignKey("ai_configs.id"), nullable=True, comment="AI配置ID")
    ai_config = relationship("AIConfig")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间") 