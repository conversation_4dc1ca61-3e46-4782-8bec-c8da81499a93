<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时区统一方案测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .timezone-config {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🕐 UTC + 前端本地化时区统一方案测试</h1>
        
        <div class="timezone-config">
            <h3>当前时区配置</h3>
            <div id="timezone-info"></div>
            <button onclick="changeTimezone('0')">切换到UTC</button>
            <button onclick="changeTimezone('8')">切换到中国时区</button>
            <button onclick="changeTimezone('-5')">切换到美国东部</button>
            <button onclick="changeTimezone('9')">切换到日本时区</button>
        </div>

        <div class="test-section">
            <h2>1. 基本时间格式化测试</h2>
            <button onclick="testBasicFormatting()">运行测试</button>
            <div id="basic-test-results"></div>
        </div>

        <div class="test-section">
            <h2>2. API数据模拟测试</h2>
            <button onclick="testApiData()">模拟API响应</button>
            <div id="api-test-results"></div>
        </div>

        <div class="test-section">
            <h2>3. 表格格式化测试</h2>
            <button onclick="testTableFormatting()">测试表格格式化</button>
            <div id="table-test-results"></div>
        </div>

        <div class="test-section">
            <h2>4. 相对时间测试</h2>
            <button onclick="testRelativeTime()">测试相对时间</button>
            <div id="relative-test-results"></div>
        </div>
    </div>

    <script type="module">
        // 模拟环境变量配置
        window.VITE_SYSTEM_TIMEZONE_OFFSET = '8';
        window.VITE_SYSTEM_TIMEZONE_NAME = 'Asia/Shanghai';

        // 模拟时区工具函数
        function getSystemTimezoneConfig() {
            const timezoneOffset = parseInt(window.VITE_SYSTEM_TIMEZONE_OFFSET || '8');
            const timezoneName = window.VITE_SYSTEM_TIMEZONE_NAME || 'Asia/Shanghai';
            
            return {
                offset: timezoneOffset,
                name: timezoneName,
                offsetString: `UTC${timezoneOffset >= 0 ? '+' : ''}${timezoneOffset}`
            };
        }

        function getSystemTimezoneOptions() {
            const config = getSystemTimezoneConfig();
            
            return {
                timeZone: config.name,
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            };
        }

        function parseUTCString(utcString) {
            if (!utcString) return null;
            
            try {
                const normalizedString = utcString.endsWith('Z') ? utcString : utcString + 'Z';
                return new Date(normalizedString);
            } catch (error) {
                console.warn('解析UTC时间字符串失败:', utcString, error);
                return null;
            }
        }

        function formatToSystemTimezone(datetime, options = {}) {
            if (!datetime) return '--';
            
            let date;
            if (typeof datetime === 'string') {
                date = parseUTCString(datetime);
            } else {
                date = datetime;
            }
            
            if (!date || isNaN(date.getTime())) return '--';
            
            const formatOptions = {
                ...getSystemTimezoneOptions(),
                ...options
            };
            
            try {
                return date.toLocaleString('zh-CN', formatOptions);
            } catch (error) {
                console.warn('格式化时间失败:', datetime, error);
                return '--';
            }
        }

        function formatDateTime(datetime) {
            return formatToSystemTimezone(datetime, {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function formatRelativeTime(datetime) {
            if (!datetime) return '--';
            
            let date;
            if (typeof datetime === 'string') {
                date = parseUTCString(datetime);
            } else {
                date = datetime;
            }
            
            if (!date || isNaN(date.getTime())) return '--';
            
            const now = new Date();
            const diff = now - date;
            const seconds = Math.floor(diff / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);
            
            if (seconds < 60) {
                return '刚刚';
            } else if (minutes < 60) {
                return `${minutes}分钟前`;
            } else if (hours < 24) {
                return `${hours}小时前`;
            } else if (days < 7) {
                if (days === 1) {
                    return '昨天 ' + formatToSystemTimezone(date, {
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                } else {
                    return `${days}天前`;
                }
            } else {
                return formatDateTime(date);
            }
        }

        // 全局函数
        window.changeTimezone = function(offset) {
            window.VITE_SYSTEM_TIMEZONE_OFFSET = offset;
            
            // 更新时区名称
            const timezoneNames = {
                '0': 'UTC',
                '8': 'Asia/Shanghai',
                '-5': 'America/New_York',
                '9': 'Asia/Tokyo'
            };
            window.VITE_SYSTEM_TIMEZONE_NAME = timezoneNames[offset] || 'UTC';
            
            updateTimezoneInfo();
        };

        window.updateTimezoneInfo = function() {
            const config = getSystemTimezoneConfig();
            const now = new Date();
            
            document.getElementById('timezone-info').innerHTML = `
                <div class="result info">
                    <strong>时区偏移:</strong> ${config.offsetString}<br>
                    <strong>时区名称:</strong> ${config.name}<br>
                    <strong>当前UTC时间:</strong> ${now.toISOString()}<br>
                    <strong>当前本地时间:</strong> ${formatToSystemTimezone(now)}
                </div>
            `;
        };

        window.testBasicFormatting = function() {
            const testTimes = [
                '2024-01-01T10:00:00Z',
                '2024-06-15T14:30:00Z',
                '2024-12-31T23:59:59Z'
            ];

            let results = '<h4>基本格式化结果:</h4>';
            testTimes.forEach(timeStr => {
                const formatted = formatDateTime(timeStr);
                results += `
                    <div class="result">
                        <strong>UTC输入:</strong> ${timeStr}<br>
                        <strong>本地显示:</strong> ${formatted}
                    </div>
                `;
            });

            document.getElementById('basic-test-results').innerHTML = results;
        };

        window.testApiData = function() {
            // 模拟后端API返回的数据
            const apiResponse = {
                id: 1,
                name: '测试任务',
                created_at: new Date().toISOString(),
                updated_at: new Date(Date.now() - 3600000).toISOString(), // 1小时前
                scheduled_time: new Date(Date.now() + 7200000).toISOString() // 2小时后
            };

            let results = '<h4>API数据格式化结果:</h4>';
            results += '<div class="result success">模拟API响应数据:</div>';
            
            Object.entries(apiResponse).forEach(([key, value]) => {
                if (key.endsWith('_at') || key.endsWith('_time')) {
                    const formatted = formatDateTime(value);
                    results += `
                        <div class="result">
                            <strong>${key}:</strong><br>
                            UTC: ${value}<br>
                            本地: ${formatted}
                        </div>
                    `;
                }
            });

            document.getElementById('api-test-results').innerHTML = results;
        };

        window.testTableFormatting = function() {
            // 模拟表格数据
            const tableData = [
                { name: '任务1', created_at: '2024-01-01T08:00:00Z' },
                { name: '任务2', created_at: '2024-01-01T12:00:00Z' },
                { name: '任务3', created_at: '2024-01-01T16:00:00Z' }
            ];

            let results = '<h4>表格格式化结果:</h4>';
            results += '<table border="1" style="width:100%; border-collapse: collapse;">';
            results += '<tr><th>任务名称</th><th>创建时间(UTC)</th><th>创建时间(本地)</th></tr>';
            
            tableData.forEach(row => {
                const formatted = formatDateTime(row.created_at);
                results += `
                    <tr>
                        <td>${row.name}</td>
                        <td>${row.created_at}</td>
                        <td>${formatted}</td>
                    </tr>
                `;
            });
            
            results += '</table>';
            document.getElementById('table-test-results').innerHTML = results;
        };

        window.testRelativeTime = function() {
            const now = new Date();
            const testTimes = [
                { label: '刚刚', time: new Date(now.getTime() - 30000) }, // 30秒前
                { label: '5分钟前', time: new Date(now.getTime() - 300000) }, // 5分钟前
                { label: '2小时前', time: new Date(now.getTime() - 7200000) }, // 2小时前
                { label: '昨天', time: new Date(now.getTime() - 86400000) }, // 1天前
                { label: '3天前', time: new Date(now.getTime() - 259200000) }, // 3天前
                { label: '1周前', time: new Date(now.getTime() - 604800000) } // 1周前
            ];

            let results = '<h4>相对时间格式化结果:</h4>';
            testTimes.forEach(({ label, time }) => {
                const utcString = time.toISOString();
                const relative = formatRelativeTime(utcString);
                results += `
                    <div class="result">
                        <strong>预期:</strong> ${label}<br>
                        <strong>UTC:</strong> ${utcString}<br>
                        <strong>相对时间:</strong> ${relative}
                    </div>
                `;
            });

            document.getElementById('relative-test-results').innerHTML = results;
        };

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTimezoneInfo();
        });
    </script>
</body>
</html>
