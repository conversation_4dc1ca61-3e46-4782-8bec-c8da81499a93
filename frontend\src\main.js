import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import 'element-plus/dist/index.css'
import App from './App.vue'
import router from './router'
import store from './store'
import axios from 'axios'

// 引入全局样式
import './assets/styles.css'

// 配置 axios 基础URL - 使用相对路径，在生产环境下自动适配域名
// axios.defaults.baseURL = 'http://localhost:5000'  // 注释掉硬编码的URL
// 在生产环境下使用相对路径，开发环境通过proxy代理
axios.defaults.baseURL = process.env.NODE_ENV === 'production' ? '' : 'http://localhost:5000'

// 响应拦截器处理认证失败
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response && error.response.status === 401) {
      // 清除token并跳转到登录页
      store.dispatch('auth/logout')
      router.push('/login')
    }
    return Promise.reject(error)
  }
)

const app = createApp(App)

// 注册所有Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(store)
app.use(router)
app.use(ElementPlus)

// 初始化应用状态
store.dispatch('initApp')

// 初始化认证状态
store.dispatch('auth/initAuth')

app.mount('#app')
