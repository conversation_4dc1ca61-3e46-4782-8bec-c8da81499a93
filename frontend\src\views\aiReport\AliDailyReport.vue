<template>
  <div class="ali-daily-report">
    <div class="page-header">
      <h2>阿里日报</h2>
      <p>查看阿里国际站每日运营数据报表</p>
    </div>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>今日概览</span>
              <el-date-picker
                v-model="selectedDate"
                type="date"
                placeholder="选择日期"
                format="YYYY年MM月DD日"
                value-format="YYYY-MM-DD"
                @change="handleDateChange"
              />
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="6">
              <div class="stat-card">
                <div class="stat-number">{{ dailyStats.inquiries }}</div>
                <div class="stat-label">今日询盘</div>
                <div class="stat-change positive">+{{ dailyStats.inquiriesGrowth }}%</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="stat-card">
                <div class="stat-number">{{ dailyStats.views }}</div>
                <div class="stat-label">商品浏览量</div>
                <div class="stat-change positive">+{{ dailyStats.viewsGrowth }}%</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="stat-card">
                <div class="stat-number">{{ dailyStats.visitors }}</div>
                <div class="stat-label">访客数</div>
                <div class="stat-change negative">{{ dailyStats.visitorsGrowth }}%</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6">
              <div class="stat-card">
                <div class="stat-number">{{ dailyStats.orders }}</div>
                <div class="stat-label">订单数</div>
                <div class="stat-change positive">+{{ dailyStats.ordersGrowth }}%</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <span>询盘来源分析</span>
          </template>
          <div class="chart-placeholder">
            <div class="mock-pie-chart">
              <div class="pie-segment" style="--percentage: 45%; --color: #409EFF;">直接搜索</div>
              <div class="pie-segment" style="--percentage: 30%; --color: #67C23A;">关键词推荐</div>
              <div class="pie-segment" style="--percentage: 15%; --color: #E6A23C;">橱窗展示</div>
              <div class="pie-segment" style="--percentage: 10%; --color: #F56C6C;">其他</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :md="12">
        <el-card>
          <template #header>
            <span>热门商品排行</span>
          </template>
          <div class="product-ranking">
            <div v-for="(product, index) in topProducts" :key="index" class="product-item">
              <div class="rank">{{ index + 1 }}</div>
              <div class="product-info">
                <div class="product-name">{{ product.name }}</div>
                <div class="product-views">{{ product.views }} 次浏览</div>
              </div>
              <div class="product-inquiries">{{ product.inquiries }} 询盘</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>今日活动记录</span>
          </template>
          <el-table :data="dailyActivities" style="width: 100%">
            <el-table-column prop="time" label="时间" width="120"/>
            <el-table-column prop="type" label="类型" width="120">
              <template #default="scope">
                <el-tag :type="getActivityTagType(scope.row.type)">
                  {{ scope.row.type }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="描述"/>
            <el-table-column prop="value" label="数值" width="100"/>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted } from 'vue'

export default defineComponent({
  name: 'AliDailyReport',
  setup() {
    const selectedDate = ref(new Date().toISOString().split('T')[0])
    
    const dailyStats = ref({
      inquiries: 24,
      inquiriesGrowth: 12.5,
      views: 1234,
      viewsGrowth: 8.3,
      visitors: 456,
      visitorsGrowth: -2.1,
      orders: 8,
      ordersGrowth: 15.2
    })

    const topProducts = ref([
      { name: '智能手机支架', views: 156, inquiries: 8 },
      { name: '蓝牙耳机', views: 142, inquiries: 6 },
      { name: '充电宝', views: 128, inquiries: 5 },
      { name: '手机壳', views: 98, inquiries: 4 },
      { name: '数据线', views: 76, inquiries: 3 }
    ])

    const dailyActivities = ref([
      { time: '09:15', type: '新询盘', description: '收到美国客户询盘', value: '1' },
      { time: '10:30', type: '商品浏览', description: '智能手机支架页面浏览激增', value: '+45' },
      { time: '11:45', type: '新订单', description: '德国客户下单蓝牙耳机', value: '1' },
      { time: '14:20', type: '客户回复', description: '法国客户回复报价询问', value: '1' },
      { time: '16:10', type: '新询盘', description: '澳洲客户询问充电宝规格', value: '1' }
    ])

    const handleDateChange = (date) => {
      console.log('选择日期:', date)
      // 这里可以根据日期重新加载数据
    }

    const getActivityTagType = (type) => {
      const typeMap = {
        新询盘: 'success',
        商品浏览: 'info',
        新订单: 'warning',
        客户回复: 'primary'
      }
      return typeMap[type] || 'info'
    }

    onMounted(() => {
      // 初始化加载数据
    })

    return {
      selectedDate,
      dailyStats,
      topProducts,
      dailyActivities,
      handleDateChange,
      getActivityTagType
    }
  }
})
</script>

<style scoped>
.ali-daily-report {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  margin-bottom: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: bold;
}

.stat-change.positive {
  color: #67C23A;
}

.stat-change.negative {
  color: #F56C6C;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 4px;
}

.mock-pie-chart {
  text-align: center;
  color: #606266;
}

.pie-segment {
  margin: 8px 0;
  padding: 4px 12px;
  border-radius: 4px;
  background: var(--color);
  color: white;
  font-size: 14px;
}

.product-ranking {
  max-height: 200px;
  overflow-y: auto;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #EBEEF5;
}

.product-item:last-child {
  border-bottom: none;
}

.rank {
  width: 30px;
  height: 30px;
  background: #409EFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.product-views {
  font-size: 12px;
  color: #909399;
}

.product-inquiries {
  font-size: 14px;
  color: #67C23A;
  font-weight: bold;
}
</style> 