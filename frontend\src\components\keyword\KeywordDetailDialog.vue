<template>
  <el-dialog
    v-model="dialogVisible"
    title="关键词详情"
    width="700px"
    :before-close="handleClose"
  >
    <div v-if="keywordData" class="detail-content">
      <!-- 基本信息 -->
      <div class="info-section">
        <h4>基本信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>关键词名：</label>
            <span class="keyword-name">{{ keywordData.keyword_name }}</span>
          </div>
          
          <div class="info-item">
            <label>创建时间：</label>
            <span>{{ formatDateTime(keywordData.created_at) }}</span>
          </div>
          
          <div class="info-item">
            <label>更新时间：</label>
            <span>{{ formatDateTime(keywordData.updated_at) }}</span>
          </div>
          
          <div class="info-item">
            <label>更新方式：</label>
            <el-tag
              :type="getUpdateMethodTagType(keywordData.update_method)"
              size="small"
            >
              {{ getUpdateMethodText(keywordData.update_method) }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 关键词信息 -->
      <div class="info-section">
        <h4>关键词信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>意图：</label>
            <el-tag v-if="keywordData.intent" :type="getIntentTagType(keywordData.intent)" size="small">
              {{ getIntentText(keywordData.intent) }}
            </el-tag>
            <span v-else class="text-muted">--</span>
          </div>

          <div class="info-item">
            <label>搜索量：</label>
            <span class="search-volume">
              {{ formatNumber(keywordData.volume || keywordData.avg_monthly_searches) || '--' }}
            </span>
          </div>

          <div class="info-item">
            <label>关键词难度：</label>
            <span v-if="keywordData.keyword_difficulty !== null">
              {{ keywordData.keyword_difficulty }}
            </span>
            <span v-else class="text-muted">--</span>
          </div>

          <div class="info-item">
            <label>搜索结果数：</label>
            <span v-if="keywordData.number_of_results">
              {{ formatNumber(keywordData.number_of_results) }}
            </span>
            <span v-else class="text-muted">--</span>
          </div>
        </div>
      </div>

      <!-- 趋势信息 -->
      <div class="info-section" v-if="keywordData.trend">
        <h4>趋势信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>趋势数据：</label>
            <div class="trend-display">
              <div class="trend-values">
                <el-tag
                  v-for="(value, index) in getTrendValues()"
                  :key="index"
                  size="mini"
                  :type="getTrendTagType(value)"
                  style="margin-right: 5px; margin-bottom: 5px"
                >
                  {{ value.toFixed(2) }}
                </el-tag>
              </div>
              <div class="trend-summary">
                <span>{{ getTrendSummary() }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 搜索量信息（兼容） -->
      <div class="info-section" v-if="keywordData.monthly_search_volumes">
        <h4>月度搜索量（兼容数据）</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>每月搜索量：</label>
            <div class="monthly-volumes">
              <el-tag
                v-for="(volume, index) in getMonthlyVolumes()"
                :key="index"
                size="mini"
                style="margin-right: 5px; margin-bottom: 5px"
              >
                {{ volume.month }}: {{ formatNumber(volume.searches) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 竞争信息 -->
      <div class="info-section">
        <h4>竞争信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>竞争级别：</label>
            <el-tag
              :type="getCompetitionTagType(keywordData.competition_level)"
              size="small"
            >
              {{ getCompetitionText(keywordData.competition_level) }}
            </el-tag>
          </div>

          <div class="info-item">
            <label>竞争密度：</label>
            <span v-if="keywordData.competitive_density !== null">
              {{ keywordData.competitive_density }}
            </span>
            <span v-else class="text-muted">--</span>
          </div>

          <div class="info-item">
            <label>竞争指数：</label>
            <span v-if="keywordData.competition_index">
              {{ keywordData.competition_index.toFixed(1) }}
            </span>
            <span v-else class="text-muted">--</span>
          </div>
        </div>
      </div>

      <!-- 出价信息 -->
      <div class="info-section">
        <h4>出价信息</h4>
        <div class="info-grid">
          <div class="info-item">
            <label>CPC (USD)：</label>
            <span v-if="keywordData.cpc_usd !== null" class="cpc-value">
              ${{ keywordData.cpc_usd.toFixed(2) }}
            </span>
            <span v-else class="text-muted">--</span>
          </div>

          <div class="info-item">
            <label>出价第20百分位：</label>
            <span v-if="keywordData.low_bid_micros">
              ¥{{ formatBid(keywordData.low_bid_micros) }}
            </span>
            <span v-else class="text-muted">--</span>
          </div>

          <div class="info-item">
            <label>出价第80百分位：</label>
            <span v-if="keywordData.high_bid_micros">
              ¥{{ formatBid(keywordData.high_bid_micros) }}
            </span>
            <span v-else class="text-muted">--</span>
          </div>

          <div class="info-item" v-if="keywordData.low_bid_micros && keywordData.high_bid_micros">
            <label>出价范围：</label>
            <span class="bid-range">
              ¥{{ formatBid(keywordData.low_bid_micros) }} - ¥{{ formatBid(keywordData.high_bid_micros) }}
            </span>
          </div>
        </div>
      </div>

      <!-- SERP特征信息 -->
      <div class="info-section" v-if="keywordData.serp_features">
        <h4>SERP特征</h4>
        <div class="tags-container">
          <div v-if="getSerpFeatures().length" class="tags-list">
            <el-tag
              v-for="feature in getSerpFeatures()"
              :key="feature"
              size="small"
              type="info"
              style="margin-right: 8px; margin-bottom: 8px"
            >
              {{ feature }}
            </el-tag>
          </div>
          <span v-else class="text-muted">暂无SERP特征</span>
        </div>
      </div>

      <!-- 标签信息 -->
      <div class="info-section">
        <h4>标签信息</h4>
        <div class="tags-container">
          <div v-if="keywordData.tags_list && keywordData.tags_list.length" class="tags-list">
            <el-tag
              v-for="tag in keywordData.tags_list"
              :key="tag"
              size="small"
              style="margin-right: 8px; margin-bottom: 8px"
            >
              {{ tag }}
            </el-tag>
          </div>
          <span v-else class="text-muted">暂无标签</span>
        </div>
      </div>

      <!-- 更新历史 -->
      <div class="info-section" v-if="showHistory">
        <h4>
          更新历史
          <el-button 
            size="mini" 
            text 
            @click="loadHistory"
            :loading="historyLoading"
          >
            刷新
          </el-button>
        </h4>
        <div class="history-list">
          <el-timeline v-if="historyList.length">
            <el-timeline-item
              v-for="item in historyList"
              :key="item.id"
              :timestamp="formatDateTime(item.updated_at)"
            >
              <div class="history-item">
                <div class="history-title">
                  {{ getUpdateMethodText(item.update_method) }} 更新
                </div>
                <div class="history-details" v-if="item.changes_summary">
                  <small class="text-muted">{{ item.changes_summary }}</small>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
          <div v-else class="no-history">
            <el-empty description="暂无更新历史" :image-size="80" />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="toggleHistory" v-if="!showHistory">
          查看更新历史
        </el-button>
        <el-button @click="showHistory = false" v-if="showHistory">
          隐藏历史
        </el-button>
        <el-button type="primary" @click="editKeyword">编辑</el-button>
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { formatNumber, formatDateTime } from '@/utils/format'
import { keywordService } from '@/services/keyword'

export default {
  name: 'KeywordDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    keywordData: {
      type: Object,
      default: null
    }
  },
  emits: ['update:visible', 'edit'],
  setup(props, { emit }) {
    const showHistory = ref(false)
    const historyLoading = ref(false)
    const historyList = ref([])

    // 计算属性
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })

    // 监听对话框显示
    watch(() => props.visible, (visible) => {
      if (visible && props.keywordData) {
        // 重置状态
        showHistory.value = false
        historyList.value = []
      }
    })

    // 获取月度搜索量
    const getMonthlyVolumes = () => {
      if (!props.keywordData?.monthly_search_volumes) return []
      
      try {
        const volumes = JSON.parse(props.keywordData.monthly_search_volumes)
        return volumes.slice(0, 12) // 只显示前12个月
      } catch (error) {
        console.error('解析月度搜索量失败:', error)
        return []
      }
    }

    // 加载更新历史
    const loadHistory = async () => {
      if (!props.keywordData?.id) return

      historyLoading.value = true
      try {
        const response = await keywordService.getUpdateHistory(props.keywordData.id)
        historyList.value = response
      } catch (error) {
        console.error('加载更新历史失败:', error)
        ElMessage.error('加载更新历史失败')
      } finally {
        historyLoading.value = false
      }
    }

    // 切换历史显示
    const toggleHistory = () => {
      showHistory.value = true
      if (historyList.value.length === 0) {
        loadHistory()
      }
    }

    // 编辑关键词
    const editKeyword = () => {
      emit('edit', props.keywordData)
      handleClose()
    }

    // 关闭对话框
    const handleClose = () => {
      showHistory.value = false
      historyList.value = []
      dialogVisible.value = false
    }

    // 辅助函数
    const getCompetitionTagType = (level) => {
      const typeMap = {
        low: 'success',
        medium: 'warning',
        high: 'danger',
        unspecified: 'info'
      }
      return typeMap[level] || 'info'
    }

    const getCompetitionText = (level) => {
      const textMap = {
        low: '低',
        medium: '中',
        high: '高',
        unspecified: '未知'
      }
      return textMap[level] || '未知'
    }

    const getUpdateMethodTagType = (method) => {
      const typeMap = {
        manual: 'primary',
        google_ads_api: 'success',
        batch_import: 'warning'
      }
      return typeMap[method] || 'info'
    }

    const getUpdateMethodText = (method) => {
      const textMap = {
        manual: '手工',
        google_ads_api: 'Google Ads',
        batch_import: '批量导入'
      }
      return textMap[method] || '未知'
    }

    const formatBid = (micros) => {
      if (!micros) return '0'
      return (micros / 1000000).toFixed(2)
    }

    // 获取意图标签类型
    const getIntentTagType = (intent) => {
      const typeMap = {
        Informational: 'info',
        Commercial: 'warning',
        Navigational: 'primary',
        Transactional: 'success'
      }
      return typeMap[intent] || 'info'
    }

    // 获取意图文本
    const getIntentText = (intent) => {
      const textMap = {
        Informational: '信息型',
        Commercial: '商业型',
        Navigational: '导航型',
        Transactional: '交易型'
      }
      return textMap[intent] || intent
    }

    // 获取趋势值数组
    const getTrendValues = () => {
      if (!props.keywordData?.trend) return []

      try {
        const trends = JSON.parse(props.keywordData.trend)
        return Array.isArray(trends) ? trends : []
      } catch (error) {
        console.error('解析趋势数据失败:', error)
        return []
      }
    }

    // 获取趋势标签类型
    const getTrendTagType = (value) => {
      if (value >= 0.8) return 'success'
      if (value >= 0.6) return 'warning'
      if (value >= 0.4) return 'info'
      return 'danger'
    }

    // 获取趋势摘要
    const getTrendSummary = () => {
      const trends = getTrendValues()
      if (trends.length < 2) return '--'

      const first = trends[0]
      const last = trends[trends.length - 1]
      const change = ((last - first) / first * 100).toFixed(1)
      return change > 0 ? `趋势上升 +${change}%` : `趋势下降 ${change}%`
    }

    // 获取SERP特征数组
    const getSerpFeatures = () => {
      if (!props.keywordData?.serp_features) return []

      try {
        const features = JSON.parse(props.keywordData.serp_features)
        return Array.isArray(features) ? features : []
      } catch (error) {
        console.error('解析SERP特征失败:', error)
        return []
      }
    }

    return {
      showHistory,
      historyLoading,
      historyList,
      dialogVisible,
      getMonthlyVolumes,
      loadHistory,
      toggleHistory,
      editKeyword,
      handleClose,
      getCompetitionTagType,
      getCompetitionText,
      getUpdateMethodTagType,
      getUpdateMethodText,
      formatBid,
      formatNumber,
      formatDateTime,
      // 新增的函数
      getIntentTagType,
      getIntentText,
      getTrendValues,
      getTrendTagType,
      getTrendSummary,
      getSerpFeatures
    }
  }
}
</script>

<style scoped>
.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.info-section {
  margin-bottom: 25px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  background: #fafafa;
}

.info-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  min-height: 32px;
}

.info-item label {
  font-weight: 600;
  color: #666;
  margin-right: 10px;
  min-width: 120px;
  white-space: nowrap;
}

.info-item span {
  flex: 1;
  word-break: break-word;
}

.keyword-name {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
}

.search-volume {
  font-size: 16px;
  font-weight: 600;
  color: #67c23a;
}

.cpc-value {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
}

.bid-range {
  font-weight: 600;
  color: #e6a23c;
}

.trend-display {
  width: 100%;
}

.trend-values {
  margin-bottom: 8px;
}

.trend-summary {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.monthly-volumes {
  max-width: 100%;
}

.tags-container {
  min-height: 32px;
  display: flex;
  align-items: flex-start;
}

.tags-list {
  flex: 1;
}

.history-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  margin-bottom: 10px;
}

.history-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.history-details {
  color: #666;
  font-size: 12px;
}

.no-history {
  text-align: center;
  padding: 20px;
}

.text-muted {
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-item label {
    margin-bottom: 5px;
    min-width: auto;
  }
}

:deep(.el-timeline) {
  padding-left: 0;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 20px;
}

:deep(.el-timeline-item__timestamp) {
  color: #909399;
  font-size: 12px;
}
</style> 