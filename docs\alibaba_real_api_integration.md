# 阿里巴巴真实API集成指南

## 概述

本文档介绍如何将阿里巴巴询盘统计、产品表现、关键词表现功能从模拟数据切换到真实API调用。

## 前置条件

### 1. 阿里国际站开发者认证
- 完成阿里国际站开发者账号注册
- 创建应用并获得 `app_key` 和 `app_secret`
- 申请相应的API权限：
  - `alibaba.mydata.self.product.get` - 产品表现数据
  - `alibaba.mydata.self.product.list.get` - 产品列表
  - `alibaba.mydata.self.product.date.get` - 产品数据时间范围
  - `alibaba.mydata.self.keyword.effect.week.get` - 关键词周统计
  - `alibaba.mydata.self.keyword.effect.month.get` - 关键词月统计
  - `alibaba.mydata.self.keyword.date.get` - 关键词数据周期
  - `alibaba.mydata.self.inquiry.industry.get` - 询盘行业列表
  - `alibaba.mydata.self.inquiry.performance.get` - 询盘表现数据

### 2. 配置更新
确保 `config.py` 中的阿里巴巴配置正确：

```python
# 阿里国际站API配置
ALIBABA_APP_KEY: str = "your_app_key"
ALIBABA_APP_SECRET: str = "your_app_secret"
ALIBABA_OAUTH_URL: str = "https://open-api.alibaba.com/oauth/authorize"
ALIBABA_TOKEN_URL: str = "https://open-api.alibaba.com/rest/auth/token/create"
ALIBABA_REFRESH_TOKEN_URL: str = "https://open-api.alibaba.com/rest/auth/token/refresh"
ALIBABA_REDIRECT_URI: str = "https://your-domain.com/api/v1/alibaba/callback"
ALIBABA_GATEWAY_URL: str = "https://open-api.alibaba.com/rest"
ALIBABA_USE_REAL_API: bool = True  # 启用真实API
```

## 切换步骤

### 1. 使用切换脚本（推荐）

```bash
# 切换到真实API模式
python backend/switch_api_mode.py real

# 切换到模拟数据模式
python backend/switch_api_mode.py mock
```

### 2. 手动切换

修改 `backend/app/core/config.py`：

```python
# 启用真实API
ALIBABA_USE_REAL_API: bool = True

# 使用模拟数据
ALIBABA_USE_REAL_API: bool = False
```

### 3. 重启服务

```bash
# 重启后端服务
cd backend
python main.py
```

## 新增的真实API服务

### 1. 产品表现服务 (`AlibabaProductServiceReal`)

**功能特性：**
- 自动获取产品列表
- 批量同步产品表现数据
- 支持按日期范围和产品ID筛选
- 自动处理API响应格式转换
- 错误处理和重试机制

**API映射：**
- 产品列表：`alibaba.mydata.self.product.list.get`
- 产品表现：`alibaba.mydata.self.product.get`
- 时间范围：`alibaba.mydata.self.product.date.get`

### 2. 关键词表现服务 (`AlibabaKeywordServiceReal`)

**功能特性：**
- 同步数据周期信息
- 支持周统计和月统计
- 自动处理分页和排序
- 支持关键词搜索过滤
- P4P数据处理

**API映射：**
- 数据周期：`alibaba.mydata.self.keyword.date.get`
- 周统计：`alibaba.mydata.self.keyword.effect.week.get`
- 月统计：`alibaba.mydata.self.keyword.effect.month.get`

### 3. 询盘统计服务 (`AlibabaInquiryServiceReal`)

**功能特性：**
- 同步行业列表
- 获取行业表现数据
- 自动计算汇总统计
- 支持主营行业标识

**API映射：**
- 行业列表：`alibaba.mydata.self.inquiry.industry.get`
- 行业表现：`alibaba.mydata.self.inquiry.performance.get`

## 数据标识

真实API同步的数据会标记为：
```python
data_source = "alibaba_api"  # 真实API数据
data_source = "mock_api"     # 模拟数据
```

## 错误处理

### 1. 常见错误及解决方案

**授权错误：**
```
阿里国际站访问令牌已过期，请重新授权
```
- 解决：系统会自动尝试刷新token，失败时需要重新授权

**API权限错误：**
```
获取产品列表失败: insufficient_scope
```
- 解决：检查应用是否申请了相应的API权限

**频率限制：**
```
API调用频率超限
```
- 解决：降低同步频率，增加重试间隔

### 2. 日志监控

真实API调用会产生详细的日志：

```python
logger.info(f"调用阿里巴巴API: {api_name}")
logger.info(f"API参数: {params}")
logger.info(f"API响应: {result}")
logger.error(f"API调用失败: {error}")
```

## 性能优化

### 1. 批量处理
- 产品数据按日期批量同步
- 关键词数据支持分页获取
- 行业数据并行处理

### 2. 缓存策略
- 本地数据库缓存API响应
- 避免重复调用相同数据
- 支持增量更新

### 3. 错误恢复
- 单个API失败不影响整体同步
- 自动跳过已同步的数据
- 支持断点续传

## 测试验证

### 1. 功能测试

```bash
# 测试产品表现同步
curl -X POST "http://localhost:5000/api/v1/alibaba-product/sync" \
  -H "Authorization: Bearer your_token" \
  -d "statistics_type=day&start_date=2024-01-01&end_date=2024-01-07"

# 测试关键词表现同步
curl -X POST "http://localhost:5000/api/v1/alibaba-keyword/sync" \
  -H "Authorization: Bearer your_token" \
  -d "statistics_type=week&start_date=2024-01-01&end_date=2024-01-07"

# 测试询盘统计同步
curl -X POST "http://localhost:5000/api/v1/alibaba-inquiry/sync" \
  -H "Authorization: Bearer your_token" \
  -d '{"date_range":{"start_date":"2024-01-01","end_date":"2024-01-07"}}'
```

### 2. 数据验证

检查数据库中的 `data_source` 字段：

```sql
-- 查看真实API数据
SELECT COUNT(*) FROM alibaba_product_performance WHERE data_source = 'alibaba_api';
SELECT COUNT(*) FROM alibaba_keyword_performance WHERE data_source = 'alibaba_api';

-- 查看模拟数据
SELECT COUNT(*) FROM alibaba_product_performance WHERE data_source = 'mock_api';
SELECT COUNT(*) FROM alibaba_keyword_performance WHERE data_source = 'mock_api';
```

## 监控和维护

### 1. 定期检查
- Token有效期监控
- API调用成功率统计
- 数据同步完整性检查

### 2. 告警设置
- Token即将过期提醒
- API调用失败告警
- 数据同步异常通知

### 3. 备份策略
- 定期备份同步的数据
- 保留模拟数据作为备用
- 建立数据恢复机制

## 注意事项

1. **API限制**：注意阿里巴巴API的调用频率限制
2. **数据一致性**：真实API数据可能与模拟数据格式略有差异
3. **错误处理**：做好API调用失败的降级处理
4. **成本控制**：监控API调用次数，避免超出配额
5. **安全性**：保护好app_secret和access_token

## 故障排除

如果遇到问题，可以：

1. 检查日志文件中的详细错误信息
2. 验证阿里巴巴开发者控制台中的应用配置
3. 确认用户授权状态和token有效性
4. 临时切换回模拟数据模式进行对比测试

```bash
# 临时切换到模拟模式
python backend/switch_api_mode.py mock

# 测试完成后切换回真实API
python backend/switch_api_mode.py real
```