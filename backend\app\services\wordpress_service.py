import httpx
import base64
from typing import Dict, Any, Optional
from datetime import datetime
import asyncio
import random
import logging

logger = logging.getLogger(__name__)

class WordPressService:
    """WordPress REST API 服务类"""
    
    # 常见浏览器User-Agent列表
    USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
    ]
    
    def __init__(self, site_url: str, username: str, app_password: str):
        self.site_url = site_url.rstrip('/')
        self.username = username
        # 处理应用程序密码：只移除首尾空白字符，保留中间的空格（WordPress应用程序密码的空格是有效字符）
        self.app_password = app_password.strip()
        self.base_url = f"{self.site_url}/wp-json/wp/v2"
        
        # 随机选择User-Agent
        self.user_agent = random.choice(self.USER_AGENTS)
        
        # 设置请求头
        self.headers = {
            "User-Agent": self.user_agent,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
    
    async def test_connection(self) -> Dict[str, Any]:
        """测试WordPress连接"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                # 测试基础API连接
                response = await client.get(
                    f"{self.site_url}/wp-json",
                    headers=self.headers,
                    auth=(self.username, self.app_password)
                )

                if response.status_code == 403:
                    return {
                        "success": False,
                        "error": f"认证失败: 请检查用户名和应用程序密码是否正确，以及用户是否有足够权限"
                    }
                elif response.status_code != 200:
                    return {
                        "success": False,
                        "error": f"API连接失败: HTTP {response.status_code}：{response.text}"
                    }

                # 测试需要认证的端点，确保认证信息有效
                auth_test_response = await client.get(
                    f"{self.base_url}/posts",
                    headers=self.headers,
                    params={"per_page": 1, "context": "edit"},
                    auth=(self.username, self.app_password)
                )

                if auth_test_response.status_code == 401:
                    return {
                        "success": False,
                        "error": "认证失败: 用户名或应用程序密码错误，或用户权限不足"
                    }
                elif auth_test_response.status_code == 403:
                    return {
                        "success": False,
                        "error": "权限不足: 用户没有足够的权限访问WordPress内容"
                    }

                # 获取站点信息
                site_info = await self.get_site_info()
                if not site_info["success"]:
                    return site_info

                return {
                    "success": True,
                    "site_info": site_info["data"]
                }

        except httpx.RequestError as e:
            return {
                "success": False,
                "error": f"连接错误: {str(e)}"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"未知错误: {str(e)}"
            }
    
    async def get_site_info(self) -> Dict[str, Any]:
        """获取WordPress站点信息"""
        try:
            logger.info(f"开始获取站点信息: {self.site_url}")
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                # 获取站点基本信息
                logger.debug(f"获取站点基本信息: {self.site_url}/wp-json")
                response = await client.get(
                    f"{self.site_url}/wp-json", 
                    headers=self.headers,
                    auth=(self.username, self.app_password)
                )
                if response.status_code != 200:
                    error_msg = f"获取站点信息失败: HTTP {response.status_code}：{response.text}"
                    logger.error(error_msg)
                    return {
                        "success": False,
                        "error": error_msg
                    }
                
                site_data = response.json()
                logger.debug(f"站点基本信息获取成功: {site_data.get('name', 'Unknown')}")
                
                # 获取文章统计
                logger.debug("获取文章统计信息")
                posts_stats = await self.get_posts_stats()
                logger.debug(f"文章统计: {posts_stats}")

                # 检查文章统计是否因认证失败
                if not posts_stats.get("success", True):
                    if "认证失败" in posts_stats.get("error", ""):
                        return {
                            "success": False,
                            "error": f"WordPress认证失败: {posts_stats.get('error')}"
                        }

                # 获取页面统计
                logger.debug("获取页面统计信息")
                pages_stats = await self.get_pages_stats()
                logger.debug(f"页面统计: {pages_stats}")

                # 检查页面统计是否因认证失败
                if not pages_stats.get("success", True):
                    if "认证失败" in pages_stats.get("error", ""):
                        return {
                            "success": False,
                            "error": f"WordPress认证失败: {pages_stats.get('error')}"
                        }
                
                # 获取博客分类
                logger.debug("获取博客分类信息")
                categories_result = await self.get_categories()
                blog_categories = categories_result.get("data", []) if categories_result.get("success") else []
                logger.debug(f"博客分类获取结果: 成功={categories_result.get('success')}, 数量={len(blog_categories)}")
                if not categories_result.get("success"):
                    logger.warning(f"获取博客分类失败: {categories_result.get('error')}")
                
                # 获取博客标签
                logger.debug("获取博客标签信息")
                tags_result = await self.get_tags()
                blog_tags = tags_result.get("data", []) if tags_result.get("success") else []
                logger.debug(f"博客标签获取结果: 成功={tags_result.get('success')}, 数量={len(blog_tags)}")
                if not tags_result.get("success"):
                    logger.warning(f"获取博客标签失败: {tags_result.get('error')}")
                
                # 获取主题名称
                logger.debug("获取活跃主题名称")
                theme_name = await self.get_active_theme_name()
                logger.debug(f"活跃主题: {theme_name}")
                
                result_data = {
                        "name": site_data.get("name", ""),
                        "description": site_data.get("description", ""),
                        "url": site_data.get("url", ""),
                        "home": site_data.get("home", ""),
                        "wordpress_version": site_data.get("wp_version", ""),
                    "theme_name": theme_name,
                        "language": site_data.get("language", ""),
                        "timezone": site_data.get("timezone_string", ""),
                        "total_posts": posts_stats.get("total", 0) if posts_stats.get("success", True) else 0,
                        "total_pages": pages_stats.get("total", 0) if pages_stats.get("success", True) else 0,
                    "blog_categories": blog_categories,
                    "blog_tags": blog_tags
                    }
                
                logger.info(f"站点信息获取完成: {self.site_url} - WordPress {result_data.get('wordpress_version', 'Unknown')}, 主题: {theme_name}, 文章: {result_data.get('total_posts', 0)}, 页面: {result_data.get('total_pages', 0)}, 分类: {len(blog_categories)}, 标签: {len(blog_tags)}")
                
                return {
                    "success": True,
                    "data": result_data
                }
                
        except Exception as e:
            error_msg = f"获取站点信息失败: {str(e)}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    async def get_posts_stats(self) -> Dict[str, Any]:
        """获取文章统计信息"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    f"{self.base_url}/posts",
                    headers=self.headers,
                    params={"per_page": 1, "context": "edit"},
                    auth=(self.username, self.app_password)
                )

                if response.status_code == 200:
                    total_posts = response.headers.get("X-WP-Total", 0)
                    return {"success": True, "total": int(total_posts)}
                elif response.status_code == 401:
                    return {"success": False, "error": "认证失败：无权限访问文章信息"}
                else:
                    return {"success": False, "error": f"获取文章统计失败: HTTP {response.status_code}"}

        except Exception as e:
            return {"success": False, "error": f"获取文章统计异常: {str(e)}"}
    
    async def get_pages_stats(self) -> Dict[str, Any]:
        """获取页面统计信息"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    f"{self.base_url}/pages",
                    headers=self.headers,
                    params={"per_page": 1, "context": "edit"},
                    auth=(self.username, self.app_password)
                )

                if response.status_code == 200:
                    total_pages = response.headers.get("X-WP-Total", 0)
                    return {"success": True, "total": int(total_pages)}
                elif response.status_code == 401:
                    return {"success": False, "error": "认证失败：无权限访问页面信息"}
                else:
                    return {"success": False, "error": f"获取页面统计失败: HTTP {response.status_code}"}

        except Exception as e:
            return {"success": False, "error": f"获取页面统计异常: {str(e)}"}
    
    async def get_active_theme_name(self) -> str:
        """获取当前激活的主题名称"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    f"{self.site_url}/wp-json/wp/v2/themes",
                    headers=self.headers,
                    auth=(self.username, self.app_password)
                )
                
                if response.status_code == 200:
                    themes = response.json()
                    for theme in themes:
                        if theme.get("status") == "active":
                            name = theme.get("name", "Unknown")
                            # 如果name是字典，提取rendered值
                            if isinstance(name, dict):
                                return name.get("rendered", name.get("raw", "Unknown"))
                            return name
                
                return "Unknown"
                
        except Exception:
            return "Unknown"
    
    async def get_categories(self) -> Dict[str, Any]:
        """获取WordPress博客分类"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    f"{self.base_url}/categories",
                    headers=self.headers,
                    params={"per_page": 100},  # 获取更多分类
                    auth=(self.username, self.app_password)
                )
                
                if response.status_code == 200:
                    categories = response.json()
                    formatted_categories = []
                    
                    for category in categories:
                        formatted_categories.append({
                            "id": category.get("id"),
                            "name": category.get("name", ""),
                            "slug": category.get("slug", ""),
                            "description": category.get("description", ""),
                            "count": category.get("count", 0),
                            "parent": category.get("parent", 0)
                        })
                    
                    return {
                        "success": True,
                        "data": formatted_categories
                    }
                else:
                    return {
                        "success": False,
                        "error": f"获取分类失败: HTTP {response.status_code}：{response.text}"
                    }
                    
        except Exception as e:
            return {
                "success": False,
                "error": f"获取分类异常: {str(e)}"
            }
    
    async def get_tags(self) -> Dict[str, Any]:
        """获取WordPress博客标签"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    f"{self.base_url}/tags",
                    headers=self.headers,
                    params={"per_page": 100},  # 获取更多标签
                    auth=(self.username, self.app_password)
                )
                
                if response.status_code == 200:
                    tags = response.json()
                    formatted_tags = []
                    
                    for tag in tags:
                        formatted_tags.append({
                            "id": tag.get("id"),
                            "name": tag.get("name", ""),
                            "slug": tag.get("slug", ""),
                            "description": tag.get("description", ""),
                            "count": tag.get("count", 0)
                        })
                    
                    return {
                        "success": True,
                        "data": formatted_tags
                    }
                else:
                    return {
                        "success": False,
                        "error": f"获取标签失败: HTTP {response.status_code}：{response.text}"
                    }
                    
        except Exception as e:
            return {
                "success": False,
                "error": f"获取标签异常: {str(e)}"
            }
    
    async def create_tag(self, name: str, description: str = "", slug: str = "") -> Dict[str, Any]:
        """创建WordPress博客标签"""
        try:
            tag_data = {
                "name": name,
                "description": description or "",
                "slug": slug or ""  # 如果不提供slug，WordPress会自动生成
            }
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/tags",
                    headers=self.headers,
                    json=tag_data,
                    auth=(self.username, self.app_password)
                )
                
                if response.status_code == 201:
                    tag = response.json()
                    return {
                        "success": True,
                        "data": {
                            "id": tag.get("id"),
                            "name": tag.get("name", ""),
                            "slug": tag.get("slug", ""),
                            "description": tag.get("description", ""),
                            "count": tag.get("count", 0)
                        }
                    }
                else:
                    error_data = response.json() if response.content else {}
                    error_message = error_data.get('message', f'HTTP {response.status_code}')
                    
                    # 处理常见的错误情况
                    if response.status_code == 400:
                        if 'already exists' in error_message.lower() or 'duplicate' in error_message.lower():
                            return {
                                "success": False,
                                "error": f"标签'{name}'已存在",
                                "error_code": "TAG_EXISTS"
                            }
                    
                    return {
                        "success": False,
                        "error": f"创建标签失败: {error_message}"
                    }
                    
        except Exception as e:
            return {
                "success": False,
                "error": f"创建标签异常: {str(e)}"
            }
    
    async def create_post(self, title: str, content: str, **kwargs) -> Dict[str, Any]:
        """创建WordPress文章"""
        try:
            post_data = {
                "title": title,
                "content": content,
                "status": kwargs.get("status", "publish"),
                "author": kwargs.get("author", 1),
                "categories": kwargs.get("categories", []),
                "tags": kwargs.get("tags", []),
                "meta": kwargs.get("meta", {}),
                "featured_media": kwargs.get("featured_media", 0)
            }
            
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.base_url}/posts",
                    headers=self.headers,
                    json=post_data,
                    auth=(self.username, self.app_password)
                )
                
                if response.status_code == 201:
                    post = response.json()
                    return {
                        "success": True,
                        "data": {
                            "id": post.get("id"),
                            "title": post.get("title", {}).get("rendered", ""),
                            "url": post.get("link", ""),
                            "status": post.get("status", ""),
                            "featured_image_id": post.get("featured_media", 0)
                        }
                    }
                else:
                    error_data = response.json() if response.content else {}
                    return {
                        "success": False,
                        "error": f"创建文章失败: {error_data.get('message', f'HTTP {response.status_code}')}"
                    }
                    
        except Exception as e:
            return {
                "success": False,
                "error": f"创建文章异常: {str(e)}"
            }
    
    async def upload_media(self, file_data: bytes, filename: str, mime_type: str) -> Dict[str, Any]:
        """上传媒体文件到WordPress"""
        try:
            headers = {
                "User-Agent": self.user_agent,
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Type": mime_type
            }
            
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.base_url}/media",
                    headers=headers,
                    content=file_data,
                    auth=(self.username, self.app_password),
                    timeout=60.0
                )
                
                if response.status_code == 201:
                    media = response.json()
                    return {
                        "success": True,
                        "data": {
                            "id": media.get("id"),
                            "url": media.get("source_url", ""),
                            "title": media.get("title", {}).get("rendered", ""),
                            "alt_text": media.get("alt_text", "")
                        }
                    }
                else:
                    error_data = response.json() if response.content else {}
                    return {
                        "success": False,
                        "error": f"上传媒体失败: {error_data.get('message', f'HTTP {response.status_code}')}"
                    }
                    
        except Exception as e:
            return {
                "success": False,
                "error": f"上传媒体异常: {str(e)}"
            } 