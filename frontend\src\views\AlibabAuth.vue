<template>
  <div class="alibaba-auth-container">
    <div class="auth-card">
      <h2>阿里国际站API授权</h2>
      
      <!-- 授权状态显示 -->
      <div class="auth-status" v-if="authInfo">
        <h3>当前授权状态</h3>
        <div class="status-info">
          <p><strong>阿里巴巴账号:</strong> {{ authInfo.alibaba_account || '未获取' }}</p>
          <p><strong>国家:</strong> {{ authInfo.country || '未知' }}</p>
          <p><strong>平台:</strong> {{ authInfo.account_platform || '未知' }}</p>
          <p><strong>授权状态:</strong> 
            <span :class="authInfo.is_active ? 'status-active' : 'status-inactive'">
              {{ authInfo.is_active ? '已授权' : '未授权' }}
            </span>
          </p>
          <p><strong>创建时间:</strong> {{ formatDate(authInfo.token_created_at) }}</p>
          <p><strong>最后刷新:</strong> {{ formatDate(authInfo.last_refresh_at) || '从未刷新' }}</p>
        </div>
      </div>

      <!-- Token状态 -->
      <div class="token-status" v-if="tokenStatus">
        <h3>Token状态</h3>
        <div class="status-info">
          <p><strong>Token有效:</strong> 
            <span :class="tokenStatus.is_valid ? 'status-active' : 'status-inactive'">
              {{ tokenStatus.is_valid ? '有效' : '无效' }}
            </span>
          </p>
          <p><strong>过期时间:</strong> {{ formatDate(tokenStatus.expires_at) }}</p>
          <p><strong>刷新Token过期:</strong> {{ formatDate(tokenStatus.refresh_expires_at) }}</p>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="auth-actions">
        <button 
          class="btn btn-primary" 
          @click="getAuthUrl"
          :disabled="loading"
          v-if="!authInfo || !authInfo.is_active"
        >
          <span v-if="loading">获取中...</span>
          <span v-else>获取授权URL</span>
        </button>

        <button 
          class="btn btn-secondary" 
          @click="getAuthInfo"
          :disabled="loading"
        >
          <span v-if="loading">查询中...</span>
          <span v-else>查询授权状态</span>
        </button>

        <button 
          class="btn btn-warning" 
          @click="refreshToken"
          :disabled="loading || !authInfo || !authInfo.is_active"
          v-if="authInfo && authInfo.is_active"
        >
          <span v-if="loading">刷新中...</span>
          <span v-else>刷新Token</span>
        </button>

        <button 
          class="btn btn-danger" 
          @click="revokeAuth"
          :disabled="loading"
          v-if="authInfo && authInfo.is_active"
        >
          <span v-if="loading">撤销中...</span>
          <span v-else>撤销授权</span>
        </button>
      </div>

      <!-- 消息显示 -->
      <div class="message" v-if="message">
        <div :class="['alert', messageType === 'error' ? 'alert-error' : 'alert-success']">
          {{ message }}
        </div>
      </div>

      <!-- 授权URL显示 -->
      <div class="auth-url" v-if="authUrl">
        <h3>授权链接</h3>
        <p class="url-text">{{ authUrl }}</p>
        <div class="url-actions">
          <button class="btn btn-primary" @click="openAuthUrl">在新窗口打开授权页面</button>
          <button class="btn btn-secondary" @click="copyUrl">复制链接</button>
        </div>
        <div class="auth-instructions">
          <h4>授权步骤：</h4>
          <ol>
            <li>点击"在新窗口打开授权页面"按钮</li>
            <li>在阿里国际站页面登录您的阿里巴巴账号</li>
            <li>点击"授权"按钮</li>
            <li>授权成功后会自动跳转回本页面</li>
            <li>刷新本页面查看授权状态</li>
          </ol>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { formatDateTime } from '@/utils/timezone'

export default {
  name: 'AlibabaAuth',
  data() {
    return {
      loading: false,
      authUrl: '',
      authInfo: null,
      tokenStatus: null,
      message: '',
      messageType: 'success'
    }
  },
  
  mounted() {
    this.getAuthInfo()
    this.getTokenStatus()
    
    // 检查URL参数，看是否是授权回调
    const urlParams = new URLSearchParams(window.location.search)
    const message = urlParams.get('message')
    const account = urlParams.get('account')
    
    if (message) {
      this.showMessage(message, message.includes('成功') ? 'success' : 'error')
      if (account) {
        this.showMessage(`关联账号: ${account}`, 'success')
      }
      // 清理URL参数
      window.history.replaceState({}, document.title, window.location.pathname)
      // 重新获取授权信息
      setTimeout(() => {
        this.getAuthInfo()
        this.getTokenStatus()
      }, 1000)
    }
  },

  methods: {
    async getAuthUrl() {
      this.loading = true
      try {
        const response = await axios.get('/api/v1/alibaba/auth-url')
        this.authUrl = response.data.auth_url
        this.showMessage('授权URL获取成功，请点击下方按钮进行授权', 'success')
      } catch (error) {
        this.showMessage(`获取授权URL失败: ${error.response?.data?.detail || error.message}`, 'error')
      } finally {
        this.loading = false
      }
    },

    async getAuthInfo() {
      this.loading = true
      try {
        const response = await axios.get('/api/v1/alibaba/auth-info')
        this.authInfo = response.data
      } catch (error) {
        if (error.response?.status !== 404) {
          this.showMessage(`获取授权信息失败: ${error.response?.data?.detail || error.message}`, 'error')
        }
      } finally {
        this.loading = false
      }
    },

    async getTokenStatus() {
      this.loading = true
      try {
        const response = await axios.get('/api/v1/alibaba/token-status')
        this.tokenStatus = response.data
      } catch (error) {
        this.showMessage(`获取Token状态失败: ${error.response?.data?.detail || error.message}`, 'error')
      } finally {
        this.loading = false
      }
    },

    async refreshToken() {
      this.loading = true
      try {
        const response = await axios.post('/api/v1/alibaba/refresh-token')
        this.showMessage('Token刷新成功', 'success')
        this.getAuthInfo()
        this.getTokenStatus()
      } catch (error) {
        this.showMessage(`Token刷新失败: ${error.response?.data?.detail || error.message}`, 'error')
      } finally {
        this.loading = false
      }
    },

    async revokeAuth() {
      if (!confirm('确定要撤销阿里国际站授权吗？')) {
        return
      }
      
      this.loading = true
      try {
        await axios.delete('/api/v1/alibaba/revoke')
        this.showMessage('授权已撤销', 'success')
        this.authInfo = null
        this.tokenStatus = null
        this.authUrl = ''
      } catch (error) {
        this.showMessage(`撤销授权失败: ${error.response?.data?.detail || error.message}`, 'error')
      } finally {
        this.loading = false
      }
    },

    openAuthUrl() {
      if (this.authUrl) {
        window.open(this.authUrl, '_blank')
      }
    },

    copyUrl() {
      if (this.authUrl) {
        navigator.clipboard.writeText(this.authUrl).then(() => {
          this.showMessage('授权链接已复制到剪贴板', 'success')
        }).catch(() => {
          this.showMessage('复制失败，请手动复制', 'error')
        })
      }
    },

    showMessage(msg, type = 'success') {
      this.message = msg
      this.messageType = type
      setTimeout(() => {
        this.message = ''
      }, 5000)
    },

    formatDate(dateString) {
      return formatDateTime(dateString) || null
    }
  }
}
</script>

<style scoped>
.alibaba-auth-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.auth-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.auth-card h2 {
  color: #333;
  margin-bottom: 24px;
  text-align: center;
}

.auth-status, .token-status {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.auth-status h3, .token-status h3 {
  color: #495057;
  margin-bottom: 12px;
}

.status-info p {
  margin: 8px 0;
  color: #6c757d;
}

.status-active {
  color: #28a745;
  font-weight: bold;
}

.status-inactive {
  color: #dc3545;
  font-weight: bold;
}

.auth-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #545b62;
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover:not(:disabled) {
  background-color: #e0a800;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.message {
  margin-bottom: 24px;
}

.alert {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.auth-url {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-top: 16px;
}

.auth-url h3 {
  color: #495057;
  margin-bottom: 12px;
}

.url-text {
  background: white;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  word-break: break-all;
  font-family: monospace;
  font-size: 12px;
  margin-bottom: 12px;
}

.url-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.auth-instructions {
  background: white;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.auth-instructions h4 {
  color: #495057;
  margin-bottom: 12px;
}

.auth-instructions ol {
  margin: 0;
  padding-left: 20px;
}

.auth-instructions li {
  margin-bottom: 8px;
  color: #6c757d;
}
</style> 