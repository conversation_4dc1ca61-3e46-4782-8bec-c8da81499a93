#!/usr/bin/env python3
"""
创建.env环境变量配置文件
运行此脚本将在CBEC目录下创建.env文件
"""

import os
from pathlib import Path

# .env文件内容 - 更新为新的域名地址
ENV_CONTENT = """# AI外贸运营系统环境配置文件

# 基本配置
PROJECT_NAME=AI Foreign Trade Operation System
API_V1_STR=/api/v1

# 安全配置
SECRET_KEY=c2fc5f09e96dfc9b4d756eca0d7b3008a3544aefaa9ab1b1e02204a56c6baa3f
ACCESS_TOKEN_EXPIRE_MINUTES=10080

# 数据库配置
DATABASE_URL=mysql+pymysql://root:S3rSTRBm3dBw8EB7@**************:13306/CBEC

# 后端服务配置
BACKEND_HOST=0.0.0.0
BACKEND_PORT=5000
BACKEND_DEBUG=true
BACKEND_RELOAD=true

# 前端服务配置
FRONTEND_HOST=0.0.0.0
FRONTEND_PORT=8080
FRONTEND_URL=https://1088442hg3xl8.vicp.fun

# CORS配置 - 更新为新域名并放宽限制用于调试
CORS_ORIGINS=http://localhost:8080,http://localhost:3000,http://127.0.0.1:8080,http://127.0.0.1:3000,https://1088442hg3xl8.vicp.fun,http://1088442hg3xl8.vicp.fun,http://wf00659340.gnway.cc:8000,https://wf00659340.gnway.cc:8000,*

# 多租户配置
MULTI_TENANT_ENABLED=true
DEFAULT_TENANT_ID=default

# 生产环境配置
PRODUCTION=false

# 日志配置
LOG_LEVEL=INFO

# 阿里国际站API配置 - 更新回调地址为新域名
ALIBABA_APP_KEY=502750
ALIBABA_APP_SECRET=a7cd4a24d3081fd89ae6233f127ba461
ALIBABA_OAUTH_URL=https://open-api.alibaba.com/oauth/authorize
ALIBABA_TOKEN_URL=https://open-api.alibaba.com/rest/auth/token/create
ALIBABA_REFRESH_TOKEN_URL=https://open-api.alibaba.com/rest/auth/token/refresh
ALIBABA_REDIRECT_URI=https://1088442hg3xl8.vicp.fun/api/v1/alibaba/callback

# 文件存储配置
UPLOAD_DIR=uploads
"""

def create_env_file():
    """创建.env文件"""
    # 获取当前脚本所在目录（CBEC目录）
    current_dir = Path(__file__).parent
    env_file_path = current_dir / ".env"
    
    try:
        # 写入.env文件
        with open(env_file_path, 'w', encoding='utf-8') as f:
            f.write(ENV_CONTENT)
        
        print(f"✅ .env文件已创建: {env_file_path}")
        print(f"📁 文件位置: {env_file_path.absolute()}")
        print("\n🔧 配置要点:")
        print("- 后端端口: 5000")
        print("- 前端端口: 8080") 
        print("- 外网地址: https://1088442hg3xl8.vicp.fun")
        print("- 阿里回调: https://1088442hg3xl8.vicp.fun/api/v1/alibaba/callback")
        print("- CORS: 已放宽所有限制用于调试")
        
    except Exception as e:
        print(f"❌ 创建.env文件失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 开始创建.env环境变量配置文件...")
    print("📝 更新内容: 阿里国际站回调地址 → https://1088442hg3xl8.vicp.fun/")
    
    if create_env_file():
        print("\n🎉 .env文件创建成功！")
        print("\n📋 下一步操作:")
        print("1. 启动后端: cd backend && python main.py")
        print("2. 启动前端: cd frontend && npm run serve") 
        print("3. 外网访问: https://1088442hg3xl8.vicp.fun")
    else:
        print("❌ 创建失败，请检查权限和路径") 