<template>
  <el-dialog
    v-model="dialogVisible"
    title="Google Ads 配置管理"
    width="1000px"
    :before-close="handleClose"
  >
    <div class="config-content">
      <!-- 工具栏 -->
      <div class="toolbar">
        <el-button type="primary" @click="showCreateForm = true" icon="el-icon-plus">
          新增配置
        </el-button>
        <el-button @click="loadConfigs" icon="el-icon-refresh">
          刷新
        </el-button>
      </div>

      <!-- 配置列表 -->
      <div class="config-list">
        <el-table :data="configList" v-loading="loading" border stripe>
          <el-table-column prop="config_name" label="配置名称" width="120" />
          <el-table-column prop="customer_id" label="客户ID" width="100" />
          <el-table-column label="开发者令牌" width="120">
            <template #default="scope">
              <span class="masked-token">
                {{ maskToken(scope.row.developer_token) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="OAuth状态" width="120" align="center">
            <template #default="scope">
              <div class="oauth-status">
                <el-tag 
                  :type="getOAuthStatusType(scope.row.authorization_status, scope.row.token_expired)"
                  size="small"
                  style="margin-bottom: 4px;"
                >
                  {{ getOAuthStatusText(scope.row.authorization_status, scope.row.token_expired) }}
                </el-tag>
                <div v-if="scope.row.last_auth_time" class="auth-time">
                  <small>{{ formatAuthTime(scope.row.last_auth_time) }}</small>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="is_active" label="启用状态" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.is_active ? 'success' : 'info'" size="small">
                {{ scope.row.is_active ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="代理状态" width="80" align="center">
            <template #default="scope">
              <el-tag :type="scope.row.use_proxy ? 'warning' : 'info'" size="small">
                {{ scope.row.use_proxy ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="140">
            <template #default="scope">
              {{ formatDateTime(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="280" fixed="right">
            <template #default="scope">
              <el-button-group>
                <el-button 
                  size="mini" 
                  @click="startOAuthAuth(scope.row)" 
                  :loading="scope.row.authoring"
                  type="warning"
                  v-if="scope.row.authorization_status !== 'authorized'"
                >
                  {{ scope.row.authorization_status === 'pending' ? '授权' : '重新授权' }}
                </el-button>
                <el-button 
                  size="mini" 
                  @click="refreshToken(scope.row)" 
                  :loading="scope.row.refreshing"
                  v-if="scope.row.authorization_status === 'authorized' && !scope.row.token_expired && scope.row.has_refresh_token"
                >
                  刷新令牌
                </el-button>
                <el-button size="mini" @click="testConfig(scope.row)" :loading="scope.row.testing">
                  测试
                </el-button>
                <el-button size="mini" type="primary" @click="editConfig(scope.row)">
                  编辑
                </el-button>
                <el-button size="mini" type="danger" @click="deleteConfig(scope.row)">
                  删除
                </el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 新增/编辑表单 -->
      <el-dialog
        v-model="showCreateForm"
        :title="isEdit ? '编辑配置' : '新增配置'"
        width="600px"
        append-to-body
      >
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="120px"
        >
          <el-form-item label="配置名称" prop="config_name">
            <el-input v-model="formData.config_name" placeholder="请输入配置名称" />
          </el-form-item>

          <el-form-item label="客户ID" prop="customer_id">
            <el-input v-model="formData.customer_id" placeholder="请输入Google Ads客户ID" />
            <div class="help-text">
              <small class="text-muted">
                格式如：123-456-7890，可在Google Ads账户右上角找到
              </small>
            </div>
          </el-form-item>

          <el-form-item label="开发者令牌" prop="developer_token">
            <el-input v-model="formData.developer_token" placeholder="请输入Google Ads开发者令牌" show-password />
            <div class="help-text">
              <small class="text-muted">
                <strong>Google Ads API开发者令牌</strong>：在Google Ads管理账户的API中心申请获取
              </small>
            </div>
          </el-form-item>

          <el-form-item label="API密钥" prop="api_key">
            <el-input v-model="formData.api_key" placeholder="请输入Google API Key（可选）" show-password />
            <div class="help-text">
              <small class="text-muted">
                <strong>Google API Key</strong>：以"AIza"开头的API密钥，在Google Cloud Console创建（可选）
              </small>
            </div>
          </el-form-item>

          <el-form-item label="客户端ID" prop="client_id">
            <el-input v-model="formData.client_id" placeholder="请输入OAuth2客户端ID" />
            <div class="help-text">
              <small class="text-muted">
                Google Cloud Console中创建的OAuth2客户端ID
              </small>
            </div>
          </el-form-item>

          <el-form-item label="客户端密钥" prop="client_secret">
            <el-input v-model="formData.client_secret" placeholder="请输入OAuth2客户端密钥" show-password />
          </el-form-item>

          <!-- OAuth授权区域 -->
          <el-form-item label="OAuth授权" v-if="isEdit && currentConfig">
            <div class="oauth-section">
              <div class="oauth-status-display">
                <el-tag 
                  :type="getOAuthStatusType(currentConfig.authorization_status, currentConfig.token_expired)"
                  size="medium"
                >
                  {{ getOAuthStatusText(currentConfig.authorization_status, currentConfig.token_expired) }}
                </el-tag>
                <span v-if="currentConfig.last_auth_time" class="auth-time">
                  最后授权：{{ formatAuthTime(currentConfig.last_auth_time) }}
                </span>
              </div>
              <div class="oauth-actions" style="margin-top: 10px;">
                <el-button 
                  type="warning" 
                  @click="startOAuthAuth(currentConfig)"
                  :loading="currentConfig.authoring"
                  v-if="currentConfig.authorization_status !== 'authorized' || currentConfig.token_expired"
                >
                  <el-icon><Key /></el-icon>
                  {{ currentConfig.authorization_status === 'pending' ? '开始授权' : '重新授权' }}
                </el-button>
                <el-button 
                  @click="refreshToken(currentConfig)"
                  :loading="currentConfig.refreshing"
                  v-if="currentConfig.authorization_status === 'authorized' && !currentConfig.token_expired && currentConfig.has_refresh_token"
                >
                  <el-icon><Refresh /></el-icon>
                  刷新令牌
                </el-button>
              </div>
              <div class="help-text">
                <small class="text-muted">
                  点击授权按钮将打开Google授权页面，完成授权后自动获取刷新令牌
                </small>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="登录客户ID" prop="login_customer_id">
            <el-input v-model="formData.login_customer_id" placeholder="可选：经理账户ID" />
            <div class="help-text">
              <small class="text-muted">
                如果使用经理账户访问子账户，请填写经理账户的客户ID
              </small>
            </div>
          </el-form-item>

          <!-- 代理服务器配置 -->
          <el-form-item label="代理服务器">
            <el-switch
              v-model="formData.use_proxy"
              active-text="启用代理"
              inactive-text="不使用代理"
              style="margin-bottom: 15px;"
            />
            
            <div v-if="formData.use_proxy" style="border: 1px solid #dcdfe6; border-radius: 4px; padding: 15px; background-color: #fafafa;">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="代理类型" prop="proxy_type" label-width="80px">
                    <el-select v-model="formData.proxy_type" placeholder="选择代理类型" style="width: 100%">
                      <el-option label="HTTP" value="http" />
                      <el-option label="HTTPS" value="https" />
                      <el-option label="SOCKS5" value="socks5" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="代理主机" prop="proxy_host" label-width="80px">
                    <el-input v-model="formData.proxy_host" placeholder="如：127.0.0.1" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="端口" prop="proxy_port" label-width="80px">
                    <el-input-number 
                      v-model="formData.proxy_port" 
                      :min="1" 
                      :max="65535" 
                      placeholder="如：8080"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="用户名" prop="proxy_username" label-width="80px">
                    <el-input v-model="formData.proxy_username" placeholder="可选" />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="密码" prop="proxy_password" label-width="80px">
                    <el-input v-model="formData.proxy_password" type="password" placeholder="可选" show-password />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <div class="help-text">
                <small class="text-muted">
                  配置代理服务器用于访问Google Ads API。如果您的网络环境需要代理才能访问Google服务，请启用此选项。
                </small>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="启用状态" prop="is_active">
            <el-switch
              v-model="formData.is_active"
              active-text="启用"
              inactive-text="禁用"
            />
          </el-form-item>
        </el-form>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showCreateForm = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitting">
              {{ isEdit ? '更新' : '创建' }}
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Key, Refresh } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/format'
import { googleAdsConfigService } from '@/services/keyword'

export default {
  name: 'GoogleAdsConfigDialog',
  components: {
    Key,
    Refresh
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    const formRef = ref()
    const loading = ref(false)
    const submitting = ref(false)
    const configList = ref([])
    const showCreateForm = ref(false)
    const isEdit = ref(false)
    const currentConfig = ref(null)

    // 计算属性
    const dialogVisible = computed({
      get: () => props.visible,
      set: (value) => emit('update:visible', value)
    })

    // 表单数据
    const formData = reactive({
      config_name: '',
      customer_id: '',
      developer_token: '',
      api_key: '',
      client_id: '',
      client_secret: '',
      login_customer_id: '',
      is_active: true,
      use_proxy: false,
      proxy_type: 'http',
      proxy_host: '',
      proxy_port: 8080,
      proxy_username: '',
      proxy_password: ''
    })

    // 表单验证规则
    const rules = {
      config_name: [
        { required: true, message: '请输入配置名称', trigger: 'blur' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
      ],
      customer_id: [
        { required: true, message: '请输入客户ID', trigger: 'blur' },
        { pattern: /^\d{3}-\d{3}-\d{4}$/, message: '格式错误，应为 123-456-7890', trigger: 'blur' }
      ],
      developer_token: [
        { required: true, message: '请输入开发者令牌', trigger: 'blur' }
      ],
      api_key: [
        { 
          validator: (rule, value, callback) => {
            if (value && !value.startsWith('AIza')) {
              callback(new Error('API密钥必须是以"AIza"开头的Google API Key'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ],
      client_id: [
        { required: true, message: '请输入客户端ID', trigger: 'blur' }
      ],
      client_secret: [
        { required: true, message: '请输入客户端密钥', trigger: 'blur' }
      ],
      proxy_host: [
        { 
          validator: (rule, value, callback) => {
            if (formData.use_proxy && !value) {
              callback(new Error('启用代理时必须填写代理主机'))
            } else {
              callback()
            }
          }, 
          trigger: 'blur' 
        }
      ],
      proxy_port: [
        { 
          validator: (rule, value, callback) => {
            if (formData.use_proxy && (!value || value < 1 || value > 65535)) {
              callback(new Error('启用代理时必须填写有效的端口号(1-65535)'))
            } else {
              callback()
            }
          }, 
          trigger: 'blur' 
        }
      ]
    }

    // OAuth状态相关方法
    const getOAuthStatusType = (status, tokenExpired = false) => {
      // 优先检查token_expired字段
      if (tokenExpired === true && status === 'authorized') {
        return 'warning'
      }
      switch (status) {
        case 'authorized': return 'success'
        case 'pending': return 'warning'
        case 'expired': return 'danger'
        case 'failed': return 'danger'
        default: return 'info'
      }
    }

    const getOAuthStatusText = (status, tokenExpired = false) => {
      // 优先检查token_expired字段
      if (tokenExpired === true && status === 'authorized') {
        return '令牌已过期'
      }
      switch (status) {
        case 'authorized': return '已授权'
        case 'pending': return '待授权'
        case 'expired': return '已过期'
        case 'failed': return '授权失败'
        default: return '未知'
      }
    }

    const formatAuthTime = (timeStr) => {
      if (!timeStr) return ''
      try {
        const date = new Date(timeStr)
        return date.toLocaleString('zh-CN')
      } catch (e) {
        return timeStr
      }
    }

    // 启动OAuth授权
    const startOAuthAuth = async (config) => {
      try {
        config.authoring = true
        const result = await googleAdsConfigService.startOAuthAuthorization(config.id)
        
        // 打开授权页面
        const authWindow = window.open(
          result.authorization_url,
          'google_oauth',
          'width=600,height=700,scrollbars=yes,resizable=yes'
        )

        // 监听授权窗口关闭
        const checkClosed = setInterval(() => {
          if (authWindow.closed) {
            clearInterval(checkClosed)
            // 授权窗口关闭后，刷新OAuth状态
            refreshOAuthStatus(config)
          }
        }, 1000)

        ElMessage.info('已打开授权页面，请完成Google账户授权')
      } catch (error) {
        console.error('启动OAuth授权失败:', error)
        ElMessage.error(error.response?.data?.detail || '启动授权失败')
      } finally {
        config.authoring = false
      }
    }

    // 刷新OAuth状态
    const refreshOAuthStatus = async (config) => {
      try {
        const status = await googleAdsConfigService.getOAuthStatus(config.id)
        
        // 更新配置状态
        Object.assign(config, {
          authorization_status: status.authorization_status,
          last_auth_time: status.last_auth_time,
          token_expired: status.token_expired,
          has_refresh_token: status.has_refresh_token
        })

        if (status.authorization_status === 'authorized' && !status.token_expired) {
          ElMessage.success('OAuth授权成功！')
          // 如果当前正在编辑这个配置，也更新当前配置对象
          if (currentConfig.value && currentConfig.value.id === config.id) {
            Object.assign(currentConfig.value, {
              authorization_status: status.authorization_status,
              last_auth_time: status.last_auth_time,
              token_expired: status.token_expired,
              has_refresh_token: status.has_refresh_token
            })
          }
        }
      } catch (error) {
        console.error('获取OAuth状态失败:', error)
      }
    }

    // 刷新访问令牌
    const refreshToken = async (config) => {
      try {
        config.refreshing = true
        const result = await googleAdsConfigService.refreshOAuthToken(config.id)
        
        if (result.success) {
          ElMessage.success('令牌刷新成功')
          // 刷新OAuth状态
          await refreshOAuthStatus(config)
        }
      } catch (error) {
        console.error('刷新令牌失败:', error)
        ElMessage.error(error.response?.data?.detail || '刷新令牌失败')
      } finally {
        config.refreshing = false
      }
    }

    // 加载配置列表
    const loadConfigs = async () => {
      loading.value = true
      try {
        const configs = await googleAdsConfigService.getConfigs(false) // 获取所有配置
        
        // 确保configs是数组
        const configArray = Array.isArray(configs) ? configs : []
        
        // 为每个配置添加状态属性并获取OAuth状态
        for (const config of configArray) {
          config.testing = false
          config.authoring = false
          config.refreshing = false
          
          // 获取OAuth状态
          try {
            const oauthStatus = await googleAdsConfigService.getOAuthStatus(config.id)
            config.authorization_status = oauthStatus.authorization_status
            config.last_auth_time = oauthStatus.last_auth_time
            config.token_expired = oauthStatus.token_expired
            config.has_refresh_token = oauthStatus.has_refresh_token
          } catch (error) {
            console.error(`获取配置${config.id}的OAuth状态失败:`, error)
            config.authorization_status = 'pending'
            config.token_expired = false
            config.has_refresh_token = false
          }
        }
        
        configList.value = configArray
        
        // 移除自动编辑第一个配置的逻辑，只显示配置列表
        // if (configArray.length > 0 && !currentConfig.value) {
        //   await editConfig(configArray[0])
        // }
      } catch (error) {
        console.error('加载配置列表失败:', error)
        ElMessage.error('加载配置列表失败')
        // 确保在出错时也设置为空数组
        configList.value = []
      } finally {
        loading.value = false
      }
    }

    // 测试配置
    const testConfig = async (config) => {
      try {
        config.testing = true
        const result = await googleAdsConfigService.testConnection(config.id)
        
        if (result.success) {
          ElMessage.success('连接测试成功')
        } else {
          ElMessage.error(result.message || '连接测试失败')
        }
      } catch (error) {
        console.error('连接测试失败:', error)
        ElMessage.error('连接测试失败')
      } finally {
        config.testing = false
      }
    }

    // 编辑配置
    const editConfig = async (config) => {
      isEdit.value = true
      currentConfig.value = { ...config }
      
      Object.assign(formData, {
        config_name: config.config_name,
        customer_id: config.customer_id,
        developer_token: config.developer_token,
        api_key: config.api_key,
        client_id: config.client_id,
        client_secret: config.client_secret,
        login_customer_id: config.login_customer_id,
        is_active: config.is_active,
        use_proxy: config.use_proxy || false,
        proxy_type: config.proxy_type || 'http',
        proxy_host: config.proxy_host || '',
        proxy_port: config.proxy_port || 8080,
        proxy_username: config.proxy_username || '',
        proxy_password: config.proxy_password || ''
      })
      
      // 获取最新的OAuth状态
      try {
        const oauthStatus = await googleAdsConfigService.getOAuthStatus(config.id)
        currentConfig.value.authorization_status = oauthStatus.authorization_status
        currentConfig.value.last_auth_time = oauthStatus.last_auth_time
        currentConfig.value.token_expired = oauthStatus.token_expired
        currentConfig.value.has_refresh_token = oauthStatus.has_refresh_token
      } catch (error) {
        console.error('获取OAuth状态失败:', error)
        currentConfig.value.authorization_status = 'pending'
        currentConfig.value.token_expired = false
        currentConfig.value.has_refresh_token = false
      }
      
      showCreateForm.value = true
    }

    // 删除配置
    const deleteConfig = async (config) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除配置 "${config.config_name}" 吗？`,
          '确认删除',
          {
            type: 'warning'
          }
        )

        await googleAdsConfigService.deleteConfig(config.id)
        ElMessage.success('删除成功')
        loadConfigs()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除配置失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }

    // 提交表单
    const handleSubmit = async () => {
      try {
        await formRef.value.validate()
        
        submitting.value = true
        
        if (isEdit.value) {
          await googleAdsConfigService.updateConfig(currentConfig.value.id, formData)
          ElMessage.success('配置更新成功')
        } else {
          await googleAdsConfigService.createConfig(formData)
          ElMessage.success('配置创建成功')
        }
        
        showCreateForm.value = false
        resetForm()
        loadConfigs()
        emit('success')
      } catch (error) {
        console.error('操作失败:', error)
        if (error.response?.data?.detail) {
          ElMessage.error(error.response.data.detail)
        } else {
          ElMessage.error('操作失败')
        }
      } finally {
        submitting.value = false
      }
    }

    // 重置表单
    const resetForm = () => {
      Object.assign(formData, {
        config_name: '',
        customer_id: '',
        developer_token: '',
        api_key: '',
        client_id: '',
        client_secret: '',
        login_customer_id: '',
        is_active: true,
        use_proxy: false,
        proxy_type: 'http',
        proxy_host: '',
        proxy_port: 8080,
        proxy_username: '',
        proxy_password: ''
      })
      isEdit.value = false
      currentConfig.value = null
    }

    // 关闭对话框
    const handleClose = () => {
      showCreateForm.value = false
      resetForm()
      emit('update:visible', false)
    }

    // 屏蔽令牌显示
    const maskToken = (token) => {
      if (!token) return ''
      if (token.length <= 8) return token
      return token.substring(0, 4) + '****' + token.substring(token.length - 4)
    }

    // 监听对话框打开
    watch(() => props.visible, (newVisible) => {
      if (newVisible) {
        loadConfigs()
      }
    })

    // 监听URL变化，检查OAuth成功回调
    const checkOAuthSuccess = () => {
      const urlParams = new URLSearchParams(window.location.search)
      if (urlParams.get('oauth_success') === '1') {
        ElMessage.success('OAuth授权成功！')
        // 清除URL参数
        window.history.replaceState({}, document.title, window.location.pathname)
        // 刷新配置列表
        loadConfigs()
      }
    }

    onMounted(() => {
      checkOAuthSuccess()
    })

    return {
      formRef,
      loading,
      submitting,
      configList,
      showCreateForm,
      isEdit,
      currentConfig,
      dialogVisible,
      formData,
      rules,
      getOAuthStatusType,
      getOAuthStatusText,
      formatAuthTime,
      startOAuthAuth,
      refreshToken,
      loadConfigs,
      testConfig,
      editConfig,
      deleteConfig,
      handleSubmit,
      handleClose,
      maskToken,
      formatDateTime
    }
  }
}
</script>

<style scoped>
.config-content {
  min-height: 400px;
}

.toolbar {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-list {
  margin-bottom: 20px;
}

.masked-token {
  font-family: monospace;
  font-size: 12px;
}

.help-text {
  margin-top: 5px;
}

.text-muted {
  color: #666;
}

.oauth-section {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.oauth-status-display {
  display: flex;
  align-items: center;
  gap: 10px;
}

.oauth-actions {
  display: flex;
  gap: 10px;
}

.oauth-status {
  text-align: center;
}

.auth-time {
  color: #666;
  font-size: 12px;
}

.el-button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.el-button-group .el-button {
  margin: 0;
}
</style> 