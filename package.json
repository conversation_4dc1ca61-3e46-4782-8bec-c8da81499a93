{"name": "cbec-parent", "private": true, "version": "1.0.0", "scripts": {"dev": "cd frontend && node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js serve", "frontend:serve": "cd frontend && node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js serve", "frontend:build": "cd frontend && node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js build", "frontend:lint": "cd frontend && node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js lint", "frontend:lint:fix": "cd frontend && node ../../node_modules/@vue/cli-service/bin/vue-cli-service.js lint --fix"}, "dependencies": {"cbec-frontend": "file:CBEC/frontend", "axios": "^0.24.0", "core-js": "^3.19.1", "element-plus": "^2.2.30", "path-browserify": "^1.0.1", "vue": "^3.2.22", "vue-router": "^4.0.12", "vuex": "^4.0.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0-beta.7", "@vue/cli-plugin-eslint": "~5.0.0-beta.7", "@vue/cli-plugin-router": "~5.0.0-beta.7", "@vue/cli-plugin-vuex": "~5.0.0-beta.7", "@vue/cli-service": "~5.0.0-beta.7", "@vue/compiler-sfc": "^3.2.22", "@vue/eslint-config-standard": "^6.1.0", "eslint-plugin-vue": "^7.0.0", "@babel/eslint-parser": "^7.12.16", "eslint": "^7.32.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.1", "sass": "^1.43.4", "sass-loader": "^12.3.0"}}