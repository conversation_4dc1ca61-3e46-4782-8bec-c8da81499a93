# PyTrends功能部署指南

## 📋 概述

PyTrends功能已经完全开发完成，可以集成Google Trends数据到关键词库系统中。本指南将指导您完成最后的部署步骤。

## ✅ 已完成的开发工作

### 后端开发（100%完成）
- ✅ **依赖安装**：已安装 `pytrends>=4.9.2`
- ✅ **数据库模型**：完整的PyTrends相关模型和字段
- ✅ **服务层**：PyTrends服务、配置服务、关键词库服务
- ✅ **API接口**：完整的REST API端点
- ✅ **错误处理**：修复了代理配置和初始化问题

### 前端开发（100%完成）
- ✅ **用户界面**：PyTrends导入对话框、配置管理对话框
- ✅ **服务接口**：完整的API调用接口
- ✅ **主页面集成**：PyTrends按钮已添加到关键词库页面
- ✅ **ESLint配置**：修复了Vue 3兼容性问题

### 数据库设计（准备就绪）
- ✅ **SQL脚本**：`add_pytrends_support.sql` 包含所有必要的表结构

## 🔧 最后部署步骤

### 1. 数据库更新（必需）

执行以下SQL语句来创建PyTrends相关的表：

```sql
-- 1. 更新枚举类型
ALTER TABLE `keyword_library` 
MODIFY COLUMN `update_method` enum('google_ads_api','manual','batch_import','pytrends') 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'manual';

-- 2. 添加PyTrends字段
ALTER TABLE `keyword_library` 
ADD COLUMN `trend_interest` decimal(5,2) DEFAULT NULL COMMENT 'Google Trends兴趣度' AFTER `competition_index`,
ADD COLUMN `trend_growth_rate` decimal(5,2) DEFAULT NULL COMMENT '趋势增长率' AFTER `trend_interest`,
ADD COLUMN `trend_region` varchar(10) DEFAULT NULL COMMENT '趋势地区代码' AFTER `trend_growth_rate`,
ADD COLUMN `trend_timeframe` varchar(20) DEFAULT 'today 12-m' COMMENT '趋势时间范围' AFTER `trend_region`,
ADD COLUMN `trend_data` text DEFAULT NULL COMMENT '趋势数据JSON' AFTER `trend_timeframe`;

-- 3. 创建PyTrends配置表
CREATE TABLE `pytrends_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `config_name` varchar(100) NOT NULL,
  `language` varchar(5) DEFAULT 'zh-CN',
  `timezone` int DEFAULT 480,
  `geo_location` varchar(5) DEFAULT 'CN',
  `default_timeframe` varchar(20) DEFAULT 'today 12-m',
  `max_keywords_per_batch` int DEFAULT 5,
  `request_delay` int DEFAULT 2,
  `retry_attempts` int DEFAULT 3,
  `use_proxy` tinyint(1) DEFAULT 0,
  `proxy_host` varchar(255) DEFAULT NULL,
  `proxy_port` int DEFAULT NULL,
  `proxy_username` varchar(255) DEFAULT NULL,
  `proxy_password` varchar(255) DEFAULT NULL,
  `proxy_type` varchar(10) DEFAULT 'http',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_name` (`config_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. 创建任务历史表
CREATE TABLE `pytrends_task_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` varchar(50) NOT NULL,
  `config_id` int NOT NULL,
  `seed_keywords` text NOT NULL,
  `search_parameters` text DEFAULT NULL,
  `status` enum('pending','running','completed','failed','cancelled') DEFAULT 'pending',
  `progress` int DEFAULT 0,
  `total_keywords` int DEFAULT 0,
  `processed_keywords` int DEFAULT 0,
  `success_keywords` int DEFAULT 0,
  `failed_keywords` int DEFAULT 0,
  `error_message` text DEFAULT NULL,
  `result_summary` text DEFAULT NULL,
  `started_at` timestamp NULL DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_id` (`task_id`),
  KEY `idx_config_id` (`config_id`),
  CONSTRAINT `fk_pytrends_task_config` FOREIGN KEY (`config_id`) REFERENCES `pytrends_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. 插入默认配置
INSERT INTO `pytrends_config` (
  `config_name`, `language`, `timezone`, `geo_location`, 
  `default_timeframe`, `max_keywords_per_batch`, `request_delay`, 
  `retry_attempts`, `is_active`
) VALUES (
  '默认配置', 'zh-CN', 480, 'CN', 
  'today 12-m', 5, 2, 3, 1
);

-- 6. 更新历史表枚举
ALTER TABLE `keyword_update_history` 
MODIFY COLUMN `update_method` enum('google_ads_api','manual','batch_import','pytrends') 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;
```

### 2. 验证部署

数据库更新完成后，您可以验证以下功能：

1. **前端界面**
   - 访问关键词库页面
   - 确认可以看到"PyTrends"按钮（位于Google Ads按钮旁边）

2. **配置管理**
   - 点击PyTrends按钮
   - 点击"管理配置"
   - 验证默认配置是否存在
   - 测试连接功能

3. **关键词导入**
   - 输入种子关键词（如："电商,跨境"）
   - 选择时间范围和地理位置
   - 执行导入测试

## 🎯 功能特性

### 1. 配置管理
- **多配置支持**：可创建多个PyTrends配置
- **语言设置**：支持中文、英文等多种语言
- **地理位置**：支持全球、中国、美国等地区
- **时间范围**：支持多种时间段（12个月、5年等）
- **代理支持**：支持HTTP/HTTPS/SOCKS5代理（暂时禁用以避免库bug）
- **连接测试**：验证配置是否可用

### 2. 关键词导入
- **种子关键词**：基于1-5个种子关键词生成相关词
- **趋势数据**：获取Google Trends兴趣度（0-100分）
- **相关查询**：获取相关搜索查询
- **上升查询**：获取趋势上升的查询
- **批量处理**：自动处理大量关键词
- **进度显示**：实时显示导入进度

### 3. 数据分析
- **兴趣度**：Google Trends兴趣度评分
- **增长率**：趋势增长率计算
- **时间序列**：历史趋势数据
- **地区数据**：特定地区的趋势信息

## 📊 使用流程

### 1. 首次设置
1. 确保数据库已更新
2. 访问关键词库页面
3. 点击"PyTrends"按钮
4. 点击"管理配置"验证默认配置

### 2. 导入关键词
1. 点击"PyTrends"按钮
2. 选择配置（或使用默认配置）
3. 输入种子关键词（每行一个或逗号分隔）
4. 设置导入参数：
   - 时间范围（建议：过去12个月）
   - 地理位置（建议：中国）
   - 分类（可选）
   - 包含相关查询（建议：开启）
   - 包含上升查询（建议：开启）
5. 点击"开始导入"
6. 等待导入完成

### 3. 查看结果
- 导入的关键词会显示在关键词库列表中
- 可以看到趋势兴趣度、增长率等数据
- 可以按趋势数据排序和筛选

## 🐛 故障排除

### 常见问题

1. **连接测试失败**
   - 检查网络连接
   - 确认可以访问Google服务
   - 检查防火墙设置

2. **导入无结果**
   - 检查种子关键词是否有效
   - 尝试更广泛的关键词
   - 调整地理位置设置

3. **导入速度慢**
   - 减少种子关键词数量
   - 增加请求延迟设置
   - 关闭相关查询功能

## 🔧 高级配置

### 性能调优
- **批次大小**：建议保持5个关键词/批
- **请求延迟**：建议2-5秒避免频率限制
- **重试次数**：建议3次处理网络问题

### 代理配置
- 代理功能暂时禁用以避免PyTrends库的bug
- 未来版本将支持完整的代理功能

## 📈 数据说明

### 趋势兴趣度
- 范围：0-100
- 100表示该词在指定时间和地区的最高搜索热度
- 50表示一半的搜索热度
- 0表示没有足够的数据

### 增长率
- 基于线性回归计算
- 正值表示上升趋势
- 负值表示下降趋势
- 单位：百分比

## 🎉 完成！

PyTrends功能现在已经完全集成到您的关键词库系统中。您可以开始使用Google Trends数据来丰富您的关键词研究工作了！

如果遇到任何问题，请检查：
1. 数据库表是否正确创建
2. 后端服务是否正常运行
3. 前端页面是否正确加载
4. 网络是否可以访问Google服务 