-- 为 google_ads_config 表添加代理服务器相关字段
-- 这个脚本用于升级现有数据库结构

-- 添加代理服务器配置字段
ALTER TABLE `google_ads_config` 
ADD COLUMN `use_proxy` tinyint(1) DEFAULT 0 COMMENT '是否使用代理服务器' AFTER `token_expiry`,
ADD COLUMN `proxy_host` varchar(255) DEFAULT NULL COMMENT '代理服务器主机' AFTER `use_proxy`,
ADD COLUMN `proxy_port` int DEFAULT NULL COMMENT '代理服务器端口' AFTER `proxy_host`,
ADD COLUMN `proxy_username` varchar(255) DEFAULT NULL COMMENT '代理服务器用户名' AFTER `proxy_port`,
ADD COLUMN `proxy_password` varchar(255) DEFAULT NULL COMMENT '代理服务器密码' AFTER `proxy_username`,
ADD COLUMN `proxy_type` varchar(10) DEFAULT 'http' COMMENT '代理类型: http, https, socks5' AFTER `proxy_password`;

-- 为代理配置字段添加索引以提高查询性能
ALTER TABLE `google_ads_config` 
ADD INDEX `idx_use_proxy` (`use_proxy`); 