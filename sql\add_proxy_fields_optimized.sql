-- 为 google_ads_config 表添加代理服务器相关字段
-- 优化版本：逐个添加字段，避免长时间锁定

-- 检查表是否存在
SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'google_ads_config';

-- 逐个添加字段，每个字段单独执行
-- 1. 添加 use_proxy 字段
ALTER TABLE `google_ads_config` 
ADD COLUMN `use_proxy` tinyint(1) DEFAULT 0 COMMENT '是否使用代理服务器' AFTER `token_expiry`;

-- 2. 添加 proxy_host 字段
ALTER TABLE `google_ads_config` 
ADD COLUMN `proxy_host` varchar(255) DEFAULT NULL COMMENT '代理服务器主机' AFTER `use_proxy`;

-- 3. 添加 proxy_port 字段
ALTER TABLE `google_ads_config` 
ADD COLUMN `proxy_port` int DEFAULT NULL COMMENT '代理服务器端口' AFTER `proxy_host`;

-- 4. 添加 proxy_username 字段
ALTER TABLE `google_ads_config` 
ADD COLUMN `proxy_username` varchar(255) DEFAULT NULL COMMENT '代理服务器用户名' AFTER `proxy_port`;

-- 5. 添加 proxy_password 字段
ALTER TABLE `google_ads_config` 
ADD COLUMN `proxy_password` varchar(255) DEFAULT NULL COMMENT '代理服务器密码' AFTER `proxy_username`;

-- 6. 添加 proxy_type 字段
ALTER TABLE `google_ads_config` 
ADD COLUMN `proxy_type` varchar(10) DEFAULT 'http' COMMENT '代理类型: http, https, socks5' AFTER `proxy_password`;

-- 7. 添加索引
ALTER TABLE `google_ads_config` 
ADD INDEX `idx_use_proxy` (`use_proxy`);

-- 验证字段是否添加成功
DESCRIBE `google_ads_config`; 