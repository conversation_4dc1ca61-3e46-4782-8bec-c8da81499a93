#!/usr/bin/env python3
"""
阿里巴巴API修复验证脚本
"""

import sys
import os
import json
from datetime import date, timedelta

# 添加项目路径到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.alibaba_service import AlibabaService
from app.core.config import settings

def test_signature_generation():
    """测试签名生成算法"""
    print("="*50)
    print("测试签名生成算法")
    print("="*50)
    
    # 模拟一个AlibabaService实例
    class MockDB:
        pass
    
    service = AlibabaService(MockDB())
    
    # 测试参数（参考官方文档示例）
    test_params = {
        "app_key": "12345678",
        "session": "test_token",
        "method": "alibaba.mydata.self.product.get",
        "sign_method": "sha256",
        "format": "json",
        "timestamp": "1517820392000",
        "simplify": "true",
        "statistics_type": "day",
        "stat_date": "2018-07-01",
        "product_ids": "123"
    }
    
    # 使用实际的app_secret
    original_secret = service.api_service.app_secret
    service.api_service.app_secret = "helloworld"  # 测试用密钥
    
    signature = service._generate_signature(test_params)
    print(f"生成的签名: {signature}")
    
    # 手动验证签名（使用官方算法）
    import hmac
    import hashlib
    
    # 排序参数
    sorted_params = sorted(test_params.items())
    query_string = ""
    for key, value in sorted_params:
        query_string += f"{key}{value}"
    
    print(f"拼接字符串: {query_string}")
    
    # 使用测试密钥生成签名
    test_secret = "helloworld"
    expected_signature = hmac.new(
        test_secret.encode('utf-8'),
        query_string.encode('utf-8'),
        hashlib.sha256
    ).digest().hex().upper()
    
    print(f"期望的签名: {expected_signature}")
    print(f"签名匹配: {signature == expected_signature}")
    
    # 恢复原始密钥
    service.api_service.app_secret = original_secret

def test_product_ids_formatting():
    """测试产品ID格式化"""
    print("\n" + "="*50)
    print("测试产品ID格式化")
    print("="*50)
    
    class MockDB:
        pass
    
    service = AlibabaService(MockDB())
    
    # 测试单个产品ID
    single_id_params = {"product_ids": ["123"]}
    test_params = {
        "app_key": "test",
        "session": "test",
        "method": "test",
        "sign_method": "sha256",
        "format": "json",
        "timestamp": "123456789",
        "simplify": "true"
    }
    
    # 模拟参数处理
    for key, value in single_id_params.items():
        if key == "product_ids" and isinstance(value, list):
            if len(value) == 1:
                test_params[key] = str(value[0])
            else:
                test_params[key] = ",".join(str(pid) for pid in value)
    
    print(f"单个产品ID处理结果: {test_params.get('product_ids')}")
    
    # 测试多个产品ID
    multi_id_params = {"product_ids": ["123", "456", "789"]}
    test_params2 = test_params.copy()
    
    for key, value in multi_id_params.items():
        if key == "product_ids" and isinstance(value, list):
            if len(value) == 1:
                test_params2[key] = str(value[0])
            else:
                test_params2[key] = ",".join(str(pid) for pid in value)
    
    print(f"多个产品ID处理结果: {test_params2.get('product_ids')}")

def test_date_validation():
    """测试日期验证"""
    print("\n" + "="*50)
    print("测试日期验证")
    print("="*50)
    
    today = date.today()
    print(f"今天: {today}")
    
    # 测试未来日期
    future_date = today + timedelta(days=10)
    print(f"未来日期: {future_date}")
    
    if future_date > today:
        print("✅ 未来日期检测正确")
    
    # 测试过早日期
    early_date = today - timedelta(days=100)
    earliest_allowed = today - timedelta(days=90)
    print(f"过早日期: {early_date}")
    print(f"最早允许日期: {earliest_allowed}")
    
    if early_date < earliest_allowed:
        print("✅ 过早日期检测正确")

def main():
    """主函数"""
    print("阿里巴巴API修复验证")
    print(f"应用密钥: {getattr(settings, 'ALIBABA_APP_KEY', 'NOT_SET')}")
    print(f"应用密钥长度: {len(getattr(settings, 'ALIBABA_APP_SECRET', ''))}")
    
    try:
        test_signature_generation()
        test_product_ids_formatting()
        test_date_validation()
        
        print("\n" + "="*50)
        print("✅ 所有测试完成")
        print("="*50)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 