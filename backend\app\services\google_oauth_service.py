import logging
import secrets
import urllib.parse
import requests
import time
from typing import Dict, Optional, Tuple
from google_auth_oauthlib.flow import Flow
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
import tempfile
import os
import json

logger = logging.getLogger(__name__)

class GoogleOAuthService:
    """Google OAuth2 授权服务 - 改进版本，支持网络超时配置"""
    
    # Google Ads API 所需的权限范围
    SCOPES = ['https://www.googleapis.com/auth/adwords']
    
    def __init__(self, client_id: str, client_secret: str, redirect_uri: str = None, proxy_config: dict = None):
        """
        初始化OAuth服务
        
        Args:
            client_id: Google OAuth2客户端ID
            client_secret: Google OAuth2客户端密钥
            redirect_uri: 重定向URI，默认为本地回调
            proxy_config: 代理服务器配置字典，包含：
                - use_proxy: 是否使用代理
                - proxy_host: 代理主机
                - proxy_port: 代理端口
                - proxy_username: 代理用户名（可选）
                - proxy_password: 代理密码（可选）
                - proxy_type: 代理类型（http, https, socks5）
        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri or "http://localhost:8080/oauth/callback"
        self.proxy_config = proxy_config or {}
        
        # 创建自定义的requests会话，配置超时和重试
        self.session = requests.Session()
        
        # 配置代理服务器
        if self.proxy_config.get('use_proxy') and self.proxy_config.get('proxy_host'):
            self._configure_proxy()
        
        # 配置重试策略
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
    def _configure_proxy(self):
        """配置代理服务器"""
        try:
            proxy_host = self.proxy_config.get('proxy_host')
            proxy_port = self.proxy_config.get('proxy_port')
            proxy_username = self.proxy_config.get('proxy_username')
            proxy_password = self.proxy_config.get('proxy_password')
            proxy_type = self.proxy_config.get('proxy_type', 'http').lower()
            
            if not proxy_host or not proxy_port:
                logger.warning("代理配置不完整，跳过代理设置")
                return
            
            # 构建代理URL
            if proxy_username and proxy_password:
                proxy_url = f"{proxy_type}://{proxy_username}:{proxy_password}@{proxy_host}:{proxy_port}"
            else:
                proxy_url = f"{proxy_type}://{proxy_host}:{proxy_port}"
            
            # 设置代理
            proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
            
            self.session.proxies.update(proxies)
            logger.info(f"已配置代理服务器: {proxy_type}://{proxy_host}:{proxy_port}")
            
        except Exception as e:
            logger.error(f"配置代理服务器失败: {e}")
            # 代理配置失败不应该阻止OAuth服务的使用
        
    def generate_authorization_url(self, state: str = None) -> Tuple[str, str]:
        """
        生成授权URL
        
        Args:
            state: 状态参数，用于防止CSRF攻击
            
        Returns:
            Tuple[授权URL, state参数]
        """
        try:
            # 如果没有提供state，生成一个随机state
            if not state:
                state = secrets.token_urlsafe(32)
            
            # 创建临时配置文件
            client_config = {
                "web": {
                    "client_id": self.client_id,
                    "client_secret": self.client_secret,
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "redirect_uris": [self.redirect_uri]
                }
            }
            
            # 创建Flow对象
            flow = Flow.from_client_config(
                client_config=client_config,
                scopes=self.SCOPES
            )
            flow.redirect_uri = self.redirect_uri
            
            # 生成授权URL
            authorization_url, _ = flow.authorization_url(
                access_type='offline',  # 获取刷新令牌
                include_granted_scopes='true',
                state=state,
                prompt='consent'  # 强制显示同意界面以获取刷新令牌
            )
            
            logger.info(f"生成授权URL成功: {authorization_url}")
            return authorization_url, state
            
        except Exception as e:
            logger.error(f"生成授权URL失败: {e}")
            raise
    
    def exchange_code_for_tokens(self, authorization_code: str, state: str = None) -> Dict[str, str]:
        """
        使用授权码换取访问令牌和刷新令牌 - 带超时配置的版本
        
        Args:
            authorization_code: 授权码
            state: 状态参数（可选）
            
        Returns:
            包含access_token和refresh_token的字典
        """
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            try:
                logger.info(f"开始令牌交换，尝试 {attempt + 1}/{max_retries}")
                
                # 使用直接的HTTP请求而不是Flow.fetch_token
                token_data = {
                    'code': authorization_code,
                    'client_id': self.client_id,
                    'client_secret': self.client_secret,
                    'redirect_uri': self.redirect_uri,
                    'grant_type': 'authorization_code'
                }
                
                headers = {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Accept': 'application/json'
                }
                
                logger.info("发送令牌交换请求...")
                response = self.session.post(
                    'https://oauth2.googleapis.com/token',
                    data=token_data,
                    headers=headers,
                    timeout=(30, 120)  # 连接超时30秒，读取超时120秒
                )
                
                logger.info(f"令牌交换响应状态码: {response.status_code}")
                
                if response.status_code == 200:
                    token_response = response.json()
                    logger.info("令牌交换成功")
                    
                    # 验证刷新令牌是否存在
                    if not token_response.get('refresh_token'):
                        logger.warning("未获取到刷新令牌，可能需要重新授权")
                        raise ValueError("未获取到刷新令牌，请重新授权并同意所有权限")
                    
                    tokens = {
                        "access_token": token_response.get('access_token'),
                        "refresh_token": token_response.get('refresh_token'),
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "client_id": self.client_id,
                        "client_secret": self.client_secret,
                        "scopes": self.SCOPES,
                        "expires_in": token_response.get('expires_in'),
                        "token_type": token_response.get('token_type', 'Bearer'),
                        "expiry": None  # 将在调用方计算
                    }
                    
                    return tokens
                else:
                    error_msg = f"令牌交换失败，状态码: {response.status_code}, 响应: {response.text}"
                    logger.error(error_msg)
                    if attempt == max_retries - 1:
                        raise Exception(error_msg)
                    
            except requests.exceptions.Timeout as e:
                logger.warning(f"令牌交换超时 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    raise Exception(f"令牌交换超时，已重试{max_retries}次")
                time.sleep(retry_delay * (attempt + 1))
                
            except requests.exceptions.ConnectionError as e:
                logger.warning(f"连接错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    raise Exception(f"网络连接失败，已重试{max_retries}次")
                time.sleep(retry_delay * (attempt + 1))
                
            except Exception as e:
                logger.error(f"令牌交换失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(retry_delay)
    
    def refresh_access_token(self, refresh_token: str) -> Dict[str, str]:
        """
        使用刷新令牌获取新的访问令牌 - 带超时配置的版本
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            新的令牌信息
        """
        max_retries = 3
        retry_delay = 2
        
        for attempt in range(max_retries):
            try:
                logger.info(f"开始刷新访问令牌，尝试 {attempt + 1}/{max_retries}")
                
                # 使用直接的HTTP请求刷新令牌
                token_data = {
                    'refresh_token': refresh_token,
                    'client_id': self.client_id,
                    'client_secret': self.client_secret,
                    'grant_type': 'refresh_token'
                }
                
                headers = {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Accept': 'application/json'
                }
                
                response = self.session.post(
                    'https://oauth2.googleapis.com/token',
                    data=token_data,
                    headers=headers,
                    timeout=(30, 120)  # 连接超时30秒，读取超时120秒
                )
                
                if response.status_code == 200:
                    token_response = response.json()
                    logger.info("访问令牌刷新成功")
                    
                    tokens = {
                        "access_token": token_response.get('access_token'),
                        "refresh_token": refresh_token,  # 刷新令牌通常不会改变
                        "token_uri": "https://oauth2.googleapis.com/token",
                        "client_id": self.client_id,
                        "client_secret": self.client_secret,
                        "scopes": self.SCOPES,
                        "expires_in": token_response.get('expires_in'),
                        "token_type": token_response.get('token_type', 'Bearer'),
                        "expiry": None  # 将在调用方计算
                    }
                    
                    return tokens
                else:
                    error_msg = f"令牌刷新失败，状态码: {response.status_code}, 响应: {response.text}"
                    logger.error(error_msg)
                    if attempt == max_retries - 1:
                        raise Exception(error_msg)
                    
            except requests.exceptions.Timeout as e:
                logger.warning(f"令牌刷新超时 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    raise Exception(f"令牌刷新超时，已重试{max_retries}次")
                time.sleep(retry_delay * (attempt + 1))
                
            except requests.exceptions.ConnectionError as e:
                logger.warning(f"连接错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    raise Exception(f"网络连接失败，已重试{max_retries}次")
                time.sleep(retry_delay * (attempt + 1))
                
            except Exception as e:
                logger.error(f"访问令牌刷新失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(retry_delay)
    
    def validate_tokens(self, access_token: str, refresh_token: str) -> bool:
        """
        验证令牌是否有效
        
        Args:
            access_token: 访问令牌
            refresh_token: 刷新令牌
            
        Returns:
            是否有效
        """
        try:
            # 创建凭证对象
            credentials = Credentials(
                token=access_token,
                refresh_token=refresh_token,
                token_uri="https://oauth2.googleapis.com/token",
                client_id=self.client_id,
                client_secret=self.client_secret,
                scopes=self.SCOPES
            )
            
            # 检查令牌是否过期，如果过期则尝试刷新
            if credentials.expired:
                request = Request()
                credentials.refresh(request)
            
            # 如果能成功执行到这里，说明令牌有效
            logger.info("令牌验证成功")
            return True
            
        except Exception as e:
            logger.error(f"令牌验证失败: {e}")
            return False 