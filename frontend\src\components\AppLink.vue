<template>
  <component :is="type" v-bind="linkProps(to)">
    <slot />
  </component>
</template>

<script>
import { defineComponent, computed } from 'vue'
import { isExternal } from '../utils/validate'

export default defineComponent({
  name: 'AppLink',
  props: {
    to: {
      type: String,
      required: true
    }
  },
  setup (props) {
    const isExternalLink = computed(() => isExternal(props.to))
    const type = computed(() => isExternalLink.value ? 'a' : 'router-link')

    const linkProps = (to) => {
      if (isExternalLink.value) {
        return {
          href: to,
          target: '_blank',
          rel: 'noopener'
        }
      }
      return {
        to: to
      }
    }

    return {
      isExternalLink,
      type,
      linkProps
    }
  }
})
</script>

<style scoped>
a, :deep(a) {
  text-decoration: none;
  color: inherit;
}
</style>
