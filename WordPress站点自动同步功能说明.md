# WordPress站点自动同步功能说明

## 功能概述

为WordPress站点管理系统增加了定时自动同步和手动一键同步功能，确保站点信息始终保持最新状态。

## 功能特性

### 1. 定时自动同步
- **执行时间**: 每天北京时间早上8:00自动执行
- **同步范围**: 所有启用状态的WordPress站点
- **并发控制**: 限制同时同步3个站点，避免服务器过载
- **自动重试**: 遇到异常时会在1小时后重试
- **日志记录**: 详细记录同步过程和结果

### 2. 手动一键同步
- **位置**: 站点管理页面右上角的绿色"一键同步"按钮
- **实时状态**: 显示同步进度和状态标签
- **后台执行**: 同步在后台进行，不阻塞界面操作
- **进度反馈**: 实时显示剩余同步站点数量

## 同步内容

每次同步会更新以下WordPress站点信息：
- WordPress版本号
- 当前主题名称
- 站点语言设置
- 时区设置
- 文章总数
- 页面总数
- 博客分类列表（包含ID、名称、Slug、描述、文章数）
- 博客标签列表（包含ID、名称、Slug、描述、文章数）
- 同步状态和时间

## 技术实现

### 后端架构
- **调度器**: `SiteSyncScheduler` 类负责定时管理
- **时区处理**: 使用pytz库确保北京时间准确性
- **并发控制**: asyncio.Semaphore限制并发数
- **错误处理**: 完善的异常捕获和日志记录

### 前端交互
- **状态显示**: 动态状态标签显示同步进度
- **进度轮询**: 每2秒检查一次同步状态
- **用户反馈**: 成功/失败消息提示

## 使用指南

### 自动同步
系统会在每天早上8点自动执行，无需人工干预。可以通过服务器日志查看执行情况。

### 手动同步
1. 打开WordPress站点管理页面
2. 点击右上角绿色的"一键同步"按钮
3. 观察状态标签了解同步进度
4. 等待同步完成的提示消息

## 状态说明

### 同步状态类型
- **正在同步**: 黄色标签，显示"正在同步..."
- **同步中**: 蓝色标签，显示剩余站点数量
- **同步完成**: 绿色标签，显示"同步完成"
- **同步失败**: 红色标签，显示"同步状态检查失败"

### 站点同步状态
- **success**: 同步成功
- **failed**: 同步失败
- **pending**: 待同步

## 性能优化

### 并发限制
- 最多同时同步3个站点
- 避免对WordPress站点造成过大压力
- 防止数据库连接池耗尽

### 超时控制
- HTTP请求超时时间：30秒
- 进度检查最长时间：5分钟
- 定时器重试间隔：1小时

## 错误处理

### 常见错误及解决方案
1. **连接超时**: 检查站点是否可访问
2. **认证失败**: 验证用户名和应用程序密码
3. **API异常**: 检查WordPress REST API是否启用

### 错误恢复
- 单个站点失败不影响其他站点同步
- 详细的错误信息记录到日志
- 失败的站点会在下次定时同步时重试

## 系统依赖

### Python包
- `pytz>=2023.3`: 时区处理
- `asyncio`: 异步编程
- `httpx`: HTTP客户端

### 配置要求
- WordPress站点必须启用REST API
- 正确的用户名和应用程序密码
- 网络连通性良好

## 维护建议

1. **定期检查日志**: 确保自动同步正常运行
2. **监控同步状态**: 关注失败的站点并及时处理
3. **更新认证信息**: 定期更新WordPress应用程序密码
4. **性能调优**: 根据服务器性能调整并发数量

## 故障排除

### 常见问题
- **定时同步不执行**: 检查服务是否正常启动
- **同步一直失败**: 验证WordPress站点设置
- **界面状态不更新**: 检查前端轮询逻辑

### 日志查看
系统日志包含详细的同步信息，可用于故障诊断和性能分析。

---

此功能大大提升了WordPress站群管理的自动化程度，减少了手工维护的工作量，确保站点信息的实时性和准确性。 