# 获取客户产品相关表现数据

获取客户产品相关表现数据

GET/POST

alibaba.mydata.self.product.get

描述：获取客户产品相关表现数据

## 参数

|  名称  |  类型  |  是否必须  |  描述  |
| --- | --- | --- | --- |
|  statistics\_type  |  String  |  是  |  统计周期，可以为"day", "week", "month"  |
|  stat\_date  |  String  |  是  |  统计日期  |
|  product\_ids  |  Number\[\]  |  是  |  待查询产品id列表  |

## 响应参数

|  名称  |  类型  |  描述  |
| --- | --- | --- |
|  result  |  Object  |  产品效果查询结果  |
|  effects  |  Object\[\]  |  产品效果  |
|  bookmark  |  Number  |  收藏买家数  |
|  click  |  Number  |  点击  |
|  compare  |  Number  |  对比买家数  |
|  fb  |  Number  |  反馈  |
|  impression  |  Number  |  曝光  |
|  keyword\_effects  |  Object\[\]  |  词来源  |
|  keyword  |  String  |  词  |
|  order  |  Number  |  提交订单数  |
|  product\_id  |  Number  |  产品id  |
|  share  |  Number  |  分享买家数  |
|  visitor  |  Number  |  访客数  |
|  total\_count  |  Number  |  返回结果数量  |

## 错误码

|  错误码  |  错误信息  |  解决方案  |
| --- | --- | --- |
|  没有数据  |  |  |

GET/POSTalibaba.mydata.self.product.get

*   PYTHON
    

```PYTHON
client = iop.IopClient(url, appkey ,appSecret)
request = iop.IopRequest('alibaba.mydata.self.product.get')
request.add_api_param('statistics_type', 'day')
request.add_api_param('stat_date', '2018-07-01')
request.add_api_param('product_ids', '123')
response = client.execute(request, access_token)
print(response.type)
print(response.body)

```

*   非精简返回
    

---
```json
{
  "code": "0",
  "alibaba_mydata_self_product_get_response": {
    "result": {
      "effects": [
        {
          "bookmark": "1",
          "compare": "1",
          "product_id": "1",
          "impression": "1",
          "share": "1",
          "fb": "1",
          "visitor": "1",
          "click": "1",
          "keyword_effects": [
            {
              "keyword": "mp3"
            }
          ],
          "order": "1"
        }
      ],
      "total_count": "1"
    }
  },
  "request_id": "0ba2887315178178017221014"
```