import httpx
import requests  # 添加requests库
import json
import asyncio
from typing import Optional, Dict, Any, Union
from datetime import datetime
import re


class DIFYService:
    """DIFY工作流服务"""
    
    def __init__(self, api_key: str, base_url: str = "http://ai.yufeiind.cn:81/v1"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    async def upload_file(self, file_content: bytes, filename: str, file_type: str = "image") -> Optional[str]:
        """上传文件到DIFY"""
        try:
            upload_url = f"{self.base_url}/files/upload"
            
            # 准备文件上传
            files = {
                'file': (filename, file_content, 'image/jpeg' if file_type == 'image' else 'application/octet-stream')
            }
            data = {
                "user": "user123",
                "type": file_type.upper()
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    upload_url,
                    headers={"Authorization": f"Bearer {self.api_key}"},
                    files=files,
                    data=data,
                    timeout=30.0
                )
                
                if response.status_code == 201:
                    result = response.json()
                    return result.get("id")
                else:
                    print(f"文件上传失败，状态码: {response.status_code}")
                    return None
                    
        except Exception as e:
            print(f"文件上传发生错误: {str(e)}")
            return None
    
    async def run_workflow(
        self, 
        keywords: str, 
        wordpress_url: str, 
        type_param: int = 1,
        tags: Optional[str] = None,
        model: Optional[str] = None,
        image_file_id: Optional[str] = None,
        wp_user: Optional[str] = None,
        wp_pwd: Optional[str] = None,
        response_mode: str = "streaming"  # 改为默认使用streaming模式
    ) -> Dict[str, Any]:
        """运行DIFY工作流 - 使用requests处理streaming响应"""
        try:
            workflow_url = f"{self.base_url}/workflows/run"
            
            # 构建输入参数
            inputs = {
                "keywords": keywords,
                "wordpress_url": wordpress_url,
                "type": type_param
            }
            
            # 添加可选参数（确保model不是空字符串）
            if model and model.strip():
                inputs["model"] = model.strip()
                
            # 添加tags参数
            if tags and tags.strip():
                inputs["tags"] = tags.strip()
            
            # 添加WordPress认证信息
            if wp_user:
                inputs["wp_user"] = wp_user
            if wp_pwd:
                inputs["wp_pwd"] = wp_pwd
            
            # 如果有图片文件ID，添加图片参数
            if image_file_id:
                inputs["img"] = [{
                    "transfer_method": "local_file",
                    "upload_file_id": image_file_id,
                    "type": "image"
                }]
            
            data = {
                "inputs": inputs,
                "response_mode": response_mode,
                "user": "user123"
            }
            
            print(f"DIFY API请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            if response_mode == "streaming":
                # 使用requests处理streaming响应，避免httpx的阻塞问题
                print("使用requests发送streaming请求...")
                
                response = requests.post(
                    workflow_url,
                    headers=self.headers,
                    json=data,
                    stream=True,
                    timeout=(30, 600)  # 连接超时30秒，读取超时600秒（10分钟）
                )
                
                print(f"DIFY API响应状态码: {response.status_code}")
                print(f"响应头: {dict(response.headers)}")
                
                if response.status_code == 200:
                    print("开始完整解析streaming响应...")
                    
                    # 完整解析streaming响应，等待工作流完成
                    result = self._parse_streaming_with_requests(response)
                    print(f"streaming解析结果: {result}")
                    
                    return result
                else:
                    error_detail = response.text
                    print(f"DIFY API错误详情: {error_detail}")
                    return {
                        "status": "error",
                        "message": f"工作流启动失败，状态码: {response.status_code}",
                        "response": error_detail,
                        "request_data": data
                    }
            else:
                # 保留blocking模式作为备选，使用httpx
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        workflow_url,
                        headers=self.headers,
                        json=data,
                        timeout=120.0
                    )
                    
                    if response.status_code == 200:
                        return response.json()
                    else:
                        error_detail = response.text
                        print(f"DIFY API错误详情: {error_detail}")
                        return {
                            "status": "error",
                            "message": f"工作流执行失败，状态码: {response.status_code}",
                            "response": error_detail,
                            "request_data": data
                        }
                    
        except Exception as e:
            return {
                "status": "error",
                "message": f"工作流执行发生错误: {str(e)}"
            }
    
    async def run_workflow_async(
        self, 
        keywords: str, 
        wordpress_url: str, 
        type_param: int = 1,
        tags: Optional[str] = None,
        model: Optional[str] = None,
        image_file_id: Optional[str] = None,
        wp_user: Optional[str] = None,
        wp_pwd: Optional[str] = None,
        response_mode: str = "streaming",
        status_callback: Optional[callable] = None
    ) -> Dict[str, Any]:
        """异步运行DIFY工作流 - 在后台线程中执行requests"""
        import asyncio
        import concurrent.futures
        
        def run_sync_workflow(status_callback=None):
            """在同步线程中执行工作流"""
            try:
                workflow_url = f"{self.base_url}/workflows/run"
                
                # 构建输入参数
                inputs = {
                    "keywords": keywords,
                    "wordpress_url": wordpress_url,
                    "type": type_param
                }
                
                # 添加可选参数
                if model and model.strip():
                    inputs["model"] = model.strip()
                    
                # 添加tags参数
                if tags and tags.strip():
                    inputs["tags"] = tags.strip()
                
                # 添加WordPress认证信息
                if wp_user:
                    inputs["wp_user"] = wp_user
                if wp_pwd:
                    inputs["wp_pwd"] = wp_pwd
                
                # 如果有图片文件ID，添加图片参数
                if image_file_id:
                    inputs["img"] = [{
                        "transfer_method": "local_file",
                        "upload_file_id": image_file_id,
                        "type": "image"
                    }]
                
                data = {
                    "inputs": inputs,
                    "response_mode": response_mode,
                    "user": "user123"
                }
                
                print(f"🔄 后台线程开始执行DIFY工作流...")
                print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
                if response_mode == "streaming":
                    response = requests.post(
                        workflow_url,
                        headers=self.headers,
                        json=data,
                        stream=True,
                        timeout=(30, 600)  # 连接超时30秒，读取超时600秒
                    )
                    
                    print(f"DIFY API响应状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        print("🔄 开始完整解析streaming响应...")
                        result = self._parse_streaming_with_requests(response, status_callback)
                        print(f"✅ streaming解析完成: {result.get('status')}")
                        return result
                    else:
                        error_detail = response.text
                        print(f"❌ DIFY API错误: {error_detail}")
                        return {
                            "status": "error",
                            "message": f"工作流启动失败，状态码: {response.status_code}",
                            "response": error_detail
                        }
                else:
                    # blocking模式的处理保持不变
                    response = requests.post(
                        workflow_url,
                        headers=self.headers,
                        json=data,
                        timeout=(30, 300)
                    )
                    
                    if response.status_code == 200:
                        return response.json()
                    else:
                        return {
                            "status": "error",
                            "message": f"工作流执行失败，状态码: {response.status_code}",
                            "response": response.text
                        }
                        
            except Exception as e:
                print(f"❌ 后台线程执行失败: {str(e)}")
                import traceback
                traceback.print_exc()
                return {
                    "status": "error",
                    "message": f"工作流执行发生错误: {str(e)}"
                }
        
        # 在线程池中执行同步代码
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = await loop.run_in_executor(executor, lambda: run_sync_workflow(status_callback))
            return result

    def _parse_streaming_with_requests(self, response, status_callback=None) -> Dict[str, Any]:
        """完整解析streaming响应，等待工作流完成，支持状态回调"""
        try:
            print("开始完整解析streaming响应，等待工作流完成...")
            
            workflow_run_id = None
            task_id = None
            final_result = None
            event_count = 0
            
            # 完整读取所有streaming事件，直到工作流完成
            for line in response.iter_lines(decode_unicode=True):
                if line:
                    event_count += 1
                    
                    if event_count % 10 == 0:
                        print(f"已处理{event_count}个事件...")
                        # 触发状态回调
                        if status_callback:
                            status_callback("processing", f"已处理{event_count}个事件")
                    
                    # 处理SSE格式
                    if line.startswith("data: "):
                        data_str = line[6:]  # 移除"data: "前缀
                        
                        if data_str == "[DONE]":
                            print("收到[DONE]信号，streaming结束")
                            if status_callback:
                                status_callback("processing", "收到完成信号")
                            break
                        elif data_str:
                            try:
                                data = json.loads(data_str)
                                event_type = data.get("event")
                                
                                # 记录workflow_started事件
                                if event_type == "workflow_started":
                                    workflow_run_id = data.get("workflow_run_id")
                                    task_id = data.get("task_id")
                                    print(f"✅ 工作流已启动: {workflow_run_id}")
                                    if status_callback:
                                        status_callback("running", f"工作流已启动: {workflow_run_id}")
                                
                                # 记录重要事件
                                elif event_type in ["node_started", "node_finished"]:
                                    node_data = data.get("data", {})
                                    node_title = node_data.get("title", "未知节点")
                                    if event_count % 5 == 0:  # 每5个事件打印一次
                                        print(f"事件: {event_type} - {node_title}")
                                    
                                    # 触发状态回调
                                    if status_callback:
                                        if event_type == "node_started":
                                            status_callback("processing", f"开始执行: {node_title}")
                                        else:
                                            status_callback("processing", f"完成执行: {node_title}")
                                
                                # 工作流完成事件
                                elif event_type == "workflow_finished":
                                    print("✅ 工作流执行完成！")
                                    final_result = data.get("data", {})
                                    if status_callback:
                                        workflow_status = final_result.get("status", "unknown")
                                        status_callback("completed", f"工作流完成，状态: {workflow_status}")
                                    break
                                    
                            except json.JSONDecodeError as e:
                                print(f"JSON解析失败: {e}")
                                continue
                    
                    # 处理ping等其他事件
                    elif line.startswith("event: "):
                        event_type = line[7:].strip()
                        if event_type == "ping":
                            print("收到ping信号")
                            if status_callback and event_count % 20 == 0:  # 每20个ping触发一次
                                status_callback("processing", "保持连接中...")
            
            print(f"streaming解析完成，共处理{event_count}个事件")
            
            # 返回完整结果
            if final_result:
                return {
                    "status": "completed",
                    "workflow_run_id": workflow_run_id,
                    "task_id": task_id,
                    "data": final_result
                }
            elif workflow_run_id:
                # 如果有workflow_run_id但没有最终结果，可能是连接中断
                return {
                    "status": "started",
                    "workflow_run_id": workflow_run_id,
                    "task_id": task_id,
                    "message": "工作流已启动，但连接在完成前中断"
                }
            else:
                return {
                    "status": "error",
                    "message": "未能获取工作流信息"
                }
            
        except Exception as e:
            print(f"完整streaming解析失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "status": "error",
                "message": f"解析streaming响应时发生错误: {str(e)}"
            }
    
    async def _parse_streaming_response(self, response) -> tuple[Optional[str], Optional[str]]:
        """解析streaming响应，提取workflow_run_id和task_id"""
        try:
            workflow_run_id = None
            task_id = None
            
            print("开始解析streaming响应...")
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            # 使用aiter_bytes()而不是aiter_lines()，避免阻塞
            buffer = b""
            chunk_count = 0
            max_chunks = 100  # 限制读取的chunk数量
            
            async for chunk in response.aiter_bytes(chunk_size=1024):
                chunk_count += 1
                buffer += chunk
                
                print(f"收到第{chunk_count}个chunk，大小: {len(chunk)}字节")
                print(f"chunk内容: {repr(chunk.decode('utf-8', errors='ignore')[:200])}")
                
                # 尝试解析buffer中的完整行
                try:
                    text = buffer.decode('utf-8')
                    lines = text.split('\n')
                    
                    # 保留最后一行（可能不完整）
                    buffer = lines[-1].encode('utf-8')
                    
                    # 处理完整的行
                    for line in lines[:-1]:
                        line = line.strip()
                        if not line:
                            continue
                            
                        print(f"处理行: {repr(line)}")
                        
                        # 处理SSE格式的数据
                        if line.startswith("data: "):
                            data_str = line[6:]
                            print(f"提取data: {repr(data_str)}")
                            
                            if data_str and data_str != "[DONE]":
                                try:
                                    data = json.loads(data_str)
                                    print(f"解析JSON: {json.dumps(data, ensure_ascii=False, indent=2)}")
                                    
                                    # 查找workflow_run_id
                                    if data.get("event") == "workflow_started":
                                        workflow_run_id = data.get("workflow_run_id")
                                        task_id = data.get("task_id")
                                        print(f"找到workflow_started: workflow_run_id={workflow_run_id}, task_id={task_id}")
                                        return workflow_run_id, task_id
                                    
                                    # 也检查其他包含workflow_run_id的数据
                                    if "workflow_run_id" in data:
                                        workflow_run_id = data.get("workflow_run_id")
                                        task_id = data.get("task_id")
                                        print(f"找到workflow_run_id: {workflow_run_id}, task_id: {task_id}")
                                        if workflow_run_id:
                                            return workflow_run_id, task_id
                                            
                                except json.JSONDecodeError as e:
                                    print(f"JSON解析失败: {e}")
                                    continue
                        
                        # 处理直接的JSON行
                        elif line.startswith("{"):
                            try:
                                data = json.loads(line)
                                print(f"直接JSON: {json.dumps(data, ensure_ascii=False, indent=2)}")
                                
                                if "workflow_run_id" in data:
                                    workflow_run_id = data.get("workflow_run_id")
                                    task_id = data.get("task_id")
                                    print(f"从直接JSON找到: workflow_run_id={workflow_run_id}, task_id={task_id}")
                                    if workflow_run_id:
                                        return workflow_run_id, task_id
                                        
                            except json.JSONDecodeError:
                                continue
                
                except UnicodeDecodeError:
                    print("Unicode解码错误，跳过此chunk")
                    continue
                
                # 限制读取的chunk数量，避免无限等待
                if chunk_count >= max_chunks:
                    print(f"已读取{max_chunks}个chunk，停止解析")
                    break
                
                # 如果已经找到workflow_run_id，提前退出
                if workflow_run_id:
                    break
            
            print(f"streaming解析完成，共处理{chunk_count}个chunk")
            print(f"最终结果: workflow_run_id={workflow_run_id}, task_id={task_id}")
            
            return workflow_run_id, task_id
            
        except Exception as e:
            print(f"解析streaming响应失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None, None
    
    async def _parse_streaming_with_timeout(self, response) -> tuple[Optional[str], Optional[str]]:
        """带超时控制的streaming解析 - 修复版本"""
        try:
            print("开始带超时的streaming解析...")
            
            workflow_run_id = None
            task_id = None
            line_count = 0
            max_lines = 10  # 限制最大行数
            
            # 使用aiter_lines()而不是aiter_bytes()，避免卡住
            async for line in response.aiter_lines():
                line_count += 1
                
                if line:  # 跳过空行
                    print(f"第{line_count}行: {repr(line[:200])}")
                    
                    # 处理SSE格式
                    if line.startswith("data: "):
                        data_str = line[6:]  # 移除"data: "前缀
                        
                        if data_str and data_str != "[DONE]":
                            try:
                                data = json.loads(data_str)
                                print(f"解析JSON成功: {json.dumps(data, ensure_ascii=False, indent=2)}")
                                
                                # 检查workflow_started事件
                                if data.get("event") == "workflow_started":
                                    workflow_run_id = data.get("workflow_run_id")
                                    task_id = data.get("task_id")
                                    print(f"✅ 找到workflow_started!")
                                    print(f"workflow_run_id: {workflow_run_id}")
                                    print(f"task_id: {task_id}")
                                    return workflow_run_id, task_id
                                
                                # 检查其他包含workflow_run_id的事件
                                if "workflow_run_id" in data:
                                    workflow_run_id = data.get("workflow_run_id")
                                    task_id = data.get("task_id")
                                    print(f"✅ 找到workflow_run_id!")
                                    print(f"workflow_run_id: {workflow_run_id}")
                                    print(f"task_id: {task_id}")
                                    if workflow_run_id:
                                        return workflow_run_id, task_id
                                        
                            except json.JSONDecodeError as e:
                                print(f"JSON解析失败: {e}")
                                continue
                    
                    # 处理直接JSON
                    elif line.startswith("{"):
                        try:
                            data = json.loads(line)
                            print(f"直接JSON: {json.dumps(data, ensure_ascii=False, indent=2)}")
                            
                            if "workflow_run_id" in data:
                                workflow_run_id = data.get("workflow_run_id")
                                task_id = data.get("task_id")
                                print(f"从直接JSON找到: {workflow_run_id}, {task_id}")
                                if workflow_run_id:
                                    return workflow_run_id, task_id
                                    
                        except json.JSONDecodeError:
                            continue
                
                # 限制读取行数，避免无限等待
                if line_count >= max_lines:
                    print(f"已读取{max_lines}行，停止解析")
                    break
                
                # 如果已经找到了workflow_run_id，立即退出
                if workflow_run_id:
                    break
            
            print(f"解析完成，共读取{line_count}行")
            print(f"最终结果: workflow_run_id={workflow_run_id}, task_id={task_id}")
            return workflow_run_id, task_id
            
        except Exception as e:
            print(f"带超时的streaming解析失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None, None
    
    async def _parse_streaming_response_simple(self, response) -> tuple[Optional[str], Optional[str]]:
        """简化的streaming响应解析方法"""
        try:
            print("开始简化streaming解析...")
            
            # 读取完整响应内容
            content = await response.aread()
            text = content.decode('utf-8', errors='ignore')
            
            print(f"完整响应内容长度: {len(text)}字符")
            print(f"响应内容前1000字符: {repr(text[:1000])}")
            
            workflow_run_id = None
            task_id = None
            
            # 按行分割并处理
            lines = text.split('\n')
            print(f"总共{len(lines)}行")
            
            for i, line in enumerate(lines[:20]):  # 只处理前20行
                line = line.strip()
                if not line:
                    continue
                    
                print(f"第{i+1}行: {repr(line)}")
                
                # 处理SSE格式
                if line.startswith("data: "):
                    data_str = line[6:]
                    if data_str and data_str != "[DONE]":
                        try:
                            data = json.loads(data_str)
                            print(f"解析JSON成功: {json.dumps(data, ensure_ascii=False, indent=2)}")
                            
                            if data.get("event") == "workflow_started":
                                workflow_run_id = data.get("workflow_run_id")
                                task_id = data.get("task_id")
                                print(f"找到workflow_started: {workflow_run_id}, {task_id}")
                                return workflow_run_id, task_id
                            
                            if "workflow_run_id" in data:
                                workflow_run_id = data.get("workflow_run_id")
                                task_id = data.get("task_id")
                                print(f"找到workflow_run_id: {workflow_run_id}, {task_id}")
                                if workflow_run_id:
                                    return workflow_run_id, task_id
                                    
                        except json.JSONDecodeError as e:
                            print(f"JSON解析失败: {e}")
                            continue
                
                # 处理直接JSON
                elif line.startswith("{"):
                    try:
                        data = json.loads(line)
                        print(f"直接JSON: {json.dumps(data, ensure_ascii=False, indent=2)}")
                        
                        if "workflow_run_id" in data:
                            workflow_run_id = data.get("workflow_run_id")
                            task_id = data.get("task_id")
                            print(f"从直接JSON找到: {workflow_run_id}, {task_id}")
                            if workflow_run_id:
                                return workflow_run_id, task_id
                                
                    except json.JSONDecodeError:
                        continue
            
            print(f"简化解析完成: workflow_run_id={workflow_run_id}, task_id={task_id}")
            return workflow_run_id, task_id
            
        except Exception as e:
            print(f"简化streaming解析失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None, None
    
    async def get_workflow_status(self, workflow_run_id: str) -> Dict[str, Any]:
        """获取工作流执行状态"""
        try:
            status_url = f"{self.base_url}/workflows/run/{workflow_run_id}"
            
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    status_url,
                    headers=self.headers,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"DIFY状态查询结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    
                    # 解析outputs字段（如果是字符串则转换为字典）
                    if "outputs" in result and isinstance(result["outputs"], str):
                        try:
                            result["outputs"] = json.loads(result["outputs"])
                        except json.JSONDecodeError:
                            print(f"警告：无法解析outputs字段: {result['outputs']}")
                    
                    return result
                else:
                    return {
                        "status": "error", 
                        "message": f"获取状态失败，状态码: {response.status_code}",
                        "response": response.text
                    }
                    
        except Exception as e:
            return {
                "status": "error",
                "message": f"获取状态发生错误: {str(e)}"
            }
    
    async def wait_for_completion(
        self, 
        workflow_run_id: str, 
        max_wait_time: int = 300,  # 最大等待5分钟
        poll_interval: int = 5     # 每5秒查询一次
    ) -> Dict[str, Any]:
        """等待工作流完成并返回最终结果"""
        start_time = datetime.now()
        
        while True:
            # 检查是否超时
            elapsed = (datetime.now() - start_time).total_seconds()
            if elapsed > max_wait_time:
                return {
                    "status": "timeout",
                    "message": f"工作流执行超时（{max_wait_time}秒）"
                }
            
            # 查询状态
            status_result = await self.get_workflow_status(workflow_run_id)
            
            if status_result.get("status") == "error":
                return status_result
            
            workflow_status = status_result.get("status")
            
            if workflow_status in ["succeeded", "failed", "stopped"]:
                # 工作流已完成
                return {
                    "status": "completed",
                    "data": status_result,
                    "workflow_status": workflow_status,
                    "outputs": status_result.get("outputs"),
                    "error": status_result.get("error")
                }
            elif workflow_status == "running":
                # 继续等待
                await asyncio.sleep(poll_interval)
                continue
            else:
                # 未知状态
                return {
                    "status": "error",
                    "message": f"未知的工作流状态: {workflow_status}",
                    "data": status_result
                }
    
    async def stop_workflow(self, task_id: str) -> Dict[str, Any]:
        """停止工作流执行"""
        try:
            stop_url = f"{self.base_url}/workflows/tasks/{task_id}/stop"
            
            data = {
                "user": "user123"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    stop_url,
                    headers=self.headers,
                    json=data,
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    return {
                        "status": "error",
                        "message": f"停止工作流失败，状态码: {response.status_code}"
                    }
                    
        except Exception as e:
            return {
                "status": "error",
                "message": f"停止工作流发生错误: {str(e)}"
            }

    def parse_result(self, result_text: str) -> Dict[str, Any]:
        """解析DIFY返回的结果文本"""
        try:
            if not result_text:
                return {"success": False, "message": "结果文本为空"}
            
            # 解析结果文本，提取标题、链接、ID等信息
            title_match = re.search(r'标题[:：]\s*(.+)', result_text)
            url_match = re.search(r'链接[:：]\s*(https?://[^\s]+)', result_text)
            id_match = re.search(r'ID[:：]\s*(\d+)', result_text)
            image_id_match = re.search(r'特色图片ID[:：]\s*(\d+)', result_text)
            
            if title_match and url_match:
                return {
                    "success": True,
                    "title": title_match.group(1).strip(),
                    "url": url_match.group(1).strip(),
                    "article_id": id_match.group(1).strip() if id_match else None,
                    "featured_image_id": image_id_match.group(1).strip() if image_id_match else None
                }
            else:
                return {
                    "success": False,
                    "message": "无法从结果中解析出标题和链接",
                    "raw_result": result_text
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"解析结果时发生错误: {str(e)}",
                "raw_result": result_text
            } 