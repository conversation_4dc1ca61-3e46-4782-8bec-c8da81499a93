from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, Date, BigInteger, DECIMAL, Enum
from sqlalchemy.sql import func
from app.models.base import BaseModel

class AlibabaKeywordPerformance(BaseModel):
    """阿里巴巴关键词表现数据模型"""
    __tablename__ = "alibaba_keyword_performance"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(50), index=True, comment="租户ID")
    user_id = Column(Integer, index=True, comment="关联的用户ID")
    
    # 关键词基本信息
    keyword = Column(String(200), index=True, comment="关键词")
    
    # 统计周期信息
    statistics_type = Column(Enum("week", "month", name="keyword_statistics_type_enum"), comment="统计周期类型")
    start_date = Column(Date, comment="统计开始日期")
    end_date = Column(Date, comment="统计结束日期")
    
    # 关键词表现数据
    sum_show_cnt = Column(BigInteger, default=0, comment="曝光量")
    sum_click_cnt = Column(BigInteger, default=0, comment="点击量")
    ctr = Column(DECIMAL(8,4), default=0.0000, comment="点击率")
    search_pv_index = Column(BigInteger, default=0, comment="搜索热度")
    gs_tp_member_set_cnt = Column(BigInteger, default=0, comment="卖家竞争度")
    
    # Top10相关数据
    avg_sum_show_cnt = Column(BigInteger, default=0, comment="Top10平均曝光量")
    avg_sum_click_cnt = Column(BigInteger, default=0, comment="Top10平均点击量")
    
    # P4P (Pay for Performance) 相关数据
    sum_p4p_show_cnt = Column(BigInteger, default=0, comment="直通车曝光量")
    sum_p4p_click_cnt = Column(BigInteger, default=0, comment="直通车点击量")
    is_p4p_kw = Column(Boolean, default=False, comment="是否为P4P推广关键词")
    
    # 关键词状态
    keywords_in_use = Column(Boolean, default=False, comment="是否已设置为关键词")
    keywords_viewed = Column(Boolean, default=False, comment="是否有曝光")
    
    # 数据同步信息
    sync_time = Column(DateTime, default=func.now(), comment="数据同步时间")
    data_source = Column(String(50), default="alibaba_api", comment="数据来源")
    
    class Config:
        table = True

class AlibabaKeywordDatePeriod(BaseModel):
    """阿里巴巴关键词数据可用时间周期模型"""
    __tablename__ = "alibaba_keyword_date_periods"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(50), index=True, comment="租户ID")
    user_id = Column(Integer, index=True, comment="关联的用户ID")
    
    # 时间周期信息
    period_type = Column(Enum("week", "month", name="keyword_period_type_enum"), comment="周期类型")
    start_date = Column(Date, comment="周期开始日期")
    end_date = Column(Date, comment="周期结束日期")
    
    # 周期状态
    is_available = Column(Boolean, default=True, comment="是否可用")
    is_current = Column(Boolean, default=False, comment="是否为当前周期")
    
    # 更新信息
    last_updated = Column(DateTime, default=func.now(), comment="最后更新时间")
    
    class Config:
        table = True

class AlibabaKeywordSummary(BaseModel):
    """阿里巴巴关键词统计汇总模型"""
    __tablename__ = "alibaba_keyword_summary"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(50), index=True, comment="租户ID")
    user_id = Column(Integer, index=True, comment="关联的用户ID")
    
    # 汇总周期信息
    summary_date = Column(Date, comment="汇总日期")
    summary_type = Column(Enum("weekly", "monthly", "quarterly", "yearly", name="keyword_summary_type_enum"), comment="汇总类型")
    
    # 汇总统计数据
    total_keywords = Column(Integer, default=0, comment="关键词总数")
    active_keywords = Column(Integer, default=0, comment="有效关键词数")
    p4p_keywords = Column(Integer, default=0, comment="P4P推广关键词数")
    
    # 汇总表现数据
    total_show = Column(BigInteger, default=0, comment="总曝光量")
    total_click = Column(BigInteger, default=0, comment="总点击量")
    avg_ctr = Column(DECIMAL(8,4), default=0.0000, comment="平均点击率")
    total_search_pv_index = Column(BigInteger, default=0, comment="总搜索热度")
    
    # P4P汇总数据
    total_p4p_show = Column(BigInteger, default=0, comment="P4P总曝光量")
    total_p4p_click = Column(BigInteger, default=0, comment="P4P总点击量")
    p4p_ctr = Column(DECIMAL(8,4), default=0.0000, comment="P4P平均点击率")
    
    # 创建时间
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    
    class Config:
        table = True

class AlibabaKeywordTrend(BaseModel):
    """阿里巴巴关键词趋势数据模型"""
    __tablename__ = "alibaba_keyword_trends"
    
    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(String(50), index=True, comment="租户ID")
    user_id = Column(Integer, index=True, comment="关联的用户ID")
    
    # 关键词信息
    keyword = Column(String(200), index=True, comment="关键词")
    trend_date = Column(Date, index=True, comment="趋势日期")
    
    # 趋势数据
    daily_show = Column(BigInteger, default=0, comment="当日曝光量")
    daily_click = Column(BigInteger, default=0, comment="当日点击量")
    daily_ctr = Column(DECIMAL(8,4), default=0.0000, comment="当日点击率")
    search_heat_index = Column(BigInteger, default=0, comment="搜索热度指数")
    
    # 趋势变化
    show_trend = Column(String(20), comment="曝光趋势 (up/down/stable)")
    click_trend = Column(String(20), comment="点击趋势 (up/down/stable)")
    ctr_trend = Column(String(20), comment="点击率趋势 (up/down/stable)")
    
    # 创建时间
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    
    class Config:
        table = True 