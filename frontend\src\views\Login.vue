<template>
  <div class="login-container">
    <div class="login-wrapper">
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        autocomplete="on"
        label-position="left"
      >
        <div class="title-container">
          <h3 class="title">AI外贸运营系统</h3>
          <div class="subtitle">SAAS版</div>
        </div>

        <el-form-item prop="email" class="form-item">
          <span class="svg-container">
            <el-icon><Message /></el-icon>
          </span>
          <el-input
            v-model="loginForm.email"
            placeholder="邮箱"
            name="email"
            type="text"
            tabindex="1"
            autocomplete="on"
            class="input-field"
          />
        </el-form-item>

        <el-form-item prop="password" class="form-item">
          <span class="svg-container">
            <el-icon><Lock /></el-icon>
          </span>
          <el-input
            v-model="loginForm.password"
            :type="passwordType"
            placeholder="密码"
            name="password"
            tabindex="2"
            autocomplete="on"
            class="input-field"
            @keyup.enter="handleLogin"
          />
          <span class="show-pwd" @click="showPwd">
            <el-icon v-if="passwordType === 'password'"><View /></el-icon>
            <el-icon v-else><Hide /></el-icon>
          </span>
        </el-form-item>

        <el-button
          :loading="loading"
          type="primary"
          class="login-btn"
          @click="handleLogin"
        >
          登录
        </el-button>

        <div class="tips">
          <div class="tip-item">
            <span class="tip-label">默认账号：</span>
            <span class="tip-value"><EMAIL></span>
          </div>
          <div class="tip-item">
            <span class="tip-label">默认密码：</span>
            <span class="tip-value">admin123</span>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Message, Lock, View, Hide } from '@element-plus/icons-vue'

export default defineComponent({
  name: 'Login',
  components: {
    Message,
    Lock,
    View,
    Hide
  },
  setup () {
    const loginForm = reactive({
      email: '<EMAIL>',
      password: 'admin123'
    })

    const loginRules = {
      email: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' }
      ]
    }

    const store = useStore()
    const router = useRouter()
    const loading = ref(false)
    const passwordType = ref('password')
    const loginFormRef = ref(null)

    const showPwd = () => {
      passwordType.value = passwordType.value === 'password' ? 'text' : 'password'
    }

    const handleLogin = () => {
      loginFormRef.value?.validate(valid => {
        if (valid) {
          loading.value = true
          store.dispatch('auth/login', {
            email: loginForm.email,
            password: loginForm.password
          }).then(() => {
            ElMessage({
              message: '登录成功',
              type: 'success',
              duration: 2000
            })
            router.push({ path: '/' })
          }).catch(error => {
            console.error(error)
            ElMessage({
              message: error.response?.data?.detail || '登录失败',
              type: 'error',
              duration: 5 * 1000
            })
          }).finally(() => {
            loading.value = false
          })
        } else {
          console.log('表单验证失败')
          return false
        }
      })
    }

    return {
      loginForm,
      loginRules,
      loading,
      passwordType,
      loginFormRef,
      showPwd,
      handleLogin
    }
  }
})
</script>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;
$primary_blue: #409EFF;

.login-container {
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.login-wrapper {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
  max-width: 480px;
  width: 100%;
}

.login-form {
  padding: 40px;
  position: relative;
}

.title-container {
  text-align: center;
  margin-bottom: 40px;

  .title {
    font-size: 28px;
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-weight: 600;
    letter-spacing: 1px;
  }

  .subtitle {
    font-size: 16px;
    color: #7f8c8d;
    font-weight: 400;
  }
}

.form-item {
  margin-bottom: 24px;
  position: relative;

  :deep(.el-form-item__error) {
    position: absolute;
    top: 100%;
    left: 0;
    font-size: 12px;
    color: #f56c6c;
    margin-top: 4px;
  }
}

.svg-container {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: $dark_gray;
  z-index: 10;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.input-field {
  width: 100%;

  :deep(.el-input__wrapper) {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 45px 12px 45px;
    transition: all 0.3s ease;
    box-shadow: none;

    &:hover {
      border-color: $primary_blue;
    }

    &.is-focus {
      border-color: $primary_blue;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }

  :deep(.el-input__inner) {
    background: transparent;
    border: none;
    color: #2c3e50;
    font-size: 14px;
    height: auto;
    line-height: 1.5;

    &::placeholder {
      color: #adb5bd;
    }

    &:-webkit-autofill {
      box-shadow: 0 0 0 1000px #f8f9fa inset !important;
      -webkit-text-fill-color: #2c3e50 !important;
      transition: background-color 5000s ease-in-out 0s;
    }
  }
}

.show-pwd {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: $dark_gray;
  cursor: pointer;
  user-select: none;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  transition: color 0.3s ease;

  &:hover {
    color: $primary_blue;
  }
}

.login-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  margin-bottom: 24px;
  background: linear-gradient(135deg, $primary_blue 0%, #5a9cfc 100%);
  border: none;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.tips {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid $primary_blue;

  .tip-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .tip-label {
    font-size: 13px;
    color: #6c757d;
    font-weight: 500;
    min-width: 80px;
  }

  .tip-value {
    font-size: 13px;
    color: #2c3e50;
    font-family: 'Courier New', monospace;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background: #dee2e6;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-container {
    padding: 10px;
  }

  .login-wrapper {
    margin: 0;
    border-radius: 8px;
  }

  .login-form {
    padding: 30px 20px;
  }

  .title-container .title {
    font-size: 24px;
  }
}

// 移除原有的深层样式选择器，避免冲突
:deep(.el-form-item) {
  margin-bottom: 0;

  .el-form-item__content {
    position: relative;
  }
}
</style>
