import logging
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session
from app.models.tenant import Tenant
from app.models.user import User
from app.models.role import Role
from app.models.subscription import Subscription
from app.core.config import settings
from app.core.security import get_password_hash

logger = logging.getLogger(__name__)

# 初始数据
FIRST_SUPERUSER = "<EMAIL>"
FIRST_SUPERUSER_PASSWORD = "admin123"
FIRST_SUPERUSER_NAME = "系统管理员"
DEFAULT_TENANT_ID = "default"
DEFAULT_TENANT_NAME = "默认租户"
DEFAULT_TENANT_DESC = "系统默认租户"

def init_db(db: Session) -> None:
    """
    初始化数据库
    """
    # 创建默认租户
    tenant = db.query(Tenant).filter(Tenant.id == DEFAULT_TENANT_ID).first()
    if not tenant:
        tenant = Tenant(
            id=DEFAULT_TENANT_ID,
            name=DEFAULT_TENANT_NAME,
            description=DEFAULT_TENANT_DESC,
            is_active=True
        )
        db.add(tenant)
        logger.info(f"已创建默认租户: {DEFAULT_TENANT_NAME}")
        db.commit()
    
    # 创建超级管理员角色
    role = db.query(Role).filter(Role.name == "超级管理员", Role.tenant_id == DEFAULT_TENANT_ID).first()
    if not role:
        role = Role(
            name="超级管理员",
            permissions=["*"],  # 所有权限
            tenant_id=DEFAULT_TENANT_ID
        )
        db.add(role)
        db.commit()
        db.refresh(role)
        logger.info("已创建超级管理员角色")
    
    # 创建超级用户
    user = db.query(User).filter(User.email == FIRST_SUPERUSER).first()
    if not user:
        user = User(
            email=FIRST_SUPERUSER,
            hashed_password=get_password_hash(FIRST_SUPERUSER_PASSWORD),
            full_name=FIRST_SUPERUSER_NAME,
            is_active=True,
            is_superuser=True,
            tenant_id=DEFAULT_TENANT_ID,
            role_id=role.id if role else None
        )
        db.add(user)
        logger.info(f"已创建超级管理员用户: {FIRST_SUPERUSER}")
        db.commit()
    
    # 创建订阅计划
    if db.query(Subscription).count() == 0:
        basic = Subscription(
            name="基础版",
            price=99.00,
            description="基础功能",
            features=["数据采集", "AI报表"],
            tenant_id=DEFAULT_TENANT_ID
        )
        pro = Subscription(
            name="专业版",
            price=299.00,
            description="专业功能",
            features=["数据采集", "AI报表", "AI站群", "AI运营"],
            tenant_id=DEFAULT_TENANT_ID
        )
        db.add_all([basic, pro])
        logger.info("已创建默认订阅计划")
        db.commit() 