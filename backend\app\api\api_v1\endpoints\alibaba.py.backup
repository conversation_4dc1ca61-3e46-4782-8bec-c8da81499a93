from fastapi import APIRouter, Depends, HTTPException, Request, Query
from fastapi.responses import RedirectResponse
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime, timedelta

from app.api import deps
from app.models.user import User
from app.models.alibaba_auth import AlibabaAuth
from app.schemas.alibaba import (
    AlibabaAuthURL, 
    AlibabaCallback, 
    AlibabaAuthResponse, 
    AlibabaAuthInfo,
    AlibabaTokenStatus
)
from app.services.alibaba_service import AlibabaAPIService
from app.core.config import settings
import logging
import uuid

logger = logging.getLogger(__name__)

router = APIRouter()
alibaba_service = AlibabaAPIService()

@router.get("/auth-url", response_model=AlibabaAuthURL)
def get_alibaba_auth_url(
    current_user: User = Depends(deps.get_current_user)
) -> AlibabaAuthURL:
    """
    获取阿里国际站授权URL
    
    生成用于用户授权的阿里国际站OAuth URL
    """
    try:
        # 生成状态参数，用于防止CSRF攻击
        state = f"{current_user.id}_{uuid.uuid4().hex}"
        
        # 生成授权URL
        auth_url = alibaba_service.generate_auth_url(state=state)
        
        logger.info(f"用户 {current_user.id} 请求阿里国际站授权URL")
        
        return AlibabaAuthURL(auth_url=auth_url, state=state)
        
    except Exception as e:
        logger.error(f"生成授权URL失败: {e}")
        raise HTTPException(status_code=500, detail=f"生成授权URL失败: {str(e)}")

@router.get("/callback")
def alibaba_auth_callback(
    request: Request,
    code: str = Query(..., description="授权码"),
    state: Optional[str] = Query(None, description="状态参数"),
    db: Session = Depends(deps.get_db)
):
    """
    阿里国际站授权回调处理
    
    处理用户授权后的回调，获取访问令牌并保存到数据库
    """
    try:
        logger.info(f"收到阿里国际站授权回调: code={code[:10]}..., state={state}")
        
        # 验证状态参数（如果提供了的话）
        user_id = None
        if state:
            try:
                user_id = int(state.split('_')[0])
            except (ValueError, IndexError):
                logger.warning(f"无效的状态参数: {state}")
        
        # 使用授权码获取访问令牌
        token_data = alibaba_service.get_access_token(code)
        
        # 如果没有从state中获取到user_id，需要用户重新登录或提供用户信息
        if not user_id:
            # 重定向到前端页面，让用户重新登录后关联账号
            return RedirectResponse(
                url=f"{settings.FRONTEND_URL}/alibaba-auth-success?message=请重新登录后关联阿里巴巴账号&account={token_data.get('account', '')}"
            )
        
        # 查找用户
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 保存授权信息
        auth_record = alibaba_service.save_auth_info(
            db=db,
            user_id=user.id,
            tenant_id=user.tenant_id,
            token_data=token_data,
            code=code
        )
        
        logger.info(f"阿里国际站授权成功: user_id={user.id}, account={token_data.get('account')}")
        
        # 重定向到前端成功页面
        return RedirectResponse(
            url=f"{settings.FRONTEND_URL}/alibaba-auth-success?message=授权成功&account={token_data.get('account', '')}"
        )
        
    except Exception as e:
        logger.error(f"阿里国际站授权回调处理失败: {e}")
        # 重定向到前端错误页面
        return RedirectResponse(
            url=f"{settings.FRONTEND_URL}/alibaba-auth-error?message={str(e)}"
        )

@router.post("/callback", response_model=AlibabaAuthResponse)
def alibaba_auth_callback_api(
    callback_data: AlibabaCallback,
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> AlibabaAuthResponse:
    """
    API方式的阿里国际站授权回调处理
    
    供前端调用的API接口，处理授权回调
    """
    try:
        logger.info(f"用户 {current_user.id} 通过API处理阿里国际站授权回调")
        
        # 使用授权码获取访问令牌
        token_data = alibaba_service.get_access_token(callback_data.code)
        
        # 保存授权信息
        auth_record = alibaba_service.save_auth_info(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            token_data=token_data,
            code=callback_data.code
        )
        
        logger.info(f"阿里国际站授权成功: user_id={current_user.id}, account={token_data.get('account')}")
        
        return AlibabaAuthResponse(
            message="阿里国际站授权成功",
            auth_info=AlibabaAuthInfo.from_orm(auth_record)
        )
        
    except Exception as e:
        logger.error(f"阿里国际站授权回调API处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"授权失败: {str(e)}")

@router.get("/auth-info", response_model=Optional[AlibabaAuthInfo])
def get_alibaba_auth_info(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Optional[AlibabaAuthInfo]:
    """
    获取当前用户的阿里国际站授权信息
    """
    try:
        auth_record = db.query(AlibabaAuth).filter(
            AlibabaAuth.user_id == current_user.id,
            AlibabaAuth.tenant_id == current_user.tenant_id,
            AlibabaAuth.is_active == True
        ).first()
        
        if not auth_record:
            return None
            
        return AlibabaAuthInfo.from_orm(auth_record)
        
    except Exception as e:
        logger.error(f"获取阿里国际站授权信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取授权信息失败: {str(e)}")

@router.get("/token-status", response_model=AlibabaTokenStatus)
def get_alibaba_token_status(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> AlibabaTokenStatus:
    """
    获取当前用户的阿里国际站Token状态
    """
    try:
        auth_record = db.query(AlibabaAuth).filter(
            AlibabaAuth.user_id == current_user.id,
            AlibabaAuth.tenant_id == current_user.tenant_id,
            AlibabaAuth.is_active == True
        ).first()
        
        if not auth_record:
            return AlibabaTokenStatus(
                is_valid=False,
                account=None,
                expires_at=None,
                refresh_expires_at=None
            )
        
        # 计算过期时间
        expires_at = auth_record.token_created_at + timedelta(seconds=auth_record.expires_in) if auth_record.expires_in else None
        refresh_expires_at = auth_record.token_created_at + timedelta(seconds=auth_record.refresh_expires_in) if auth_record.refresh_expires_in else None
        
        # 检查是否有效
        is_valid = expires_at and datetime.utcnow() < expires_at
        
        return AlibabaTokenStatus(
            is_valid=is_valid,
            account=auth_record.alibaba_account,
            expires_at=expires_at,
            refresh_expires_at=refresh_expires_at
        )
        
    except Exception as e:
        logger.error(f"获取阿里国际站Token状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取Token状态失败: {str(e)}")

@router.post("/refresh-token", response_model=AlibabaAuthResponse)
def refresh_alibaba_token(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> AlibabaAuthResponse:
    """
    刷新阿里国际站访问令牌
    """
    try:
        auth_record = db.query(AlibabaAuth).filter(
            AlibabaAuth.user_id == current_user.id,
            AlibabaAuth.tenant_id == current_user.tenant_id,
            AlibabaAuth.is_active == True
        ).first()
        
        if not auth_record:
            raise HTTPException(status_code=404, detail="未找到阿里国际站授权信息")
            
        if not auth_record.refresh_token:
            raise HTTPException(status_code=400, detail="没有有效的刷新令牌")
        
        # 检查refresh_token是否过期
        refresh_expire_time = auth_record.token_created_at + timedelta(seconds=auth_record.refresh_expires_in)
        if datetime.utcnow() >= refresh_expire_time:
            raise HTTPException(status_code=400, detail="刷新令牌已过期，请重新授权")
        
        # 刷新token
        new_token_data = alibaba_service.refresh_access_token(auth_record.refresh_token)
        
        # 更新数据库
        updated_auth_record = alibaba_service.save_auth_info(
            db=db,
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            token_data=new_token_data
        )
        updated_auth_record.last_refresh_at = datetime.utcnow()
        db.commit()
        
        logger.info(f"成功刷新阿里国际站访问令牌: user_id={current_user.id}")
        
        return AlibabaAuthResponse(
            message="访问令牌刷新成功",
            auth_info=AlibabaAuthInfo.from_orm(updated_auth_record)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新阿里国际站访问令牌失败: {e}")
        raise HTTPException(status_code=500, detail=f"刷新访问令牌失败: {str(e)}")

@router.delete("/revoke")
def revoke_alibaba_auth(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
):
    """
    撤销阿里国际站授权
    """
    try:
        auth_record = db.query(AlibabaAuth).filter(
            AlibabaAuth.user_id == current_user.id,
            AlibabaAuth.tenant_id == current_user.tenant_id,
            AlibabaAuth.is_active == True
        ).first()
        
        if not auth_record:
            raise HTTPException(status_code=404, detail="未找到阿里国际站授权信息")
        
        # 将授权标记为非激活状态
        auth_record.is_active = False
        auth_record.access_token = None
        auth_record.refresh_token = None
        db.commit()
        
        logger.info(f"撤销阿里国际站授权: user_id={current_user.id}")
        
        return {"message": "阿里国际站授权已撤销"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"撤销阿里国际站授权失败: {e}")
        raise HTTPException(status_code=500, detail=f"撤销授权失败: {str(e)}") 