#!/usr/bin/env python3
"""
简单测试阿里巴巴ICBU产品列表API调用

使用方法:
python test_icbu_simple.py
"""

import sys
import os
import logging
import hashlib
import hmac
import time
import requests
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# API配置（从config.py获取）
APP_KEY = "502750"
APP_SECRET = "a7cd4a24d3081fd89ae6233f127ba461"
GATEWAY_URL = "https://eco.taobao.com/router/rest"  # 标准阿里巴巴API网关

# 测试用的access_token
TEST_ACCESS_TOKEN = "50000900a25ONSMuApzOPCg6jVchtCtwG9Fey1721d840BOU0EhlSL2grVHUIkb"

def call_alibaba_api(access_token: str, api_name: str, api_params: dict) -> dict:
    """
    调用阿里巴巴API的通用方法
    """
    # 构建请求参数 - 使用阿里国际站正确的参数格式
    params = {
        "app_key": APP_KEY,
        "access_token": access_token,
        "sign_method": "sha256",  # 使用sha256签名方法
        "format": "json",
        "v": "2.0",
        "timestamp": str(int(time.time() * 1000)),  # 毫秒时间戳
        "method": api_name
    }
    
    # 添加API特定参数
    for key, value in api_params.items():
        if isinstance(value, (dict, list)):
            params[key] = json.dumps(value)
        else:
            params[key] = str(value)
    
    # 生成签名 - 使用正确的阿里巴巴签名算法
    sign_string = APP_SECRET
    for key in sorted(params.keys()):
        sign_string += f"{key}{params[key]}"
    sign_string += APP_SECRET
    
    signature = hmac.new(
        APP_SECRET.encode('utf-8'),
        sign_string.encode('utf-8'),
        hashlib.sha256  # 使用SHA256
    ).hexdigest().upper()
    
    params["sign"] = signature
    
    try:
        logger.info(f"调用阿里巴巴API: {api_name}")
        logger.info(f"请求URL: {GATEWAY_URL}")
        logger.info(f"API参数: {params}")
        
        response = requests.post(GATEWAY_URL, data=params, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        logger.info(f"API响应: {result}")
        
        return result
        
    except requests.RequestException as e:
        logger.error(f"调用阿里巴巴API异常: {e}")
        raise Exception(f"调用API失败: {e}")

def test_icbu_product_list():
    """测试ICBU产品列表API"""
    logger.info("=" * 60)
    logger.info("开始测试阿里巴巴ICBU产品列表API")
    logger.info("=" * 60)
    
    # 测试API调用
    try:
        api_params = {
            "language": "ENGLISH",
            "page_size": 10,
            "current_page": 1
        }
        
        response = call_alibaba_api(
            TEST_ACCESS_TOKEN,
            "alibaba.icbu.product.list",
            api_params
        )
        
        logger.info("✅ API调用成功")
        
        # 检查响应格式
        if response.get("error_response"):
            logger.error(f"❌ API返回错误: {response['error_response']}")
            error_code = response['error_response'].get('code', 'Unknown')
            error_msg = response['error_response'].get('msg', 'Unknown error')
            logger.error(f"错误代码: {error_code}")
            logger.error(f"错误信息: {error_msg}")
        elif "alibaba_icbu_product_list_response" in response:
            logger.info("✅ 响应格式正确")
            
            result = response["alibaba_icbu_product_list_response"]
            logger.info(f"总数: {result.get('total_item', 0)}")
            logger.info(f"当前页: {result.get('current_page', 1)}")
            logger.info(f"页大小: {result.get('page_size', 10)}")
            
            products = result.get("products", [])
            if isinstance(products, list):
                logger.info(f"产品数量: {len(products)}")
                if len(products) > 0:
                    logger.info("第一个产品信息:")
                    first_product = products[0]
                    for key, value in first_product.items():
                        logger.info(f"  {key}: {value}")
            else:
                logger.info(f"产品数据: {products}")
        else:
            logger.warning(f"⚠️ 响应格式异常: {response}")
            
    except Exception as e:
        logger.error(f"❌ API调用失败: {e}")
        logger.error(f"错误类型: {type(e).__name__}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
    
    logger.info("=" * 60)
    logger.info("测试完成")
    logger.info("=" * 60)

if __name__ == "__main__":
    test_icbu_product_list() 