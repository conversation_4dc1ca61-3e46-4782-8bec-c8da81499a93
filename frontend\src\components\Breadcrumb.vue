<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="item.path">
        <span v-if="index === breadcrumbs.length - 1" class="no-redirect">{{ item.meta.title }}</span>
        <a v-else @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script>
import { defineComponent, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default defineComponent({
  name: 'Breadcrumb',
  setup () {
    const route = useRoute()
    const router = useRouter()
    const store = useStore()
    const breadcrumbs = ref([])

    const getBreadcrumb = () => {
      let matched = route.matched.filter(item => item.meta && item.meta.title)
      const first = matched[0]

      // 如果不是首页，添加首页作为第一个面包屑
      if (first && first.path !== '/') {
        matched = [
          { path: '/', meta: { title: '首页' } },
          ...matched
        ]
      }

      breadcrumbs.value = matched.filter(item => item.meta && item.meta.title && !item.meta.hiddenInBread)
      store.dispatch('updateBreadcrumbs', breadcrumbs.value)
    }

    const handleLink = (item) => {
      router.push(item.path)
    }

    // 监听路由变化
    watch(
      () => route.path,
      () => getBreadcrumb(),
      { immediate: true }
    )

    return {
      breadcrumbs,
      handleLink
    }
  }
})
</script>

<style scoped>
.app-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 60px;
  margin-left: 8px;
}

.app-breadcrumb .no-redirect {
  color: #97a8be;
  cursor: text;
}
</style>
