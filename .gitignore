# 环境变量配置文件（包含敏感信息）
.env
frontend/.env.local

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Node.js相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log

# 上传文件目录
uploads/
backend/uploads/

# 数据库文件
*.db
*.sqlite3

# 临时文件
.tmp/
.temp/

# 操作系统相关
.DS_Store
Thumbs.db 