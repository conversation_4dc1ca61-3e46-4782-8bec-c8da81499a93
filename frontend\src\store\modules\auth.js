import axios from 'axios'

const state = {
  token: localStorage.getItem('token') || '',
  status: '',
  error: null
}

const getters = {
  isAuthenticated: state => !!state.token,
  authStatus: state => state.status,
  authError: state => state.error,
  token: state => state.token
}

const actions = {
  // 用户登录
  login ({ commit }, user) {
    return new Promise((resolve, reject) => {
      commit('auth_request')
      // FormData格式用于OAuth2兼容
      const formData = new FormData()
      formData.append('username', user.email)
      formData.append('password', user.password)

      axios.post('/api/v1/auth/login/access-token', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
        .then(resp => {
          const token = resp.data.access_token
          // 存储token
          localStorage.setItem('token', token)
          // 立即设置axios请求头
          axios.defaults.headers.common.Authorization = `Bearer ${token}`
          commit('auth_success', token)
          resolve(resp)
        })
        .catch(err => {
          console.error('Login error:', err)
          const errorMessage = err.response?.data?.detail || err.message || '登录失败'
          commit('auth_error', errorMessage)
          localStorage.removeItem('token')
          delete axios.defaults.headers.common.Authorization
          reject(err)
        })
    })
  },

  // 用户登出
  logout ({ commit }) {
    return new Promise((resolve) => {
      commit('logout')
      localStorage.removeItem('token')
      delete axios.defaults.headers.common.Authorization
      resolve()
    })
  },

  // 初始化认证状态
  initAuth ({ commit }) {
    return new Promise((resolve) => {
      const token = localStorage.getItem('token')
      if (token) {
        // 设置axios请求头
        axios.defaults.headers.common.Authorization = `Bearer ${token}`
        commit('auth_success', token)
      }
      resolve()
    })
  }
}

const mutations = {
  auth_request (state) {
    state.status = 'loading'
    state.error = null
  },
  auth_success (state, token) {
    state.status = 'success'
    state.token = token
    state.error = null
  },
  auth_error (state, error) {
    state.status = 'error'
    state.error = error
  },
  logout (state) {
    state.status = ''
    state.token = ''
    state.error = null
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
