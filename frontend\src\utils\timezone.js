/**
 * 全局时区配置和处理工具
 * 
 * 采用 UTC + 前端本地化 方案：
 * - 后端传来UTC时间（ISO格式）
 * - 前端根据环境变量配置自动转换为指定时区显示
 * - 支持全局时区配置，修改环境变量即可改变整个系统的时区显示
 */

/**
 * 从环境变量获取系统时区配置
 * 
 * @returns {Object} 时区配置对象
 */
export function getSystemTimezoneConfig() {
  // 从环境变量读取时区配置，默认为中国时区
  let timezoneOffset = 8
  let timezoneName = 'Asia/Shanghai'

  try {
    let envOffset = null
    let envName = null

    // 方式1：process.env（Vue CLI 标准方式）
    if (typeof process !== 'undefined' && process.env) {
      envOffset = process.env.VUE_APP_SYSTEM_TIMEZONE_OFFSET
      envName = process.env.VUE_APP_SYSTEM_TIMEZONE_NAME
    }

    // 方式2：全局变量（DefinePlugin 注入）
    if (!envOffset && typeof VUE_APP_SYSTEM_TIMEZONE_OFFSET !== 'undefined') {
      envOffset = VUE_APP_SYSTEM_TIMEZONE_OFFSET
    }
    if (!envName && typeof VUE_APP_SYSTEM_TIMEZONE_NAME !== 'undefined') {
      envName = VUE_APP_SYSTEM_TIMEZONE_NAME
    }

    // 方式3：Vite 环境
    if ((!envOffset || !envName) && typeof import.meta !== 'undefined' && import.meta.env) {
      envOffset = envOffset || import.meta.env.VITE_SYSTEM_TIMEZONE_OFFSET
      envName = envName || import.meta.env.VITE_SYSTEM_TIMEZONE_NAME
    }

    // 应用环境变量
    if (envOffset) {
      timezoneOffset = parseInt(envOffset)
    }
    if (envName) {
      timezoneName = envName
    }
  } catch (error) {
    console.warn('读取时区环境变量失败，使用默认值:', error)
  }

  return {
    offset: timezoneOffset,
    name: timezoneName,
    offsetString: `UTC${timezoneOffset >= 0 ? '+' : ''}${timezoneOffset}`
  }
}

/**
 * 获取系统时区的Intl.DateTimeFormat选项
 * 
 * @returns {Object} Intl.DateTimeFormat选项
 */
export function getSystemTimezoneOptions() {
  const config = getSystemTimezoneConfig()
  
  return {
    timeZone: config.name,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }
}

/**
 * 解析UTC时间字符串为Date对象
 * 
 * @param {string} utcString - UTC时间字符串（如 "2024-01-01T10:00:00Z"）
 * @returns {Date|null} Date对象，如果解析失败返回null
 */
export function parseUTCString(utcString) {
  if (!utcString) {
    return null
  }

  try {
    // 确保字符串以Z结尾（UTC标识）
    const normalizedString = utcString.endsWith('Z') ? utcString : utcString + 'Z'
    return new Date(normalizedString)
  } catch (error) {
    console.warn('解析UTC时间字符串失败:', utcString, error)
    return null
  }
}

/**
 * 格式化UTC时间为系统时区显示
 *
 * @param {string|Date} datetime - UTC时间字符串或Date对象
 * @param {Object} options - 格式化选项
 * @returns {string} 格式化后的时间字符串
 */
export function formatToSystemTimezone(datetime, options = {}) {
  if (!datetime) {
    return '--'
  }

  let date
  if (typeof datetime === 'string') {
    date = parseUTCString(datetime)
  } else {
    date = datetime
  }

  if (!date || isNaN(date.getTime())) {
    return '--'
  }

  // 获取系统时区配置
  const config = getSystemTimezoneConfig()

  // 合并默认选项和用户选项，确保使用配置的时区
  const formatOptions = {
    ...getSystemTimezoneOptions(),
    ...options,
    timeZone: config.name // 强制使用配置的时区
  }

  try {
    return date.toLocaleString('zh-CN', formatOptions)
  } catch (error) {
    console.warn('格式化时间失败:', datetime, error)
    return '--'
  }
}

/**
 * 格式化为简短的日期时间显示
 * 
 * @param {string|Date} datetime - UTC时间字符串或Date对象
 * @returns {string} 格式化后的时间字符串
 */
export function formatDateTime(datetime) {
  return formatToSystemTimezone(datetime, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

/**
 * 格式化为日期显示（不含时间）
 * 
 * @param {string|Date} datetime - UTC时间字符串或Date对象
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(datetime) {
  return formatToSystemTimezone(datetime, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

/**
 * 格式化为时间显示（不含日期）
 * 
 * @param {string|Date} datetime - UTC时间字符串或Date对象
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(datetime) {
  return formatToSystemTimezone(datetime, {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

/**
 * 格式化为相对时间显示（如：刚刚、5分钟前、昨天等）
 * 
 * @param {string|Date} datetime - UTC时间字符串或Date对象
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(datetime) {
  if (!datetime) {
    return '--'
  }
  
  let date
  if (typeof datetime === 'string') {
    date = parseUTCString(datetime)
  } else {
    date = datetime
  }
  
  if (!date || isNaN(date.getTime())) {
    return '--'
  }
  
  const now = new Date()
  const diff = now - date
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  
  if (seconds < 60) {
    return '刚刚'
  } else if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    if (days === 1) {
      return '昨天 ' + formatTime(date)
    } else {
      return `${days}天前`
    }
  } else {
    // 超过一周显示具体日期
    return formatDateTime(date)
  }
}

/**
 * 将UTC时间字符串转换为本地时间Date对象（用于表单编辑）
 *
 * 这个函数的目的是让表单显示正确的本地时间
 * 策略：创建一个Date对象，其本地时间显示为我们期望的时间
 *
 * @param {string} utcString - UTC时间字符串
 * @returns {Date|null} 用于表单显示的Date对象
 */
export function fromUTCString(utcString) {
  if (!utcString) {
    return null
  }

  try {
    const config = getSystemTimezoneConfig()

    // 解析UTC时间
    const utcDate = parseUTCString(utcString)
    if (!utcDate) {
      return null
    }

    // 计算本地时间的各个组件
    const utcTime = utcDate.getTime()
    const offsetMs = config.offset * 60 * 60 * 1000
    const localTime = new Date(utcTime + offsetMs)

    // 创建一个新的Date对象，使其在本地时区显示时显示为正确的时间
    // 我们需要"欺骗"Date对象，让它以为这是本地时间
    const year = localTime.getUTCFullYear()
    const month = localTime.getUTCMonth()
    const date = localTime.getUTCDate()
    const hours = localTime.getUTCHours()
    const minutes = localTime.getUTCMinutes()
    const seconds = localTime.getUTCSeconds()

    // 使用本地时间构造函数，这样表单会显示正确的时间
    return new Date(year, month, date, hours, minutes, seconds)
  } catch (error) {
    console.warn('转换UTC时间为本地时间失败:', utcString, error)
    return null
  }
}

/**
 * 将本地时间转换为UTC时间字符串（用于发送给后端）
 *
 * 这个函数处理从表单来的Date对象，将其转换为UTC时间字符串
 *
 * @param {Date} localDate - 表单中的Date对象（表示本地时间）
 * @returns {string} UTC时间字符串
 */
export function toUTCString(localDate) {
  if (!localDate || isNaN(localDate.getTime())) {
    return null
  }

  try {
    const config = getSystemTimezoneConfig()

    // 获取表单中Date对象的各个组件（这些被视为本地时间）
    const year = localDate.getFullYear()
    const month = localDate.getMonth()
    const date = localDate.getDate()
    const hours = localDate.getHours()
    const minutes = localDate.getMinutes()
    const seconds = localDate.getSeconds()

    // 创建UTC时间：本地时间减去时区偏移
    const offsetMs = config.offset * 60 * 60 * 1000
    const localTime = new Date(year, month, date, hours, minutes, seconds)
    const utcTime = new Date(localTime.getTime() - offsetMs)

    return utcTime.toISOString()
  } catch (error) {
    console.warn('转换本地时间为UTC时间失败:', localDate, error)
    return null
  }
}

/**
 * 获取当前系统时区的时间信息
 * 
 * @returns {Object} 时区信息对象
 */
export function getTimezoneInfo() {
  const config = getSystemTimezoneConfig()
  const now = new Date()
  
  return {
    config,
    currentUTC: now.toISOString(),
    currentLocal: formatToSystemTimezone(now),
    offsetMinutes: now.getTimezoneOffset(),
    systemOffset: config.offset
  }
}

/**
 * 表格列的时间格式化函数（Element Plus表格使用）
 * 
 * @param {Object} row - 行数据
 * @param {Object} column - 列配置
 * @param {string} cellValue - 单元格值
 * @returns {string} 格式化后的时间字符串
 */
export function formatTableDateTime(row, column, cellValue) {
  return formatDateTime(cellValue)
}

/**
 * 表格列的日期格式化函数（Element Plus表格使用）
 * 
 * @param {Object} row - 行数据
 * @param {Object} column - 列配置
 * @param {string} cellValue - 单元格值
 * @returns {string} 格式化后的日期字符串
 */
export function formatTableDate(row, column, cellValue) {
  return formatDate(cellValue)
}

// 导出所有函数
export default {
  getSystemTimezoneConfig,
  getSystemTimezoneOptions,
  parseUTCString,
  formatToSystemTimezone,
  formatDateTime,
  formatDate,
  formatTime,
  formatRelativeTime,
  fromUTCString,
  toUTCString,
  getTimezoneInfo,
  formatTableDateTime,
  formatTableDate
}
