#!/usr/bin/env python3
"""
测试阿里巴巴API产品ID参数格式
"""

import sys
import os
import json

# 添加项目路径到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_product_ids_formats():
    """测试不同的产品ID格式"""
    print("="*60)
    print("测试阿里巴巴API产品ID参数格式")
    print("="*60)
    
    # 测试数据
    product_ids = ["1001", "1002", "1003", "1004", "1005"]
    
    print(f"原始产品ID列表: {product_ids}")
    print()
    
    # 格式1：JSON数组格式
    format1 = json.dumps(product_ids)
    print(f"格式1 - JSON数组格式: {format1}")
    print(f"格式1 长度: {len(format1)}")
    print()
    
    # 格式2：JSON数组格式（数字类型）
    format2 = json.dumps([int(pid) for pid in product_ids])
    print(f"格式2 - JSON数组格式（数字）: {format2}")
    print(f"格式2 长度: {len(format2)}")
    print()
    
    # 格式3：逗号分隔字符串
    format3 = ",".join(product_ids)
    print(f"格式3 - 逗号分隔字符串: {format3}")
    print(f"格式3 长度: {len(format3)}")
    print()
    
    # 格式4：单个产品ID（模拟官方示例）
    format4 = product_ids[0]
    print(f"格式4 - 单个产品ID: {format4}")
    print(f"格式4 长度: {len(format4)}")
    print()
    
    # 测试大量产品ID的情况
    large_product_ids = [f"160{i:010d}" for i in range(1, 201)]  # 200个产品ID
    large_format = json.dumps(large_product_ids)
    
    print(f"大量产品ID测试:")
    print(f"产品数量: {len(large_product_ids)}")
    print(f"JSON格式长度: {len(large_format)}")
    print(f"前10个: {large_product_ids[:10]}")
    print()
    
    # 分批处理示例
    batch_size = 50
    batches = [large_product_ids[i:i + batch_size] for i in range(0, len(large_product_ids), batch_size)]
    
    print(f"分批处理:")
    print(f"总批数: {len(batches)}")
    for i, batch in enumerate(batches):
        batch_format = json.dumps(batch)
        print(f"批次 {i+1}: {len(batch)} 个产品，JSON长度: {len(batch_format)}")
        if i >= 2:  # 只显示前3批
            print("...")
            break

def test_url_length_limits():
    """测试URL长度限制"""
    print("\n" + "="*60)
    print("测试URL长度限制")
    print("="*60)
    
    # 模拟一个基础URL
    base_url = "https://open-api.alibaba.com/sync"
    base_params = {
        "app_key": "502750",
        "session": "50000900a25ONSMuApzOPCg6jVchtCtwG9Fey1721d840BOU0EhlSL2grVHUIkb",
        "method": "alibaba.mydata.self.product.get",
        "sign_method": "sha256",
        "format": "json",
        "timestamp": "1749019680365",
        "simplify": "true",
        "statistics_type": "day",
        "stat_date": "2025-06-04",
        "sign": "273EB1ED416826F781750D094AEF52390EBF81B7C7B2B28A57120657503E65DE"
    }
    
    # 计算基础参数的长度
    import urllib.parse
    base_query = urllib.parse.urlencode(base_params)
    base_length = len(base_url) + len(base_query) + 1  # +1 for '?'
    
    print(f"基础URL长度: {base_length}")
    
    # 测试不同数量的产品ID
    for count in [10, 50, 100, 200]:
        product_ids = [f"160{i:010d}" for i in range(1, count + 1)]
        product_ids_param = json.dumps(product_ids)
        
        # URL编码后的长度
        encoded_param = urllib.parse.quote(product_ids_param)
        total_length = base_length + len("&product_ids=") + len(encoded_param)
        
        print(f"{count:3d} 个产品ID: 参数长度={len(product_ids_param):5d}, URL编码后={len(encoded_param):5d}, 总URL长度={total_length:5d}")
        
        # 检查是否超过常见的URL长度限制
        if total_length > 2048:
            print(f"    ⚠️  超过2KB限制")
        if total_length > 8192:
            print(f"    ❌ 超过8KB限制")

def main():
    """主函数"""
    print("阿里巴巴API产品ID格式测试\n")
    
    try:
        test_product_ids_formats()
        test_url_length_limits()
        
        print("\n" + "="*60)
        print("✅ 测试完成")
        print("="*60)
        
        print("\n建议:")
        print("1. 使用JSON数组格式传递产品ID")
        print("2. 每批最多50个产品ID以避免URL过长")
        print("3. 确保产品ID为字符串格式以保持一致性")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 