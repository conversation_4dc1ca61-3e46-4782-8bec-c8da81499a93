<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关键词库导入模板字段说明</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #409eff;
            padding-bottom: 10px;
        }
        h2 {
            color: #409eff;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        .required {
            color: #f56c6c;
            font-weight: bold;
        }
        .optional {
            color: #67c23a;
            font-weight: bold;
        }
        .recommended {
            color: #e6a23c;
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .field-required {
            background-color: #fef0f0;
        }
        .field-recommended {
            background-color: #fdf6ec;
        }
        .field-optional {
            background-color: #f0f9ff;
        }
        .example {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #409eff;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .download-section {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .download-btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background-color: #409eff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
        }
        .download-btn:hover {
            background-color: #337ab7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>关键词库导入模板字段说明</h1>
        
        <div class="note">
            <strong>重要提示：</strong>
            <ul>
                <li><span class="required">红色字段</span>为必填字段，必须包含在导入文件中</li>
                <li><span class="recommended">橙色字段</span>为推荐字段，建议填写以获得更完整的数据</li>
                <li><span class="optional">绿色字段</span>为可选字段，可根据需要选择性填写</li>
            </ul>
        </div>

        <h2>字段列表</h2>
        <table>
            <thead>
                <tr>
                    <th>字段名（中文）</th>
                    <th>字段名（英文）</th>
                    <th>类型</th>
                    <th>必填性</th>
                    <th>说明</th>
                    <th>示例</th>
                </tr>
            </thead>
            <tbody>
                <tr class="field-required">
                    <td><span class="required">关键词名</span></td>
                    <td><span class="required">keyword_name</span></td>
                    <td>文本</td>
                    <td><span class="required">必填</span></td>
                    <td>关键词名称，必须唯一</td>
                    <td>claw machine</td>
                </tr>
                <tr class="field-recommended">
                    <td><span class="recommended">意图</span></td>
                    <td><span class="recommended">intent</span></td>
                    <td>文本</td>
                    <td><span class="recommended">推荐</span></td>
                    <td>关键词意图类型</td>
                    <td>Informational, Commercial, Transactional, Navigational</td>
                </tr>
                <tr class="field-recommended">
                    <td><span class="recommended">搜索量</span></td>
                    <td><span class="recommended">volume</span></td>
                    <td>整数</td>
                    <td><span class="recommended">推荐</span></td>
                    <td>月搜索量</td>
                    <td>40500</td>
                </tr>
                <tr class="field-optional">
                    <td><span class="optional">趋势</span></td>
                    <td><span class="optional">trend</span></td>
                    <td>JSON数组</td>
                    <td><span class="optional">可选</span></td>
                    <td>12个月趋势数据（0-1范围）</td>
                    <td>[0.54,0.54,0.44,0.66,0.81,0.81,0.66,1.00,0.66,0.81,0.66,0.81]</td>
                </tr>
                <tr class="field-recommended">
                    <td><span class="recommended">关键词难度</span></td>
                    <td><span class="recommended">keyword_difficulty</span></td>
                    <td>整数</td>
                    <td><span class="recommended">推荐</span></td>
                    <td>关键词难度（0-100）</td>
                    <td>49</td>
                </tr>
                <tr class="field-recommended">
                    <td><span class="recommended">CPC (USD)</span></td>
                    <td><span class="recommended">cpc_usd</span></td>
                    <td>小数</td>
                    <td><span class="recommended">推荐</span></td>
                    <td>每次点击费用（美元）</td>
                    <td>0.78</td>
                </tr>
                <tr class="field-optional">
                    <td><span class="optional">竞争密度</span></td>
                    <td><span class="optional">competitive_density</span></td>
                    <td>整数</td>
                    <td><span class="optional">可选</span></td>
                    <td>竞争密度（0-1）</td>
                    <td>1</td>
                </tr>
                <tr class="field-optional">
                    <td><span class="optional">SERP特征</span></td>
                    <td><span class="optional">serp_features</span></td>
                    <td>JSON数组</td>
                    <td><span class="optional">可选</span></td>
                    <td>SERP特征列表</td>
                    <td>["Sitelinks", "Reviews", "Image"]</td>
                </tr>
                <tr class="field-optional">
                    <td><span class="optional">搜索结果数</span></td>
                    <td><span class="optional">number_of_results</span></td>
                    <td>整数</td>
                    <td><span class="optional">可选</span></td>
                    <td>搜索结果总数</td>
                    <td>74200000</td>
                </tr>
                <tr class="field-optional">
                    <td><span class="optional">平均每月搜索量</span></td>
                    <td><span class="optional">avg_monthly_searches</span></td>
                    <td>整数</td>
                    <td><span class="optional">可选</span></td>
                    <td>兼容字段，建议使用"搜索量"</td>
                    <td>40500</td>
                </tr>
                <tr class="field-optional">
                    <td><span class="optional">竞争级别</span></td>
                    <td><span class="optional">competition_level</span></td>
                    <td>枚举</td>
                    <td><span class="optional">可选</span></td>
                    <td>竞争级别</td>
                    <td>low, medium, high</td>
                </tr>
                <tr class="field-optional">
                    <td><span class="optional">竞争指数</span></td>
                    <td><span class="optional">competition_index</span></td>
                    <td>小数</td>
                    <td><span class="optional">可选</span></td>
                    <td>竞争指数（0-100）</td>
                    <td>65.5</td>
                </tr>
                <tr class="field-optional">
                    <td><span class="optional">出价第20百分位</span></td>
                    <td><span class="optional">low_bid_micros</span></td>
                    <td>整数</td>
                    <td><span class="optional">可选</span></td>
                    <td>低出价（微货币单位）</td>
                    <td>780000</td>
                </tr>
                <tr class="field-optional">
                    <td><span class="optional">出价第80百分位</span></td>
                    <td><span class="optional">high_bid_micros</span></td>
                    <td>整数</td>
                    <td><span class="optional">可选</span></td>
                    <td>高出价（微货币单位）</td>
                    <td>1200000</td>
                </tr>
                <tr class="field-optional">
                    <td><span class="optional">货币代码</span></td>
                    <td><span class="optional">currency_code</span></td>
                    <td>文本</td>
                    <td><span class="optional">可选</span></td>
                    <td>货币代码</td>
                    <td>USD, CNY</td>
                </tr>
                <tr class="field-optional">
                    <td><span class="optional">语言代码</span></td>
                    <td><span class="optional">language_code</span></td>
                    <td>文本</td>
                    <td><span class="optional">可选</span></td>
                    <td>语言代码</td>
                    <td>en-US, zh-CN</td>
                </tr>
                <tr class="field-optional">
                    <td><span class="optional">分类</span></td>
                    <td><span class="optional">category</span></td>
                    <td>文本</td>
                    <td><span class="optional">可选</span></td>
                    <td>关键词分类</td>
                    <td>游戏设备</td>
                </tr>
                <tr class="field-optional">
                    <td><span class="optional">标签</span></td>
                    <td><span class="optional">tags</span></td>
                    <td>文本</td>
                    <td><span class="optional">可选</span></td>
                    <td>标签（逗号分隔）</td>
                    <td>娃娃机,游戏</td>
                </tr>
            </tbody>
        </table>

        <h2>数据格式示例</h2>
        
        <h3>趋势数据格式</h3>
        <div class="example">
            <strong>格式：</strong>JSON数组，包含12个月的趋势值<br>
            <strong>示例：</strong>[0.54,0.54,0.44,0.66,0.81,0.81,0.66,1.00,0.66,0.81,0.66,0.81]<br>
            <strong>说明：</strong>数值范围0-1，表示相对趋势强度
        </div>

        <h3>SERP特征格式</h3>
        <div class="example">
            <strong>格式：</strong>JSON数组，包含SERP特征名称<br>
            <strong>示例：</strong>["Sitelinks", "Reviews", "Image", "Video", "People also ask"]<br>
            <strong>常见特征：</strong>Sitelinks, Reviews, Image, Video, People also ask, Related searches, Popular products, Shopping, Local results
        </div>

        <div class="download-section">
            <h2>下载模板文件</h2>
            <p>请下载以下模板文件，按照格式填写数据后导入：</p>
            <a href="keyword_import_template.csv" class="download-btn" download>下载中文CSV模板</a>
            <a href="keyword_import_template_en.csv" class="download-btn" download>下载英文CSV模板</a>
        </div>

        <div class="note">
            <strong>导入注意事项：</strong>
            <ul>
                <li>文件格式：支持 CSV 和 Excel (.xlsx, .xls) 格式</li>
                <li>文件大小：不超过 10MB</li>
                <li>记录数量：建议单次导入不超过 1000 条记录</li>
                <li>字符编码：CSV 文件请使用 UTF-8 编码</li>
                <li>重复处理：相同关键词名会被跳过，不会重复导入</li>
            </ul>
        </div>
    </div>
</body>
</html>
