# CBEC 项目 AWS Linux 2023 部署指南

## 📋 概述

本指南提供了在 AWS Linux 2023 环境下部署 CBEC 跨境电商管理系统的完整方案，包含环境检测、依赖安装和项目部署三个脚本。

## 🚀 快速开始

### 1. 准备工作

#### 1.1 AWS EC2 实例要求
- **操作系统**: Amazon Linux 2023
- **实例类型**: 建议 t3.medium 或更高配置
- **存储**: 至少 20GB EBS 存储
- **内存**: 建议 4GB 或以上
- **网络**: 配置安全组开放 22, 80, 443 端口

#### 1.2 连接到服务器
```bash
# 使用SSH连接到EC2实例
ssh -i your-key.pem ec2-user@your-server-ip
```

#### 1.3 下载部署脚本
```bash
# 下载环境配置脚本
wget https://your-domain.com/aws-linux-2023-setup.sh
chmod +x aws-linux-2023-setup.sh

# 下载项目部署脚本
wget https://your-domain.com/cbec-deploy.sh  
chmod +x cbec-deploy.sh
```

### 2. 环境配置（第一步）

运行环境检测和依赖安装脚本：

```bash
./aws-linux-2023-setup.sh
```

#### 脚本功能说明
- ✅ 操作系统检测（Amazon Linux 2023）
- ✅ 网络连接测试
- ✅ 系统更新和基础工具安装
- ✅ Node.js 18+ 安装
- ✅ Python 3.11+ 环境配置
- ✅ MySQL/PostgreSQL 客户端安装
- ✅ Redis 安装和配置
- ✅ Nginx 安装
- ✅ 防火墙配置
- ✅ 系统参数优化
- ✅ 项目目录结构创建
- ✅ SSL 工具安装
- ✅ 辅助脚本和别名创建

#### 预期输出
```
[INFO] 开始 AWS Linux 2023 CBEC 环境配置...
[SUCCESS] 操作系统检查通过: Amazon Linux 2023
[SUCCESS] 网络连接正常
[SUCCESS] 系统更新完成
[SUCCESS] 基础工具安装完成
...
[SUCCESS] === 环境配置完成 ===
```

### 3. 重启系统
```bash
sudo reboot
```

### 4. 项目部署（第二步）

重新连接到服务器后，运行项目部署脚本：

```bash
./cbec-deploy.sh
```

#### 部署过程中的输入
脚本会要求您提供以下信息：
1. **Git仓库地址**: 您的CBEC项目Git仓库URL
2. **数据库主机**: 数据库服务器地址（默认localhost）
3. **数据库用户名**: 数据库用户（默认root）  
4. **数据库密码**: 数据库密码
5. **数据库名称**: 数据库名（默认CBEC）

#### 部署流程
- ✅ 环境依赖检查
- ✅ 用户输入收集和确认
- ✅ 项目代码克隆
- ✅ 环境变量配置
- ✅ 后端Python虚拟环境创建
- ✅ 后端依赖安装
- ✅ 数据库迁移执行
- ✅ 前端依赖安装和构建
- ✅ Systemd服务创建
- ✅ Nginx配置
- ✅ 服务启动
- ✅ 健康检查
- ✅ 管理脚本创建

## 🛠️ 部署后管理

### 系统管理命令

部署完成后，您可以使用以下命令管理系统：

```bash
# 查看系统状态
cbec-status

# 进入应用目录
cbec-app

# 查看应用日志
cbec-logs

# 查看Nginx日志
cbec-nginx
```

### 服务管理命令

```bash
# 启动服务
cbec start

# 停止服务  
cbec stop

# 重启服务
cbec restart

# 查看服务状态
cbec status

# 查看日志
cbec logs backend    # 后端日志
cbec logs nginx      # Nginx日志

# 更新项目
cbec update
```

### 访问地址

部署完成后，您可以通过以下地址访问：

- **前端界面**: `http://your-server-ip`
- **API文档**: `http://your-server-ip/api/docs`
- **健康检查**: `http://your-server-ip/health`

## 🔧 配置说明

### 重要文件位置

```
/opt/cbec-app/              # 项目根目录
├── backend/                # 后端代码
│   ├── venv/              # Python虚拟环境
│   ├── main.py            # 后端入口
│   └── requirements.txt   # Python依赖
├── frontend/              # 前端代码
│   ├── dist/              # 构建产物
│   └── package.json       # Node.js依赖
├── .env                   # 环境配置文件
└── manage.sh              # 管理脚本

/etc/nginx/conf.d/cbec.conf # Nginx配置
/etc/systemd/system/        # 系统服务配置
├── cbec-backend.service   
└── cbec-frontend.service

/var/log/cbec/             # 应用日志目录
/var/www/uploads/          # 文件上传目录
```

### 环境变量配置

主要配置项（位于 `/opt/cbec-app/.env`）：

```bash
# 数据库配置
DATABASE_URL=mysql+pymysql://user:pass@host:3306/dbname

# 服务配置
BACKEND_HOST=0.0.0.0
BACKEND_PORT=5000
FRONTEND_PORT=8080

# 环境设置
PRODUCTION=true
LOG_LEVEL=INFO

# 安全配置
SECRET_KEY=your-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=10080
```

## 🔐 安全配置

### SSL证书配置（可选）

如果您有域名，可以配置SSL证书：

```bash
# 安装SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期设置
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 防火墙配置

```bash
# 检查防火墙状态
sudo firewall-cmd --list-all

# 开放额外端口（如需要）
sudo firewall-cmd --permanent --add-port=YOUR_PORT/tcp
sudo firewall-cmd --reload
```

## 📊 监控和维护

### 系统监控

```bash
# 查看系统资源使用
htop

# 查看磁盘使用
df -h

# 查看服务状态
sudo systemctl status cbec-backend nginx redis

# 查看端口监听
ss -tuln | grep -E ':(80|443|5000|8000)'
```

### 日志管理

```bash
# 查看后端服务日志
sudo journalctl -u cbec-backend -f

# 查看Nginx访问日志
sudo tail -f /var/log/nginx/access.log

# 查看系统日志
sudo journalctl -f
```

### 定期维护

```bash
# 更新系统包
sudo dnf update -y

# 清理日志文件
sudo journalctl --vacuum-time=30d

# 备份重要数据
tar -czf ~/cbec-backup-$(date +%Y%m%d).tar.gz /opt/cbec-app/.env /var/www/uploads/
```

## 🚨 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 查看详细错误日志
sudo journalctl -u cbec-backend --no-pager -l

# 检查端口占用
ss -tuln | grep :5000
```

#### 2. 数据库连接失败
```bash
# 测试数据库连接
mysql -h DB_HOST -u DB_USER -p DB_NAME

# 检查防火墙设置
sudo firewall-cmd --list-all
```

#### 3. 前端无法访问
```bash
# 检查Nginx配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx

# 检查前端构建
ls -la /opt/cbec-app/frontend/dist/
```

#### 4. API请求失败
```bash
# 测试后端API
curl http://localhost:5000/health

# 检查CORS配置
curl -H "Origin: http://your-domain.com" http://localhost:5000/api/
```

### 性能优化

#### 1. 数据库优化
```bash
# MySQL配置优化（如使用MySQL）
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf

# 添加优化参数
[mysqld]
innodb_buffer_pool_size = 1G
max_connections = 200
query_cache_size = 32M
```

#### 2. Nginx优化
```bash
# 编辑Nginx配置
sudo nano /etc/nginx/conf.d/cbec.conf

# 添加缓存和压缩优化
gzip on;
gzip_types text/plain text/css application/json application/javascript;
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 检查生成的环境报告和部署报告
2. 查看相关日志文件
3. 确认网络和防火墙配置
4. 验证数据库连接和权限

## 📝 更新日志

- **v1.0** - 初始版本，支持基础部署功能
- 支持Amazon Linux 2023
- 自动化环境配置和项目部署
- 完整的服务管理和监控方案 