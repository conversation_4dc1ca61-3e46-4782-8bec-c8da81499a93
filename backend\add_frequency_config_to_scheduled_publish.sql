-- 定时发布频率配置数据库迁移脚本
-- 执行时间：2024-01-XX
-- 说明：为定时发布计划表添加结束时间和发布频率配置字段

-- 1. 为 scheduled_publish_plans 表添加新的频率配置字段
ALTER TABLE `scheduled_publish_plans` 
ADD COLUMN `end_time` datetime NULL COMMENT '任务结束时间' AFTER `scheduled_time`,
ADD COLUMN `frequency_type` varchar(20) NOT NULL DEFAULT 'once' COMMENT '发布频率类型：once/daily/weekly/custom' AFTER `end_time`,
ADD COLUMN `weekly_days` json NULL COMMENT '每周执行日期（0-6，0为周日）' AFTER `frequency_type`,
ADD COLUMN `custom_interval_value` int NULL COMMENT '自定义间隔数值' AFTER `weekly_days`,
ADD COLUMN `custom_interval_unit` varchar(10) NULL COMMENT '自定义间隔单位：hours/days/weeks' AFTER `custom_interval_value`,
ADD COLUMN `daily_time` time NULL COMMENT '每日执行时间（HH:MM格式）' AFTER `custom_interval_unit`,
ADD COLUMN `max_executions` int NULL COMMENT '最大执行次数限制' AFTER `daily_time`,
ADD COLUMN `current_executions` int NOT NULL DEFAULT 0 COMMENT '当前已执行次数' AFTER `max_executions`,
ADD COLUMN `next_execution_time` datetime NULL COMMENT '下次执行时间' AFTER `current_executions`;

-- 2. 添加新字段的索引
ALTER TABLE `scheduled_publish_plans` 
ADD INDEX `idx_frequency_type` (`frequency_type`),
ADD INDEX `idx_end_time` (`end_time`),
ADD INDEX `idx_next_execution` (`next_execution_time`),
ADD INDEX `idx_active_next_execution` (`is_active`, `next_execution_time`);

-- 3. 为 scheduled_publish_tasks 表添加执行次数相关字段
ALTER TABLE `scheduled_publish_tasks`
ADD COLUMN `execution_sequence` int NULL COMMENT '执行序号（第几次执行）' AFTER `plan_id`,
ADD COLUMN `scheduled_execution_time` datetime NULL COMMENT '计划执行时间' AFTER `execution_sequence`;

-- 4. 添加任务表新字段的索引
ALTER TABLE `scheduled_publish_tasks`
ADD INDEX `idx_scheduled_execution` (`scheduled_execution_time`),
ADD INDEX `idx_plan_sequence` (`plan_id`, `execution_sequence`);

-- 5. 创建频率配置枚举约束（MySQL 8.0+支持，如果是较低版本请删除此部分）
-- ALTER TABLE `scheduled_publish_plans` 
-- ADD CONSTRAINT `chk_frequency_type` CHECK (`frequency_type` IN ('once', 'daily', 'weekly', 'custom'));

-- ALTER TABLE `scheduled_publish_plans` 
-- ADD CONSTRAINT `chk_custom_interval_unit` CHECK (`custom_interval_unit` IS NULL OR `custom_interval_unit` IN ('hours', 'days', 'weeks'));

-- 6. 更新现有数据的默认值
UPDATE `scheduled_publish_plans` 
SET 
    `frequency_type` = 'once',
    `current_executions` = 0,
    `next_execution_time` = `scheduled_time`
WHERE `frequency_type` IS NULL OR `frequency_type` = '';

-- 7. 创建频率配置相关的视图
CREATE OR REPLACE VIEW `v_scheduled_plans_with_frequency` AS
SELECT 
    p.*,
    CASE 
        WHEN p.frequency_type = 'once' THEN '仅执行一次'
        WHEN p.frequency_type = 'daily' THEN CONCAT('每天', IFNULL(p.daily_time, ''), '执行')
        WHEN p.frequency_type = 'weekly' THEN CONCAT('每周执行')
        WHEN p.frequency_type = 'custom' THEN CONCAT('每', p.custom_interval_value, 
            CASE p.custom_interval_unit 
                WHEN 'hours' THEN '小时'
                WHEN 'days' THEN '天'
                WHEN 'weeks' THEN '周'
                ELSE ''
            END, '执行一次')
        ELSE '未知频率'
    END as frequency_description,
    CASE 
        WHEN p.end_time IS NOT NULL AND p.end_time < NOW() THEN '已过期'
        WHEN p.max_executions IS NOT NULL AND p.current_executions >= p.max_executions THEN '已完成'
        WHEN p.is_active = 0 THEN '已禁用'
        WHEN p.next_execution_time IS NULL OR p.next_execution_time > NOW() THEN '等待中'
        ELSE '可执行'
    END as execution_status,
    CASE 
        WHEN p.max_executions IS NOT NULL THEN 
            CONCAT(p.current_executions, '/', p.max_executions)
        ELSE 
            CAST(p.current_executions AS CHAR)
    END as execution_progress
FROM `scheduled_publish_plans` p;

-- 8. 创建下次执行时间计算的存储过程（可选）
DELIMITER //

CREATE PROCEDURE `CalculateNextExecutionTime`(IN plan_id INT)
BEGIN
    DECLARE v_frequency_type VARCHAR(20);
    DECLARE v_scheduled_time DATETIME;
    DECLARE v_end_time DATETIME;
    DECLARE v_custom_interval_value INT;
    DECLARE v_custom_interval_unit VARCHAR(10);
    DECLARE v_daily_time TIME;
    DECLARE v_weekly_days JSON;
    DECLARE v_max_executions INT;
    DECLARE v_current_executions INT;
    DECLARE v_next_execution DATETIME;
    
    -- 获取计划配置
    SELECT frequency_type, scheduled_time, end_time, custom_interval_value, 
           custom_interval_unit, daily_time, weekly_days, max_executions, current_executions
    INTO v_frequency_type, v_scheduled_time, v_end_time, v_custom_interval_value,
         v_custom_interval_unit, v_daily_time, v_weekly_days, v_max_executions, v_current_executions
    FROM scheduled_publish_plans 
    WHERE id = plan_id;
    
    -- 计算下次执行时间
    CASE v_frequency_type
        WHEN 'once' THEN
            -- 仅执行一次，如果已执行则设为NULL
            IF v_current_executions > 0 THEN
                SET v_next_execution = NULL;
            ELSE
                SET v_next_execution = v_scheduled_time;
            END IF;
            
        WHEN 'daily' THEN
            -- 每天执行
            SET v_next_execution = DATE_ADD(CURDATE(), INTERVAL 1 DAY);
            IF v_daily_time IS NOT NULL THEN
                SET v_next_execution = TIMESTAMP(DATE(v_next_execution), v_daily_time);
            END IF;
            
        WHEN 'custom' THEN
            -- 自定义间隔
            CASE v_custom_interval_unit
                WHEN 'hours' THEN
                    SET v_next_execution = DATE_ADD(NOW(), INTERVAL v_custom_interval_value HOUR);
                WHEN 'days' THEN
                    SET v_next_execution = DATE_ADD(NOW(), INTERVAL v_custom_interval_value DAY);
                WHEN 'weeks' THEN
                    SET v_next_execution = DATE_ADD(NOW(), INTERVAL v_custom_interval_value WEEK);
                ELSE
                    SET v_next_execution = DATE_ADD(NOW(), INTERVAL 1 DAY);
            END CASE;
            
        ELSE
            SET v_next_execution = NULL;
    END CASE;
    
    -- 检查结束时间和最大执行次数限制
    IF v_end_time IS NOT NULL AND v_next_execution > v_end_time THEN
        SET v_next_execution = NULL;
    END IF;
    
    IF v_max_executions IS NOT NULL AND v_current_executions >= v_max_executions THEN
        SET v_next_execution = NULL;
    END IF;
    
    -- 更新下次执行时间
    UPDATE scheduled_publish_plans 
    SET next_execution_time = v_next_execution 
    WHERE id = plan_id;
    
END //

DELIMITER ;

-- 9. 创建清理过期任务的存储过程
DELIMITER //

CREATE PROCEDURE `CleanupExpiredPlans`()
BEGIN
    -- 禁用已过期的计划
    UPDATE scheduled_publish_plans 
    SET is_active = 0 
    WHERE is_active = 1 
    AND (
        (end_time IS NOT NULL AND end_time < NOW()) 
        OR 
        (max_executions IS NOT NULL AND current_executions >= max_executions)
    );
    
    -- 取消已过期计划的待执行任务
    UPDATE scheduled_publish_tasks t
    JOIN scheduled_publish_plans p ON t.plan_id = p.id
    SET t.status = 'cancelled'
    WHERE t.status = 'pending'
    AND p.is_active = 0;
    
END //

DELIMITER ;

-- 10. 创建定时任务调度器需要的查询视图
CREATE OR REPLACE VIEW `v_executable_plans` AS
SELECT 
    p.*,
    COUNT(t.id) as pending_tasks_count
FROM scheduled_publish_plans p
LEFT JOIN scheduled_publish_tasks t ON p.id = t.plan_id AND t.status = 'pending'
WHERE p.is_active = 1
AND (p.end_time IS NULL OR p.end_time > NOW())
AND (p.max_executions IS NULL OR p.current_executions < p.max_executions)
AND (p.next_execution_time IS NULL OR p.next_execution_time <= NOW())
GROUP BY p.id;

-- 11. 插入一些示例数据（测试用，生产环境请删除）
/*
INSERT INTO `scheduled_publish_plans` (
    `plan_name`, 
    `keywords`, 
    `scheduled_time`,
    `end_time`,
    `frequency_type`,
    `custom_interval_value`,
    `custom_interval_unit`,
    `max_executions`,
    `ai_model`
) VALUES 
(
    '每日AI文章发布',
    'AI工具,人工智能,机器学习',
    NOW(),
    DATE_ADD(NOW(), INTERVAL 30 DAY),
    'daily',
    NULL,
    NULL,
    30,
    'WanX'
),
(
    '每周技术分享',
    '技术分享,编程教程,开发经验',
    NOW(),
    NULL,
    'weekly',
    NULL,
    NULL,
    NULL,
    'Jimeng'
),
(
    '自定义间隔发布',
    '产品介绍,功能更新',
    NOW(),
    DATE_ADD(NOW(), INTERVAL 60 DAY),
    'custom',
    3,
    'days',
    20,
    'WanX'
);
*/

-- 12. 创建触发器自动更新执行次数（可选）
DELIMITER //

CREATE TRIGGER `tr_update_execution_count` 
AFTER UPDATE ON `scheduled_publish_tasks`
FOR EACH ROW
BEGIN
    IF NEW.status = 'success' AND OLD.status != 'success' THEN
        UPDATE scheduled_publish_plans 
        SET current_executions = current_executions + 1
        WHERE id = NEW.plan_id;
        
        -- 自动计算下次执行时间
        CALL CalculateNextExecutionTime(NEW.plan_id);
    END IF;
END //

DELIMITER ;

-- 执行完成提示
SELECT '定时发布频率配置迁移完成！' as message;
SELECT '新增字段：end_time, frequency_type, weekly_days, custom_interval_value, custom_interval_unit, daily_time, max_executions, current_executions, next_execution_time' as added_fields;
SELECT '新增索引：idx_frequency_type, idx_end_time, idx_next_execution, idx_active_next_execution' as added_indexes;
SELECT '新增视图：v_scheduled_plans_with_frequency, v_executable_plans' as added_views;
SELECT '新增存储过程：CalculateNextExecutionTime, CleanupExpiredPlans' as added_procedures; 