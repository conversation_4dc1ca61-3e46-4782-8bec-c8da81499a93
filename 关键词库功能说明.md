# 关键词库功能说明

## 功能概述

关键词库管理系统是一个完整的关键词数据管理解决方案，支持手工添加、Google Ads API导入和批量文件导入三种数据维护方式。系统提供了丰富的数据管理功能，包括搜索、过滤、统计分析等。

## 核心功能

### 1. 数据字段

关键词库包含以下核心字段：

#### 新字段（推荐使用）
- **关键词名** - 关键词文本内容
- **意图** - 关键词意图类型（Informational/Commercial/Navigational/Transactional）
- **搜索量** - 关键词的月搜索量数值
- **趋势** - 12个月趋势数据（JSON数组格式）
- **关键词难度** - SEO难度评分（0-100）
- **CPC (USD)** - 每次点击费用（美元）
- **竞争密度** - 广告竞争密度（0-1）
- **SERP特征** - 搜索结果页特征（JSON数组格式）
- **搜索结果数** - 搜索结果总数量
- **分类** - 关键词分类
- **标签** - 自定义标签（如长尾词、热门词等）

#### 兼容字段（保持向后兼容）
- **平均每月搜索量** - 过去12个月的平均搜索量
- **每月搜索量** - 每个月的具体搜索量数据（JSON数组）
- **竞争级别** - 低/中/高/未知
- **竞争指数** - 0-100的数值表示竞争激烈程度
- **出价范围** - 第20/80百分位的出价建议（微货币单位）
- **更新时间** - 数据最后更新时间
- **更新方式** - 手工/Google Ads API/批量导入

### 2. 数据维护方式

#### 2.1 手工添加
- 通过表单界面手动输入关键词信息
- 支持完整的数据验证
- 适合少量精确数据的录入

#### 2.2 Google Ads API导入
- 集成Google Ads API获取关键词数据
- 支持种子关键词和网站URL作为输入
- 可配置语言、地理位置等参数
- 自动获取搜索量、竞争级别、出价建议等数据
- 支持多个Google Ads账户配置管理

#### 2.3 批量文件导入
- 支持CSV和Excel文件格式
- 提供标准模板下载（包含新字段和兼容字段）
- 支持新字段导入：意图、搜索量、趋势、关键词难度、CPC、竞争密度、SERP特征、搜索结果数
- 支持兼容字段导入：平均每月搜索量、竞争级别、竞争指数、出价范围等
- 支持大批量数据导入（建议不超过1000条）
- 包含数据验证和错误报告
- 提供详细的字段说明和示例

### 3. 查询和过滤功能

#### 3.1 搜索条件
- 关键词名称模糊搜索
- 意图类型筛选
- 标签搜索
- 分类筛选
- 竞争级别筛选
- 更新方式筛选
- 搜索量范围筛选（新版本和兼容版本）
- 关键词难度范围筛选
- CPC范围筛选
- 竞争密度筛选

#### 3.2 排序功能
- 支持按关键词名、搜索量、关键词难度、CPC、竞争密度、搜索结果数排序
- 支持按竞争指数、更新时间排序（兼容）
- 升序/降序切换

#### 3.3 分页功能
- 支持20/50/100/200条每页
- 页码跳转
- 总数统计

### 4. 统计分析功能

#### 4.1 概览统计
- 关键词总数
- 平均月搜索量（优先使用新的搜索量字段）
- 平均CPC（美元）
- 按更新方式分布统计
- 按竞争级别分布统计
- 按意图类型分布统计
- 按关键词难度分布统计

#### 4.2 图表展示
- 更新方式分布饼图
- 竞争级别分布饼图
- 意图类型分布饼图
- 关键词难度分布柱状图
- 使用ECharts图表库

#### 4.3 热门关键词
- Top 10搜索量最高的关键词
- 包含完整的关键词信息展示
- 支持新字段的展示

### 5. 数据管理功能

#### 5.1 CRUD操作
- 创建新关键词
- 查看关键词详情
- 编辑关键词信息
- 删除关键词

#### 5.2 批量操作
- 批量删除
- 批量导出

#### 5.3 数据导出
- 支持Excel和CSV格式导出
- 可按当前筛选条件导出
- 包含所有新字段和兼容字段数据
- 导出格式与导入模板保持一致
- 支持中文字段名导出

### 6. Google Ads集成

#### 6.1 配置管理
- 多账户配置支持
- OAuth2认证配置
- 开发者令牌管理
- 连接测试功能

#### 6.2 数据同步
- 关键词提示API集成
- 历史指标API集成
- 自动数据更新
- 错误处理和重试机制

## 技术架构

### 后端技术栈
- **框架**: FastAPI
- **数据库**: MySQL
- **ORM**: SQLAlchemy
- **API集成**: Google Ads API Python客户端
- **数据处理**: Pandas
- **验证**: Pydantic

### 前端技术栈
- **框架**: Vue 3
- **UI库**: Element Plus
- **图表**: ECharts
- **状态管理**: 响应式API (ref, reactive)
- **HTTP客户端**: Axios

### 数据库设计
- **keyword_library** - 主表
- **google_ads_config** - Google Ads配置表
- **keyword_update_history** - 更新历史表
- **keyword_import_tasks** - 导入任务表

## 安装和部署

### 后端部署

1. 安装依赖：
```bash
cd backend
pip install -r requirements.txt
```

2. 数据库初始化：
```bash
mysql -u root -p < ../sql/keyword_library_schema.sql
```

3. 启动服务：
```bash
uvicorn app.main:app --reload
```

### 前端部署

1. 安装依赖：
```bash
cd frontend
npm install
```

2. 开发环境启动：
```bash
npm run serve
```

3. 生产环境构建：
```bash
npm run build
```

## Google Ads API配置

### 1. 获取API访问权限
1. 访问Google Ads API中心
2. 申请开发者令牌
3. 创建OAuth2客户端

### 2. 配置步骤
1. 在系统中添加Google Ads配置
2. 填写开发者令牌、客户端ID、客户端密钥
3. 获取刷新令牌（通过OAuth2流程）
4. 测试连接确保配置正确

### 3. 使用限制
- 需要Google Ads账户
- 开发者令牌需要审批
- API有配额限制
- 遵循Google Ads API政策

## 使用指南

### 1. 手工添加关键词
1. 点击"添加关键词"按钮
2. 填写表单信息
3. 设置标签（可选）
4. 保存关键词

### 2. Google Ads导入
1. 配置Google Ads账户
2. 点击"Google Ads导入"
3. 选择配置和设置参数
4. 执行导入操作

### 3. 批量文件导入
1. 下载导入模板
2. 按格式填写数据
3. 上传文件执行导入
4. 查看导入结果

### 4. 数据查询
1. 使用搜索框查找关键词
2. 设置过滤条件
3. 选择排序方式
4. 浏览分页结果

### 5. 统计分析
1. 点击"统计信息"查看概览
2. 查看分布图表
3. 分析热门关键词
4. 导出统计报告

## 注意事项

### 1. 数据质量
- 确保关键词名称唯一性
- 定期更新Google Ads数据
- 验证导入文件格式
- 监控数据一致性

### 2. 性能优化
- 大批量导入分批处理
- 合理设置分页大小
- 避免频繁API调用
- 优化数据库查询

### 3. 安全考虑
- 保护Google Ads凭证
- 定期更新访问令牌
- 监控API使用情况
- 备份重要数据

### 4. 错误处理
- 监控导入失败记录
- 处理API限制错误
- 验证数据完整性
- 提供错误恢复机制

## 扩展功能

### 未来规划
- 支持更多搜索引擎API
- 添加关键词趋势分析
- 实现自动化数据更新
- 增加数据可视化报表
- 支持团队协作功能

### 自定义开发
- 扩展API接口
- 添加新的数据字段
- 集成其他数据源
- 定制业务逻辑
- 优化用户界面 