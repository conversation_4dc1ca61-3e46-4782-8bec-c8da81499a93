import json
import time
import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import pandas as pd
import requests
import uuid

# 尝试导入pytrends
try:
    from pytrends.request import TrendReq
    PYTRENDS_AVAILABLE = True
except ImportError:
    TrendReq = None
    PYTRENDS_AVAILABLE = False

# 初始化日志记录器
logger = logging.getLogger(__name__)

# 处理urllib3版本兼容性
try:
    from urllib3.util import Retry
    # 检测urllib3版本是否支持allowed_methods参数
    USE_ALLOWED_METHODS = hasattr(Retry.DEFAULT, 'allowed_methods')
    
    # 为PyTrends添加兼容性补丁
    if USE_ALLOWED_METHODS:
        # 如果系统使用新版本urllib3，我们需要修补PyTrends的Retry使用
        import urllib3.util
        original_retry_init = urllib3.util.Retry.__init__
        
        def patched_retry_init(self, *args, **kwargs):
            # 将method_whitelist转换为allowed_methods
            if 'method_whitelist' in kwargs and 'allowed_methods' not in kwargs:
                kwargs['allowed_methods'] = kwargs.pop('method_whitelist')
            return original_retry_init(self, *args, **kwargs)
        
        urllib3.util.Retry.__init__ = patched_retry_init
        logger.info("已应用urllib3兼容性补丁")
        
except ImportError:
    USE_ALLOWED_METHODS = False

class PyTrendsService:
    def __init__(self, config: Dict[str, Any] = None, proxy_config: Dict[str, Any] = None):
        """
        初始化PyTrends服务
        
        Args:
            config: PyTrends配置字典
            proxy_config: 代理配置字典
        """
        if not PYTRENDS_AVAILABLE:
            raise ImportError("pytrends库未安装，请运行: pip install pytrends")
        
        self.config = config or {}
        self.proxy_config = proxy_config or {}
        
        # 默认配置
        self.language = self.config.get('language', 'en-GB')  # 改为英国英语
        self.timezone = self.config.get('timezone', 0)  # 改为英国时区(GMT)
        self.geo_location = self.config.get('geo_location', 'GB')  # 改为英国
        self.default_timeframe = self.config.get('default_timeframe', 'today 1-m')  # 改为过去30天
        self.max_keywords_per_batch = self.config.get('max_keywords_per_batch', 5)
        self.request_delay = self.config.get('request_delay', 2)
        self.retry_attempts = self.config.get('retry_attempts', 3)
        
        # 初始化PyTrends客户端
        self._init_client()
    
    def _init_client(self):
        """初始化PyTrends客户端"""
        try:
            # 构建代理配置 - PyTrends期望的是列表格式，不是字典格式
            proxies = None
            if self.proxy_config and self.proxy_config.get('use_proxy') and self.proxy_config.get('proxy_host'):
                proxy_url = self._build_proxy_url()
                # PyTrends期望代理配置是一个URL列表，而不是字典
                proxies = [proxy_url]
                logger.info(f"使用代理配置: {proxy_url}")
            
            # 初始化TrendReq参数 - 使用PyTrends文档推荐的格式
            trend_req_params = {
                'hl': self.language[:2],  # 语言代码，取前两位
                'tz': self.timezone,
                'timeout': (60, 120),  # PyTrends原生支持的timeout参数
                'retries': self.retry_attempts,
                'backoff_factor': 1.0
            }
            
            # 添加代理配置
            if proxies:
                trend_req_params['proxies'] = proxies
            
            # 添加requests参数（用于SSL验证等）
            trend_req_params['requests_args'] = {'verify': True}
            
            logger.info(f"尝试初始化PyTrends，参数: {trend_req_params}")
            
            # 初始化TrendReq
            self.pytrends = TrendReq(**trend_req_params)
            
            logger.info(f"PyTrends客户端初始化成功，语言: {self.language}, 时区: {self.timezone}")
            
        except Exception as e:
            logger.error(f"PyTrends客户端初始化失败: {e}")
            # 如果完整初始化失败，尝试简化参数
            try:
                logger.info("尝试简化参数重新初始化PyTrends")
                simple_params = {
                    'hl': self.language[:2],
                    'tz': self.timezone,
                    'timeout': (30, 60),
                    'retries': 2,
                    'backoff_factor': 0.5,
                    'requests_args': {'verify': True}
                }
                if proxies:
                    simple_params['proxies'] = proxies
                
                logger.info(f"简化参数: {simple_params}")
                self.pytrends = TrendReq(**simple_params)
                logger.info("PyTrends简化参数初始化成功")
            except Exception as e2:
                logger.error(f"PyTrends简化初始化也失败: {e2}")
                # 最后尝试最小配置
                try:
                    logger.info("尝试最小配置初始化PyTrends")
                    minimal_params = {
                        'hl': self.language[:2],
                        'tz': self.timezone,
                        'requests_args': {'verify': False}  # 禁用SSL验证作为最后手段
                    }
                    logger.info(f"最小参数: {minimal_params}")
                    self.pytrends = TrendReq(**minimal_params)
                    logger.info("PyTrends最小配置初始化成功")
                except Exception as e3:
                    logger.error(f"PyTrends最小配置初始化也失败: {e3}")
                    raise
    
    def _build_proxy_url(self) -> str:
        """构建代理URL"""
        proxy_type = self.proxy_config.get('proxy_type', 'http')
        host = self.proxy_config.get('proxy_host')
        port = self.proxy_config.get('proxy_port')
        username = self.proxy_config.get('proxy_username')
        password = self.proxy_config.get('proxy_password')
        
        # 确保代理URL格式正确，PyTrends只支持https代理
        if proxy_type.lower() not in ['http', 'https']:
            proxy_type = 'https'  # 强制使用https，因为PyTrends主要支持https代理
        
        if username and password:
            return f"{proxy_type}://{username}:{password}@{host}:{port}"
        else:
            return f"{proxy_type}://{host}:{port}"
    
    def get_interest_over_time(
        self,
        keywords: List[str],
        timeframe: str = None,
        geo: str = None,
        category: int = 0,
        gprop: str = ''
    ) -> Dict[str, Any]:
        """
        获取关键词的时间趋势数据
        
        Args:
            keywords: 关键词列表（最多5个）
            timeframe: 时间范围，默认为配置的default_timeframe
            geo: 地理位置代码，默认为配置的geo_location
            category: 分类ID，0表示全部分类
            gprop: Google产品类型（'', 'images', 'news', 'youtube', 'froogle'）
            
        Returns:
            包含趋势数据的字典
        """
        if len(keywords) > 5:
            raise ValueError("PyTrends一次最多支持5个关键词")
        
        timeframe = timeframe or self.default_timeframe
        geo = geo or self.geo_location
        
        # 重试机制
        for attempt in range(self.retry_attempts + 1):
            try:
                logger.info(f"尝试获取关键词趋势数据 (第{attempt + 1}次): {keywords}")
                
                # 添加请求前延迟（速率限制）
                if attempt > 0:
                    delay = self.request_delay * (2 ** (attempt - 1))  # 指数退避
                    logger.info(f"等待 {delay} 秒后重试...")
                    time.sleep(delay)
                elif hasattr(self, '_last_request_time'):
                    # 确保请求间隔
                    elapsed = time.time() - self._last_request_time
                    if elapsed < self.request_delay:
                        sleep_time = self.request_delay - elapsed
                        logger.info(f"速率限制：等待 {sleep_time:.2f} 秒...")
                        time.sleep(sleep_time)
                
                # 记录请求时间
                self._last_request_time = time.time()
                
                # 构建有效载荷
                self.pytrends.build_payload(
                    kw_list=keywords,
                    cat=category,
                    timeframe=timeframe,
                    geo=geo,
                    gprop=gprop
                )
                
                # 获取兴趣度随时间变化的数据
                interest_over_time_df = self.pytrends.interest_over_time()
                
                if interest_over_time_df.empty:
                    logger.warning(f"关键词 {keywords} 没有找到趋势数据")
                    return self._empty_result(keywords)
                
                # 处理数据
                result = {
                    'keywords': keywords,
                    'timeframe': timeframe,
                    'geo_location': geo,
                    'category': category,
                    'data_points': [],
                    'keyword_stats': {}
                }
                
                # 转换时间序列数据
                if not interest_over_time_df.empty:
                    # 删除isPartial列（如果存在）
                    if 'isPartial' in interest_over_time_df.columns:
                        interest_over_time_df = interest_over_time_df.drop(columns=['isPartial'])
                    
                    # 转换为字典格式
                    time_data = []
                    for date, row in interest_over_time_df.iterrows():
                        data_point = {
                            'date': date.strftime('%Y-%m-%d') if hasattr(date, 'strftime') else str(date),
                            'values': {}
                        }
                        for keyword in keywords:
                            if keyword in row:
                                data_point['values'][keyword] = int(row[keyword]) if pd.notna(row[keyword]) else 0
                        time_data.append(data_point)
                    
                    result['data_points'] = time_data
                    
                    # 计算关键词统计信息
                    for keyword in keywords:
                        if keyword in interest_over_time_df.columns:
                            values = interest_over_time_df[keyword].dropna()
                            if not values.empty:
                                result['keyword_stats'][keyword] = {
                                    'avg_interest': float(values.mean()),
                                    'max_interest': float(values.max()),
                                    'min_interest': float(values.min()),
                                    'trend': self._calculate_trend(values.tolist())
                                }
                            else:
                                result['keyword_stats'][keyword] = {
                                    'avg_interest': 0,
                                    'max_interest': 0,
                                    'min_interest': 0,
                                    'trend': 0
                                }
                
                logger.info(f"成功获取关键词趋势数据: {keywords}")
                return result
                
            except (requests.exceptions.ReadTimeout, requests.exceptions.ConnectTimeout, 
                   requests.exceptions.Timeout) as e:
                logger.warning(f"请求超时 (第{attempt + 1}次尝试): {e}")
                if attempt == self.retry_attempts:
                    logger.error(f"请求超时，已达到最大重试次数 ({self.retry_attempts})")
                    raise
                continue
                
            except requests.exceptions.ConnectionError as e:
                logger.warning(f"连接错误 (第{attempt + 1}次尝试): {e}")
                if attempt == self.retry_attempts:
                    logger.error(f"连接错误，已达到最大重试次数 ({self.retry_attempts})")
                    raise
                continue
                
            except Exception as e:
                error_msg = str(e).lower()
                # 检查是否是速率限制错误
                if '429' in error_msg or 'too many requests' in error_msg or 'rate limit' in error_msg:
                    logger.warning(f"遇到速率限制 (第{attempt + 1}次尝试): {e}")
                    if attempt == self.retry_attempts:
                        logger.error(f"速率限制，已达到最大重试次数 ({self.retry_attempts})")
                        raise
                    # 对于速率限制，增加更长的等待时间
                    delay = self.request_delay * (3 ** attempt)  # 更激进的退避
                    logger.info(f"速率限制延迟：等待 {delay} 秒...")
                    time.sleep(delay)
                    continue
                else:
                    logger.error(f"获取趋势数据失败: {e}")
                    raise
    
    def get_related_topics_and_queries(
        self,
        keywords: List[str],
        timeframe: str = None,
        geo: str = None,
        category: int = 0
    ) -> Dict[str, Any]:
        """
        获取相关主题和查询
        
        Args:
            keywords: 关键词列表
            timeframe: 时间范围
            geo: 地理位置代码
            category: 分类ID
            
        Returns:
            包含相关主题和查询的字典
        """
        if len(keywords) > 5:
            raise ValueError("PyTrends一次最多支持5个关键词")
        
        timeframe = timeframe or self.default_timeframe
        geo = geo or self.geo_location
        
        # 重试机制
        for attempt in range(self.retry_attempts + 1):
            try:
                logger.info(f"尝试获取相关主题和查询 (第{attempt + 1}次): {keywords}")
                
                # 添加请求前延迟（速率限制）
                if attempt > 0:
                    delay = self.request_delay * (2 ** (attempt - 1))  # 指数退避
                    logger.info(f"等待 {delay} 秒后重试...")
                    time.sleep(delay)
                elif hasattr(self, '_last_request_time'):
                    # 确保请求间隔
                    elapsed = time.time() - self._last_request_time
                    if elapsed < self.request_delay:
                        sleep_time = self.request_delay - elapsed
                        logger.info(f"速率限制：等待 {sleep_time:.2f} 秒...")
                        time.sleep(sleep_time)
                
                # 记录请求时间
                self._last_request_time = time.time()
                
                # 构建有效载荷
                self.pytrends.build_payload(
                    kw_list=keywords,
                    cat=category,
                    timeframe=timeframe,
                    geo=geo
                )
                
                result = {
                    'keywords': keywords,
                    'related_topics': {},
                    'related_queries': {},
                    'rising_topics': {},
                    'rising_queries': {}
                }
                
                for i, keyword in enumerate(keywords):
                    try:
                        logger.debug(f"处理关键词 {i+1}/{len(keywords)}: {keyword}")
                        
                        # 相关主题
                        try:
                            related_topics = self.pytrends.related_topics()
                            logger.debug(f"关键词 {keyword} 相关主题数据类型: {type(related_topics)}")
                            
                            if related_topics and keyword in related_topics:
                                topic_data = related_topics[keyword]
                                logger.debug(f"关键词 {keyword} 主题数据: {type(topic_data)}")
                                
                                if topic_data is not None and isinstance(topic_data, dict):
                                    if 'top' in topic_data:
                                        top_topics = topic_data['top']
                                        if isinstance(top_topics, pd.DataFrame) and not top_topics.empty:
                                            result['related_topics'][keyword] = top_topics.head(10).to_dict('records')
                                            logger.debug(f"获取到 {len(top_topics)} 个相关主题")
                                    
                                    if 'rising' in topic_data:
                                        rising_topics = topic_data['rising']
                                        if isinstance(rising_topics, pd.DataFrame) and not rising_topics.empty:
                                            result['rising_topics'][keyword] = rising_topics.head(10).to_dict('records')
                                            logger.debug(f"获取到 {len(rising_topics)} 个上升主题")
                            else:
                                logger.debug(f"关键词 {keyword} 没有相关主题数据")
                        except Exception as topic_error:
                            logger.warning(f"获取关键词 {keyword} 的相关主题失败: {topic_error}")
                            import traceback
                            logger.debug(f"主题错误详情: {traceback.format_exc()}")
                        
                        # 相关查询
                        try:
                            related_queries = self.pytrends.related_queries()
                            logger.debug(f"关键词 {keyword} 相关查询数据类型: {type(related_queries)}")
                            
                            if related_queries and keyword in related_queries:
                                query_data = related_queries[keyword]
                                logger.debug(f"关键词 {keyword} 查询数据: {type(query_data)}")
                                
                                if query_data is not None and isinstance(query_data, dict):
                                    if 'top' in query_data:
                                        top_queries = query_data['top']
                                        if isinstance(top_queries, pd.DataFrame) and not top_queries.empty:
                                            result['related_queries'][keyword] = top_queries.head(10).to_dict('records')
                                            logger.debug(f"获取到 {len(top_queries)} 个相关查询")
                                    
                                    if 'rising' in query_data:
                                        rising_queries = query_data['rising']
                                        if isinstance(rising_queries, pd.DataFrame) and not rising_queries.empty:
                                            result['rising_queries'][keyword] = rising_queries.head(10).to_dict('records')
                                            logger.debug(f"获取到 {len(rising_queries)} 个上升查询")
                            else:
                                logger.debug(f"关键词 {keyword} 没有相关查询数据")
                        except Exception as query_error:
                            logger.warning(f"获取关键词 {keyword} 的相关查询失败: {query_error}")
                            import traceback
                            logger.debug(f"查询错误详情: {traceback.format_exc()}")
                    
                    except Exception as e:
                        logger.warning(f"获取关键词 {keyword} 的相关数据失败: {e}")
                        import traceback
                        logger.debug(f"错误详情: {traceback.format_exc()}")
                        continue
                    
                    # 添加延迟以避免频率限制
                    if i < len(keywords) - 1:  # 不是最后一个关键词
                        time.sleep(self.request_delay)
                
                logger.info(f"成功获取相关主题和查询: {keywords}")
                return result
                
            except (requests.exceptions.ReadTimeout, requests.exceptions.ConnectTimeout, 
                   requests.exceptions.Timeout) as e:
                logger.warning(f"请求超时 (第{attempt + 1}次尝试): {e}")
                if attempt == self.retry_attempts:
                    logger.error(f"请求超时，已达到最大重试次数 ({self.retry_attempts})")
                    raise
                continue
                
            except requests.exceptions.ConnectionError as e:
                logger.warning(f"连接错误 (第{attempt + 1}次尝试): {e}")
                if attempt == self.retry_attempts:
                    logger.error(f"连接错误，已达到最大重试次数 ({self.retry_attempts})")
                    raise
                continue
                
            except Exception as e:
                error_msg = str(e).lower()
                # 检查是否是速率限制错误
                if '429' in error_msg or 'too many requests' in error_msg or 'rate limit' in error_msg:
                    logger.warning(f"遇到速率限制 (第{attempt + 1}次尝试): {e}")
                    if attempt == self.retry_attempts:
                        logger.error(f"速率限制，已达到最大重试次数 ({self.retry_attempts})")
                        raise
                    # 对于速率限制，增加更长的等待时间
                    delay = self.request_delay * (3 ** attempt)  # 更激进的退避
                    logger.info(f"速率限制延迟：等待 {delay} 秒...")
                    time.sleep(delay)
                    continue
                else:
                    logger.error(f"获取相关主题和查询失败: {e}")
                    raise
    
    def generate_keyword_ideas(
        self,
        seed_keywords: List[str],
        timeframe: str = None,
        geo_location: str = None,
        include_related: bool = True,
        include_rising: bool = True
    ) -> List[Dict[str, Any]]:
        """
        基于种子关键词生成关键词创意
        
        Args:
            seed_keywords: 种子关键词列表
            timeframe: 时间范围
            geo_location: 地理位置代码
            include_related: 是否包含相关查询
            include_rising: 是否包含上升查询
            
        Returns:
            关键词创意列表
        """
        timeframe = timeframe or self.default_timeframe
        geo_location = geo_location or self.geo_location
        
        all_keywords = []
        all_keyword_data = []
        
        try:
            # 分批处理种子关键词
            for i in range(0, len(seed_keywords), self.max_keywords_per_batch):
                batch_keywords = seed_keywords[i:i + self.max_keywords_per_batch]
                
                logger.info(f"处理关键词批次 {i//self.max_keywords_per_batch + 1}: {batch_keywords}")
                
                # 批次级别的重试机制
                for batch_attempt in range(self.retry_attempts + 1):
                    try:
                        # 获取趋势数据
                        interest_data = self.get_interest_over_time(
                            keywords=batch_keywords,
                            timeframe=timeframe,
                            geo=geo_location
                        )
                        
                        # 处理种子关键词数据
                        for keyword in batch_keywords:
                            keyword_stats = interest_data.get('keyword_stats', {}).get(keyword, {})
                            
                            keyword_data = {
                                'keyword_name': keyword,
                                'avg_monthly_searches': None,  # PyTrends不提供确切的搜索量
                                'monthly_searches': None,
                                'competition_level': 'unspecified',
                                'competition_index': None,
                                'trend_interest': keyword_stats.get('avg_interest', 0),
                                'trend_growth_rate': keyword_stats.get('trend', 0),
                                'trend_region': geo_location,
                                'trend_timeframe': timeframe,
                                'trend_data': json.dumps(interest_data) if interest_data else None,
                                'low_bid_micros': None,
                                'high_bid_micros': None,
                                'currency_code': 'CNY',
                                'language_code': self.language,
                                'update_method': 'pytrends'
                            }
                            
                            all_keyword_data.append(keyword_data)
                            all_keywords.append(keyword)
                        
                        # 获取相关关键词
                        if include_related or include_rising:
                            try:
                                related_data = self.get_related_topics_and_queries(
                                    keywords=batch_keywords,
                                    timeframe=timeframe,
                                    geo=geo_location
                                )
                                
                                # 处理相关查询
                                for keyword in batch_keywords:
                                    if include_related and keyword in related_data.get('related_queries', {}):
                                        related_queries = related_data['related_queries'][keyword]
                                        for query_data in related_queries[:5]:  # 限制数量
                                            query_keyword = query_data.get('query', '')
                                            if query_keyword and query_keyword not in all_keywords:
                                                related_keyword_data = {
                                                    'keyword_name': query_keyword,
                                                    'avg_monthly_searches': None,
                                                    'monthly_searches': None,
                                                    'competition_level': 'unspecified',
                                                    'competition_index': None,
                                                    'trend_interest': query_data.get('value', 0),
                                                    'trend_growth_rate': None,
                                                    'trend_region': geo_location,
                                                    'trend_timeframe': timeframe,
                                                    'trend_data': json.dumps({'source': 'related_query', 'parent_keyword': keyword}),
                                                    'low_bid_micros': None,
                                                    'high_bid_micros': None,
                                                    'currency_code': 'CNY',
                                                    'language_code': self.language,
                                                    'update_method': 'pytrends'
                                                }
                                                all_keyword_data.append(related_keyword_data)
                                                all_keywords.append(query_keyword)
                                    
                                    if include_rising and keyword in related_data.get('rising_queries', {}):
                                        rising_queries = related_data['rising_queries'][keyword]
                                        for query_data in rising_queries[:3]:  # 限制数量
                                            query_keyword = query_data.get('query', '')
                                            if query_keyword and query_keyword not in all_keywords:
                                                rising_keyword_data = {
                                                    'keyword_name': query_keyword,
                                                    'avg_monthly_searches': None,
                                                    'monthly_searches': None,
                                                    'competition_level': 'unspecified',
                                                    'competition_index': None,
                                                    'trend_interest': query_data.get('value', 0),
                                                    'trend_growth_rate': 100,  # 上升查询假设为高增长
                                                    'trend_region': geo_location,
                                                    'trend_timeframe': timeframe,
                                                    'trend_data': json.dumps({'source': 'rising_query', 'parent_keyword': keyword}),
                                                    'low_bid_micros': None,
                                                    'high_bid_micros': None,
                                                    'currency_code': 'CNY',
                                                    'language_code': self.language,
                                                    'update_method': 'pytrends'
                                                }
                                                all_keyword_data.append(rising_keyword_data)
                                                all_keywords.append(query_keyword)
                            except Exception as related_error:
                                logger.warning(f"获取批次 {batch_keywords} 的相关数据失败: {related_error}")
                                # 相关数据失败不影响主要关键词数据，继续处理
                        
                        # 批次成功，跳出重试循环
                        break
                        
                    except Exception as batch_error:
                        logger.warning(f"批次 {batch_keywords} 处理失败 (第{batch_attempt + 1}次尝试): {batch_error}")
                        if batch_attempt == self.retry_attempts:
                            # 最后一次尝试失败，记录错误但继续处理下一批次
                            logger.error(f"批次 {batch_keywords} 处理失败，跳过该批次")
                            break
                        else:
                            # 等待后重试
                            delay = self.request_delay * (2 ** batch_attempt)
                            logger.info(f"批次重试等待 {delay} 秒...")
                            time.sleep(delay)
                
                # 添加延迟以避免频率限制
                if i + self.max_keywords_per_batch < len(seed_keywords):
                    delay_time = self.request_delay * 2  # 批次间增加更长延迟
                    logger.info(f"批次间延迟 {delay_time} 秒...")
                    time.sleep(delay_time)
            
            logger.info(f"PyTrends生成关键词完成: 总计 {len(all_keyword_data)} 个关键词")
            return all_keyword_data
            
        except Exception as e:
            logger.error(f"生成关键词创意失败: {e}")
            raise
    
    def _calculate_trend(self, values: List[float]) -> float:
        """
        计算趋势增长率
        
        Args:
            values: 数值列表
            
        Returns:
            趋势增长率（百分比）
        """
        if len(values) < 2:
            return 0.0
        
        # 简单线性回归计算趋势
        n = len(values)
        x = list(range(n))
        y = values
        
        # 计算斜率
        x_mean = sum(x) / n
        y_mean = sum(y) / n
        
        numerator = sum((x[i] - x_mean) * (y[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return 0.0
        
        slope = numerator / denominator
        
        # 转换为百分比增长率
        if y_mean > 0:
            growth_rate = (slope / y_mean) * 100
        else:
            growth_rate = 0.0
        
        return round(growth_rate, 2)
    
    def _empty_result(self, keywords: List[str]) -> Dict[str, Any]:
        """返回空结果"""
        return {
            'keywords': keywords,
            'timeframe': self.default_timeframe,
            'geo_location': self.geo_location,
            'category': 0,
            'data_points': [],
            'keyword_stats': {keyword: {
                'avg_interest': 0,
                'max_interest': 0,
                'min_interest': 0,
                'trend': 0
            } for keyword in keywords}
        }
    
    def test_connection(self) -> Dict[str, Any]:
        """测试PyTrends连接"""
        try:
            # 尝试获取一个简单的查询
            test_keywords = ['python']
            result = self.get_interest_over_time(
                keywords=test_keywords,
                timeframe='today 3-m',
                geo='',  # 全球
                category=0
            )
            
            return {
                'success': True,
                'message': 'PyTrends连接测试成功',
                'test_data': result
            }
            
        except Exception as e:
            logger.error(f"PyTrends连接测试失败: {e}")
            return {
                'success': False,
                'message': f'PyTrends连接测试失败: {str(e)}',
                'test_data': None
            }

def _create_retry_config(retries: int = 3, backoff_factor: float = 1.0):
    """
    创建兼容的urllib3 Retry配置
    
    Args:
        retries: 重试次数
        backoff_factor: 退避因子
        
    Returns:
        适配urllib3版本的Retry配置字典
    """
    retry_config = {
        'total': retries,
        'read': retries,
        'connect': retries,
        'backoff_factor': backoff_factor,
        'status_forcelist': [429, 500, 502, 503, 504]
    }
    
    # 根据urllib3版本使用正确的参数名称
    if USE_ALLOWED_METHODS:
        # urllib3 >= 1.26.0 使用 allowed_methods
        retry_config['allowed_methods'] = frozenset(['GET', 'POST'])
    else:
        # urllib3 < 1.26.0 使用 method_whitelist
        retry_config['method_whitelist'] = frozenset(['GET', 'POST'])
    
    return retry_config 