<template>
  <div class="keyword-performance">
    <div class="page-header">
      <h2>关键词表现</h2>
      <p>查看关键词在阿里国际站的表现数据，包括曝光量、点击量、点击率等关键指标</p>
    </div>

    <!-- 筛选条件 -->
    <el-card style="margin-bottom: 20px;">
      <template #header>
        <span>筛选条件</span>
      </template>
      
      <el-row :gutter="20">
        <el-col :xs="24" :sm="6">
          <div class="filter-item">
            <label>统计周期：</label>
            <el-select v-model="filters.statisticsType" @change="handleStatisticsTypeChange">
              <el-option label="周统计" value="week" />
              <el-option label="月统计" value="month" />
            </el-select>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="8">
          <div class="filter-item">
            <label>数据周期：</label>
            <el-select 
              v-model="filters.selectedPeriod" 
              placeholder="选择数据周期"
              @change="handlePeriodChange"
            >
              <el-option
                v-for="period in availablePeriods"
                :key="`${period.start_date}-${period.end_date}`"
                :label="`${period.start_date} 至 ${period.end_date}`"
                :value="`${period.start_date}|${period.end_date}`"
              />
            </el-select>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="6">
          <div class="filter-item">
            <label>关键词：</label>
            <el-input
              v-model="filters.keyword"
              placeholder="输入关键词搜索"
              clearable
              @input="handleKeywordInput"
            />
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="4">
          <div class="filter-item">
            <el-button type="primary" @click="loadData" :loading="loading">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button type="success" @click="showSyncDialog" :loading="syncLoading">
              <el-icon><Download /></el-icon>
              同步数据
            </el-button>
            <el-button @click="resetFilters">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </el-col>
      </el-row>
      
      <!-- 高级筛选 -->
      <el-collapse v-model="activeCollapse">
        <el-collapse-item title="高级筛选" name="advanced">
          <el-row :gutter="20">
            <el-col :xs="24" :sm="8">
              <div class="filter-item">
                <label>推广类型：</label>
                <el-select v-model="filters.isP4p">
                  <el-option label="全部" value="ALL" />
                  <el-option label="外贸直通车推广词" value="YES" />
                  <el-option label="非外贸直通车推广词" value="NO" />
                </el-select>
              </div>
            </el-col>
            
            <el-col :xs="24" :sm="8">
              <div class="filter-item">
                <label>关键词状态：</label>
                <el-select v-model="filters.keywordsInUse">
                  <el-option label="全部" value="ALL" />
                  <el-option label="已设置为关键词" value="YES" />
                  <el-option label="未设置为关键词" value="NO" />
                </el-select>
              </div>
            </el-col>
            
            <el-col :xs="24" :sm="8">
              <div class="filter-item">
                <label>曝光状态：</label>
                <el-select v-model="filters.keywordsViewed">
                  <el-option label="全部" value="ALL" />
                  <el-option label="有曝光" value="YES" />
                  <el-option label="无曝光" value="NO" />
                </el-select>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :xs="24" :sm="8">
              <div class="filter-item">
                <label>排序字段：</label>
                <el-select v-model="filters.orderByField">
                  <el-option label="曝光量" value="sumShowCnt" />
                  <el-option label="点击量" value="sumClickCnt" />
                  <el-option label="点击率" value="ctr" />
                  <el-option label="搜索热度" value="searchPvIndex" />
                  <el-option label="卖家竞争度" value="gsTpMemberSetCnt" />
                </el-select>
              </div>
            </el-col>
            
            <el-col :xs="24" :sm="8">
              <div class="filter-item">
                <label>排序方式：</label>
                <el-select v-model="filters.orderByMode">
                  <el-option label="降序" value="desc" />
                  <el-option label="升序" value="asc" />
                </el-select>
              </div>
            </el-col>
          </el-row>
        </el-collapse-item>
      </el-collapse>
    </el-card>

    <!-- 统计概览 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <div class="stat-card">
            <div class="stat-number">{{ overview.totalKeywords || 0 }}</div>
            <div class="stat-label">关键词总数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <div class="stat-card">
            <div class="stat-number">{{ overview.totalShow || 0 }}</div>
            <div class="stat-label">总曝光量</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <div class="stat-card">
            <div class="stat-number">{{ overview.totalClick || 0 }}</div>
            <div class="stat-label">总点击量</div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card>
          <div class="stat-card">
            <div class="stat-number">{{ overview.avgCtr || '0%' }}</div>
            <div class="stat-label">平均点击率</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 关键词表现详情表格 -->
    <el-card>
      <template #header>
        <div class="card-header">
          <span>关键词表现详情</span>
          <div>
            <el-button type="text" @click="exportData">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="tableData" 
        style="width: 100%"
        v-loading="loading"
        :default-sort="{ prop: 'sumShowCnt', order: 'descending' }"
      >
        <el-table-column prop="keyword" label="关键词" min-width="200" fixed="left" sortable />
        <el-table-column prop="sumShowCnt" label="曝光量" width="120" sortable />
        <el-table-column prop="sumClickCnt" label="点击量" width="120" sortable />
        <el-table-column label="点击率" width="120" sortable>
          <template #default="scope">
            {{ formatPercentage(scope.row.ctr) }}
          </template>
        </el-table-column>
        <el-table-column prop="searchPvIndex" label="搜索热度" width="120" sortable />
        <el-table-column prop="gsTpMemberSetCnt" label="卖家竞争度" width="130" sortable />
        <el-table-column prop="avgSumShowCnt" label="Top10平均曝光" width="140" sortable />
        <el-table-column prop="avgSumClickCnt" label="Top10平均点击" width="140" sortable />
        <el-table-column prop="sumP4pShowCnt" label="直通车曝光" width="120" sortable />
        <el-table-column prop="sumP4pClickCnt" label="直通车点击" width="120" sortable />
        <el-table-column label="是否推广词" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.isP4pKw ? 'success' : 'info'">
              {{ scope.row.isP4pKw ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="viewTrend(scope.row)">
              趋势
            </el-button>
            <el-button type="text" size="small" @click="viewDetails(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div style="text-align: right; margin-top: 20px;">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 关键词详情弹窗 -->
    <el-dialog v-model="showDetails" title="关键词详情" width="600px">
      <div v-if="selectedKeyword">
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="关键词">{{ selectedKeyword.keyword }}</el-descriptions-item>
          <el-descriptions-item label="是否推广词">
            <el-tag :type="selectedKeyword.isP4pKw ? 'success' : 'info'">
              {{ selectedKeyword.isP4pKw ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="曝光量">{{ selectedKeyword.sumShowCnt }}</el-descriptions-item>
          <el-descriptions-item label="点击量">{{ selectedKeyword.sumClickCnt }}</el-descriptions-item>
          <el-descriptions-item label="点击率">{{ formatPercentage(selectedKeyword.ctr) }}</el-descriptions-item>
          <el-descriptions-item label="搜索热度">{{ selectedKeyword.searchPvIndex }}</el-descriptions-item>
          <el-descriptions-item label="卖家竞争度">{{ selectedKeyword.gsTpMemberSetCnt }}</el-descriptions-item>
          <el-descriptions-item label="Top10平均曝光">{{ selectedKeyword.avgSumShowCnt }}</el-descriptions-item>
          <el-descriptions-item label="Top10平均点击">{{ selectedKeyword.avgSumClickCnt }}</el-descriptions-item>
          <el-descriptions-item label="直通车曝光">{{ selectedKeyword.sumP4pShowCnt }}</el-descriptions-item>
          <el-descriptions-item label="直通车点击">{{ selectedKeyword.sumP4pClickCnt }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 趋势图弹窗 -->
    <el-dialog v-model="showTrend" title="关键词趋势" width="800px">
      <div v-if="selectedKeyword">
        <h4>{{ selectedKeyword.keyword }} - 趋势分析</h4>
        <div class="trend-placeholder">
          <p>这里将显示关键词的趋势图表</p>
          <p>包括曝光趋势、点击趋势、点击率趋势等</p>
        </div>
      </div>
    </el-dialog>

    <!-- 数据同步对话框 -->
    <el-dialog
      v-model="syncDialogVisible"
      title="同步关键词表现数据"
      width="600px"
    >
      <el-form :model="syncForm" label-width="100px">
        <el-form-item label="统计周期">
          <el-select v-model="syncForm.statisticsType" style="width: 100%">
            <el-option label="周统计" value="week" />
            <el-option label="月统计" value="month" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="syncForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY年MM月DD日"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="强制更新">
          <el-switch v-model="syncForm.forceUpdate" />
          <div style="font-size: 12px; color: #909399; margin-top: 4px;">
            开启后将覆盖已有的数据
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="syncDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSync" :loading="syncLoading">
          开始同步
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Download, Refresh } from '@element-plus/icons-vue'
import { keywordPerformanceApi } from '../../services/alibaba'

export default defineComponent({
  name: 'KeywordPerformance',
  components: {
    Search,
    Download,
    Refresh
  },
  setup() {
    const loading = ref(false)
    const showDetails = ref(false)
    const showTrend = ref(false)
    const selectedKeyword = ref(null)
    const activeCollapse = ref([])
    const syncDialogVisible = ref(false)
    const syncLoading = ref(false)
    
    // 筛选条件
    const filters = ref({
      statisticsType: 'week',
      selectedPeriod: '',
      keyword: '',
      isP4p: 'ALL',
      keywordsInUse: 'ALL',
      keywordsViewed: 'ALL',
      orderByField: 'sumShowCnt',
      orderByMode: 'desc'
    })
    
    // 同步表单
    const syncForm = ref({
      statisticsType: 'week',
      dateRange: [],
      forceUpdate: false
    })
    
    // 可用数据周期
    const availablePeriods = ref([])
    
    // 表格数据
    const tableData = ref([])
    const allTableData = ref([]) // 存储所有数据，用于分页
    
    // 统计概览
    const overview = computed(() => {
      const totalKeywords = allTableData.value.length
      const totalShow = allTableData.value.reduce((sum, item) => sum + (item.sumShowCnt || 0), 0)
      const totalClick = allTableData.value.reduce((sum, item) => sum + (item.sumClickCnt || 0), 0)
      const avgCtr = totalShow > 0 ? ((totalClick / totalShow) * 100).toFixed(2) + '%' : '0%'
      
      return {
        totalKeywords,
        totalShow,
        totalClick,
        avgCtr
      }
    })
    
    // 分页
    const pagination = ref({
      page: 1,
      size: 10, // 修改为10条一页
      total: 0
    })

    // 格式化百分比
    const formatPercentage = (value) => {
      if (!value) return '0%'
      const num = parseFloat(value)
      if (num <= 1) {
        return (num * 100).toFixed(2) + '%'
      }
      return num.toFixed(2) + '%'
    }

    // 获取可用数据周期
    const loadAvailablePeriods = async () => {
      try {
        const response = await keywordPerformanceApi.getDatePeriods(filters.value.statisticsType)
        if (response.data && response.data.resultList) {
          availablePeriods.value = response.data.resultList
          // 自动选择第一个可用周期
          if (availablePeriods.value.length > 0 && !filters.value.selectedPeriod) {
            const latest = availablePeriods.value[0]
            filters.value.selectedPeriod = `${latest.start_date}|${latest.end_date}`
          }
        }
      } catch (error) {
        console.error('获取数据周期失败:', error)
      }
    }

    // 加载数据
    const loadData = async () => {
      if (!filters.value.selectedPeriod) {
        // 如果没有选择周期，尝试自动选择第一个
        if (availablePeriods.value.length > 0) {
          const latest = availablePeriods.value[0]
          filters.value.selectedPeriod = `${latest.start_date}|${latest.end_date}`
        } else {
          ElMessage.warning('请选择数据周期')
          return
        }
      }

      loading.value = true
      try {
        const [startDate, endDate] = filters.value.selectedPeriod.split('|')
        
        // 使用简单的查询参数
        const params = {
          start_date: startDate,
          end_date: endDate,
          keyword: filters.value.keyword || undefined
        }

        // 根据统计类型调用不同API
        let response
        if (filters.value.statisticsType === 'week') {
          response = await keywordPerformanceApi.getWeeklyData(params)
        } else {
          response = await keywordPerformanceApi.getMonthlyData(params)
        }
        
        if (response.code === 200) {
          // 处理返回的数据
          const data = response.data || []
          allTableData.value = data.map(item => ({
            keyword: item.keyword,
            sumShowCnt: item.sum_show_cnt,
            sumClickCnt: item.sum_click_cnt,
            ctr: item.ctr,
            searchPvIndex: item.search_pv_index,
            gsTpMemberSetCnt: item.gs_tp_member_set_cnt,
            avgSumShowCnt: item.avg_sum_show_cnt,
            avgSumClickCnt: item.avg_sum_click_cnt,
            sumP4pShowCnt: item.sum_p4p_show_cnt,
            sumP4pClickCnt: item.sum_p4p_click_cnt,
            isP4pKw: item.is_p4p_kw
          }))
          
          // 重置到第一页并更新分页数据
          pagination.value.page = 1
          updatePaginatedData()
        } else {
          ElMessage.error(response.message || '获取数据失败')
        }
      } catch (error) {
        console.error('获取关键词表现数据失败:', error)
        ElMessage.error('获取关键词表现数据失败')
      } finally {
        loading.value = false
      }
    }

    // 统计类型变化
    const handleStatisticsTypeChange = () => {
      loadAvailablePeriods()
    }

    // 数据周期变化
    const handlePeriodChange = () => {
      // 周期变化时可以触发其他逻辑
    }

    // 关键词输入
    const handleKeywordInput = () => {
      // 可以添加防抖逻辑
    }

    // 重置筛选条件
    const resetFilters = () => {
      filters.value = {
        statisticsType: 'week',
        selectedPeriod: '',
        keyword: '',
        isP4p: 'ALL',
        keywordsInUse: 'ALL',
        keywordsViewed: 'ALL',
        orderByField: 'sumShowCnt',
        orderByMode: 'desc'
      }
      allTableData.value = []
      tableData.value = []
      pagination.value.page = 1
      pagination.value.total = 0
      loadAvailablePeriods()
    }

    // 分页大小变化
    const handleSizeChange = (val) => {
      pagination.value.size = val
      pagination.value.page = 1 // 重置到第一页
      updatePaginatedData()
    }

    // 当前页变化
    const handleCurrentChange = (val) => {
      pagination.value.page = val
      updatePaginatedData()
    }

    // 查看详情
    const viewDetails = (row) => {
      selectedKeyword.value = row
      showDetails.value = true
    }

    // 查看趋势
    const viewTrend = (row) => {
      selectedKeyword.value = row
      showTrend.value = true
    }

    // 导出数据
    const exportData = () => {
      ElMessage.info('导出功能开发中...')
    }

    // 显示数据同步对话框
    const showSyncDialog = () => {
      // 初始化同步表单，设置默认日期范围
      const today = new Date()
      const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      
      syncForm.value = {
        statisticsType: filters.value.statisticsType || 'week',
        dateRange: [
          lastWeek.toISOString().split('T')[0],
          today.toISOString().split('T')[0]
        ],
        forceUpdate: false
      }
      syncDialogVisible.value = true
    }

    // 同步数据
    const handleSync = async () => {
      // 验证表单
      if (!syncForm.value.dateRange || syncForm.value.dateRange.length !== 2) {
        ElMessage.warning('请选择时间范围')
        return
      }
      
      if (!syncForm.value.dateRange[0] || !syncForm.value.dateRange[1]) {
        ElMessage.warning('请选择完整的时间范围')
        return
      }
      
      syncLoading.value = true
      try {
        const response = await keywordPerformanceApi.syncData(syncForm.value)
        if (response.code === 200) {
          ElMessage.success(`数据同步成功！同步了 ${response.data.keywords} 个关键词，${response.data.performance_records} 条表现记录`)
          syncDialogVisible.value = false
          
          // 刷新当前页面数据
          await loadAvailablePeriods()
          await loadData()
        } else {
          ElMessage.error(response.message || '数据同步失败')
        }
      } catch (error) {
        console.error('同步数据失败:', error)
        if (error.response && error.response.status === 400) {
          ElMessage.error(error.response.data.detail || '数据同步失败，请检查阿里国际站授权状态')
        } else if (error.response && error.response.status === 422) {
          ElMessage.error('请求参数错误，请检查时间范围设置')
        } else {
          ElMessage.error('数据同步失败，请稍后重试')
        }
      } finally {
        syncLoading.value = false
      }
    }

    // 分页数据计算
    const updatePaginatedData = () => {
      const start = (pagination.value.page - 1) * pagination.value.size
      const end = start + pagination.value.size
      tableData.value = allTableData.value.slice(start, end)
      pagination.value.total = allTableData.value.length
    }

    onMounted(async () => {
      await loadAvailablePeriods()
      // 自动加载数据
      setTimeout(async () => {
        if (availablePeriods.value.length > 0) {
          if (!filters.value.selectedPeriod) {
            const latest = availablePeriods.value[0]
            filters.value.selectedPeriod = `${latest.start_date}|${latest.end_date}`
          }
          await loadData()
        }
      }, 500)
    })

    return {
      loading,
      showDetails,
      showTrend,
      selectedKeyword,
      activeCollapse,
      filters,
      syncForm,
      availablePeriods,
      tableData,
      allTableData,
      overview,
      pagination,
      formatPercentage,
      updatePaginatedData,
      loadData,
      handleStatisticsTypeChange,
      handlePeriodChange,
      handleKeywordInput,
      resetFilters,
      handleSizeChange,
      handleCurrentChange,
      viewDetails,
      viewTrend,
      exportData,
      syncDialogVisible,
      syncLoading,
      showSyncDialog,
      handleSync
    }
  }
})
</script>

<style scoped>
.keyword-performance {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.filter-item label {
  display: inline-block;
  width: 80px;
  text-align: right;
  margin-right: 10px;
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.stat-card {
  text-align: center;
  padding: 20px 0;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.trend-placeholder {
  text-align: center;
  padding: 40px;
  background-color: #f8f9fa;
  border-radius: 4px;
  color: #909399;
}

@media (max-width: 768px) {
  .filter-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-item label {
    width: auto;
    margin-bottom: 5px;
  }
}
</style> 