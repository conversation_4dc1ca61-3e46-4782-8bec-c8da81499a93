# AI站群系统前端启动脚本
Write-Host "启动AI站群系统前端..." -ForegroundColor Green

# 检查Node.js
try {
    $nodeVersion = node --version 2>$null
    Write-Host "Node.js版本: $nodeVersion" -ForegroundColor Cyan
} catch {
    Write-Host "错误: 未安装Node.js或不在PATH中" -ForegroundColor Red
    exit 1
}

# 检查依赖
if (!(Test-Path "node_modules")) {
    Write-Host "正在安装依赖..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "依赖安装失败!" -ForegroundColor Red
        exit 1
    }
}

# 启动开发服务器
Write-Host "启动开发服务器..." -ForegroundColor Yellow
Write-Host "前端地址: http://localhost:8080" -ForegroundColor Green
Write-Host "按 Ctrl+C 停止服务" -ForegroundColor Yellow

npm run serve 