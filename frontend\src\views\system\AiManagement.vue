<template>
  <div class="ai-management">
    <div class="page-header">
      <h2>AI管理</h2>
      <p>管理AI服务配置，包括API密钥和服务参数</p>
    </div>

    <!-- 操作栏 -->
    <el-card style="margin-bottom: 20px;">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-input
            v-model="searchText"
            placeholder="搜索配置名称"
            style="width: 300px; margin-right: 10px;"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select v-model="serviceTypeFilter" placeholder="服务类型" style="width: 150px; margin-right: 10px;" @change="loadData">
            <el-option label="全部" value="" />
            <el-option label="DIFY" value="DIFY" />
          </el-select>
          
          <el-select v-model="statusFilter" placeholder="状态" style="width: 120px;" @change="loadData">
            <el-option label="全部" value="" />
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-col>
        <el-col :span="8" style="text-align: right;">
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            新增配置
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 配置列表 -->
    <el-card>
      <el-table :data="tableData" v-loading="loading" style="width: 100%">
        <el-table-column prop="name" label="配置名称" min-width="200" />
        <el-table-column prop="service_type" label="服务类型" width="120" />
        <el-table-column label="API密钥" width="200">
          <template #default="scope">
            <span>{{ maskApiKey(scope.row.api_key) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="base_url" label="API地址" min-width="250" />
        <el-table-column prop="model_name" label="模型名称" width="150" />
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
              {{ scope.row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="text" size="small" @click="testConnection(scope.row)">
              测试
            </el-button>
            <el-button type="text" size="small" @click="editConfig(scope.row)">
              编辑
            </el-button>
            <el-button type="text" size="small" danger @click="deleteConfig(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div style="text-align: right; margin-top: 20px;">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog 
      v-model="showDialog" 
      :title="dialogTitle" 
      width="600px"
      @close="resetForm"
    >
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="formRules" 
        label-width="120px"
      >
        <el-form-item label="配置名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入配置名称" />
        </el-form-item>
        
        <el-form-item label="服务类型" prop="service_type">
          <el-select v-model="formData.service_type" placeholder="请选择服务类型" style="width: 100%;">
            <el-option label="DIFY" value="DIFY" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="API密钥" prop="api_key">
          <el-input 
            v-model="formData.api_key" 
            type="password" 
            show-password
            placeholder="请输入API密钥" 
          />
        </el-form-item>
        
        <el-form-item label="API地址" prop="base_url">
          <el-input v-model="formData.base_url" placeholder="如：http://ai.yufeiind.cn:81/v1" />
        </el-form-item>
        
        <el-form-item label="模型名称">
          <el-input v-model="formData.model_name" placeholder="可选，如：gpt-4" />
        </el-form-item>
        
        <el-form-item label="最大Token数">
          <el-input-number v-model="formData.max_tokens" :min="100" :max="32000" style="width: 100%;" />
        </el-form-item>
        
        <el-form-item label="创造性参数">
          <el-input-number 
            v-model="formData.temperature" 
            :min="0" 
            :max="2" 
            :step="0.1" 
            style="width: 100%;" 
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-switch v-model="formData.is_active" active-text="启用" inactive-text="禁用" />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input 
            v-model="formData.description" 
            type="textarea" 
            :rows="3" 
            placeholder="可选，配置描述信息" 
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showDialog = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import apiClient from '@/utils/request'

export default defineComponent({
  name: 'AiManagement',
  components: {
    Search,
    Plus
  },
  setup() {
    const loading = ref(false)
    const submitting = ref(false)
    const showDialog = ref(false)
    const isEdit = ref(false)
    const searchText = ref('')
    const serviceTypeFilter = ref('')
    const statusFilter = ref('')
    const tableData = ref([])
    const formRef = ref()
    
    const pagination = reactive({
      page: 1,
      size: 10,
      total: 0
    })
    
    const formData = reactive({
      name: '',
      service_type: 'DIFY',
      api_key: '',
      base_url: 'http://ai.yufeiind.cn:81/v1',
      model_name: '',
      max_tokens: 4000,
      temperature: 0.7,
      is_active: true,
      description: ''
    })
    
    const formRules = {
      name: [
        { required: true, message: '请输入配置名称', trigger: 'blur' }
      ],
      service_type: [
        { required: true, message: '请选择服务类型', trigger: 'change' }
      ],
      api_key: [
        { required: true, message: '请输入API密钥', trigger: 'blur' }
      ]
    }
    
    const dialogTitle = computed(() => isEdit.value ? '编辑AI配置' : '新增AI配置')
    
    // 加载数据
    const loadData = async () => {
      loading.value = true
      try {
        const params = {
          page: pagination.page,
          size: pagination.size,
          search: searchText.value || undefined,
          service_type: serviceTypeFilter.value || undefined,
          is_active: statusFilter.value !== '' ? statusFilter.value : undefined
        }
        
        const response = await apiClient.get('/v1/ai-config/', { params })
        // 检查响应数据结构（request.js已经处理了response.data）
        if (response && response.items) {
          tableData.value = response.items
          pagination.total = response.total
        } else {
          console.warn('AI配置API返回数据格式异常:', response)
          tableData.value = []
          pagination.total = 0
        }
      } catch (error) {
        ElMessage.error('加载数据失败: ' + error.message)
      } finally {
        loading.value = false
      }
    }
    
    // 搜索处理
    let searchTimer = null
    const handleSearch = () => {
      clearTimeout(searchTimer)
      searchTimer = setTimeout(() => {
        pagination.page = 1
        loadData()
      }, 500)
    }
    
    // 分页处理
    const handleSizeChange = (size) => {
      pagination.size = size
      pagination.page = 1
      loadData()
    }
    
    const handleCurrentChange = (page) => {
      pagination.page = page
      loadData()
    }
    
    // 显示创建对话框
    const showCreateDialog = () => {
      isEdit.value = false
      resetForm()
      showDialog.value = true
    }
    
    // 编辑配置
    const editConfig = (row) => {
      isEdit.value = true
      Object.assign(formData, row)
      showDialog.value = true
    }
    
    // 重置表单
    const resetForm = () => {
      Object.assign(formData, {
        name: '',
        service_type: 'DIFY',
        api_key: '',
        base_url: 'http://ai.yufeiind.cn:81/v1',
        model_name: '',
        max_tokens: 4000,
        temperature: 0.7,
        is_active: true,
        description: ''
      })
      if (formRef.value) {
        formRef.value.clearValidate()
      }
    }
    
    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return
      
      try {
        await formRef.value.validate()
        submitting.value = true
        
        if (isEdit.value) {
          await apiClient.put(`/v1/ai-config/${formData.id}`, formData)
          ElMessage.success('更新成功')
        } else {
          await apiClient.post('/v1/ai-config/', formData)
          ElMessage.success('创建成功')
        }
        
        showDialog.value = false
        await loadData()
      } catch (error) {
        if (error.name !== 'validation-error') {
          ElMessage.error('操作失败: ' + error.message)
        }
      } finally {
        submitting.value = false
      }
    }
    
    // 删除配置
    const deleteConfig = async (row) => {
      try {
        await ElMessageBox.confirm('确定要删除这个AI配置吗？', '确认删除', {
          type: 'warning'
        })
        
        await apiClient.delete(`/v1/ai-config/${row.id}`)
        ElMessage.success('删除成功')
        await loadData()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败: ' + error.message)
        }
      }
    }
    
    // 测试连接
    const testConnection = async (row) => {
      try {
        const response = await apiClient.post(`/v1/ai-config/${row.id}/test`)
        if (response.status === 'success') {
          ElMessage.success('连接测试成功')
        } else {
          ElMessage.error('连接测试失败: ' + response.message)
        }
      } catch (error) {
        ElMessage.error('连接测试失败: ' + error.message)
      }
    }
    
    // 掩码API密钥
    const maskApiKey = (apiKey) => {
      if (!apiKey) return ''
      if (apiKey.length <= 8) return apiKey
      return apiKey.substring(0, 4) + '****' + apiKey.substring(apiKey.length - 4)
    }
    
    // 格式化日期时间
    const formatDateTime = (dateTime) => {
      if (!dateTime) return ''
      return new Date(dateTime).toLocaleString('zh-CN')
    }
    
    onMounted(() => {
      loadData()
    })
    
    return {
      loading,
      submitting,
      showDialog,
      isEdit,
      searchText,
      serviceTypeFilter,
      statusFilter,
      tableData,
      pagination,
      formData,
      formRules,
      formRef,
      dialogTitle,
      loadData,
      handleSearch,
      handleSizeChange,
      handleCurrentChange,
      showCreateDialog,
      editConfig,
      resetForm,
      submitForm,
      deleteConfig,
      testConnection,
      maskApiKey,
      formatDateTime
    }
  }
})
</script>

<style scoped>
.ai-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}
</style> 